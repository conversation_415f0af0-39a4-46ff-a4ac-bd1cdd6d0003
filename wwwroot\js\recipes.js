// إدارة الوصفات
let recipes = [];
let filteredRecipes = [];

// تحميل الصفحة
document.addEventListener('DOMContentLoaded', function() {
    console.log('🔓 صفحة إدارة الوصفات - تم تجاوز فحص المصادقة');
    loadRecipes();
});

// تحميل بيانات الوصفات
function loadRecipes() {
    console.log('تحميل بيانات الوصفات...');
    
    // بيانات تجريبية
    recipes = [
        {
            id: 1,
            name: 'خبز أبيض عادي',
            category: 'bread',
            ingredients: [
                { name: 'دقيق أبيض', quantity: 1000, unit: 'جرام', cost: 450 },
                { name: 'خميرة فورية', quantity: 10, unit: 'جرام', cost: 25 },
                { name: 'ملح', quantity: 15, unit: 'جرام', cost: 2 },
                { name: 'سكر', quantity: 20, unit: 'جرام', cost: 16 },
                { name: 'زيت', quantity: 50, unit: 'مل', cost: 30 }
            ],
            prepTime: 120,
            servings: 20,
            description: 'وصفة الخبز الأبيض التقليدي',
            totalCost: 523,
            isActive: true,
            createdDate: '2024-12-15'
        },
        {
            id: 2,
            name: 'كيك شوكولاتة',
            category: 'cake',
            ingredients: [
                { name: 'دقيق أبيض', quantity: 300, unit: 'جرام', cost: 135 },
                { name: 'سكر', quantity: 200, unit: 'جرام', cost: 160 },
                { name: 'كاكاو', quantity: 50, unit: 'جرام', cost: 100 },
                { name: 'بيض', quantity: 3, unit: 'حبة', cost: 90 },
                { name: 'زبدة', quantity: 100, unit: 'جرام', cost: 200 }
            ],
            prepTime: 90,
            servings: 8,
            description: 'كيك شوكولاتة لذيذ ومميز',
            totalCost: 685,
            isActive: true,
            createdDate: '2024-12-14'
        },
        {
            id: 3,
            name: 'معجنات بالجبن',
            category: 'pastry',
            ingredients: [
                { name: 'دقيق أبيض', quantity: 500, unit: 'جرام', cost: 225 },
                { name: 'جبن أبيض', quantity: 200, unit: 'جرام', cost: 400 },
                { name: 'زيت', quantity: 100, unit: 'مل', cost: 60 },
                { name: 'خميرة فورية', quantity: 7, unit: 'جرام', cost: 17.5 }
            ],
            prepTime: 150,
            servings: 15,
            description: 'معجنات محشوة بالجبن الطازج',
            totalCost: 702.5,
            isActive: true,
            createdDate: '2024-12-13'
        },
        {
            id: 4,
            name: 'بسكويت سادة',
            category: 'cookies',
            ingredients: [
                { name: 'دقيق أبيض', quantity: 400, unit: 'جرام', cost: 180 },
                { name: 'سكر', quantity: 150, unit: 'جرام', cost: 120 },
                { name: 'زبدة', quantity: 150, unit: 'جرام', cost: 300 },
                { name: 'بيض', quantity: 1, unit: 'حبة', cost: 30 }
            ],
            prepTime: 60,
            servings: 30,
            description: 'بسكويت سادة مقرمش',
            totalCost: 630,
            isActive: false,
            createdDate: '2024-12-12'
        }
    ];
    
    filteredRecipes = [...recipes];
    displayRecipes(filteredRecipes);
    updateStatistics();
}

// عرض بيانات الوصفات
function displayRecipes(items) {
    const tbody = document.getElementById('recipesTableBody');
    
    if (items.length === 0) {
        tbody.innerHTML = `
            <tr>
                <td colspan="7" class="text-center text-muted">
                    <i class="fas fa-utensils fa-2x mb-2"></i>
                    <p>لا توجد وصفات</p>
                </td>
            </tr>
        `;
        return;
    }
    
    tbody.innerHTML = items.map(recipe => `
        <tr>
            <td>
                <div>
                    <strong>${recipe.name}</strong>
                    <br>
                    <small class="text-muted">${recipe.description}</small>
                </div>
            </td>
            <td>
                <span class="badge ${getCategoryBadge(recipe.category)}">
                    ${getCategoryText(recipe.category)}
                </span>
            </td>
            <td>${recipe.ingredients.length}</td>
            <td><strong>${formatCurrency(recipe.totalCost)}</strong></td>
            <td>${recipe.prepTime} دقيقة</td>
            <td>
                <span class="badge ${recipe.isActive ? 'bg-success' : 'bg-secondary'}">
                    ${recipe.isActive ? 'نشط' : 'غير نشط'}
                </span>
            </td>
            <td>
                <div class="btn-group btn-group-sm">
                    <button class="btn btn-outline-primary" onclick="viewRecipe(${recipe.id})" title="عرض التفاصيل">
                        <i class="fas fa-eye"></i>
                    </button>
                    <button class="btn btn-outline-success" onclick="editRecipe(${recipe.id})" title="تعديل">
                        <i class="fas fa-edit"></i>
                    </button>
                    <button class="btn btn-outline-info" onclick="copyRecipe(${recipe.id})" title="نسخ">
                        <i class="fas fa-copy"></i>
                    </button>
                    <button class="btn btn-outline-danger" onclick="deleteRecipe(${recipe.id})" title="حذف">
                        <i class="fas fa-trash"></i>
                    </button>
                </div>
            </td>
        </tr>
    `).join('');
}

// تحديث الإحصائيات
function updateStatistics() {
    const totalRecipes = recipes.length;
    const activeRecipes = recipes.filter(r => r.isActive).length;
    const avgCost = recipes.length > 0 ? recipes.reduce((sum, r) => sum + r.totalCost, 0) / recipes.length : 0;
    const totalIngredients = recipes.reduce((sum, r) => sum + r.ingredients.length, 0);
    
    document.getElementById('totalRecipes').textContent = totalRecipes;
    document.getElementById('activeRecipes').textContent = activeRecipes;
    document.getElementById('avgCost').textContent = formatCurrency(avgCost);
    document.getElementById('totalIngredients').textContent = totalIngredients;
}

// تصفية الوصفات
function filterRecipes() {
    const categoryFilter = document.getElementById('categoryFilter').value;
    const searchTerm = document.getElementById('searchRecipes').value.toLowerCase();
    
    filteredRecipes = recipes.filter(recipe => {
        const matchesCategory = !categoryFilter || recipe.category === categoryFilter;
        const matchesSearch = !searchTerm || recipe.name.toLowerCase().includes(searchTerm);
        
        return matchesCategory && matchesSearch;
    });
    
    displayRecipes(filteredRecipes);
}

// إظهار نافذة إضافة وصفة
function showAddRecipeModal() {
    document.getElementById('recipeForm').reset();
    document.getElementById('ingredientsList').innerHTML = '';
    addIngredient(); // إضافة مكون واحد افتراضي
    const modal = new bootstrap.Modal(document.getElementById('addRecipeModal'));
    modal.show();
}

// إضافة مكون جديد
function addIngredient() {
    const ingredientsList = document.getElementById('ingredientsList');
    const ingredientIndex = ingredientsList.children.length;
    
    const ingredientDiv = document.createElement('div');
    ingredientDiv.className = 'row mb-2 ingredient-row';
    ingredientDiv.innerHTML = `
        <div class="col-md-4">
            <input type="text" class="form-control" placeholder="اسم المكون" required>
        </div>
        <div class="col-md-2">
            <input type="number" class="form-control" placeholder="الكمية" step="0.001" required>
        </div>
        <div class="col-md-2">
            <select class="form-select" required>
                <option value="">الوحدة</option>
                <option value="جرام">جرام</option>
                <option value="كيلوجرام">كيلوجرام</option>
                <option value="مل">مل</option>
                <option value="لتر">لتر</option>
                <option value="حبة">حبة</option>
                <option value="ملعقة">ملعقة</option>
            </select>
        </div>
        <div class="col-md-3">
            <input type="number" class="form-control" placeholder="التكلفة (ر.ي)" step="0.001" required>
        </div>
        <div class="col-md-1">
            <button type="button" class="btn btn-outline-danger btn-sm" onclick="removeIngredient(this)">
                <i class="fas fa-times"></i>
            </button>
        </div>
    `;
    
    ingredientsList.appendChild(ingredientDiv);
}

// حذف مكون
function removeIngredient(button) {
    button.closest('.ingredient-row').remove();
}

// حفظ الوصفة
function saveRecipe() {
    const form = document.getElementById('recipeForm');
    if (!form.checkValidity()) {
        form.reportValidity();
        return;
    }
    
    const name = document.getElementById('recipeName').value;
    const category = document.getElementById('recipeCategory').value;
    const prepTime = parseInt(document.getElementById('prepTime').value) || 0;
    const servings = parseInt(document.getElementById('servings').value) || 1;
    const description = document.getElementById('recipeDescription').value;
    
    // جمع المكونات
    const ingredientRows = document.querySelectorAll('.ingredient-row');
    const ingredients = [];
    let totalCost = 0;
    
    ingredientRows.forEach(row => {
        const inputs = row.querySelectorAll('input, select');
        const ingredient = {
            name: inputs[0].value,
            quantity: parseFloat(inputs[1].value),
            unit: inputs[2].value,
            cost: parseFloat(inputs[3].value)
        };
        ingredients.push(ingredient);
        totalCost += ingredient.cost;
    });
    
    const newRecipe = {
        id: recipes.length + 1,
        name: name,
        category: category,
        ingredients: ingredients,
        prepTime: prepTime,
        servings: servings,
        description: description,
        totalCost: totalCost,
        isActive: true,
        createdDate: new Date().toISOString().split('T')[0]
    };
    
    recipes.push(newRecipe);
    
    bootstrap.Modal.getInstance(document.getElementById('addRecipeModal')).hide();
    loadRecipes();
    alert('تم حفظ الوصفة بنجاح!');
}

// دوال مساعدة
function getCategoryText(category) {
    const categories = {
        'bread': 'خبز',
        'pastry': 'معجنات',
        'cake': 'كيك',
        'cookies': 'بسكويت'
    };
    return categories[category] || category;
}

function getCategoryBadge(category) {
    const badges = {
        'bread': 'bg-primary',
        'pastry': 'bg-success',
        'cake': 'bg-warning',
        'cookies': 'bg-info'
    };
    return badges[category] || 'bg-secondary';
}

function formatCurrency(amount) {
    return new Intl.NumberFormat('ar-YE', {
        style: 'currency',
        currency: 'YER',
        minimumFractionDigits: 3,
        maximumFractionDigits: 3
    }).format(amount).replace('YER', 'ر.ي');
}

// وظائف إضافية
function viewRecipe(id) {
    const recipe = recipes.find(r => r.id === id);
    if (recipe) {
        alert(`تفاصيل الوصفة: ${recipe.name}\nالمكونات: ${recipe.ingredients.length}\nالتكلفة: ${formatCurrency(recipe.totalCost)}`);
    }
}

function editRecipe(id) {
    alert('تعديل الوصفة - سيتم تطويره لاحقاً');
}

function copyRecipe(id) {
    alert('نسخ الوصفة - سيتم تطويره لاحقاً');
}

function deleteRecipe(id) {
    if (confirm('هل أنت متأكد من حذف هذه الوصفة؟')) {
        recipes = recipes.filter(r => r.id !== id);
        loadRecipes();
        alert('تم حذف الوصفة بنجاح!');
    }
}

function generateRecipeReport() {
    alert('تقرير الوصفات - سيتم تطويره لاحقاً');
}
