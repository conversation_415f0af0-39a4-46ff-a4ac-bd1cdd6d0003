<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إدارة الأصناف والمنتجات - نظام إدارة مخبوزات ANW</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="css/style.css" rel="stylesheet">
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary">
        <div class="container-fluid">
            <a class="navbar-brand" href="dashboard.html">
                <i class="fas fa-bread-slice me-2"></i>
                نظام إدارة مخبوزات ANW
            </a>
            <div class="navbar-nav ms-auto">
                <a class="nav-link" href="dashboard.html">
                    <i class="fas fa-home"></i> الرئيسية
                </a>
                <a class="nav-link" href="#" onclick="logout()">
                    <i class="fas fa-sign-out-alt"></i> تسجيل الخروج
                </a>
            </div>
        </div>
    </nav>

    <div class="container-fluid mt-4">
        <div class="row">
            <!-- Sidebar -->
            <div class="col-md-3">
                <div class="card">
                    <div class="card-header">
                        <h5><i class="fas fa-list"></i> القوائم</h5>
                    </div>
                    <div class="list-group list-group-flush">
                        <a href="dashboard.html" class="list-group-item list-group-item-action">
                            <i class="fas fa-tachometer-alt"></i> لوحة التحكم
                        </a>
                        <a href="units.html" class="list-group-item list-group-item-action">
                            <i class="fas fa-balance-scale"></i> وحدات القياس
                        </a>
                        <a href="parties.html" class="list-group-item list-group-item-action">
                            <i class="fas fa-handshake"></i> العملاء والموردين
                        </a>
                        <a href="items-management.html" class="list-group-item list-group-item-action active">
                            <i class="fas fa-boxes"></i> الأصناف والمنتجات
                        </a>
                        <a href="invoices.html" class="list-group-item list-group-item-action">
                            <i class="fas fa-file-invoice"></i> الفواتير
                        </a>
                        <a href="inventory.html" class="list-group-item list-group-item-action">
                            <i class="fas fa-warehouse"></i> المخزون
                        </a>
                    </div>
                </div>
            </div>

            <!-- Main Content -->
            <div class="col-md-9">
                <div class="card">
                    <div class="card-header">
                        <h4><i class="fas fa-boxes"></i> إدارة الأصناف والمنتجات</h4>
                    </div>
                    <div class="card-body">
                        <!-- Navigation Tabs -->
                        <ul class="nav nav-tabs" id="itemsTabs" role="tablist">
                            <li class="nav-item" role="presentation">
                                <button class="nav-link active" id="materials-tab" data-bs-toggle="tab" data-bs-target="#materials" type="button" role="tab">
                                    <i class="fas fa-cubes me-2"></i>الخامات
                                </button>
                            </li>
                            <li class="nav-item" role="presentation">
                                <button class="nav-link" id="products-tab" data-bs-toggle="tab" data-bs-target="#products" type="button" role="tab">
                                    <i class="fas fa-box-open me-2"></i>المنتجات
                                </button>
                            </li>
                            <li class="nav-item" role="presentation">
                                <button class="nav-link" id="services-tab" data-bs-toggle="tab" data-bs-target="#services" type="button" role="tab">
                                    <i class="fas fa-concierge-bell me-2"></i>الخدمات
                                </button>
                            </li>
                            <li class="nav-item" role="presentation">
                                <button class="nav-link" id="production-tab" data-bs-toggle="tab" data-bs-target="#production" type="button" role="tab">
                                    <i class="fas fa-industry me-2"></i>الإنتاج
                                </button>
                            </li>
                        </ul>

                        <!-- Tab Content -->
                        <div class="tab-content mt-3" id="itemsTabContent">
                            <!-- Materials Tab -->
                            <div class="tab-pane fade show active" id="materials" role="tabpanel">
                                <div class="d-flex justify-content-between align-items-center mb-3">
                                    <h5><i class="fas fa-cubes me-2"></i>إدارة الخامات</h5>
                                    <div>
                                        <button class="btn btn-success" onclick="showAddMaterialModal()">
                                            <i class="fas fa-plus"></i> إضافة خامة جديدة
                                        </button>
                                        <button class="btn btn-info" onclick="printMaterialsReport()">
                                            <i class="fas fa-print"></i> طباعة التقرير
                                        </button>
                                        <button class="btn btn-warning" onclick="exportMaterialsToExcel()">
                                            <i class="fas fa-file-excel"></i> تصدير Excel
                                        </button>
                                    </div>
                                </div>
                                
                                <!-- Materials Content -->
                                <div id="materialsContent">
                                    <div class="text-center p-4">
                                        <div class="spinner-border text-primary" role="status">
                                            <span class="visually-hidden">جاري التحميل...</span>
                                        </div>
                                        <p class="mt-2">جاري تحميل الخامات...</p>
                                    </div>
                                </div>
                            </div>

                            <!-- Products Tab -->
                            <div class="tab-pane fade" id="products" role="tabpanel">
                                <div class="d-flex justify-content-between align-items-center mb-3">
                                    <h5><i class="fas fa-box-open me-2"></i>إدارة المنتجات</h5>
                                    <button class="btn btn-success" onclick="showAddProductModal()">
                                        <i class="fas fa-plus"></i> إضافة منتج جديد
                                    </button>
                                </div>
                                
                                <!-- Products Content -->
                                <div id="productsContent">
                                    <div class="text-center p-4">
                                        <div class="spinner-border text-primary" role="status">
                                            <span class="visually-hidden">جاري التحميل...</span>
                                        </div>
                                        <p class="mt-2">جاري تحميل المنتجات...</p>
                                    </div>
                                </div>
                            </div>

                            <!-- Services Tab -->
                            <div class="tab-pane fade" id="services" role="tabpanel">
                                <div class="d-flex justify-content-between align-items-center mb-3">
                                    <h5><i class="fas fa-concierge-bell me-2"></i>إدارة الخدمات</h5>
                                    <button class="btn btn-success" onclick="showAddServiceModal()">
                                        <i class="fas fa-plus"></i> إضافة خدمة جديدة
                                    </button>
                                </div>
                                
                                <!-- Services Content -->
                                <div id="servicesContent">
                                    <div class="text-center p-4">
                                        <div class="spinner-border text-primary" role="status">
                                            <span class="visually-hidden">جاري التحميل...</span>
                                        </div>
                                        <p class="mt-2">جاري تحميل الخدمات...</p>
                                    </div>
                                </div>
                            </div>

                            <!-- Production Tab -->
                            <div class="tab-pane fade" id="production" role="tabpanel">
                                <div class="d-flex justify-content-between align-items-center mb-3">
                                    <h5><i class="fas fa-industry me-2"></i>عمليات الإنتاج</h5>
                                    <button class="btn btn-success" onclick="showAddProductionModal()">
                                        <i class="fas fa-plus"></i> إضافة عملية إنتاج
                                    </button>
                                </div>
                                
                                <!-- Production Content -->
                                <div id="productionContent">
                                    <div class="text-center p-4">
                                        <div class="spinner-border text-primary" role="status">
                                            <span class="visually-hidden">جاري التحميل...</span>
                                        </div>
                                        <p class="mt-2">جاري تحميل عمليات الإنتاج...</p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Add/Edit Material Modal -->
    <div class="modal fade" id="materialModal" tabindex="-1">
        <div class="modal-dialog modal-xl">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="materialModalTitle">إضافة خامة جديدة</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <form id="materialForm">
                        <input type="hidden" id="materialId">

                        <!-- Basic Information -->
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="materialName" class="form-label">اسم الخامة *</label>
                                    <input type="text" class="form-control" id="materialName" required>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="materialCode" class="form-label">كود الخامة</label>
                                    <input type="text" class="form-control" id="materialCode">
                                </div>
                            </div>
                        </div>

                        <!-- Units System -->
                        <div class="card mb-3">
                            <div class="card-header">
                                <h6><i class="fas fa-balance-scale me-2"></i>اختيار وحدة القياس</h6>
                            </div>
                            <div class="card-body">
                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label for="unitSelect" class="form-label">اختر وحدة القياس *</label>
                                            <select class="form-select" id="unitSelect" required onchange="updateSelectedUnit()">
                                                <option value="">-- اختر وحدة القياس --</option>
                                            </select>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label for="majorUnitPrice" class="form-label">سعر الشراء (بالوحدة الكبرى) *</label>
                                            <input type="number" class="form-control" id="majorUnitPrice" step="0.001" required onchange="calculateUnitPrices()">
                                        </div>
                                    </div>
                                </div>

                                <!-- Unit Details Display -->
                                <div id="unitDetails" class="alert alert-info" style="display: none;">
                                    <h6><i class="fas fa-info-circle me-2"></i>تفاصيل وحدة القياس:</h6>
                                    <div id="unitDetailsContent"></div>
                                </div>

                                <!-- Calculated Prices -->
                                <div id="calculatedPrices" class="row" style="display: none;">
                                    <div class="col-md-4">
                                        <div class="mb-3">
                                            <label class="form-label">سعر الوحدة الكبرى</label>
                                            <input type="number" class="form-control" id="displayMajorPrice" readonly>
                                        </div>
                                    </div>
                                    <div class="col-md-4">
                                        <div class="mb-3">
                                            <label class="form-label">سعر الوحدة المتوسطة</label>
                                            <input type="number" class="form-control" id="displayMediumPrice" readonly>
                                        </div>
                                    </div>
                                    <div class="col-md-4">
                                        <div class="mb-3">
                                            <label class="form-label">سعر الوحدة الصغرى</label>
                                            <input type="number" class="form-control" id="displayMinorPrice" readonly>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Stock Information -->
                        <div class="row">
                            <div class="col-md-4">
                                <div class="mb-3">
                                    <label for="currentStock" class="form-label">المخزون الحالي (بالوحدة الكبرى)</label>
                                    <input type="number" class="form-control" id="currentStock" step="0.001" value="0" onchange="calculateStockUnits()">
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="mb-3">
                                    <label for="minStock" class="form-label">الحد الأدنى (بالوحدة الكبرى)</label>
                                    <input type="number" class="form-control" id="minStock" step="0.001" value="0">
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="mb-3">
                                    <label for="categorySelect" class="form-label">الفئة</label>
                                    <select class="form-select" id="categorySelect">
                                        <option value="">-- اختر الفئة --</option>
                                    </select>
                                </div>
                            </div>
                        </div>

                        <!-- Additional Information -->
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="supplierName" class="form-label">المورد الرئيسي</label>
                                    <input type="text" class="form-control" id="supplierName">
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="storageLocation" class="form-label">موقع التخزين</label>
                                    <input type="text" class="form-control" id="storageLocation">
                                </div>
                            </div>
                        </div>

                        <div class="mb-3">
                            <label for="materialNotes" class="form-label">ملاحظات</label>
                            <textarea class="form-control" id="materialNotes" rows="2"></textarea>
                        </div>

                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" id="materialActive" checked>
                            <label class="form-check-label" for="materialActive">
                                خامة نشطة
                            </label>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                    <button type="button" class="btn btn-success" onclick="saveMaterial()">
                        <i class="fas fa-save me-2"></i>حفظ الخامة
                    </button>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="js/auth-simple.js"></script>
    <script src="js/items-management.js"></script>

    <style>
        /* تنسيق نظام الوحدات الهرمي */
        .units-hierarchy, .prices-hierarchy, .stock-hierarchy {
            font-size: 0.85em;
        }

        .unit-level, .price-level, .stock-level {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 2px;
        }

        .unit-level:last-child, .price-level:last-child, .stock-level:last-child {
            margin-bottom: 0;
        }

        .units-hierarchy .badge {
            min-width: 60px;
            text-align: center;
        }

        .prices-hierarchy .price-level {
            border-bottom: 1px solid #eee;
            padding: 2px 0;
        }

        .prices-hierarchy .price-level:last-child {
            border-bottom: none;
        }

        .stock-hierarchy {
            position: relative;
        }

        .min-stock {
            text-align: center;
            padding: 5px;
            background-color: #f8f9fa;
            border-radius: 4px;
        }

        /* تحسين عرض الجداول */
        .table td {
            vertical-align: middle;
        }

        .table th {
            white-space: nowrap;
        }

        /* تحسين عرض الأزرار */
        .btn-group-sm .btn {
            padding: 0.25rem 0.4rem;
            font-size: 0.75rem;
        }

        /* تحسين التبويبات */
        .nav-tabs .nav-link {
            border-radius: 0.375rem 0.375rem 0 0;
        }

        .nav-tabs .nav-link.active {
            background-color: #0d6efd;
            color: white;
            border-color: #0d6efd;
        }

        /* تحسين عرض البطاقات */
        .card {
            box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
            border: 1px solid rgba(0, 0, 0, 0.125);
        }

        .card-header {
            background-color: #f8f9fa;
            border-bottom: 1px solid rgba(0, 0, 0, 0.125);
        }
    </style>
</body>
</html>
