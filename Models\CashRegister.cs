using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace ANWBakerySystem.Models
{
    /// <summary>
    /// نموذج الصناديق النقدية
    /// Cash Register model
    /// </summary>
    public class CashRegister
    {
        [Key]
        public int CashRegisterId { get; set; }

        [Required]
        [StringLength(200)]
        [Display(Name = "اسم الصندوق")]
        public string RegisterName { get; set; } = string.Empty;

        [StringLength(200)]
        [Display(Name = "الموقع")]
        public string? Location { get; set; }

        [StringLength(200)]
        [Display(Name = "المسؤول")]
        public string? ResponsiblePerson { get; set; }

        [Required]
        [StringLength(10)]
        [Display(Name = "العملة")]
        public string Currency { get; set; } = "YER";

        [Display(Name = "الرصيد الافتتاحي")]
        [Column(TypeName = "decimal(18,3)")]
        public decimal OpeningBalance { get; set; } = 0;

        [Display(Name = "الرصيد الحالي")]
        [Column(TypeName = "decimal(18,3)")]
        public decimal CurrentBalance { get; set; } = 0;

        [Display(Name = "الحد الأدنى للرصيد")]
        [Column(TypeName = "decimal(18,3)")]
        public decimal? MinimumBalance { get; set; }

        [Display(Name = "الحد الأقصى للرصيد")]
        [Column(TypeName = "decimal(18,3)")]
        public decimal? MaximumBalance { get; set; }

        // تم إزالة الربط المحاسبي - نظام مبسط

        [Display(Name = "نشط")]
        public bool IsActive { get; set; } = true;

        [StringLength(500)]
        [Display(Name = "ملاحظات")]
        public string? Notes { get; set; }

        [Display(Name = "تاريخ الإنشاء")]
        public DateTime CreatedAt { get; set; } = DateTime.Now;

        [Display(Name = "تاريخ التحديث")]
        public DateTime? UpdatedAt { get; set; }

        [Display(Name = "منشئ بواسطة")]
        public int? CreatedBy { get; set; }

        [Display(Name = "محدث بواسطة")]
        public int? UpdatedBy { get; set; }

        // Navigation properties - تم إزالة الربط المحاسبي
        public virtual User? Creator { get; set; }
        public virtual User? Updater { get; set; }
        public virtual ICollection<CashTransaction> CashTransactions { get; set; } = new List<CashTransaction>();
    }

    /// <summary>
    /// نموذج المعاملات النقدية
    /// Cash Transaction model
    /// </summary>
    public class CashTransaction
    {
        [Key]
        public int CashTransactionId { get; set; }

        [Required]
        [Display(Name = "الصندوق النقدي")]
        public int CashRegisterId { get; set; }

        [Required]
        [StringLength(50)]
        [Display(Name = "رقم المعاملة")]
        public string TransactionNumber { get; set; } = string.Empty;

        [Required]
        [Display(Name = "نوع المعاملة")]
        public CashTransactionType TransactionType { get; set; }

        [Required]
        [Display(Name = "تاريخ المعاملة")]
        public DateTime TransactionDate { get; set; } = DateTime.Now;

        [Required]
        [Display(Name = "المبلغ")]
        [Column(TypeName = "decimal(18,3)")]
        public decimal Amount { get; set; }

        [Required]
        [StringLength(500)]
        [Display(Name = "البيان")]
        public string Description { get; set; } = string.Empty;

        [StringLength(100)]
        [Display(Name = "رقم المرجع")]
        public string? ReferenceNumber { get; set; }

        [StringLength(200)]
        [Display(Name = "الطرف الآخر")]
        public string? OtherParty { get; set; }

        [Display(Name = "الرصيد بعد المعاملة")]
        [Column(TypeName = "decimal(18,3)")]
        public decimal BalanceAfter { get; set; } = 0;

        [StringLength(500)]
        [Display(Name = "ملاحظات")]
        public string? Notes { get; set; }

        [Display(Name = "تاريخ الإنشاء")]
        public DateTime CreatedAt { get; set; } = DateTime.Now;

        [Display(Name = "منشئ بواسطة")]
        public int? CreatedBy { get; set; }

        // Navigation properties
        public virtual CashRegister CashRegister { get; set; } = null!;
        public virtual User? Creator { get; set; }
    }

    /// <summary>
    /// أنواع المعاملات النقدية
    /// Cash Transaction Types
    /// </summary>
    public enum CashTransactionType
    {
        [Display(Name = "إيداع")]
        Deposit = 1,

        [Display(Name = "سحب")]
        Withdrawal = 2,

        [Display(Name = "تحويل من صندوق آخر")]
        TransferIn = 3,

        [Display(Name = "تحويل إلى صندوق آخر")]
        TransferOut = 4,

        [Display(Name = "تحويل من بنك")]
        BankToCash = 5,

        [Display(Name = "تحويل إلى بنك")]
        CashToBank = 6,

        [Display(Name = "مبيعات نقدية")]
        CashSales = 7,

        [Display(Name = "مشتريات نقدية")]
        CashPurchase = 8,

        [Display(Name = "مصروفات")]
        Expense = 9,

        [Display(Name = "تسوية")]
        Adjustment = 10
    }
}
