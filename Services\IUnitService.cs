using ANWBakerySystem.Models;

namespace ANWBakerySystem.Services
{
    /// <summary>
    /// واجهة خدمة وحدات القياس المتقدمة
    /// Advanced Unit Service Interface
    /// </summary>
    public interface IUnitService
    {
        Task<IEnumerable<Unit>> GetAllUnitsAsync();
        Task<IEnumerable<Unit>> GetActiveUnitsAsync();
        Task<Unit?> GetUnitByIdAsync(int unitId);
        Task<UnitResult> CreateUnitAsync(CreateUnitRequest request);
        Task<UnitResult> UpdateUnitAsync(int unitId, UpdateUnitRequest request);
        Task<bool> DeleteUnitAsync(int unitId);
        
        // حسابات التحويل المتقدمة
        // Advanced Conversion Calculations
        Task<ConversionResult> ConvertQuantityAsync(ConvertQuantityRequest request);
        Task<decimal> ConvertToBaseUnitAsync(decimal quantity, int unitId, UnitType unitType);
        Task<decimal> ConvertFromBaseUnitAsync(decimal baseQuantity, int unitId, UnitType unitType);
        Task<decimal> CalculatePriceByUnitAsync(decimal basePrice, int unitId, UnitType unitType);
        
        // التحقق من صحة التحويلات
        // Validate Conversions
        Task<bool> ValidateUnitConversionAsync(int fromUnitId, int toUnitId, UnitType fromType, UnitType toType);
        Task<UnitConversionInfo> GetConversionInfoAsync(int fromUnitId, int toUnitId, UnitType fromType, UnitType toType);
        
        // تقارير الوحدات
        // Unit Reports
        Task<IEnumerable<UnitUsageReport>> GetUnitUsageReportAsync();
        Task<IEnumerable<UnitConversion>> GetConversionHistoryAsync(int unitId);
    }

    #region Request/Response Models

    public class CreateUnitRequest
    {
        public string UnitName { get; set; } = string.Empty;
        public string UnitSymbol { get; set; } = string.Empty;
        public string? UnitNameEn { get; set; }
        public decimal LargeUnitCount { get; set; } = 1;
        public string LargeUnitName { get; set; } = string.Empty;
        public decimal MediumUnitCount { get; set; } = 1;
        public string MediumUnitName { get; set; } = string.Empty;
        public decimal SmallUnitCount { get; set; } = 1;
        public string SmallUnitName { get; set; } = string.Empty;
        public string BaseUnitName { get; set; } = string.Empty;
        public string? ConversionNotes { get; set; }
        public int CreatedBy { get; set; }
    }

    public class UpdateUnitRequest
    {
        public string UnitName { get; set; } = string.Empty;
        public string UnitSymbol { get; set; } = string.Empty;
        public string? UnitNameEn { get; set; }
        public decimal LargeUnitCount { get; set; }
        public string LargeUnitName { get; set; } = string.Empty;
        public decimal MediumUnitCount { get; set; }
        public string MediumUnitName { get; set; } = string.Empty;
        public decimal SmallUnitCount { get; set; }
        public string SmallUnitName { get; set; } = string.Empty;
        public string BaseUnitName { get; set; } = string.Empty;
        public string? ConversionNotes { get; set; }
        public bool IsActive { get; set; }
        public int UpdatedBy { get; set; }
    }

    public class ConvertQuantityRequest
    {
        public decimal Quantity { get; set; }
        public int FromUnitId { get; set; }
        public UnitType FromUnitType { get; set; }
        public int ToUnitId { get; set; }
        public UnitType ToUnitType { get; set; }
        public int? UserId { get; set; }
        public string? ReferenceNumber { get; set; }
        public string? OperationType { get; set; }
    }

    public class UnitResult
    {
        public bool Success { get; set; }
        public Unit? Unit { get; set; }
        public string? Message { get; set; }
        public IEnumerable<string>? Errors { get; set; }
    }

    public class ConversionResult
    {
        public bool Success { get; set; }
        public decimal ConvertedQuantity { get; set; }
        public decimal ConversionFactor { get; set; }
        public string? Message { get; set; }
        public UnitConversionInfo? ConversionInfo { get; set; }
    }

    public class UnitConversionInfo
    {
        public Unit FromUnit { get; set; } = null!;
        public Unit ToUnit { get; set; } = null!;
        public UnitType FromUnitType { get; set; }
        public UnitType ToUnitType { get; set; }
        public decimal ConversionFactor { get; set; }
        public string ConversionDescription { get; set; } = string.Empty;
    }

    public class UnitUsageReport
    {
        public Unit Unit { get; set; } = null!;
        public int ItemsCount { get; set; }
        public int ConversionsCount { get; set; }
        public DateTime LastUsed { get; set; }
        public bool IsActive { get; set; }
    }

    #endregion
}
