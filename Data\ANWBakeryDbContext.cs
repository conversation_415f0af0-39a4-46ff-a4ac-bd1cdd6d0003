using Microsoft.EntityFrameworkCore;
using ANWBakerySystem.Models;

namespace ANWBakerySystem.Data
{
    /// <summary>
    /// سياق قاعدة البيانات لنظام إدارة مخبوزات ANW - مبسط
    /// Database context for ANW Bakery Management System - Simplified
    /// </summary>
    public class ANWBakeryDbContext : DbContext
    {
        public ANWBakeryDbContext(DbContextOptions<ANWBakeryDbContext> options) : base(options)
        {
        }

        // الجداول الأساسية
        // Basic tables
        public DbSet<User> Users { get; set; }
        public DbSet<Permission> Permissions { get; set; }
        public DbSet<Unit> Units { get; set; }
        public DbSet<Party> Parties { get; set; }
        public DbSet<Item> Items { get; set; }
        public DbSet<ItemCategory> ItemCategories { get; set; }
        public DbSet<Invoice> Invoices { get; set; }
        public DbSet<InvoiceItem> InvoiceItems { get; set; }
        // تم إزالة جدول الحسابات - نظام مبسط
        public DbSet<Bank> Banks { get; set; }

        // الجداول المفقودة
        // Missing tables
        public DbSet<UserPermission> UserPermissions { get; set; }
        public DbSet<UnitConversion> UnitConversions { get; set; }
        public DbSet<PartyTransaction> PartyTransactions { get; set; }
        public DbSet<Inventory> Inventories { get; set; }
        public DbSet<InventoryMovement> InventoryMovements { get; set; }
        public DbSet<Warehouse> Warehouses { get; set; }

        // جداول الإنتاج والوصفات
        // Production and Recipe tables
        public DbSet<Recipe> Recipes { get; set; }
        public DbSet<RecipeItem> RecipeItems { get; set; }
        public DbSet<ProductionOrder> ProductionOrders { get; set; }

        // تم إزالة جداول المحاسبة - نظام مبسط
        // Accounting tables removed - simplified system

        // جداول البنوك والصناديق
        // Banks and Cash Registers tables
        public DbSet<BankAccount> BankAccounts { get; set; }
        public DbSet<BankTransaction> BankTransactions { get; set; }
        public DbSet<CashRegister> CashRegisters { get; set; }
        public DbSet<CashTransaction> CashTransactions { get; set; }



        protected override void OnModelCreating(ModelBuilder modelBuilder)
        {
            base.OnModelCreating(modelBuilder);

            // تكوين العلاقات
            ConfigureRelationships(modelBuilder);

            // فهارس أساسية
            ConfigureIndexes(modelBuilder);

            // بيانات أولية
            SeedData(modelBuilder);
        }

        private void ConfigureRelationships(ModelBuilder modelBuilder)
        {
            // علاقات المستخدمين والصلاحيات
            modelBuilder.Entity<UserPermission>()
                .HasOne(up => up.User)
                .WithMany()
                .HasForeignKey(up => up.UserId)
                .OnDelete(DeleteBehavior.Cascade);

            modelBuilder.Entity<UserPermission>()
                .HasOne(up => up.Permission)
                .WithMany()
                .HasForeignKey(up => up.PermissionId)
                .OnDelete(DeleteBehavior.Cascade);

            // علاقات حركات المخزون
            modelBuilder.Entity<InventoryMovement>()
                .HasOne(im => im.FromWarehouse)
                .WithMany()
                .HasForeignKey(im => im.FromWarehouseId)
                .OnDelete(DeleteBehavior.Restrict);

            modelBuilder.Entity<InventoryMovement>()
                .HasOne(im => im.ToWarehouse)
                .WithMany()
                .HasForeignKey(im => im.ToWarehouseId)
                .OnDelete(DeleteBehavior.Restrict);

            modelBuilder.Entity<InventoryMovement>()
                .HasOne(im => im.Warehouse)
                .WithMany()
                .HasForeignKey(im => im.WarehouseId)
                .OnDelete(DeleteBehavior.Restrict);

            // علاقات الأصناف والوحدات
            modelBuilder.Entity<Item>()
                .HasOne(i => i.Unit)
                .WithMany(u => u.Items)
                .HasForeignKey(i => i.UnitId)
                .OnDelete(DeleteBehavior.Restrict);

            // علاقات الفواتير
            modelBuilder.Entity<Invoice>()
                .HasOne(i => i.Party)
                .WithMany()
                .HasForeignKey(i => i.PartyId)
                .OnDelete(DeleteBehavior.Restrict);

            modelBuilder.Entity<InvoiceItem>()
                .HasOne(ii => ii.Invoice)
                .WithMany(i => i.InvoiceItems)
                .HasForeignKey(ii => ii.InvoiceId)
                .OnDelete(DeleteBehavior.Cascade);

            modelBuilder.Entity<InvoiceItem>()
                .HasOne(ii => ii.Item)
                .WithMany()
                .HasForeignKey(ii => ii.ItemId)
                .OnDelete(DeleteBehavior.Restrict);

            modelBuilder.Entity<InvoiceItem>()
                .HasOne(ii => ii.Unit)
                .WithMany()
                .HasForeignKey(ii => ii.UnitId)
                .OnDelete(DeleteBehavior.Restrict);

            // علاقات تحويلات الوحدات
            modelBuilder.Entity<UnitConversion>()
                .HasOne(uc => uc.FromUnit)
                .WithMany(u => u.ConversionsFrom)
                .HasForeignKey(uc => uc.FromUnitId)
                .OnDelete(DeleteBehavior.Restrict);

            modelBuilder.Entity<UnitConversion>()
                .HasOne(uc => uc.ToUnit)
                .WithMany(u => u.ConversionsTo)
                .HasForeignKey(uc => uc.ToUnitId)
                .OnDelete(DeleteBehavior.Restrict);

            modelBuilder.Entity<UnitConversion>()
                .HasOne(uc => uc.Converter)
                .WithMany()
                .HasForeignKey(uc => uc.ConvertedBy)
                .OnDelete(DeleteBehavior.Restrict);

            // علاقات المستخدمين الذاتية
            modelBuilder.Entity<User>()
                .HasOne(u => u.Creator)
                .WithMany(u => u.CreatedUsers)
                .HasForeignKey(u => u.CreatedBy)
                .OnDelete(DeleteBehavior.Restrict);

            modelBuilder.Entity<User>()
                .HasOne(u => u.Updater)
                .WithMany(u => u.UpdatedUsers)
                .HasForeignKey(u => u.UpdatedBy)
                .OnDelete(DeleteBehavior.Restrict);

            modelBuilder.Entity<InvoiceItem>()
                .HasOne(ii => ii.Item)
                .WithMany(i => i.InvoiceItems)
                .HasForeignKey(ii => ii.ItemId)
                .OnDelete(DeleteBehavior.Restrict);

            // تم إزالة علاقات القيود اليومية - نظام مبسط

            // علاقات البنوك
            modelBuilder.Entity<BankAccount>()
                .HasOne(ba => ba.Bank)
                .WithMany(b => b.BankAccounts)
                .HasForeignKey(ba => ba.BankId)
                .OnDelete(DeleteBehavior.Restrict);

            modelBuilder.Entity<BankTransaction>()
                .HasOne(bt => bt.BankAccount)
                .WithMany(ba => ba.BankTransactions)
                .HasForeignKey(bt => bt.BankAccountId)
                .OnDelete(DeleteBehavior.Cascade);

            // علاقات الصناديق
            modelBuilder.Entity<CashTransaction>()
                .HasOne(ct => ct.CashRegister)
                .WithMany(cr => cr.CashTransactions)
                .HasForeignKey(ct => ct.CashRegisterId)
                .OnDelete(DeleteBehavior.Cascade);

            // علاقات الوصفات
            modelBuilder.Entity<RecipeItem>()
                .HasOne(ri => ri.Recipe)
                .WithMany(r => r.RecipeItems)
                .HasForeignKey(ri => ri.RecipeId)
                .OnDelete(DeleteBehavior.Cascade);

            modelBuilder.Entity<RecipeItem>()
                .HasOne(ri => ri.Item)
                .WithMany(i => i.RecipeItems)
                .HasForeignKey(ri => ri.ItemId)
                .OnDelete(DeleteBehavior.Restrict);
        }

        private void ConfigureIndexes(ModelBuilder modelBuilder)
        {
            // فهارس المستخدمين
            modelBuilder.Entity<User>()
                .HasIndex(u => u.Username)
                .IsUnique();

            modelBuilder.Entity<User>()
                .HasIndex(u => u.Email)
                .IsUnique();

            // فهارس الوحدات
            modelBuilder.Entity<Unit>()
                .HasIndex(u => u.UnitName)
                .IsUnique();

            // فهارس الأطراف
            modelBuilder.Entity<Party>()
                .HasIndex(p => p.PartyCode)
                .IsUnique();

            // فهارس الأصناف
            modelBuilder.Entity<Item>()
                .HasIndex(i => i.ItemCode)
                .IsUnique();

            // فهارس الفواتير
            modelBuilder.Entity<Invoice>()
                .HasIndex(i => i.InvoiceNumber)
                .IsUnique();

            // تم إزالة فهارس الحسابات والقيود - نظام مبسط

            // فهارس الحسابات البنكية
            modelBuilder.Entity<BankAccount>()
                .HasIndex(ba => ba.AccountNumber)
                .IsUnique();

            // فهارس الحركات البنكية
            modelBuilder.Entity<BankTransaction>()
                .HasIndex(bt => bt.TransactionNumber)
                .IsUnique();

            // فهارس الحركات النقدية
            modelBuilder.Entity<CashTransaction>()
                .HasIndex(ct => ct.TransactionNumber)
                .IsUnique();
        }

        private void SeedData(ModelBuilder modelBuilder)
        {
            // إضافة المستخدم الافتراضي
            modelBuilder.Entity<User>().HasData(
                new User
                {
                    UserId = 1,
                    Username = "admin",
                    FullName = "مدير النظام",
                    FullNameEn = "System Administrator",
                    Email = "<EMAIL>",
                    Phone = "*********",
                    PasswordHash = "admin123", // سيتم تشفيرها لاحقاً
                    Role = UserRole.Admin,
                    IsActive = true,
                    CreatedAt = DateTime.Now,
                    MustChangePassword = false
                }
            );

            // إضافة الصلاحيات الأساسية - نظام مبسط بدون حسابات
            var permissions = new[]
            {
                new Permission { PermissionId = 1, PermissionName = "items_view", DisplayName = "عرض المنتجات", ModuleName = "items", ModuleDisplayName = "المنتجات والخامات", DisplayOrder = 1 },
                new Permission { PermissionId = 2, PermissionName = "items_add", DisplayName = "إضافة منتج", ModuleName = "items", ModuleDisplayName = "المنتجات والخامات", DisplayOrder = 2 },
                new Permission { PermissionId = 3, PermissionName = "parties_view", DisplayName = "عرض العملاء والموردين", ModuleName = "parties", ModuleDisplayName = "العملاء والموردين", DisplayOrder = 1 },
                new Permission { PermissionId = 4, PermissionName = "parties_add", DisplayName = "إضافة عميل/مورد", ModuleName = "parties", ModuleDisplayName = "العملاء والموردين", DisplayOrder = 2 },
                new Permission { PermissionId = 5, PermissionName = "invoices_view", DisplayName = "عرض الفواتير", ModuleName = "invoices", ModuleDisplayName = "الفواتير", DisplayOrder = 1 },
                new Permission { PermissionId = 6, PermissionName = "invoices_add", DisplayName = "إضافة فاتورة", ModuleName = "invoices", ModuleDisplayName = "الفواتير", DisplayOrder = 2 },
                new Permission { PermissionId = 7, PermissionName = "users_view", DisplayName = "عرض المستخدمين", ModuleName = "users", ModuleDisplayName = "المستخدمين", DisplayOrder = 1 },
                new Permission { PermissionId = 8, PermissionName = "users_add", DisplayName = "إضافة مستخدم", ModuleName = "users", ModuleDisplayName = "المستخدمين", DisplayOrder = 2 }
            };

            modelBuilder.Entity<Permission>().HasData(permissions);

            // منح جميع الصلاحيات للمدير
            var userPermissions = permissions.Select(p => new UserPermission
            {
                UserPermissionId = p.PermissionId,
                UserId = 1,
                PermissionId = p.PermissionId,
                IsGranted = true,
                GrantedAt = DateTime.Now,
                GrantedBy = 1
            }).ToArray();

            modelBuilder.Entity<UserPermission>().HasData(userPermissions);

            // تم إزالة الحسابات الأساسية - نظام مبسط

            // إضافة صندوق نقدي افتراضي
            modelBuilder.Entity<CashRegister>().HasData(
                new CashRegister
                {
                    CashRegisterId = 1,
                    RegisterName = "الصندوق الرئيسي",
                    Location = "المكتب الرئيسي",
                    ResponsiblePerson = "أمين الصندوق",
                    OpeningBalance = 0,
                    CurrentBalance = 0,
                    IsActive = true,
                    CreatedAt = DateTime.Now,
                    CreatedBy = 1
                }
            );

            // إضافة بنك افتراضي
            modelBuilder.Entity<Bank>().HasData(
                new Bank
                {
                    BankId = 1,
                    BankName = "البنك الأهلي اليمني",
                    BankNameEn = "National Bank of Yemen",
                    BankCode = "NBY",
                    SwiftCode = "NBYEYESA",
                    Address = "صنعاء - اليمن",
                    Phone = "01-123456",
                    IsActive = true,
                    CreatedAt = DateTime.Now,
                    CreatedBy = 1
                }
            );

            // إضافة مخزن افتراضي
            modelBuilder.Entity<Warehouse>().HasData(
                new Warehouse
                {
                    WarehouseId = 1,
                    WarehouseCode = "WH001",
                    WarehouseName = "المخزن الرئيسي",
                    WarehouseNameEn = "Main Warehouse",
                    Address = "المكتب الرئيسي",
                    ResponsiblePerson = "أمين المخزن",
                    WarehouseType = WarehouseType.Main,
                    IsMainWarehouse = true,
                    IsActive = true,
                    CreatedAt = DateTime.Now,
                    CreatedBy = 1
                }
            );

            // إضافة وحدات قياس افتراضية
            modelBuilder.Entity<Unit>().HasData(
                new Unit
                {
                    UnitId = 1,
                    UnitName = "كيس دقيق",
                    UnitNameEn = "Flour Bag",
                    UnitSymbol = "كيس",
                    LargeUnitCount = 1,
                    LargeUnitName = "كيس",
                    MediumUnitCount = 50,
                    MediumUnitName = "كيلوجرام",
                    SmallUnitCount = 50000,
                    SmallUnitName = "جرام",
                    BaseUnitName = "جرام",
                    ConversionNotes = "1 كيس = 50 كيلوجرام = 50000 جرام",
                    IsActive = true,
                    CreatedAt = DateTime.Now,
                    CreatedBy = 1
                },
                new Unit
                {
                    UnitId = 2,
                    UnitName = "قطعة",
                    UnitNameEn = "Piece",
                    UnitSymbol = "قطعة",
                    LargeUnitCount = 1,
                    LargeUnitName = "قطعة",
                    MediumUnitCount = 1,
                    MediumUnitName = "قطعة",
                    SmallUnitCount = 1,
                    SmallUnitName = "قطعة",
                    BaseUnitName = "قطعة",
                    ConversionNotes = "وحدة عد بسيطة",
                    IsActive = true,
                    CreatedAt = DateTime.Now,
                    CreatedBy = 1
                }
            );
        }
    }
}
