using Microsoft.EntityFrameworkCore;
using ANWBakerySystem.Models;

namespace ANWBakerySystem.Data
{
    public class ANWBakeryDbContext : DbContext
    {
        public ANWBakeryDbContext(DbContextOptions<ANWBakeryDbContext> options) : base(options)
        {
        }

        // الجداول الأساسية - شاملة
        public DbSet<User> Users { get; set; }
        public DbSet<Permission> Permissions { get; set; }
        public DbSet<UserPermission> UserPermissions { get; set; }
        public DbSet<Unit> Units { get; set; }
        public DbSet<UnitConversion> UnitConversions { get; set; }
        public DbSet<Party> Parties { get; set; }
        public DbSet<PartyTransaction> PartyTransactions { get; set; }
        public DbSet<Item> Items { get; set; }
        public DbSet<ItemCategory> ItemCategories { get; set; }
        public DbSet<Invoice> Invoices { get; set; }
        public DbSet<InvoiceItem> InvoiceItems { get; set; }
        public DbSet<Inventory> Inventories { get; set; }
        public DbSet<InventoryMovement> InventoryMovements { get; set; }
        public DbSet<Warehouse> Warehouses { get; set; }
        public DbSet<Bank> Banks { get; set; }
        public DbSet<CashRegister> CashRegisters { get; set; }

        protected override void OnModelCreating(ModelBuilder modelBuilder)
        {
            base.OnModelCreating(modelBuilder);

            // تجاهل جميع Navigation Properties لتجنب مشاكل العلاقات
            IgnoreAllNavigationProperties(modelBuilder);

            // فهارس أساسية
            ConfigureIndexes(modelBuilder);

            // بيانات أولية
            SeedData(modelBuilder);
        }

        private void IgnoreAllNavigationProperties(ModelBuilder modelBuilder)
        {
            // تجاهل جميع العلاقات المعقدة
            modelBuilder.Entity<User>()
                .Ignore(u => u.Creator)
                .Ignore(u => u.Updater)
                .Ignore(u => u.CreatedUsers)
                .Ignore(u => u.UpdatedUsers)
                .Ignore(u => u.UserPermissions);

            modelBuilder.Entity<Permission>()
                .Ignore(p => p.UserPermissions);

            modelBuilder.Entity<UserPermission>()
                .Ignore(up => up.User)
                .Ignore(up => up.Permission)
                .Ignore(up => up.GrantedByUser)
                .Ignore(up => up.RevokedByUser);

            modelBuilder.Entity<Item>()
                .Ignore(i => i.Unit)
                .Ignore(i => i.Category)
                .Ignore(i => i.Creator)
                .Ignore(i => i.Updater)
                .Ignore(i => i.InvoiceItems)
                .Ignore(i => i.Inventories)
                .Ignore(i => i.InventoryMovements)
                .Ignore(i => i.RecipeItems);

            modelBuilder.Entity<Party>()
                .Ignore(p => p.Creator)
                .Ignore(p => p.Updater)
                .Ignore(p => p.Invoices)
                .Ignore(p => p.PartyTransactions);

            modelBuilder.Entity<Invoice>()
                .Ignore(i => i.Party)
                .Ignore(i => i.Creator)
                .Ignore(i => i.Updater)
                .Ignore(i => i.InvoiceItems);

            modelBuilder.Entity<InvoiceItem>()
                .Ignore(ii => ii.Invoice)
                .Ignore(ii => ii.Item)
                .Ignore(ii => ii.Unit);

            modelBuilder.Entity<Unit>()
                .Ignore(u => u.Creator)
                .Ignore(u => u.Updater)
                .Ignore(u => u.Items)
                .Ignore(u => u.ConversionsFrom)
                .Ignore(u => u.ConversionsTo);

            modelBuilder.Entity<InventoryMovement>()
                .Ignore(im => im.Item)
                .Ignore(im => im.Unit)
                .Ignore(im => im.Warehouse)
                .Ignore(im => im.FromWarehouse)
                .Ignore(im => im.ToWarehouse)
                .Ignore(im => im.Invoice)
                .Ignore(im => im.Creator);

            modelBuilder.Entity<Warehouse>()
                .Ignore(w => w.Creator)
                .Ignore(w => w.Updater)
                .Ignore(w => w.Inventories)
                .Ignore(w => w.InventoryMovements);

            modelBuilder.Entity<Inventory>()
                .Ignore(i => i.Item)
                .Ignore(i => i.Warehouse)
                .Ignore(i => i.Creator)
                .Ignore(i => i.Updater);

            modelBuilder.Entity<PartyTransaction>()
                .Ignore(pt => pt.Party)
                .Ignore(pt => pt.Creator)
                .Ignore(pt => pt.Updater);

            modelBuilder.Entity<UnitConversion>()
                .Ignore(uc => uc.FromUnit)
                .Ignore(uc => uc.ToUnit)
                .Ignore(uc => uc.Converter);

            modelBuilder.Entity<ItemCategory>()
                .Ignore(ic => ic.Creator)
                .Ignore(ic => ic.Updater)
                .Ignore(ic => ic.Items);

            modelBuilder.Entity<Bank>()
                .Ignore(b => b.Creator)
                .Ignore(b => b.Updater)
                .Ignore(b => b.BankAccounts);

            modelBuilder.Entity<CashRegister>()
                .Ignore(cr => cr.Creator)
                .Ignore(cr => cr.Updater)
                .Ignore(cr => cr.CashTransactions);
        }

        private void ConfigureIndexes(ModelBuilder modelBuilder)
        {
            // فهارس المستخدمين
            modelBuilder.Entity<User>()
                .HasIndex(u => u.Username)
                .IsUnique();

            modelBuilder.Entity<User>()
                .HasIndex(u => u.Email)
                .IsUnique();

            // فهارس الأطراف
            modelBuilder.Entity<Party>()
                .HasIndex(p => p.PartyCode)
                .IsUnique();

            // فهارس الأصناف
            modelBuilder.Entity<Item>()
                .HasIndex(i => i.ItemCode)
                .IsUnique();

            // فهارس الفواتير
            modelBuilder.Entity<Invoice>()
                .HasIndex(i => i.InvoiceNumber)
                .IsUnique();
        }

        private void SeedData(ModelBuilder modelBuilder)
        {
            // إضافة المستخدم الافتراضي
            modelBuilder.Entity<User>().HasData(
                new User
                {
                    UserId = 1,
                    Username = "admin",
                    FullName = "مدير النظام",
                    FullNameEn = "System Administrator",
                    Email = "<EMAIL>",
                    Phone = "777123456",
                    PasswordHash = "admin123", // سيتم تشفيرها لاحقاً
                    Role = UserRole.Admin,
                    IsActive = true,
                    CreatedAt = DateTime.Now,
                    MustChangePassword = false
                }
            );

            // الصلاحيات الأساسية
            var permissions = new[]
            {
                new Permission { PermissionId = 1, PermissionName = "admin", DisplayName = "مدير النظام", ModuleName = "admin", ModuleDisplayName = "الإدارة", DisplayOrder = 1 },
                new Permission { PermissionId = 2, PermissionName = "items_view", DisplayName = "عرض المنتجات", ModuleName = "items", ModuleDisplayName = "المنتجات", DisplayOrder = 1 },
                new Permission { PermissionId = 3, PermissionName = "parties_view", DisplayName = "عرض العملاء", ModuleName = "parties", ModuleDisplayName = "العملاء", DisplayOrder = 1 },
                new Permission { PermissionId = 4, PermissionName = "invoices_view", DisplayName = "عرض الفواتير", ModuleName = "invoices", ModuleDisplayName = "الفواتير", DisplayOrder = 1 }
            };

            modelBuilder.Entity<Permission>().HasData(permissions);

            // منح جميع الصلاحيات للمدير
            var userPermissions = permissions.Select(p => new UserPermission
            {
                UserPermissionId = p.PermissionId,
                UserId = 1,
                PermissionId = p.PermissionId,
                IsGranted = true,
                GrantedAt = DateTime.Now,
                GrantedBy = 1
            }).ToArray();

            modelBuilder.Entity<UserPermission>().HasData(userPermissions);

            // إضافة صندوق نقدي افتراضي
            modelBuilder.Entity<CashRegister>().HasData(
                new CashRegister
                {
                    CashRegisterId = 1,
                    RegisterName = "الصندوق الرئيسي",
                    Location = "المكتب الرئيسي",
                    ResponsiblePerson = "أمين الصندوق",
                    OpeningBalance = 0,
                    CurrentBalance = 0,
                    IsActive = true,
                    CreatedAt = DateTime.Now,
                    CreatedBy = 1
                }
            );

            // إضافة بنك افتراضي
            modelBuilder.Entity<Bank>().HasData(
                new Bank
                {
                    BankId = 1,
                    BankName = "البنك الأهلي اليمني",
                    BankNameEn = "National Bank of Yemen",
                    BankCode = "NBY",
                    SwiftCode = "NBYEYESA",
                    Address = "صنعاء - اليمن",
                    Phone = "01-123456",
                    IsActive = true,
                    CreatedAt = DateTime.Now,
                    CreatedBy = 1
                }
            );

            // وحدة قياس أساسية
            modelBuilder.Entity<Unit>().HasData(
                new Unit
                {
                    UnitId = 1,
                    UnitName = "قطعة",
                    UnitNameEn = "Piece",
                    UnitSymbol = "قطعة",
                    LargeUnitCount = 1,
                    LargeUnitName = "قطعة",
                    MediumUnitCount = 1,
                    MediumUnitName = "قطعة",
                    SmallUnitCount = 1,
                    SmallUnitName = "قطعة",
                    BaseUnitName = "قطعة",
                    ConversionNotes = "وحدة عد بسيطة",
                    IsActive = true,
                    CreatedAt = DateTime.Now,
                    CreatedBy = 1
                }
            );


        }
    }
}
