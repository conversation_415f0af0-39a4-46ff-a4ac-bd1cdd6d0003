using Microsoft.EntityFrameworkCore;
using ANWBakerySystem.Models;

namespace ANWBakerySystem.Data
{
    /// <summary>
    /// سياق قاعدة البيانات المبسط - نظام مخبوزات ANW
    /// Simplified Database Context - ANW Bakery System
    /// </summary>
    public class ANWBakeryDbContext : DbContext
    {
        public ANWBakeryDbContext(DbContextOptions<ANWBakeryDbContext> options) : base(options)
        {
        }

        // الجداول الأساسية - شاملة
        public DbSet<User> Users { get; set; }
        public DbSet<Permission> Permissions { get; set; }
        public DbSet<UserPermission> UserPermissions { get; set; }
        public DbSet<Unit> Units { get; set; }
        public DbSet<UnitConversion> UnitConversions { get; set; }
        public DbSet<Party> Parties { get; set; }
        public DbSet<PartyTransaction> PartyTransactions { get; set; }
        public DbSet<Item> Items { get; set; }
        public DbSet<ItemCategory> ItemCategories { get; set; }
        public DbSet<Invoice> Invoices { get; set; }
        public DbSet<InvoiceItem> InvoiceItems { get; set; }
        public DbSet<Inventory> Inventories { get; set; }
        public DbSet<InventoryMovement> InventoryMovements { get; set; }
        public DbSet<Warehouse> Warehouses { get; set; }
        public DbSet<Bank> Banks { get; set; }
        public DbSet<CashRegister> CashRegisters { get; set; }

        protected override void OnModelCreating(ModelBuilder modelBuilder)
        {
            base.OnModelCreating(modelBuilder);

            // تكوين مبسط للعلاقات
            ConfigureSimpleRelationships(modelBuilder);

            // فهارس أساسية
            ConfigureBasicIndexes(modelBuilder);

            // بيانات أولية
            SeedBasicData(modelBuilder);
        }

        private void ConfigureSimpleRelationships(ModelBuilder modelBuilder)
        {
            // علاقات المستخدمين والصلاحيات
            modelBuilder.Entity<UserPermission>()
                .HasKey(up => up.UserPermissionId);

            // علاقات الفواتير - مبسطة
            modelBuilder.Entity<InvoiceItem>()
                .HasKey(ii => ii.InvoiceItemId);

            // تجنب العلاقات المعقدة مؤقتاً
        }

        private void ConfigureBasicIndexes(ModelBuilder modelBuilder)
        {
            // فهارس المستخدمين
            modelBuilder.Entity<User>()
                .HasIndex(u => u.Username)
                .IsUnique();

            modelBuilder.Entity<User>()
                .HasIndex(u => u.Email)
                .IsUnique();

            // فهارس الأطراف
            modelBuilder.Entity<Party>()
                .HasIndex(p => p.PartyCode)
                .IsUnique();

            // فهارس الأصناف
            modelBuilder.Entity<Item>()
                .HasIndex(i => i.ItemCode)
                .IsUnique();

            // فهارس الفواتير
            modelBuilder.Entity<Invoice>()
                .HasIndex(i => i.InvoiceNumber)
                .IsUnique();
        }

        private void SeedBasicData(ModelBuilder modelBuilder)
        {
            // إضافة المستخدم الافتراضي
            modelBuilder.Entity<User>().HasData(
                new User
                {
                    UserId = 1,
                    Username = "admin",
                    FullName = "مدير النظام",
                    FullNameEn = "System Administrator",
                    Email = "<EMAIL>",
                    Phone = "777123456",
                    PasswordHash = "admin123", // سيتم تشفيرها لاحقاً
                    Role = UserRole.Admin,
                    IsActive = true,
                    CreatedAt = DateTime.Now,
                    MustChangePassword = false
                }
            );

            // إضافة الصلاحيات الأساسية
            var permissions = new[]
            {
                new Permission { PermissionId = 1, PermissionName = "items_view", DisplayName = "عرض المنتجات", ModuleName = "items", ModuleDisplayName = "المنتجات والخامات", DisplayOrder = 1 },
                new Permission { PermissionId = 2, PermissionName = "items_add", DisplayName = "إضافة منتج", ModuleName = "items", ModuleDisplayName = "المنتجات والخامات", DisplayOrder = 2 },
                new Permission { PermissionId = 3, PermissionName = "parties_view", DisplayName = "عرض العملاء والموردين", ModuleName = "parties", ModuleDisplayName = "العملاء والموردين", DisplayOrder = 1 },
                new Permission { PermissionId = 4, PermissionName = "parties_add", DisplayName = "إضافة عميل/مورد", ModuleName = "parties", ModuleDisplayName = "العملاء والموردين", DisplayOrder = 2 },
                new Permission { PermissionId = 5, PermissionName = "invoices_view", DisplayName = "عرض الفواتير", ModuleName = "invoices", ModuleDisplayName = "الفواتير", DisplayOrder = 1 },
                new Permission { PermissionId = 6, PermissionName = "invoices_add", DisplayName = "إضافة فاتورة", ModuleName = "invoices", ModuleDisplayName = "الفواتير", DisplayOrder = 2 },
                new Permission { PermissionId = 7, PermissionName = "users_view", DisplayName = "عرض المستخدمين", ModuleName = "users", ModuleDisplayName = "المستخدمين", DisplayOrder = 1 },
                new Permission { PermissionId = 8, PermissionName = "users_add", DisplayName = "إضافة مستخدم", ModuleName = "users", ModuleDisplayName = "المستخدمين", DisplayOrder = 2 }
            };

            modelBuilder.Entity<Permission>().HasData(permissions);

            // منح جميع الصلاحيات للمدير
            var userPermissions = permissions.Select(p => new UserPermission
            {
                UserPermissionId = p.PermissionId,
                UserId = 1,
                PermissionId = p.PermissionId,
                IsGranted = true,
                GrantedAt = DateTime.Now,
                GrantedBy = 1
            }).ToArray();

            modelBuilder.Entity<UserPermission>().HasData(userPermissions);

            // إضافة صندوق نقدي افتراضي
            modelBuilder.Entity<CashRegister>().HasData(
                new CashRegister
                {
                    CashRegisterId = 1,
                    RegisterName = "الصندوق الرئيسي",
                    Location = "المكتب الرئيسي",
                    ResponsiblePerson = "أمين الصندوق",
                    OpeningBalance = 0,
                    CurrentBalance = 0,
                    IsActive = true,
                    CreatedAt = DateTime.Now,
                    CreatedBy = 1
                }
            );

            // إضافة بنك افتراضي
            modelBuilder.Entity<Bank>().HasData(
                new Bank
                {
                    BankId = 1,
                    BankName = "البنك الأهلي اليمني",
                    BankNameEn = "National Bank of Yemen",
                    BankCode = "NBY",
                    SwiftCode = "NBYEYESA",
                    Address = "صنعاء - اليمن",
                    Phone = "01-123456",
                    IsActive = true,
                    CreatedAt = DateTime.Now,
                    CreatedBy = 1
                }
            );

            // إضافة وحدات قياس افتراضية
            modelBuilder.Entity<Unit>().HasData(
                new Unit
                {
                    UnitId = 1,
                    UnitName = "كيس دقيق",
                    UnitNameEn = "Flour Bag",
                    UnitSymbol = "كيس",
                    LargeUnitCount = 1,
                    LargeUnitName = "كيس",
                    MediumUnitCount = 50,
                    MediumUnitName = "كيلوجرام",
                    SmallUnitCount = 50000,
                    SmallUnitName = "جرام",
                    BaseUnitName = "جرام",
                    ConversionNotes = "1 كيس = 50 كيلوجرام = 50000 جرام",
                    IsActive = true,
                    CreatedAt = DateTime.Now,
                    CreatedBy = 1
                },
                new Unit
                {
                    UnitId = 2,
                    UnitName = "قطعة",
                    UnitNameEn = "Piece",
                    UnitSymbol = "قطعة",
                    LargeUnitCount = 1,
                    LargeUnitName = "قطعة",
                    MediumUnitCount = 1,
                    MediumUnitName = "قطعة",
                    SmallUnitCount = 1,
                    SmallUnitName = "قطعة",
                    BaseUnitName = "قطعة",
                    ConversionNotes = "وحدة عد بسيطة",
                    IsActive = true,
                    CreatedAt = DateTime.Now,
                    CreatedBy = 1
                }
            );

            // إضافة مخزن افتراضي
            modelBuilder.Entity<Warehouse>().HasData(
                new Warehouse
                {
                    WarehouseId = 1,
                    WarehouseName = "المخزن الرئيسي",
                    WarehouseNameEn = "Main Warehouse",
                    Location = "المبنى الرئيسي",
                    ResponsiblePerson = "أمين المخزن",
                    IsActive = true,
                    CreatedAt = DateTime.Now,
                    CreatedBy = 1
                }
            );

            // إضافة فئة أصناف افتراضية
            modelBuilder.Entity<ItemCategory>().HasData(
                new ItemCategory
                {
                    CategoryId = 1,
                    CategoryName = "خامات",
                    CategoryNameEn = "Raw Materials",
                    Description = "المواد الخام المستخدمة في الإنتاج",
                    IsActive = true,
                    CreatedAt = DateTime.Now,
                    CreatedBy = 1
                },
                new ItemCategory
                {
                    CategoryId = 2,
                    CategoryName = "منتجات",
                    CategoryNameEn = "Products",
                    Description = "المنتجات النهائية الجاهزة للبيع",
                    IsActive = true,
                    CreatedAt = DateTime.Now,
                    CreatedBy = 1
                }
            );
        }
    }
}
