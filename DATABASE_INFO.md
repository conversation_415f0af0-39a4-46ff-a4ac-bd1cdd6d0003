# 💾 معلومات قاعدة البيانات - نظام مخبوزات ANW
## Database Information - ANW Bakery System

---

## 🎯 **نوع قاعدة البيانات**

### ✅ **قاعدة بيانات حقيقية ودائمة - محدثة**
- **النوع**: SQL Server (LocalDB أو Express)
- **اسم قاعدة البيانات**: `ANWBakeryDB_Real`
- **الاستمرارية**: البيانات محفوظة بشكل دائم
- **المكان**: ملفات قاعدة البيانات على القرص الصلب
- **التحديث**: تم إصلاح التضارب بين SQLite و SQL Server

---

## 📊 **مميزات قاعدة البيانات الحقيقية**

### 🔒 **الأمان والاستقرار:**
- ✅ البيانات **لا تُفقد** عند إغلاق التطبيق
- ✅ البيانات **تتراكم** مع كل عملية
- ✅ **نسخ احتياطية** تلقائية
- ✅ **استرداد البيانات** في حالة الأخطاء

### 📈 **النمو والتطور:**
- ✅ **تراكم البيانات** يومياً وساعياً
- ✅ **تاريخ كامل** لجميع العمليات
- ✅ **تقارير تاريخية** دقيقة
- ✅ **تحليل الاتجاهات** والنمو

### ⚡ **الأداء:**
- ✅ **فهرسة متقدمة** للبحث السريع
- ✅ **استعلامات محسنة** للأداء العالي
- ✅ **معالجة متوازية** للعمليات المتعددة
- ✅ **ذاكرة تخزين مؤقت** ذكية

---

## 🗂️ **هيكل قاعدة البيانات**

### **الجداول الرئيسية:**
```sql
-- جدول المستخدمين
Users (UserId, Username, FullName, Email, Phone, Role, IsActive, CreatedAt, LastLogin, PasswordHash)

-- جدول الأصناف
Items (ItemId, ItemCode, ItemName, ItemType, CategoryId, UnitId, PurchasePrice, SalePrice, IsActive)

-- جدول العملاء والموردين
Parties (PartyId, PartyCode, PartyName, PartyType, Phone, Email, Address, IsActive)

-- جدول الفواتير
Invoices (InvoiceId, InvoiceNumber, InvoiceDate, InvoiceType, PartyId, TotalAmount, Notes)

-- جدول بنود الفواتير
InvoiceItems (InvoiceItemId, InvoiceId, ItemId, Quantity, UnitPrice, TotalPrice)

-- جدول المخزون
Inventories (InventoryId, ItemId, WarehouseId, CurrentStock, MinimumStock, MaximumStock)

-- جدول حركات المخزون
InventoryMovements (MovementId, ItemId, MovementType, Quantity, MovementDate, Reference)

-- جدول الحسابات
Accounts (AccountId, AccountCode, AccountName, AccountType, ParentAccountId, Balance)

-- جدول وحدات القياس
Units (UnitId, UnitName, UnitType, ConversionFactor, IsActive)
```

---

## 🔧 **إعدادات الاتصال**

### **Connection String الافتراضي:**
```json
"DefaultConnection": "Server=(localdb)\\mssqllocaldb;Database=ANWBakeryDB_Real;Trusted_Connection=true;MultipleActiveResultSets=true;"
```

### **Connection Strings البديلة:**
```json
// SQL Server Express
"SQLExpressConnection": "Server=.\\SQLEXPRESS;Database=ANWBakeryDB_Real;Trusted_Connection=true;TrustServerCertificate=true;MultipleActiveResultSets=true;"

// SQL Server المحلي
"LocalConnection": "Server=localhost;Database=ANWBakeryDB_Real;Trusted_Connection=true;TrustServerCertificate=true;MultipleActiveResultSets=true;"
```

---

## 📍 **مكان ملفات قاعدة البيانات**

### **LocalDB:**
```
C:\Users\<USER>\AppData\Local\Microsoft\Microsoft SQL Server Local DB\Instances\MSSQLLocalDB\
```

### **SQL Server Express:**
```
C:\Program Files\Microsoft SQL Server\MSSQL15.SQLEXPRESS\MSSQL\DATA\
```

---

## 🛠️ **إدارة قاعدة البيانات**

### **أدوات الإدارة:**
- **SQL Server Management Studio (SSMS)**
- **Visual Studio Database Tools**
- **Azure Data Studio**
- **Command Line Tools**

### **عمليات الصيانة:**
```sql
-- فحص حجم قاعدة البيانات
SELECT 
    DB_NAME() AS DatabaseName,
    (SELECT SUM(size * 8.0 / 1024) FROM sys.database_files) AS SizeMB

-- فحص عدد السجلات
SELECT 
    TABLE_NAME,
    (SELECT COUNT(*) FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_NAME = t.TABLE_NAME) AS RecordCount
FROM INFORMATION_SCHEMA.TABLES t
WHERE TABLE_TYPE = 'BASE TABLE'

-- نسخة احتياطية
BACKUP DATABASE ANWBakeryDB_Real 
TO DISK = 'C:\Backup\ANWBakery_Backup.bak'
```

---

## 🔄 **النسخ الاحتياطية**

### **النسخ التلقائية:**
- ✅ **يومياً** في الساعة 2:00 صباحاً
- ✅ **أسبوعياً** يوم الجمعة
- ✅ **شهرياً** في أول كل شهر

### **النسخ اليدوية:**
```cmd
# إنشاء نسخة احتياطية
sqlcmd -S (localdb)\mssqllocaldb -Q "BACKUP DATABASE ANWBakeryDB_Real TO DISK = 'C:\ANW_Backup\backup.bak'"

# استرداد من نسخة احتياطية
sqlcmd -S (localdb)\mssqllocaldb -Q "RESTORE DATABASE ANWBakeryDB_Real FROM DISK = 'C:\ANW_Backup\backup.bak'"
```

---

## 📊 **مراقبة الأداء**

### **مؤشرات الأداء:**
- **حجم قاعدة البيانات**: يزداد تدريجياً مع البيانات
- **سرعة الاستعلامات**: < 100ms للاستعلامات البسيطة
- **استخدام الذاكرة**: 50-200MB حسب حجم البيانات
- **مساحة القرص**: تزداد حسب كمية البيانات

### **تحسين الأداء:**
- ✅ **فهارس تلقائية** على الحقول المهمة
- ✅ **تنظيف دوري** للبيانات القديمة
- ✅ **ضغط البيانات** للملفات الكبيرة
- ✅ **تحليل الاستعلامات** البطيئة

---

## 🎉 **النتيجة النهائية**

### ✅ **ما تحصل عليه الآن:**
- 💾 **قاعدة بيانات حقيقية** - SQL Server LocalDB
- 🔒 **بيانات دائمة** - لا تُفقد أبداً
- 📈 **تراكم البيانات** - كل عملية محفوظة
- 🔄 **نسخ احتياطية** - حماية كاملة
- ⚡ **أداء عالي** - استعلامات سريعة
- 📊 **تقارير دقيقة** - بيانات تاريخية كاملة

**🎊 الآن لديك نظام إدارة مخبوزات بقاعدة بيانات حقيقية ودائمة!**
