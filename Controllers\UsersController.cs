using Microsoft.AspNetCore.Mvc;
using ANWBakerySystem.Data;
using ANWBakerySystem.Models;
using Microsoft.EntityFrameworkCore;

namespace ANWBakerySystem.Controllers
{
    [ApiController]
    [Route("api/[controller]")]
    public class UsersController : ControllerBase
    {
        private readonly ANWBakeryDbContext _context;

        public UsersController(ANWBakeryDbContext context)
        {
            _context = context;
        }

        // GET: api/users
        [HttpGet]
        public async Task<ActionResult<IEnumerable<object>>> GetUsers()
        {
            try
            {
                var users = await _context.Users
                    .Select(u => new
                    {
                        u.UserId,
                        u.Username,
                        u.FullName,
                        u.Email,
                        u.Phone,
                        u.Role,
                        u.IsActive,
                        u.LastLogin,
                        u.CreatedAt
                    })
                    .ToListAsync();

                return Ok(users);
            }
            catch (Exception ex)
            {
                return StatusCode(500, new { message = "خطأ في جلب المستخدمين", error = ex.Message });
            }
        }

        // GET: api/users/{id}
        [HttpGet("{id}")]
        public async Task<ActionResult<object>> GetUser(int id)
        {
            try
            {
                var user = await _context.Users
                    .Where(u => u.UserId == id)
                    .Select(u => new
                    {
                        u.UserId,
                        u.Username,
                        u.FullName,
                        u.Email,
                        u.Phone,
                        u.Role,
                        u.IsActive,
                        u.LastLogin,
                        u.CreatedAt
                    })
                    .FirstOrDefaultAsync();

                if (user == null)
                {
                    return NotFound(new { message = "المستخدم غير موجود" });
                }

                return Ok(user);
            }
            catch (Exception ex)
            {
                return StatusCode(500, new { message = "خطأ في جلب المستخدم", error = ex.Message });
            }
        }

        // POST: api/users
        [HttpPost]
        public async Task<ActionResult<object>> CreateUser([FromBody] CreateUserRequest request)
        {
            try
            {
                // التحقق من وجود اسم المستخدم
                var existingUser = await _context.Users
                    .FirstOrDefaultAsync(u => u.Username == request.Username);

                if (existingUser != null)
                {
                    return BadRequest(new { message = "اسم المستخدم موجود بالفعل" });
                }

                // التحقق من وجود البريد الإلكتروني
                if (!string.IsNullOrEmpty(request.Email))
                {
                    var existingEmail = await _context.Users
                        .FirstOrDefaultAsync(u => u.Email == request.Email);

                    if (existingEmail != null)
                    {
                        return BadRequest(new { message = "البريد الإلكتروني موجود بالفعل" });
                    }
                }

                var user = new User
                {
                    Username = request.Username,
                    FullName = request.FullName,
                    Email = request.Email,
                    Phone = request.Phone,
                    Role = request.Role,
                    IsActive = request.IsActive,
                    CreatedAt = DateTime.Now,
                    PasswordHash = BCrypt.Net.BCrypt.HashPassword(request.Password)
                };

                _context.Users.Add(user);
                await _context.SaveChangesAsync();

                return Ok(new
                {
                    message = "تم إنشاء المستخدم بنجاح",
                    userId = user.UserId
                });
            }
            catch (Exception ex)
            {
                return StatusCode(500, new { message = "خطأ في إنشاء المستخدم", error = ex.Message });
            }
        }

        // PUT: api/users/{id}
        [HttpPut("{id}")]
        public async Task<ActionResult> UpdateUser(int id, [FromBody] UpdateUserRequest request)
        {
            try
            {
                var user = await _context.Users.FindAsync(id);
                if (user == null)
                {
                    return NotFound(new { message = "المستخدم غير موجود" });
                }

                // التحقق من اسم المستخدم
                if (request.Username != user.Username)
                {
                    var existingUser = await _context.Users
                        .FirstOrDefaultAsync(u => u.Username == request.Username && u.UserId != id);

                    if (existingUser != null)
                    {
                        return BadRequest(new { message = "اسم المستخدم موجود بالفعل" });
                    }
                }

                // التحقق من البريد الإلكتروني
                if (!string.IsNullOrEmpty(request.Email) && request.Email != user.Email)
                {
                    var existingEmail = await _context.Users
                        .FirstOrDefaultAsync(u => u.Email == request.Email && u.UserId != id);

                    if (existingEmail != null)
                    {
                        return BadRequest(new { message = "البريد الإلكتروني موجود بالفعل" });
                    }
                }

                user.Username = request.Username;
                user.FullName = request.FullName;
                user.Email = request.Email;
                user.Phone = request.Phone;
                user.Role = request.Role;
                user.IsActive = request.IsActive;

                // تحديث كلمة المرور إذا تم توفيرها
                if (!string.IsNullOrEmpty(request.Password))
                {
                    user.PasswordHash = BCrypt.Net.BCrypt.HashPassword(request.Password);
                }

                await _context.SaveChangesAsync();

                return Ok(new { message = "تم تحديث المستخدم بنجاح" });
            }
            catch (Exception ex)
            {
                return StatusCode(500, new { message = "خطأ في تحديث المستخدم", error = ex.Message });
            }
        }

        // DELETE: api/users/{id}
        [HttpDelete("{id}")]
        public async Task<ActionResult> DeleteUser(int id)
        {
            try
            {
                var user = await _context.Users.FindAsync(id);
                if (user == null)
                {
                    return NotFound(new { message = "المستخدم غير موجود" });
                }

                _context.Users.Remove(user);
                await _context.SaveChangesAsync();

                return Ok(new { message = "تم حذف المستخدم بنجاح" });
            }
            catch (Exception ex)
            {
                return StatusCode(500, new { message = "خطأ في حذف المستخدم", error = ex.Message });
            }
        }
    }

    public class CreateUserRequest
    {
        public string Username { get; set; } = string.Empty;
        public string FullName { get; set; } = string.Empty;
        public string Email { get; set; } = string.Empty;
        public string Phone { get; set; } = string.Empty;
        public UserRole Role { get; set; } = UserRole.User;
        public bool IsActive { get; set; } = true;
        public string Password { get; set; } = string.Empty;
    }

    public class UpdateUserRequest
    {
        public string Username { get; set; } = string.Empty;
        public string FullName { get; set; } = string.Empty;
        public string Email { get; set; } = string.Empty;
        public string Phone { get; set; } = string.Empty;
        public UserRole Role { get; set; } = UserRole.User;
        public bool IsActive { get; set; } = true;
        public string? Password { get; set; }
    }
}
