using Microsoft.IdentityModel.Tokens;
using System.IdentityModel.Tokens.Jwt;
using System.Security.Claims;
using System.Text;
using ANWBakerySystem.Models;
using ANWBakerySystem.Repositories;

namespace ANWBakerySystem.Services
{
    /// <summary>
    /// تطبيق خدمة المصادقة والتوثيق
    /// Authentication Service Implementation
    /// </summary>
    public class AuthService : IAuthService
    {
        private readonly IUserRepository _userRepository;
        private readonly IConfiguration _configuration;

        public AuthService(IUserRepository userRepository, IConfiguration configuration)
        {
            _userRepository = userRepository;
            _configuration = configuration;
        }

        public async Task<AuthResult> LoginAsync(string username, string password)
        {
            try
            {
                var user = await _userRepository.AuthenticateUserAsync(username, password);
                if (user == null)
                {
                    return new AuthResult
                    {
                        Success = false,
                        Message = "اسم المستخدم أو كلمة المرور غير صحيحة"
                    };
                }

                if (!user.IsActive)
                {
                    return new AuthResult
                    {
                        Success = false,
                        Message = "حساب المستخدم غير نشط"
                    };
                }

                if (user.IsLocked)
                {
                    return new AuthResult
                    {
                        Success = false,
                        Message = "حساب المستخدم مقفل. يرجى الاتصال بالمدير"
                    };
                }

                var token = await GenerateJwtTokenAsync(user);
                var permissions = await GetUserPermissionsAsync(user.UserId);

                await _userRepository.UpdateLastLoginAsync(user.UserId);

                return new AuthResult
                {
                    Success = true,
                    Token = token,
                    User = user,
                    Message = "تم تسجيل الدخول بنجاح",
                    ExpiresAt = DateTime.Now.AddHours(GetTokenExpiryHours()),
                    Permissions = permissions
                };
            }
            catch (Exception ex)
            {
                return new AuthResult
                {
                    Success = false,
                    Message = $"خطأ في تسجيل الدخول: {ex.Message}"
                };
            }
        }

        public async Task<AuthResult> RefreshTokenAsync(string token)
        {
            try
            {
                var user = await GetUserFromTokenAsync(token);
                if (user == null)
                {
                    return new AuthResult
                    {
                        Success = false,
                        Message = "رمز غير صالح"
                    };
                }

                var newToken = await GenerateJwtTokenAsync(user);
                var permissions = await GetUserPermissionsAsync(user.UserId);

                return new AuthResult
                {
                    Success = true,
                    Token = newToken,
                    User = user,
                    Message = "تم تجديد الرمز بنجاح",
                    ExpiresAt = DateTime.Now.AddHours(GetTokenExpiryHours()),
                    Permissions = permissions
                };
            }
            catch (Exception ex)
            {
                return new AuthResult
                {
                    Success = false,
                    Message = $"خطأ في تجديد الرمز: {ex.Message}"
                };
            }
        }

        public async Task<bool> LogoutAsync(string token)
        {
            // يمكن إضافة منطق إضافي هنا مثل إضافة الرمز لقائمة سوداء
            // Additional logic can be added here like adding token to blacklist
            await Task.CompletedTask;
            return true;
        }

        public async Task<bool> ChangePasswordAsync(int userId, string currentPassword, string newPassword)
        {
            try
            {
                var user = await _userRepository.GetUserByIdAsync(userId);
                if (user == null) return false;

                if (!BCrypt.Net.BCrypt.Verify(currentPassword, user.PasswordHash))
                    return false;

                var newPasswordHash = BCrypt.Net.BCrypt.HashPassword(newPassword);
                return await _userRepository.UpdatePasswordAsync(userId, newPasswordHash);
            }
            catch
            {
                return false;
            }
        }

        public async Task<bool> ResetPasswordAsync(string email)
        {
            try
            {
                var user = await _userRepository.GetUserByEmailAsync(email);
                if (user == null) return false;

                // إنشاء كلمة مرور مؤقتة
                var tempPassword = GenerateTemporaryPassword();
                var tempPasswordHash = BCrypt.Net.BCrypt.HashPassword(tempPassword);

                var success = await _userRepository.UpdatePasswordAsync(user.UserId, tempPasswordHash);
                if (success)
                {
                    user.MustChangePassword = true;
                    await _userRepository.UpdateUserAsync(user);

                    // هنا يمكن إرسال كلمة المرور المؤقتة عبر البريد الإلكتروني
                    // Here you can send the temporary password via email
                }

                return success;
            }
            catch
            {
                return false;
            }
        }

        public async Task<string> GenerateJwtTokenAsync(User user)
        {
            var jwtSettings = _configuration.GetSection("JwtSettings");
            var secretKey = jwtSettings["SecretKey"];
            var issuer = jwtSettings["Issuer"];
            var audience = jwtSettings["Audience"];

            var key = new SymmetricSecurityKey(Encoding.UTF8.GetBytes(secretKey));
            var credentials = new SigningCredentials(key, SecurityAlgorithms.HmacSha256);

            var claims = new List<Claim>
            {
                new Claim(ClaimTypes.NameIdentifier, user.UserId.ToString()),
                new Claim(ClaimTypes.Name, user.Username),
                new Claim(ClaimTypes.Email, user.Email),
                new Claim("FullName", user.FullName),
                new Claim(ClaimTypes.Role, user.Role.ToString()),
                new Claim("IsActive", user.IsActive.ToString()),
                new Claim("MustChangePassword", user.MustChangePassword.ToString())
            };

            // إضافة الصلاحيات كـ Claims
            var permissions = await GetUserPermissionsAsync(user.UserId);
            foreach (var permission in permissions)
            {
                claims.Add(new Claim("Permission", permission));
            }

            var token = new JwtSecurityToken(
                issuer: issuer,
                audience: audience,
                claims: claims,
                expires: DateTime.Now.AddHours(GetTokenExpiryHours()),
                signingCredentials: credentials
            );

            return new JwtSecurityTokenHandler().WriteToken(token);
        }

        public Task<bool> ValidateTokenAsync(string token)
        {
            try
            {
                var jwtSettings = _configuration.GetSection("JwtSettings");
                var secretKey = jwtSettings["SecretKey"];

                var tokenHandler = new JwtSecurityTokenHandler();
                var key = Encoding.UTF8.GetBytes(secretKey);

                tokenHandler.ValidateToken(token, new TokenValidationParameters
                {
                    ValidateIssuerSigningKey = true,
                    IssuerSigningKey = new SymmetricSecurityKey(key),
                    ValidateIssuer = true,
                    ValidIssuer = jwtSettings["Issuer"],
                    ValidateAudience = true,
                    ValidAudience = jwtSettings["Audience"],
                    ValidateLifetime = true,
                    ClockSkew = TimeSpan.Zero
                }, out SecurityToken validatedToken);

                return Task.FromResult(true);
            }
            catch
            {
                return Task.FromResult(false);
            }
        }

        public async Task<User?> GetUserFromTokenAsync(string token)
        {
            try
            {
                var tokenHandler = new JwtSecurityTokenHandler();
                var jsonToken = tokenHandler.ReadJwtToken(token);

                var userIdClaim = jsonToken.Claims.FirstOrDefault(c => c.Type == ClaimTypes.NameIdentifier);
                if (userIdClaim == null || !int.TryParse(userIdClaim.Value, out int userId))
                    return null;

                return await _userRepository.GetUserByIdAsync(userId);
            }
            catch
            {
                return null;
            }
        }

        public async Task<bool> HasPermissionAsync(int userId, string permissionName)
        {
            return await _userRepository.HasPermissionAsync(userId, permissionName);
        }

        public async Task<bool> HasModuleAccessAsync(int userId, string moduleName)
        {
            var userPermissions = await _userRepository.GetUserPermissionsAsync(userId);
            return userPermissions.Any(up => up.Permission.ModuleName == moduleName && up.IsGranted);
        }

        #region Private Helper Methods

        private async Task<IEnumerable<string>> GetUserPermissionsAsync(int userId)
        {
            var userPermissions = await _userRepository.GetUserPermissionsAsync(userId);
            return userPermissions
                .Where(up => up.IsGranted)
                .Select(up => up.Permission.PermissionName)
                .ToList();
        }

        private int GetTokenExpiryHours()
        {
            var jwtSettings = _configuration.GetSection("JwtSettings");
            return int.TryParse(jwtSettings["ExpiryInHours"], out int hours) ? hours : 24;
        }

        private string GenerateTemporaryPassword()
        {
            const string chars = "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789";
            var random = new Random();
            return new string(Enumerable.Repeat(chars, 8)
                .Select(s => s[random.Next(s.Length)]).ToArray());
        }

        #endregion
    }
}
