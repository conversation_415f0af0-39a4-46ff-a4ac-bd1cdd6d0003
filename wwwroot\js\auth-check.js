// نظام فحص المصادقة والصلاحيات
class AuthManager {
    constructor() {
        this.currentUser = null;
        this.currentPage = window.location.pathname.split('/').pop();
        this.init();
    }

    // تهيئة النظام
    init() {
        this.checkAuthentication();
        this.loadUserInfo();
        this.checkPagePermissions();
        this.addUserInfoToNavbar();
        this.addLogoutButton();
    }

    // فحص حالة تسجيل الدخول
    checkAuthentication() {
        const isLoggedIn = localStorage.getItem('isLoggedIn');
        const authToken = localStorage.getItem('authToken');
        const userInfo = localStorage.getItem('userInfo');

        // السماح بالوصول لصفحات معينة بدون تسجيل دخول
        const publicPages = ['index.html', 'login.html', ''];
        
        if (publicPages.includes(this.currentPage)) {
            return;
        }

        if (!isLoggedIn || !authToken || !userInfo) {
            console.log('❌ المستخدم غير مسجل الدخول - توجيه لصفحة الدخول');
            window.location.href = 'login.html';
            return;
        }

        this.currentUser = JSON.parse(userInfo);
        console.log('✅ المستخدم مسجل الدخول:', this.currentUser.fullName);
    }

    // تحميل معلومات المستخدم
    loadUserInfo() {
        if (!this.currentUser) return;

        // البحث عن عناصر عرض معلومات المستخدم
        const userNameElement = document.getElementById('userName');
        const userRoleElement = document.getElementById('userRole');

        if (userNameElement) {
            userNameElement.textContent = `مرحباً، ${this.currentUser.fullName}`;
        }

        if (userRoleElement) {
            userRoleElement.textContent = this.getRoleDisplayName(this.currentUser.role);
        }
    }

    // الحصول على اسم الدور للعرض
    getRoleDisplayName(role) {
        const roles = {
            'Admin': 'مدير النظام',
            'Manager': 'مدير',
            'Accountant': 'محاسب',
            'Sales': 'موظف مبيعات',
            'Warehouse': 'موظف مخزن',
            'Cashier': 'أمين صندوق',
            'Employee': 'موظف',
            'User': 'مستخدم'
        };
        return roles[role] || 'مستخدم';
    }

    // فحص صلاحيات الصفحة الحالية
    checkPagePermissions() {
        if (!this.currentUser) return;

        const userPermissions = this.currentUser.permissions || [];
        const userRole = this.currentUser.role;

        // تعريف الصلاحيات المطلوبة لكل صفحة
        const pagePermissions = {
            'units-simple.html': ['items', 'all'],
            'items-simple.html': ['items', 'all'],
            'parties-simple.html': ['customers', 'suppliers', 'all'],
            'invoices-simple.html': ['invoices', 'all'],
            'inventory-simple.html': ['inventory', 'items', 'all'],
            'cash-simple.html': ['cash', 'all'],
            'banks-simple.html': ['banks', 'all'],
            'employees-simple.html': ['employees', 'all'],
            'owners-simple.html': ['owners', 'all'],
            'users-simple.html': ['users_manage', 'all'],
            'reports-simple.html': ['reports', 'all']
        };

        // فحص صلاحية الوصول للصفحة الحالية
        const requiredPermissions = pagePermissions[this.currentPage];
        if (requiredPermissions) {
            const hasPermission = requiredPermissions.some(perm => 
                userPermissions.includes(perm) || userRole === 'Admin'
            );

            if (!hasPermission) {
                alert('ليس لديك صلاحية للوصول لهذه الصفحة');
                window.location.href = 'dashboard.html';
                return;
            }
        }

        // إخفاء الروابط غير المسموحة في الشريط الجانبي
        this.hideUnauthorizedLinks(pagePermissions, userPermissions, userRole);
    }

    // إخفاء الروابط غير المسموحة
    hideUnauthorizedLinks(pagePermissions, userPermissions, userRole) {
        document.querySelectorAll('.sidebar a[href], .nav a[href]').forEach(link => {
            const href = link.getAttribute('href');
            if (href && href !== 'dashboard.html' && pagePermissions[href]) {
                const requiredPermissions = pagePermissions[href];
                const hasPermission = requiredPermissions.some(perm => 
                    userPermissions.includes(perm) || userRole === 'Admin'
                );

                if (!hasPermission) {
                    link.style.display = 'none';
                    // إخفاء العنصر الأب إذا كان li
                    if (link.parentElement.tagName === 'LI') {
                        link.parentElement.style.display = 'none';
                    }
                }
            }
        });
    }

    // إضافة معلومات المستخدم للشريط العلوي
    addUserInfoToNavbar() {
        if (!this.currentUser) return;

        const navbar = document.querySelector('.navbar .container-fluid, .navbar .container');
        if (navbar && !document.getElementById('userInfoNavbar')) {
            const userInfo = document.createElement('div');
            userInfo.id = 'userInfoNavbar';
            userInfo.className = 'navbar-text me-3';
            userInfo.innerHTML = `
                <span class="text-primary">
                    <i class="fas fa-user me-1"></i>
                    ${this.currentUser.fullName}
                </span>
                <small class="text-muted d-block">${this.getRoleDisplayName(this.currentUser.role)}</small>
            `;
            
            // إدراج قبل العناصر الموجودة
            const existingNav = navbar.querySelector('.navbar-nav');
            if (existingNav) {
                navbar.insertBefore(userInfo, existingNav);
            } else {
                navbar.appendChild(userInfo);
            }
        }
    }

    // إضافة زر تسجيل الخروج
    addLogoutButton() {
        if (!this.currentUser) return;

        const navbar = document.querySelector('.navbar .navbar-nav, .navbar .container-fluid');
        if (navbar && !document.getElementById('logoutBtn')) {
            const logoutBtn = document.createElement('div');
            logoutBtn.id = 'logoutBtn';
            logoutBtn.className = 'nav-item ms-auto';
            logoutBtn.innerHTML = `
                <button class="btn btn-outline-danger btn-sm" onclick="authManager.logout()">
                    <i class="fas fa-sign-out-alt me-1"></i>
                    تسجيل الخروج
                </button>
            `;
            navbar.appendChild(logoutBtn);
        }
    }

    // تسجيل الخروج
    logout() {
        if (confirm('هل أنت متأكد من تسجيل الخروج؟')) {
            // مسح جميع بيانات المستخدم
            localStorage.removeItem('authToken');
            localStorage.removeItem('userInfo');
            localStorage.removeItem('isLoggedIn');
            localStorage.removeItem('user');
            localStorage.removeItem('token');
            
            console.log('🔓 تم تسجيل الخروج بنجاح');
            
            // توجيه لصفحة الدخول
            window.location.href = 'login.html';
        }
    }

    // الحصول على المستخدم الحالي
    getCurrentUser() {
        return this.currentUser;
    }

    // فحص صلاحية معينة
    hasPermission(permission) {
        if (!this.currentUser) return false;
        if (this.currentUser.role === 'Admin') return true;
        
        const userPermissions = this.currentUser.permissions || [];
        return userPermissions.includes(permission) || userPermissions.includes('all');
    }
}

// إنشاء مثيل عام من مدير المصادقة
let authManager;

// تهيئة النظام عند تحميل الصفحة
document.addEventListener('DOMContentLoaded', function() {
    authManager = new AuthManager();
});

// تصدير للاستخدام في ملفات أخرى
if (typeof module !== 'undefined' && module.exports) {
    module.exports = AuthManager;
}
