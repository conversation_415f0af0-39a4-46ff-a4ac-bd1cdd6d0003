@echo off
echo ========================================
echo   ANW Bakery Management System
echo   نظام إدارة مخبوزات ANW
echo ========================================

cd /d "C:\ANW_bakery"

REM التحقق من وجود قاعدة البيانات
if not exist "ANWBakery.db" (
    echo.
    echo ⚠️  قاعدة البيانات غير موجودة!
    echo 🔧 سيتم إنشاؤها الآن...
    echo.
    call create_database.cmd
    goto :end
)

echo.
echo 🔧 بناء المشروع...
dotnet build --verbosity quiet

if %ERRORLEVEL% NEQ 0 (
    echo ❌ فشل في بناء المشروع!
    pause
    exit /b 1
)

echo.
echo 🚀 تشغيل التطبيق...
echo.
echo 🌐 سيتم فتح المتصفح تلقائياً على: http://localhost:5000
echo 👤 المستخدم: admin
echo 🔑 كلمة المرور: admin123
echo.
echo ✅ قاعدة البيانات: ANWBakery.db موجودة ومتصلة
echo.

timeout /t 2 >nul
start http://localhost:5000

dotnet run

:end
pause
