<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار APIs - نظام إدارة مخبوزات ANW</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css" rel="stylesheet">
    <style>
        body { font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; }
        .api-test { margin-bottom: 20px; padding: 15px; border: 1px solid #ddd; border-radius: 5px; }
        .success { background-color: #d4edda; border-color: #c3e6cb; }
        .error { background-color: #f8d7da; border-color: #f5c6cb; }
        .loading { background-color: #fff3cd; border-color: #ffeaa7; }
        pre { background: #f8f9fa; padding: 10px; border-radius: 3px; max-height: 200px; overflow-y: auto; }
    </style>
</head>
<body>
    <div class="container mt-4">
        <h1 class="text-center mb-4">🧪 اختبار APIs - نظام إدارة مخبوزات ANW</h1>
        
        <div class="row">
            <div class="col-md-6">
                <h3>🏪 اختبار ItemsController</h3>
                <div id="items-test" class="api-test">
                    <button onclick="testItems()" class="btn btn-primary">اختبار المنتجات</button>
                    <div id="items-result"></div>
                </div>

                <h3>👥 اختبار PartiesController</h3>
                <div id="parties-test" class="api-test">
                    <button onclick="testParties()" class="btn btn-primary">اختبار الأطراف</button>
                    <div id="parties-result"></div>
                </div>

                <h3>📄 اختبار InvoicesController</h3>
                <div id="invoices-test" class="api-test">
                    <button onclick="testInvoices()" class="btn btn-primary">اختبار الفواتير</button>
                    <div id="invoices-result"></div>
                </div>

                <h3>📦 اختبار InventoryController</h3>
                <div id="inventory-test" class="api-test">
                    <button onclick="testInventory()" class="btn btn-primary">اختبار المخزون</button>
                    <div id="inventory-result"></div>
                </div>
            </div>

            <div class="col-md-6">
                <h3>🏭 اختبار ProductionController</h3>
                <div id="production-test" class="api-test">
                    <button onclick="testProduction()" class="btn btn-primary">اختبار الإنتاج</button>
                    <div id="production-result"></div>
                </div>

                <h3>💰 اختبار AccountsController</h3>
                <div id="accounts-test" class="api-test">
                    <button onclick="testAccounts()" class="btn btn-primary">اختبار المحاسبة</button>
                    <div id="accounts-result"></div>
                </div>

                <h3>🏦 اختبار BanksController</h3>
                <div id="banks-test" class="api-test">
                    <button onclick="testBanks()" class="btn btn-primary">اختبار البنوك</button>
                    <div id="banks-result"></div>
                </div>

                <h3>🔧 اختبار AuthController</h3>
                <div id="auth-test" class="api-test">
                    <button onclick="testAuth()" class="btn btn-primary">اختبار المصادقة</button>
                    <div id="auth-result"></div>
                </div>
            </div>
        </div>

        <div class="row mt-4">
            <div class="col-12">
                <button onclick="testAllAPIs()" class="btn btn-success btn-lg w-100">🚀 اختبار جميع APIs</button>
            </div>
        </div>
    </div>

    <script>
        const API_BASE = 'http://localhost:5000/api';

        async function makeRequest(url, method = 'GET', data = null) {
            try {
                const options = {
                    method: method,
                    headers: {
                        'Content-Type': 'application/json',
                    }
                };

                if (data) {
                    options.body = JSON.stringify(data);
                }

                const response = await fetch(url, options);
                const result = await response.json();
                
                return {
                    success: response.ok,
                    status: response.status,
                    data: result
                };
            } catch (error) {
                return {
                    success: false,
                    error: error.message
                };
            }
        }

        function showResult(elementId, result) {
            const element = document.getElementById(elementId);
            const resultDiv = element.querySelector('div[id$="-result"]');
            
            if (result.success) {
                element.className = 'api-test success';
                resultDiv.innerHTML = `
                    <h5>✅ نجح الاختبار</h5>
                    <p><strong>الحالة:</strong> ${result.status}</p>
                    <pre>${JSON.stringify(result.data, null, 2)}</pre>
                `;
            } else {
                element.className = 'api-test error';
                resultDiv.innerHTML = `
                    <h5>❌ فشل الاختبار</h5>
                    <p><strong>الخطأ:</strong> ${result.error || result.status}</p>
                    <pre>${JSON.stringify(result.data || result.error, null, 2)}</pre>
                `;
            }
        }

        function showLoading(elementId) {
            const element = document.getElementById(elementId);
            const resultDiv = element.querySelector('div[id$="-result"]');
            element.className = 'api-test loading';
            resultDiv.innerHTML = '<h5>⏳ جاري الاختبار...</h5>';
        }

        async function testItems() {
            showLoading('items-test');
            const result = await makeRequest(`${API_BASE}/items`);
            showResult('items-test', result);
        }

        async function testParties() {
            showLoading('parties-test');
            const result = await makeRequest(`${API_BASE}/parties`);
            showResult('parties-test', result);
        }

        async function testInvoices() {
            showLoading('invoices-test');
            const result = await makeRequest(`${API_BASE}/invoices`);
            showResult('invoices-test', result);
        }

        async function testInventory() {
            showLoading('inventory-test');
            const result = await makeRequest(`${API_BASE}/inventory`);
            showResult('inventory-test', result);
        }

        async function testProduction() {
            showLoading('production-test');
            const result = await makeRequest(`${API_BASE}/production/recipes`);
            showResult('production-test', result);
        }

        async function testAccounts() {
            showLoading('accounts-test');
            const result = await makeRequest(`${API_BASE}/accounts`);
            showResult('accounts-test', result);
        }

        async function testBanks() {
            showLoading('banks-test');
            const result = await makeRequest(`${API_BASE}/banks`);
            showResult('banks-test', result);
        }

        async function testAuth() {
            showLoading('auth-test');
            const result = await makeRequest(`${API_BASE}/auth/test`);
            showResult('auth-test', result);
        }

        async function testAllAPIs() {
            console.log('🚀 بدء اختبار جميع APIs...');
            
            await testAuth();
            await new Promise(resolve => setTimeout(resolve, 500));
            
            await testItems();
            await new Promise(resolve => setTimeout(resolve, 500));
            
            await testParties();
            await new Promise(resolve => setTimeout(resolve, 500));
            
            await testInvoices();
            await new Promise(resolve => setTimeout(resolve, 500));
            
            await testInventory();
            await new Promise(resolve => setTimeout(resolve, 500));
            
            await testProduction();
            await new Promise(resolve => setTimeout(resolve, 500));
            
            await testAccounts();
            await new Promise(resolve => setTimeout(resolve, 500));
            
            await testBanks();
            
            console.log('✅ انتهى اختبار جميع APIs');
        }

        // اختبار تلقائي عند تحميل الصفحة
        window.onload = function() {
            console.log('🔧 صفحة اختبار APIs جاهزة');
            console.log('📡 عنوان API الأساسي:', API_BASE);
        };
    </script>
</body>
</html>
