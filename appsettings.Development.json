{"ConnectionStrings": {"DefaultConnection": "Server=.\\SQLEXPRESS;Database=ANWBakeryDB;Trusted_Connection=true;TrustServerCertificate=true;"}, "JwtSettings": {"SecretKey": "ANWBakerySecretKey2024!@#$%^&*()_+", "Issuer": "ANWBakerySystem", "Audience": "ANWBakeryUsers", "ExpiryInHours": 24}, "Logging": {"LogLevel": {"Default": "Information", "Microsoft.AspNetCore": "Warning", "Microsoft.EntityFrameworkCore.Database.Command": "Information"}}, "AllowedHosts": "*", "CurrencySettings": {"PrimaryCurrency": "YER", "CurrencySymbol": "ر.ي", "CurrencyName": "ريال يمني", "DecimalPlaces": 3, "IsRTL": true}, "CompanySettings": {"CompanyName": "مخبوزات ANW", "CompanyNameEn": "ANW <PERSON>", "Address": "اليمن - صنعاء", "Phone": "+967-1-234567", "Email": "<EMAIL>", "TaxNumber": "*********", "CommercialRegister": "*********"}}