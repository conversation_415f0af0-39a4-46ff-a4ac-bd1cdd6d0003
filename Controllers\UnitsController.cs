using Microsoft.AspNetCore.Mvc;
using ANWBakerySystem.Data;
using ANWBakerySystem.Models;
using Microsoft.EntityFrameworkCore;

namespace ANWBakerySystem.Controllers
{
    [ApiController]
    [Route("api/[controller]")]
    public class UnitsController : ControllerBase
    {
        private readonly ANWBakeryDbContext _context;

        public UnitsController(ANWBakeryDbContext context)
        {
            _context = context;
        }

        // GET: api/units
        [HttpGet]
        public async Task<ActionResult> GetUnits()
        {
            try
            {
                var units = await _context.Units
                    .Where(u => u.IsActive)
                    .OrderBy(u => u.UnitName)
                    .Select(u => new
                    {
                        u.UnitId,
                        u.UnitName,
                        u.UnitNameEn,
                        u.LargeUnitName,
                        u.MediumUnitName,
                        u.SmallUnitName,
                        u.LargeUnitCount,
                        u.MediumUnitCount,
                        u.SmallUnitCount,
                        u.IsActive,
                        u.CreatedAt
                    })
                    .ToListAsync();

                return Ok(new
                {
                    success = true,
                    data = units,
                    message = "تم جلب الوحدات بنجاح"
                });
            }
            catch (Exception ex)
            {
                return StatusCode(500, new
                {
                    success = false,
                    message = "خطأ في جلب الوحدات",
                    error = ex.Message
                });
            }
        }

        // GET: api/units/{id}
        [HttpGet("{id}")]
        public async Task<ActionResult> GetUnit(int id)
        {
            try
            {
                var unit = await _context.Units
                    .Where(u => u.UnitId == id)
                    .Select(u => new
                    {
                        u.UnitId,
                        u.UnitName,
                        u.UnitNameEn,
                        u.LargeUnitName,
                        u.MediumUnitName,
                        u.SmallUnitName,
                        u.LargeUnitCount,
                        u.MediumUnitCount,
                        u.SmallUnitCount,
                        u.IsActive,
                        u.CreatedAt
                    })
                    .FirstOrDefaultAsync();

                if (unit == null)
                {
                    return NotFound(new
                    {
                        success = false,
                        message = "الوحدة غير موجودة"
                    });
                }

                return Ok(new
                {
                    success = true,
                    data = unit,
                    message = "تم جلب الوحدة بنجاح"
                });
            }
            catch (Exception ex)
            {
                return StatusCode(500, new
                {
                    success = false,
                    message = "خطأ في جلب الوحدة",
                    error = ex.Message
                });
            }
        }

        // POST: api/units
        [HttpPost]
        public async Task<ActionResult> CreateUnit([FromBody] CreateUnitRequest request)
        {
            try
            {
                // التحقق من عدم وجود وحدة بنفس الاسم
                var existingUnit = await _context.Units
                    .FirstOrDefaultAsync(u => u.UnitName == request.UnitName);

                if (existingUnit != null)
                {
                    return BadRequest(new
                    {
                        success = false,
                        message = "اسم الوحدة موجود بالفعل"
                    });
                }

                var unit = new Unit
                {
                    UnitName = request.UnitName,
                    UnitNameEn = request.UnitNameEn,
                    UnitSymbol = request.UnitSymbol ?? request.UnitName.Substring(0, Math.Min(3, request.UnitName.Length)),
                    LargeUnitName = request.LargeUnitName,
                    MediumUnitName = request.MediumUnitName,
                    SmallUnitName = request.SmallUnitName,
                    LargeUnitCount = request.LargeUnitCount,
                    MediumUnitCount = request.MediumUnitCount,
                    SmallUnitCount = request.SmallUnitCount,
                    BaseUnitName = request.SmallUnitName,
                    IsActive = request.IsActive,
                    CreatedAt = DateTime.Now
                };

                _context.Units.Add(unit);
                await _context.SaveChangesAsync();

                return Ok(new
                {
                    success = true,
                    data = new { unitId = unit.UnitId },
                    message = "تم إنشاء الوحدة بنجاح"
                });
            }
            catch (Exception ex)
            {
                return StatusCode(500, new
                {
                    success = false,
                    message = "خطأ في إنشاء الوحدة",
                    error = ex.Message
                });
            }
        }

        // PUT: api/units/{id}
        [HttpPut("{id}")]
        public async Task<ActionResult> UpdateUnit(int id, [FromBody] UpdateUnitRequest request)
        {
            try
            {
                var unit = await _context.Units.FindAsync(id);
                if (unit == null)
                {
                    return NotFound(new
                    {
                        success = false,
                        message = "الوحدة غير موجودة"
                    });
                }

                // التحقق من عدم وجود وحدة بنفس الاسم
                if (request.UnitName != unit.UnitName)
                {
                    var existingUnit = await _context.Units
                        .FirstOrDefaultAsync(u => u.UnitName == request.UnitName && u.UnitId != id);

                    if (existingUnit != null)
                    {
                        return BadRequest(new
                        {
                            success = false,
                            message = "اسم الوحدة موجود بالفعل"
                        });
                    }
                }

                unit.UnitName = request.UnitName;
                unit.UnitNameEn = request.UnitNameEn;
                unit.UnitSymbol = request.UnitSymbol ?? request.UnitName.Substring(0, Math.Min(3, request.UnitName.Length));
                unit.LargeUnitName = request.LargeUnitName;
                unit.MediumUnitName = request.MediumUnitName;
                unit.SmallUnitName = request.SmallUnitName;
                unit.LargeUnitCount = request.LargeUnitCount;
                unit.MediumUnitCount = request.MediumUnitCount;
                unit.SmallUnitCount = request.SmallUnitCount;
                unit.BaseUnitName = request.SmallUnitName;
                unit.IsActive = request.IsActive;

                await _context.SaveChangesAsync();

                return Ok(new
                {
                    success = true,
                    message = "تم تحديث الوحدة بنجاح"
                });
            }
            catch (Exception ex)
            {
                return StatusCode(500, new
                {
                    success = false,
                    message = "خطأ في تحديث الوحدة",
                    error = ex.Message
                });
            }
        }

        // DELETE: api/units/{id}
        [HttpDelete("{id}")]
        public async Task<ActionResult> DeleteUnit(int id)
        {
            try
            {
                var unit = await _context.Units.FindAsync(id);
                if (unit == null)
                {
                    return NotFound(new
                    {
                        success = false,
                        message = "الوحدة غير موجودة"
                    });
                }

                // التحقق من عدم استخدام الوحدة في أصناف
                var isUsed = await _context.Items.AnyAsync(i => i.UnitId == id);
                if (isUsed)
                {
                    return BadRequest(new
                    {
                        success = false,
                        message = "لا يمكن حذف الوحدة لأنها مستخدمة في أصناف"
                    });
                }

                _context.Units.Remove(unit);
                await _context.SaveChangesAsync();

                return Ok(new
                {
                    success = true,
                    message = "تم حذف الوحدة بنجاح"
                });
            }
            catch (Exception ex)
            {
                return StatusCode(500, new
                {
                    success = false,
                    message = "خطأ في حذف الوحدة",
                    error = ex.Message
                });
            }
        }
    }

    public class CreateUnitRequest
    {
        public string UnitName { get; set; } = string.Empty;
        public string? UnitNameEn { get; set; }
        public string? UnitSymbol { get; set; }
        public string LargeUnitName { get; set; } = string.Empty;
        public string MediumUnitName { get; set; } = string.Empty;
        public string SmallUnitName { get; set; } = string.Empty;
        public decimal LargeUnitCount { get; set; } = 1;
        public decimal MediumUnitCount { get; set; } = 1;
        public decimal SmallUnitCount { get; set; } = 1;
        public bool IsActive { get; set; } = true;
    }

    public class UpdateUnitRequest
    {
        public string UnitName { get; set; } = string.Empty;
        public string? UnitNameEn { get; set; }
        public string? UnitSymbol { get; set; }
        public string LargeUnitName { get; set; } = string.Empty;
        public string MediumUnitName { get; set; } = string.Empty;
        public string SmallUnitName { get; set; } = string.Empty;
        public decimal LargeUnitCount { get; set; } = 1;
        public decimal MediumUnitCount { get; set; } = 1;
        public decimal SmallUnitCount { get; set; } = 1;
        public bool IsActive { get; set; } = true;
    }
}
