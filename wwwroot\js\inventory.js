// إدارة المخزون - متصل بـ APIs
const API_BASE_URL = '/api';
let inventoryItems = [];
let filteredInventory = [];

// تحميل الصفحة
document.addEventListener('DOMContentLoaded', function() {
    console.log('🔄 تحميل صفحة إدارة المخزون...');
    initializePage();
});

// تهيئة الصفحة
async function initializePage() {
    try {
        await loadInventory();
        await populateItemDropdowns();
        console.log('✅ تم تحميل صفحة المخزون بنجاح');
    } catch (error) {
        console.error('❌ خطأ في تحميل صفحة المخزون:', error);
        showError('خطأ في تحميل البيانات. يرجى إعادة تحميل الصفحة.');
    }
}

// تحميل بيانات المخزون من API
async function loadInventory() {
    console.log('🔄 تحميل بيانات المخزون...');
    showLoading();

    try {
        const response = await fetch(`${API_BASE_URL}/inventory`);
        if (response.ok) {
            const result = await response.json();
            if (result.success) {
                inventoryItems = result.data || [];
                filteredInventory = [...inventoryItems];
                displayInventory(filteredInventory);
                updateStatistics();
                console.log('✅ تم تحميل المخزون:', inventoryItems.length);
                return;
            }
        }

        // في حالة فشل API، استخدم بيانات تجريبية
        console.warn('⚠️ فشل تحميل المخزون من API، استخدام بيانات تجريبية');
        loadSampleInventory();

    } catch (error) {
        console.error('❌ خطأ في تحميل المخزون:', error);
        loadSampleInventory();
    } finally {
        hideLoading();
    }
}

// تحميل بيانات تجريبية للمخزون
function loadSampleInventory() {
    inventoryItems = [
        {
            id: 1,
            itemName: 'دقيق أبيض',
            category: 'raw_materials',
            currentQuantity: 150.500,
            unit: 'كيلوجرام',
            minQuantity: 50.000,
            avgCost: 450.000,
            totalValue: 67725.000,
            status: 'in_stock',
            lastUpdated: '2024-12-15'
        },
        {
            id: 2,
            itemName: 'سكر أبيض',
            category: 'raw_materials',
            currentQuantity: 25.000,
            unit: 'كيلوجرام',
            minQuantity: 30.000,
            avgCost: 800.000,
            totalValue: 20000.000,
            status: 'low_stock',
            lastUpdated: '2024-12-14'
        },
        {
            id: 3,
            itemName: 'خميرة فورية',
            category: 'raw_materials',
            currentQuantity: 0.000,
            unit: 'كيلوجرام',
            minQuantity: 5.000,
            avgCost: 2500.000,
            totalValue: 0.000,
            status: 'out_of_stock',
            lastUpdated: '2024-12-13'
        },
        {
            id: 4,
            itemName: 'خبز أبيض',
            category: 'products',
            currentQuantity: 200.000,
            unit: 'رغيف',
            minQuantity: 50.000,
            avgCost: 25.000,
            totalValue: 5000.000,
            status: 'in_stock',
            lastUpdated: '2024-12-15'
        },
        {
            id: 5,
            itemName: 'كيك شوكولاتة',
            category: 'products',
            currentQuantity: 15.000,
            unit: 'قطعة',
            minQuantity: 20.000,
            avgCost: 1500.000,
            totalValue: 22500.000,
            status: 'low_stock',
            lastUpdated: '2024-12-15'
        },
        {
            id: 6,
            itemName: 'أكياس ورقية',
            category: 'packaging',
            currentQuantity: 500.000,
            unit: 'قطعة',
            minQuantity: 100.000,
            avgCost: 15.000,
            totalValue: 7500.000,
            status: 'in_stock',
            lastUpdated: '2024-12-14'
        }
    ];

    filteredInventory = [...inventoryItems];
    displayInventory(filteredInventory);
    updateStatistics();
    console.log('📦 تم تحميل المخزون التجريبي:', inventoryItems.length);
}

// عرض بيانات المخزون
function displayInventory(items) {
    const tbody = document.getElementById('inventoryTableBody');
    
    if (items.length === 0) {
        tbody.innerHTML = `
            <tr>
                <td colspan="9" class="text-center text-muted">
                    <i class="fas fa-warehouse fa-2x mb-2"></i>
                    <p>لا توجد أصناف في المخزون</p>
                </td>
            </tr>
        `;
        return;
    }
    
    tbody.innerHTML = items.map(item => `
        <tr class="${getRowClass(item.status)}">
            <td>
                <div>
                    <strong>${item.itemName}</strong>
                    <br>
                    <small class="text-muted">آخر تحديث: ${item.lastUpdated}</small>
                </div>
            </td>
            <td>
                <span class="badge ${getCategoryBadge(item.category)}">
                    ${getCategoryText(item.category)}
                </span>
            </td>
            <td><strong>${formatQuantity(item.currentQuantity)}</strong></td>
            <td>${item.unit}</td>
            <td>${formatQuantity(item.minQuantity)}</td>
            <td>${formatCurrency(item.avgCost)}</td>
            <td><strong>${formatCurrency(item.totalValue)}</strong></td>
            <td>
                <span class="badge ${getStatusBadge(item.status)}">
                    ${getStatusText(item.status)}
                </span>
            </td>
            <td>
                <div class="btn-group btn-group-sm">
                    <button class="btn btn-outline-primary" onclick="viewItemHistory(${item.id})" title="تاريخ الحركة">
                        <i class="fas fa-history"></i>
                    </button>
                    <button class="btn btn-outline-success" onclick="adjustStock(${item.id})" title="تعديل المخزون">
                        <i class="fas fa-edit"></i>
                    </button>
                    <button class="btn btn-outline-warning" onclick="recordWaste(${item.id})" title="تسجيل تالف">
                        <i class="fas fa-trash"></i>
                    </button>
                </div>
            </td>
        </tr>
    `).join('');
}

// تحديث الإحصائيات
function updateStatistics() {
    const totalItems = inventoryItems.length;
    const inventoryValue = inventoryItems.reduce((sum, item) => sum + item.totalValue, 0);
    const lowStockItems = inventoryItems.filter(item => item.status === 'low_stock').length;
    const outOfStockItems = inventoryItems.filter(item => item.status === 'out_of_stock').length;
    
    document.getElementById('totalItems').textContent = totalItems;
    document.getElementById('inventoryValue').textContent = formatCurrency(inventoryValue);
    document.getElementById('lowStockItems').textContent = lowStockItems;
    document.getElementById('outOfStockItems').textContent = outOfStockItems;
}

// تصفية المخزون
function filterInventory() {
    const categoryFilter = document.getElementById('categoryFilter').value;
    const statusFilter = document.getElementById('stockStatusFilter').value;
    const searchTerm = document.getElementById('searchInventory').value.toLowerCase();
    
    filteredInventory = inventoryItems.filter(item => {
        const matchesCategory = !categoryFilter || item.category === categoryFilter;
        const matchesStatus = !statusFilter || item.status === statusFilter;
        const matchesSearch = !searchTerm || item.itemName.toLowerCase().includes(searchTerm);
        
        return matchesCategory && matchesStatus && matchesSearch;
    });
    
    displayInventory(filteredInventory);
}

// ملء قوائم الأصناف من API
async function populateItemDropdowns() {
    console.log('🔄 تحميل قوائم الأصناف...');

    try {
        // تحميل الأصناف من API
        const response = await fetch(`${API_BASE_URL}/items/active`);
        let items = [];

        if (response.ok) {
            const result = await response.json();
            if (result.success) {
                items = result.data || [];
            }
        }

        // إذا فشل API، استخدم بيانات المخزون الحالية
        if (items.length === 0) {
            items = inventoryItems;
        }

        const dropdowns = ['adjustmentItem', 'wasteItem'];

        dropdowns.forEach(dropdownId => {
            const dropdown = document.getElementById(dropdownId);
            if (dropdown) {
                dropdown.innerHTML = '<option value="">-- اختر الصنف --</option>';
                items.forEach(item => {
                    const option = document.createElement('option');
                    option.value = item.itemId || item.id;

                    // تحديد الكمية والوحدة
                    const quantity = item.currentStock || item.currentQuantity || 0;
                    const unit = item.majorUnit?.unitName || item.unit || 'وحدة';

                    option.textContent = `${item.itemName} (${formatQuantity(quantity)} ${unit})`;
                    dropdown.appendChild(option);
                });
            }
        });

        console.log('✅ تم تحميل قوائم الأصناف:', items.length);

    } catch (error) {
        console.error('❌ خطأ في تحميل قوائم الأصناف:', error);

        // استخدام بيانات المخزون الحالية كـ fallback
        const dropdowns = ['adjustmentItem', 'wasteItem'];

        dropdowns.forEach(dropdownId => {
            const dropdown = document.getElementById(dropdownId);
            if (dropdown) {
                dropdown.innerHTML = '<option value="">-- اختر الصنف --</option>';
                inventoryItems.forEach(item => {
                    const option = document.createElement('option');
                    option.value = item.id;
                    option.textContent = `${item.itemName} (${formatQuantity(item.currentQuantity)} ${item.unit})`;
                    dropdown.appendChild(option);
                });
            }
        });
    }
}

// إظهار نافذة تعديل المخزون
function showStockAdjustmentModal() {
    populateItemDropdowns();
    const modal = new bootstrap.Modal(document.getElementById('stockAdjustmentModal'));
    modal.show();
}

// إظهار نافذة تسجيل التالف
function showWasteModal() {
    populateItemDropdowns();
    const modal = new bootstrap.Modal(document.getElementById('wasteModal'));
    modal.show();
}

// حفظ تعديل المخزون عبر API
async function saveStockAdjustment() {
    const form = document.getElementById('stockAdjustmentForm');
    if (!form.checkValidity()) {
        form.reportValidity();
        return;
    }

    const itemId = parseInt(document.getElementById('adjustmentItem').value);
    const adjustmentType = document.getElementById('adjustmentType').value;
    const quantity = parseFloat(document.getElementById('adjustmentQuantity').value);
    const reason = document.getElementById('adjustmentReason').value;

    try {
        showSaveLoading('تعديل المخزون');

        // تحضير بيانات التعديل
        const adjustmentData = {
            itemId: itemId,
            adjustmentType: getAdjustmentTypeValue(adjustmentType),
            quantity: quantity,
            reason: reason,
            adjustmentDate: new Date().toISOString()
        };

        const response = await fetch(`${API_BASE_URL}/inventory/adjust`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify(adjustmentData)
        });

        const result = await response.json();

        if (response.ok && result.success) {
            showSuccess('تم تعديل المخزون بنجاح');
            bootstrap.Modal.getInstance(document.getElementById('stockAdjustmentModal')).hide();
            form.reset();
            await loadInventory(); // إعادة تحميل المخزون
        } else {
            showError(result.message || 'حدث خطأ أثناء تعديل المخزون');
        }

    } catch (error) {
        console.error('❌ خطأ في تعديل المخزون:', error);

        // محاكاة التعديل محلياً كـ fallback
        saveStockAdjustmentLocal(itemId, adjustmentType, quantity, reason);

    } finally {
        hideSaveLoading();
    }
}

// تعديل المخزون محلياً (fallback)
function saveStockAdjustmentLocal(itemId, adjustmentType, quantity, reason) {
    
    const item = inventoryItems.find(i => i.id === itemId);
    if (!item) {
        showError('الصنف غير موجود');
        return;
    }

    let newQuantity = item.currentQuantity;

    switch (adjustmentType) {
        case 'increase':
            newQuantity += quantity;
            break;
        case 'decrease':
            newQuantity = Math.max(0, newQuantity - quantity);
            break;
        case 'set':
            newQuantity = quantity;
            break;
    }

    // تحديث الكمية والقيمة
    item.currentQuantity = newQuantity;
    item.totalValue = newQuantity * item.avgCost;
    item.lastUpdated = new Date().toISOString().split('T')[0];

    // تحديث الحالة
    if (newQuantity === 0) {
        item.status = 'out_of_stock';
    } else if (newQuantity <= item.minQuantity) {
        item.status = 'low_stock';
    } else {
        item.status = 'in_stock';
    }

    // إنشاء سجل حركة
    console.log('سجل تعديل المخزون:', {
        itemId: itemId,
        itemName: item.itemName,
        adjustmentType: adjustmentType,
        quantity: quantity,
        oldQuantity: adjustmentType === 'set' ? 'غير محدد' :
                    (adjustmentType === 'increase' ? newQuantity - quantity : newQuantity + quantity),
        newQuantity: newQuantity,
        reason: reason,
        date: new Date().toISOString()
    });

    bootstrap.Modal.getInstance(document.getElementById('stockAdjustmentModal')).hide();
    document.getElementById('stockAdjustmentForm').reset();

    loadSampleInventory(); // إعادة تحميل البيانات التجريبية
    showSuccess('تم تعديل المخزون بنجاح (محلياً)');
}

// حفظ سجل التالف
function saveWasteRecord() {
    const form = document.getElementById('wasteForm');
    if (!form.checkValidity()) {
        form.reportValidity();
        return;
    }
    
    const itemId = parseInt(document.getElementById('wasteItem').value);
    const wasteQuantity = parseFloat(document.getElementById('wasteQuantity').value);
    const wasteReason = document.getElementById('wasteReason').value;
    const notes = document.getElementById('wasteNotes').value;
    
    const item = inventoryItems.find(i => i.id === itemId);
    if (!item) {
        alert('الصنف غير موجود');
        return;
    }
    
    if (wasteQuantity > item.currentQuantity) {
        alert('الكمية التالفة أكبر من الكمية المتوفرة في المخزون');
        return;
    }
    
    // تقليل الكمية
    item.currentQuantity -= wasteQuantity;
    item.totalValue = item.currentQuantity * item.avgCost;
    item.lastUpdated = new Date().toISOString().split('T')[0];
    
    // تحديث الحالة
    if (item.currentQuantity === 0) {
        item.status = 'out_of_stock';
    } else if (item.currentQuantity <= item.minQuantity) {
        item.status = 'low_stock';
    } else {
        item.status = 'in_stock';
    }
    
    // إنشاء سجل تالف
    console.log('سجل التالف:', {
        itemId: itemId,
        itemName: item.itemName,
        wasteQuantity: wasteQuantity,
        reason: getWasteReasonText(wasteReason),
        notes: notes,
        cost: wasteQuantity * item.avgCost,
        date: new Date().toISOString()
    });
    
    bootstrap.Modal.getInstance(document.getElementById('wasteModal')).hide();
    form.reset();
    
    loadInventory();
    alert('تم تسجيل التالف بنجاح!');
}

// توليد تقرير المخزون
function generateInventoryReport() {
    const reportData = {
        date: new Date().toLocaleDateString('ar-YE'),
        totalItems: inventoryItems.length,
        totalValue: inventoryItems.reduce((sum, item) => sum + item.totalValue, 0),
        lowStockItems: inventoryItems.filter(item => item.status === 'low_stock'),
        outOfStockItems: inventoryItems.filter(item => item.status === 'out_of_stock'),
        items: inventoryItems
    };
    
    console.log('تقرير المخزون:', reportData);
    alert('تم إنشاء تقرير المخزون - سيتم تطوير الطباعة والتصدير لاحقاً');
}

// دوال مساعدة
function getCategoryText(category) {
    const categories = {
        'raw_materials': 'مواد خام',
        'products': 'منتجات',
        'packaging': 'مواد تعبئة'
    };
    return categories[category] || category;
}

function getCategoryBadge(category) {
    const badges = {
        'raw_materials': 'bg-primary',
        'products': 'bg-success',
        'packaging': 'bg-info'
    };
    return badges[category] || 'bg-secondary';
}

function getStatusText(status) {
    const statuses = {
        'in_stock': 'متوفر',
        'low_stock': 'مخزون منخفض',
        'out_of_stock': 'غير متوفر'
    };
    return statuses[status] || status;
}

function getStatusBadge(status) {
    const badges = {
        'in_stock': 'bg-success',
        'low_stock': 'bg-warning',
        'out_of_stock': 'bg-danger'
    };
    return badges[status] || 'bg-secondary';
}

function getRowClass(status) {
    const classes = {
        'low_stock': 'table-warning',
        'out_of_stock': 'table-danger'
    };
    return classes[status] || '';
}

function getWasteReasonText(reason) {
    const reasons = {
        'expired': 'انتهاء الصلاحية',
        'damaged': 'تلف أثناء التخزين',
        'production_error': 'خطأ في الإنتاج',
        'other': 'أخرى'
    };
    return reasons[reason] || reason;
}

function formatQuantity(quantity) {
    return new Intl.NumberFormat('ar-YE', {
        minimumFractionDigits: 3,
        maximumFractionDigits: 3
    }).format(quantity);
}

function formatCurrency(amount) {
    return new Intl.NumberFormat('ar-YE', {
        style: 'currency',
        currency: 'YER',
        minimumFractionDigits: 3,
        maximumFractionDigits: 3
    }).format(amount).replace('YER', 'ر.ي');
}

// وظائف إضافية
function adjustStock(itemId) {
    document.getElementById('adjustmentItem').value = itemId;
    showStockAdjustmentModal();
}

function recordWaste(itemId) {
    document.getElementById('wasteItem').value = itemId;
    showWasteModal();
}

function viewItemHistory(itemId) {
    alert('عرض تاريخ حركة الصنف - سيتم تطويره لاحقاً');
}

// وظائف المساعدة
function getAdjustmentTypeValue(type) {
    const types = {
        'increase': 1,
        'decrease': 2,
        'set': 3
    };
    return types[type] || 1;
}

function showLoading() {
    const tbody = document.getElementById('inventoryTableBody');
    tbody.innerHTML = `
        <tr>
            <td colspan="9" class="text-center">
                <div class="spinner-border text-primary" role="status">
                    <span class="visually-hidden">جاري التحميل...</span>
                </div>
                <p class="mt-2">جاري تحميل المخزون...</p>
            </td>
        </tr>
    `;
}

function hideLoading() {
    // سيتم إخفاء التحميل عند عرض البيانات
}

function showError(message) {
    const alertDiv = document.createElement('div');
    alertDiv.className = 'alert alert-danger alert-dismissible fade show position-fixed';
    alertDiv.style.cssText = 'top: 20px; right: 20px; z-index: 9999; min-width: 300px;';
    alertDiv.innerHTML = `
        <i class="fas fa-exclamation-triangle me-2"></i>
        ${message}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    `;
    document.body.appendChild(alertDiv);

    setTimeout(() => {
        if (alertDiv.parentNode) {
            alertDiv.remove();
        }
    }, 5000);
}

function showSuccess(message) {
    const alertDiv = document.createElement('div');
    alertDiv.className = 'alert alert-success alert-dismissible fade show position-fixed';
    alertDiv.style.cssText = 'top: 20px; right: 20px; z-index: 9999; min-width: 300px;';
    alertDiv.innerHTML = `
        <i class="fas fa-check-circle me-2"></i>
        ${message}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    `;
    document.body.appendChild(alertDiv);

    setTimeout(() => {
        if (alertDiv.parentNode) {
            alertDiv.remove();
        }
    }, 3000);
}

function showSaveLoading(operation = 'العملية') {
    // يمكن تخصيص هذه الوظيفة حسب الحاجة
    console.log(`جاري تنفيذ ${operation}...`);
}

function hideSaveLoading() {
    // يمكن تخصيص هذه الوظيفة حسب الحاجة
    console.log('تم انتهاء العملية');
}

// تسجيل الخروج
function logout() {
    if (confirm('هل أنت متأكد من تسجيل الخروج؟')) {
        localStorage.removeItem('isLoggedIn');
        window.location.href = 'login.html';
    }
}
