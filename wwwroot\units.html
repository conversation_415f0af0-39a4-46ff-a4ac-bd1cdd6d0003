<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إدارة وحدات القياس - نظام إدارة مخبوزات ANW</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="css/style.css" rel="stylesheet">
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary">
        <div class="container-fluid">
            <a class="navbar-brand" href="dashboard.html">
                <i class="fas fa-bread-slice me-2"></i>
                نظام إدارة مخبوزات ANW
            </a>
            <div class="navbar-nav ms-auto">
                <a class="nav-link" href="dashboard.html">
                    <i class="fas fa-home"></i> الرئيسية
                </a>
                <a class="nav-link" href="#" onclick="logout()">
                    <i class="fas fa-sign-out-alt"></i> تسجيل الخروج
                </a>
            </div>
        </div>
    </nav>

    <div class="container-fluid mt-4">
        <div class="row">
            <!-- Sidebar -->
            <div class="col-md-3">
                <div class="card">
                    <div class="card-header">
                        <h5><i class="fas fa-list"></i> القوائم</h5>
                    </div>
                    <div class="list-group list-group-flush">
                        <a href="dashboard.html" class="list-group-item list-group-item-action">
                            <i class="fas fa-tachometer-alt"></i> لوحة التحكم
                        </a>
                        <a href="units.html" class="list-group-item list-group-item-action active">
                            <i class="fas fa-balance-scale"></i> وحدات القياس
                        </a>
                        <a href="items-management.html" class="list-group-item list-group-item-action">
                            <i class="fas fa-boxes"></i> الأصناف والمنتجات
                        </a>
                        <a href="parties.html" class="list-group-item list-group-item-action">
                            <i class="fas fa-users"></i> العملاء والموردين
                        </a>
                        <a href="invoices.html" class="list-group-item list-group-item-action">
                            <i class="fas fa-file-invoice"></i> الفواتير
                        </a>
                    </div>
                </div>
            </div>

            <!-- Main Content -->
            <div class="col-md-9">
                <div class="card">
                    <div class="card-header d-flex justify-content-between align-items-center">
                        <h4><i class="fas fa-balance-scale"></i> إدارة وحدات القياس المرجعية</h4>
                        <div>
                            <button class="btn btn-success" onclick="showAddModal()">
                                <i class="fas fa-plus"></i> إضافة وحدة جديدة
                            </button>
                            <button class="btn btn-info" onclick="printUnitsReport()">
                                <i class="fas fa-print"></i> طباعة التقرير
                            </button>
                        </div>
                    </div>
                    <div class="card-body">
                        <!-- Search and Filter -->
                        <div class="row mb-3">
                            <div class="col-md-4">
                                <input type="text" class="form-control" id="searchInput" placeholder="البحث في وحدات القياس..." onkeyup="searchUnits()">
                            </div>
                            <div class="col-md-3">
                                <select class="form-select" id="typeFilter" onchange="filterUnits()">
                                    <option value="">جميع الأنواع</option>
                                    <option value="weight">وزن</option>
                                    <option value="volume">حجم</option>
                                    <option value="length">طول</option>
                                    <option value="piece">قطعة</option>
                                    <option value="custom">مخصص</option>
                                </select>
                            </div>
                            <div class="col-md-3">
                                <select class="form-select" id="statusFilter" onchange="filterUnits()">
                                    <option value="">جميع الحالات</option>
                                    <option value="true">نشط</option>
                                    <option value="false">غير نشط</option>
                                </select>
                            </div>
                            <div class="col-md-2">
                                <button class="btn btn-primary w-100" onclick="loadUnits()">
                                    <i class="fas fa-sync"></i> تحديث
                                </button>
                            </div>
                        </div>

                        <!-- Units Table -->
                        <div class="table-responsive">
                            <table class="table table-striped table-hover">
                                <thead class="table-dark">
                                    <tr>
                                        <th>الرقم</th>
                                        <th>اسم الوحدة</th>
                                        <th>الرمز</th>
                                        <th>الوحدة الكبرى</th>
                                        <th>الوحدة المتوسطة</th>
                                        <th>الوحدة الصغرى</th>
                                        <th>عدد الاستخدامات</th>
                                        <th>الحالة</th>
                                        <th>الإجراءات</th>
                                    </tr>
                                </thead>
                                <tbody id="unitsTableBody">
                                    <tr>
                                        <td colspan="9" class="text-center">
                                            <div class="spinner-border text-primary" role="status">
                                                <span class="visually-hidden">جاري التحميل...</span>
                                            </div>
                                        </td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Add/Edit Unit Modal -->
    <div class="modal fade" id="unitModal" tabindex="-1">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="modalTitle">إضافة وحدة قياس مرجعية جديدة</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <form id="unitForm">
                        <input type="hidden" id="unitId">

                        <!-- Basic Information -->
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="unitName" class="form-label">اسم الوحدة *</label>
                                    <input type="text" class="form-control" id="unitName" required placeholder="مثال: كيس دقيق 50كجم">
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="unitSymbol" class="form-label">رمز الوحدة *</label>
                                    <input type="text" class="form-control" id="unitSymbol" required placeholder="مثال: كيس">
                                </div>
                            </div>
                        </div>

                        <div class="mb-3">
                            <label for="unitNameEn" class="form-label">الاسم بالإنجليزية</label>
                            <input type="text" class="form-control" id="unitNameEn" placeholder="مثال: Flour Bag 50kg">
                        </div>

                        <!-- Hierarchical Units System -->
                        <div class="card mb-3">
                            <div class="card-header">
                                <h6><i class="fas fa-layer-group me-2"></i>النظام الهرمي للوحدات</h6>
                            </div>
                            <div class="card-body">
                                <div class="row">
                                    <!-- Major Unit -->
                                    <div class="col-md-4">
                                        <h6 class="text-primary">الوحدة الكبرى</h6>
                                        <div class="mb-3">
                                            <label for="majorUnitName" class="form-label">اسم الوحدة *</label>
                                            <input type="text" class="form-control" id="majorUnitName" required placeholder="مثال: كيس">
                                        </div>
                                        <div class="mb-3">
                                            <label for="majorUnitFactor" class="form-label">المعامل</label>
                                            <input type="number" class="form-control" id="majorUnitFactor" value="1" readonly>
                                            <small class="text-muted">دائماً = 1</small>
                                        </div>
                                    </div>

                                    <!-- Medium Unit -->
                                    <div class="col-md-4">
                                        <h6 class="text-info">الوحدة المتوسطة</h6>
                                        <div class="mb-3">
                                            <label for="mediumUnitName" class="form-label">اسم الوحدة *</label>
                                            <input type="text" class="form-control" id="mediumUnitName" required placeholder="مثال: كيلوجرام">
                                        </div>
                                        <div class="mb-3">
                                            <label for="mediumUnitFactor" class="form-label">العدد في الوحدة الكبرى *</label>
                                            <input type="number" class="form-control" id="mediumUnitFactor" required placeholder="مثال: 50" onchange="updateExample()">
                                            <small class="text-muted">كم وحدة متوسطة في الكبرى</small>
                                        </div>
                                    </div>

                                    <!-- Minor Unit -->
                                    <div class="col-md-4">
                                        <h6 class="text-secondary">الوحدة الصغرى</h6>
                                        <div class="mb-3">
                                            <label for="minorUnitName" class="form-label">اسم الوحدة *</label>
                                            <input type="text" class="form-control" id="minorUnitName" required placeholder="مثال: جرام">
                                        </div>
                                        <div class="mb-3">
                                            <label for="minorUnitFactor" class="form-label">العدد في الوحدة الكبرى *</label>
                                            <input type="number" class="form-control" id="minorUnitFactor" required placeholder="مثال: 50000" onchange="updateExample()">
                                            <small class="text-muted">كم وحدة صغرى في الكبرى</small>
                                        </div>
                                    </div>
                                </div>

                                <div class="alert alert-success">
                                    <h6><i class="fas fa-info-circle me-2"></i>مثال على النظام:</h6>
                                    <div id="unitsExample">
                                        <span id="exampleText">أدخل البيانات لرؤية المثال</span>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Additional Information -->
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="description" class="form-label">الوصف</label>
                                    <textarea class="form-control" id="description" rows="2" placeholder="وصف مختصر للوحدة"></textarea>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="sortOrder" class="form-label">ترتيب العرض</label>
                                    <input type="number" class="form-control" id="sortOrder" value="1">
                                </div>
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="isActive" checked>
                                    <label class="form-check-label" for="isActive">
                                        وحدة نشطة
                                    </label>
                                </div>
                            </div>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                    <button type="button" class="btn btn-success" onclick="saveUnit()">
                        <i class="fas fa-save me-2"></i>حفظ الوحدة
                    </button>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="js/auth-simple.js"></script>
    <script src="js/units.js"></script>
</body>
</html>
