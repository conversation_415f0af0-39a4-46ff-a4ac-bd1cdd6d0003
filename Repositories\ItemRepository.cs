using Microsoft.EntityFrameworkCore;
using ANWBakerySystem.Data;
using ANWBakerySystem.Models;

namespace ANWBakerySystem.Repositories
{
    /// <summary>
    /// تطبيق مستودع المنتجات والأصناف
    /// Item Repository Implementation
    /// </summary>
    public class ItemRepository : IItemRepository
    {
        private readonly ANWBakeryDbContext _context;

        public ItemRepository(ANWBakeryDbContext context)
        {
            _context = context;
        }

        public async Task<IEnumerable<Item>> GetAllItemsAsync()
        {
            return await _context.Items
                .Include(i => i.Unit)
                .Include(i => i.Category)
                .Include(i => i.Creator)
                .Include(i => i.Updater)
                .OrderBy(i => i.ItemName)
                .ToListAsync();
        }

        public async Task<IEnumerable<Item>> GetActiveItemsAsync()
        {
            return await _context.Items
                .Include(i => i.Unit)
                .Include(i => i.Category)
                .Where(i => i.IsActive)
                .OrderBy(i => i.ItemName)
                .ToListAsync();
        }

        public async Task<IEnumerable<Item>> GetItemsByCategoryAsync(int categoryId)
        {
            return await _context.Items
                .Include(i => i.Unit)
                .Include(i => i.Category)
                .Where(i => i.CategoryId == categoryId && i.IsActive)
                .OrderBy(i => i.ItemName)
                .ToListAsync();
        }

        public async Task<IEnumerable<Item>> GetItemsByTypeAsync(ItemType itemType)
        {
            return await _context.Items
                .Include(i => i.Unit)
                .Include(i => i.Category)
                .Where(i => i.ItemType == itemType && i.IsActive)
                .OrderBy(i => i.ItemName)
                .ToListAsync();
        }

        public async Task<Item?> GetItemByIdAsync(int itemId)
        {
            return await _context.Items
                .Include(i => i.Unit)
                .Include(i => i.Category)
                .Include(i => i.Creator)
                .Include(i => i.Updater)
                .FirstOrDefaultAsync(i => i.ItemId == itemId);
        }

        public async Task<Item?> GetItemByCodeAsync(string itemCode)
        {
            return await _context.Items
                .Include(i => i.Unit)
                .Include(i => i.Category)
                .FirstOrDefaultAsync(i => i.ItemCode == itemCode);
        }

        public async Task<Item?> GetItemByBarcodeAsync(string barcode)
        {
            return await _context.Items
                .Include(i => i.Unit)
                .Include(i => i.Category)
                .FirstOrDefaultAsync(i => i.Barcode == barcode);
        }

        public async Task<Item> CreateItemAsync(Item item)
        {
            _context.Items.Add(item);
            await _context.SaveChangesAsync();
            return item;
        }

        public async Task<Item> UpdateItemAsync(Item item)
        {
            item.UpdatedAt = DateTime.Now;
            _context.Items.Update(item);
            await _context.SaveChangesAsync();
            return item;
        }

        public async Task<bool> DeleteItemAsync(int itemId)
        {
            var item = await _context.Items.FindAsync(itemId);
            if (item == null) return false;

            item.IsActive = false;
            item.UpdatedAt = DateTime.Now;
            await _context.SaveChangesAsync();
            return true;
        }

        public async Task<bool> ItemExistsAsync(int itemId)
        {
            return await _context.Items.AnyAsync(i => i.ItemId == itemId);
        }

        public async Task<bool> ItemCodeExistsAsync(string itemCode)
        {
            return await _context.Items.AnyAsync(i => i.ItemCode == itemCode);
        }

        public async Task<bool> BarcodeExistsAsync(string barcode)
        {
            return await _context.Items.AnyAsync(i => i.Barcode == barcode);
        }

        #region Price Management

        public async Task<decimal> GetItemPriceAsync(int itemId, UnitType unitType)
        {
            var item = await _context.Items.FindAsync(itemId);
            if (item == null) return 0;

            return item.GetPriceByUnitType(unitType);
        }

        public async Task<bool> UpdateItemPriceAsync(int itemId, UnitType unitType, decimal price)
        {
            var item = await _context.Items.FindAsync(itemId);
            if (item == null) return false;

            switch (unitType)
            {
                case UnitType.Large:
                    item.SalePriceLarge = price;
                    break;
                case UnitType.Medium:
                    item.SalePriceMedium = price;
                    break;
                case UnitType.Small:
                    item.SalePriceSmall = price;
                    break;
                default:
                    item.SalePrice = price;
                    break;
            }

            item.UpdatedAt = DateTime.Now;
            await _context.SaveChangesAsync();
            return true;
        }

        public async Task<bool> UpdateAllItemPricesAsync(int itemId, decimal purchasePrice, decimal salePrice, 
            decimal salePriceLarge, decimal salePriceMedium, decimal salePriceSmall)
        {
            var item = await _context.Items.FindAsync(itemId);
            if (item == null) return false;

            item.PurchasePrice = purchasePrice;
            item.SalePrice = salePrice;
            item.SalePriceLarge = salePriceLarge;
            item.SalePriceMedium = salePriceMedium;
            item.SalePriceSmall = salePriceSmall;
            item.UpdatedAt = DateTime.Now;

            await _context.SaveChangesAsync();
            return true;
        }

        #endregion

        #region Item Categories

        public async Task<IEnumerable<ItemCategory>> GetAllCategoriesAsync()
        {
            return await _context.ItemCategories
                .Include(c => c.ParentCategory)
                .Include(c => c.Creator)
                .Include(c => c.Updater)
                .OrderBy(c => c.CategoryName)
                .ToListAsync();
        }

        public async Task<IEnumerable<ItemCategory>> GetActiveCategoriesAsync()
        {
            return await _context.ItemCategories
                .Where(c => c.IsActive)
                .OrderBy(c => c.DisplayOrder)
                .ThenBy(c => c.CategoryName)
                .ToListAsync();
        }

        public async Task<IEnumerable<ItemCategory>> GetMainCategoriesAsync()
        {
            return await _context.ItemCategories
                .Where(c => c.ParentCategoryId == null && c.IsActive)
                .OrderBy(c => c.DisplayOrder)
                .ThenBy(c => c.CategoryName)
                .ToListAsync();
        }

        public async Task<IEnumerable<ItemCategory>> GetSubCategoriesAsync(int parentCategoryId)
        {
            return await _context.ItemCategories
                .Where(c => c.ParentCategoryId == parentCategoryId && c.IsActive)
                .OrderBy(c => c.DisplayOrder)
                .ThenBy(c => c.CategoryName)
                .ToListAsync();
        }

        public async Task<ItemCategory?> GetCategoryByIdAsync(int categoryId)
        {
            return await _context.ItemCategories
                .Include(c => c.ParentCategory)
                .Include(c => c.SubCategories)
                .Include(c => c.Items)
                .FirstOrDefaultAsync(c => c.CategoryId == categoryId);
        }

        public async Task<ItemCategory> CreateCategoryAsync(ItemCategory category)
        {
            _context.ItemCategories.Add(category);
            await _context.SaveChangesAsync();
            return category;
        }

        public async Task<ItemCategory> UpdateCategoryAsync(ItemCategory category)
        {
            category.UpdatedAt = DateTime.Now;
            _context.ItemCategories.Update(category);
            await _context.SaveChangesAsync();
            return category;
        }

        public async Task<bool> DeleteCategoryAsync(int categoryId)
        {
            var category = await _context.ItemCategories.FindAsync(categoryId);
            if (category == null) return false;

            category.IsActive = false;
            category.UpdatedAt = DateTime.Now;
            await _context.SaveChangesAsync();
            return true;
        }

        #endregion

        #region Search and Filter

        public async Task<IEnumerable<Item>> SearchItemsAsync(string searchTerm)
        {
            return await _context.Items
                .Include(i => i.Unit)
                .Include(i => i.Category)
                .Where(i => i.IsActive && 
                           (i.ItemName.Contains(searchTerm) || 
                            i.ItemCode.Contains(searchTerm) || 
                            (i.Barcode != null && i.Barcode.Contains(searchTerm))))
                .OrderBy(i => i.ItemName)
                .ToListAsync();
        }

        public async Task<IEnumerable<Item>> GetLowStockItemsAsync()
        {
            return await _context.Items
                .Include(i => i.Unit)
                .Include(i => i.Category)
                .Where(i => i.IsActive)
                .ToListAsync();
            // ملاحظة: يحتاج ربط مع جدول المخزون لحساب الكمية الفعلية
        }

        public async Task<IEnumerable<Item>> GetExpiredItemsAsync()
        {
            var today = DateTime.Today;
            return await _context.Items
                .Include(i => i.Unit)
                .Include(i => i.Category)
                .Where(i => i.IsActive && i.ShelfLifeDays.HasValue)
                .ToListAsync();
            // ملاحظة: يحتاج ربط مع جدول المخزون لحساب تاريخ الانتهاء
        }

        public async Task<IEnumerable<Item>> GetItemsNearExpiryAsync(int days)
        {
            var targetDate = DateTime.Today.AddDays(days);
            return await _context.Items
                .Include(i => i.Unit)
                .Include(i => i.Category)
                .Where(i => i.IsActive && i.ShelfLifeDays.HasValue)
                .ToListAsync();
            // ملاحظة: يحتاج ربط مع جدول المخزون لحساب تاريخ الانتهاء
        }

        #endregion
    }
}
