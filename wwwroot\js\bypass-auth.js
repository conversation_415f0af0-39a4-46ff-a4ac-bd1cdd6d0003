// تجاوز المصادقة مؤقتاً للاختبار
// Bypass authentication temporarily for testing

console.log('🔓 تجاوز المصادقة مؤقتاً للاختبار');

// إنشاء بيانات مستخدم وهمية
const fakeUser = {
    userId: 1,
    username: 'admin',
    fullName: 'مدير النظام',
    role: 'Admin'
};

const fakeToken = 'fake-token-for-testing';

// حفظ البيانات الوهمية
localStorage.setItem('anw_token', fakeToken);
localStorage.setItem('anw_user', JSON.stringify(fakeUser));

console.log('✅ تم إنشاء جلسة وهمية للاختبار');

// تحديث معلومات المستخدم في الواجهة
function updateUserInfo(userData) {
    // تحديث اسم المستخدم في الشريط العلوي
    const userNameElements = document.querySelectorAll('.user-name');
    userNameElements.forEach(element => {
        element.textContent = userData.fullName || userData.username;
    });
    
    // تحديث صورة المستخدم
    const userAvatarElements = document.querySelectorAll('.user-avatar');
    userAvatarElements.forEach(element => {
        element.textContent = (userData.fullName || userData.username).charAt(0).toUpperCase();
    });
}

// تحديث الواجهة
document.addEventListener('DOMContentLoaded', function() {
    updateUserInfo(fakeUser);
});

// تعطيل التحقق من المصادقة
window.checkAuth = function() {
    return true;
};

// تعطيل إعادة التوجيه
window.authenticatedFetch = function(url, options = {}) {
    return fetch(url, options);
};

console.log('🔓 تم تعطيل نظام المصادقة مؤقتاً');
