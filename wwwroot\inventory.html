<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إدارة المخزون - نظام إدارة مخبوزات ANW</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="css/style.css" rel="stylesheet">
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary">
        <div class="container-fluid">
            <a class="navbar-brand" href="dashboard.html">
                <i class="fas fa-bread-slice me-2"></i>
                نظام إدارة مخبوزات ANW
            </a>
            <div class="navbar-nav ms-auto">
                <a class="nav-link" href="dashboard.html">
                    <i class="fas fa-home"></i> الرئيسية
                </a>
                <a class="nav-link" href="#" onclick="logout()">
                    <i class="fas fa-sign-out-alt"></i> تسجيل الخروج
                </a>
            </div>
        </div>
    </nav>

    <div class="container-fluid mt-4">
        <div class="row">
            <!-- Sidebar -->
            <div class="col-md-3">
                <div class="sidebar-container">
                    <div class="card">
                        <div class="card-header">
                            <h5><i class="fas fa-list"></i> القوائم</h5>
                        </div>
                        <div class="list-group list-group-flush">
                            <a href="dashboard.html" class="list-group-item list-group-item-action">
                                <i class="fas fa-tachometer-alt"></i> لوحة التحكم
                            </a>
                            <a href="accounts.html" class="list-group-item list-group-item-action">
                                <i class="fas fa-sitemap"></i> شجرة الحسابات
                            </a>
                            <a href="banks.html" class="list-group-item list-group-item-action">
                                <i class="fas fa-university"></i> البنوك والصناديق
                            </a>
                            <a href="employees.html" class="list-group-item list-group-item-action">
                                <i class="fas fa-users"></i> الموظفين
                            </a>
                            <a href="journal.html" class="list-group-item list-group-item-action">
                                <i class="fas fa-book"></i> القيود المحاسبية
                            </a>
                            <a href="reports.html" class="list-group-item list-group-item-action">
                                <i class="fas fa-chart-bar"></i> التقارير المالية
                            </a>
                            <a href="units.html" class="list-group-item list-group-item-action">
                                <i class="fas fa-balance-scale"></i> وحدات القياس
                            </a>
                            <a href="parties.html" class="list-group-item list-group-item-action">
                                <i class="fas fa-handshake"></i> العملاء والموردين
                            </a>
                            <a href="items-management.html" class="list-group-item list-group-item-action">
                                <i class="fas fa-boxes"></i> الأصناف والمنتجات
                            </a>
                            <a href="invoices.html" class="list-group-item list-group-item-action">
                                <i class="fas fa-file-invoice"></i> الفواتير
                            </a>
                            <a href="inventory.html" class="list-group-item list-group-item-action active">
                                <i class="fas fa-warehouse"></i> المخزون
                            </a>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Main Content -->
            <div class="col-md-9">
                <!-- Inventory Statistics -->
                <div class="row mb-4">
                    <div class="col-md-3">
                        <div class="card bg-primary text-white">
                            <div class="card-body">
                                <div class="d-flex justify-content-between">
                                    <div>
                                        <h6>إجمالي الأصناف</h6>
                                        <h3 id="totalItems">0</h3>
                                    </div>
                                    <div class="align-self-center">
                                        <i class="fas fa-boxes fa-2x"></i>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="card bg-success text-white">
                            <div class="card-body">
                                <div class="d-flex justify-content-between">
                                    <div>
                                        <h6>قيمة المخزون</h6>
                                        <h3 id="inventoryValue">0 ر.ي</h3>
                                    </div>
                                    <div class="align-self-center">
                                        <i class="fas fa-dollar-sign fa-2x"></i>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="card bg-warning text-white">
                            <div class="card-body">
                                <div class="d-flex justify-content-between">
                                    <div>
                                        <h6>مخزون منخفض</h6>
                                        <h3 id="lowStockItems">0</h3>
                                    </div>
                                    <div class="align-self-center">
                                        <i class="fas fa-exclamation-triangle fa-2x"></i>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="card bg-danger text-white">
                            <div class="card-body">
                                <div class="d-flex justify-content-between">
                                    <div>
                                        <h6>مخزون منتهي</h6>
                                        <h3 id="outOfStockItems">0</h3>
                                    </div>
                                    <div class="align-self-center">
                                        <i class="fas fa-times-circle fa-2x"></i>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Inventory Management -->
                <div class="card">
                    <div class="card-header d-flex justify-content-between align-items-center">
                        <h4><i class="fas fa-warehouse"></i> إدارة المخزون</h4>
                        <div>
                            <button class="btn btn-success" onclick="showStockAdjustmentModal()">
                                <i class="fas fa-edit"></i> تعديل مخزون
                            </button>
                            <button class="btn btn-warning" onclick="showWasteModal()">
                                <i class="fas fa-trash"></i> تسجيل تالف
                            </button>
                            <button class="btn btn-info" onclick="generateInventoryReport()">
                                <i class="fas fa-file-alt"></i> تقرير المخزون
                            </button>
                        </div>
                    </div>
                    <div class="card-body">
                        <!-- Filters -->
                        <div class="row mb-3">
                            <div class="col-md-3">
                                <select class="form-select" id="categoryFilter" onchange="filterInventory()">
                                    <option value="">جميع الفئات</option>
                                    <option value="raw_materials">مواد خام</option>
                                    <option value="products">منتجات</option>
                                    <option value="packaging">مواد تعبئة</option>
                                </select>
                            </div>
                            <div class="col-md-3">
                                <select class="form-select" id="stockStatusFilter" onchange="filterInventory()">
                                    <option value="">جميع الحالات</option>
                                    <option value="in_stock">متوفر</option>
                                    <option value="low_stock">مخزون منخفض</option>
                                    <option value="out_of_stock">غير متوفر</option>
                                </select>
                            </div>
                            <div class="col-md-4">
                                <input type="text" class="form-control" id="searchInventory" 
                                       placeholder="البحث في المخزون..." onkeyup="filterInventory()">
                            </div>
                            <div class="col-md-2">
                                <button class="btn btn-primary w-100" onclick="loadInventory()">
                                    <i class="fas fa-sync"></i> تحديث
                                </button>
                            </div>
                        </div>

                        <!-- Inventory Table -->
                        <div class="table-responsive">
                            <table class="table table-hover">
                                <thead class="table-light">
                                    <tr>
                                        <th>الصنف</th>
                                        <th>الفئة</th>
                                        <th>الكمية الحالية</th>
                                        <th>الوحدة</th>
                                        <th>الحد الأدنى</th>
                                        <th>متوسط التكلفة</th>
                                        <th>القيمة الإجمالية</th>
                                        <th>الحالة</th>
                                        <th>إجراءات</th>
                                    </tr>
                                </thead>
                                <tbody id="inventoryTableBody">
                                    <!-- Inventory items will be loaded here -->
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Stock Adjustment Modal -->
    <div class="modal fade" id="stockAdjustmentModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">تعديل المخزون</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <form id="stockAdjustmentForm">
                        <div class="mb-3">
                            <label for="adjustmentItem" class="form-label">الصنف *</label>
                            <select class="form-select" id="adjustmentItem" required>
                                <option value="">-- اختر الصنف --</option>
                            </select>
                        </div>
                        <div class="mb-3">
                            <label for="adjustmentType" class="form-label">نوع التعديل *</label>
                            <select class="form-select" id="adjustmentType" required>
                                <option value="">-- اختر النوع --</option>
                                <option value="increase">زيادة</option>
                                <option value="decrease">نقص</option>
                                <option value="set">تعيين كمية محددة</option>
                            </select>
                        </div>
                        <div class="mb-3">
                            <label for="adjustmentQuantity" class="form-label">الكمية *</label>
                            <input type="number" class="form-control" id="adjustmentQuantity" 
                                   step="0.001" min="0" required>
                        </div>
                        <div class="mb-3">
                            <label for="adjustmentReason" class="form-label">السبب *</label>
                            <textarea class="form-control" id="adjustmentReason" rows="3" required
                                      placeholder="اكتب سبب التعديل..."></textarea>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                    <button type="button" class="btn btn-success" onclick="saveStockAdjustment()">حفظ التعديل</button>
                </div>
            </div>
        </div>
    </div>

    <!-- Waste Modal -->
    <div class="modal fade" id="wasteModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">تسجيل تالف</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <form id="wasteForm">
                        <div class="mb-3">
                            <label for="wasteItem" class="form-label">الصنف *</label>
                            <select class="form-select" id="wasteItem" required>
                                <option value="">-- اختر الصنف --</option>
                            </select>
                        </div>
                        <div class="mb-3">
                            <label for="wasteQuantity" class="form-label">الكمية التالفة *</label>
                            <input type="number" class="form-control" id="wasteQuantity" 
                                   step="0.001" min="0" required>
                        </div>
                        <div class="mb-3">
                            <label for="wasteReason" class="form-label">سبب التلف *</label>
                            <select class="form-select" id="wasteReason" required>
                                <option value="">-- اختر السبب --</option>
                                <option value="expired">انتهاء الصلاحية</option>
                                <option value="damaged">تلف أثناء التخزين</option>
                                <option value="production_error">خطأ في الإنتاج</option>
                                <option value="other">أخرى</option>
                            </select>
                        </div>
                        <div class="mb-3">
                            <label for="wasteNotes" class="form-label">ملاحظات</label>
                            <textarea class="form-control" id="wasteNotes" rows="3"
                                      placeholder="ملاحظات إضافية..."></textarea>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                    <button type="button" class="btn btn-danger" onclick="saveWasteRecord()">تسجيل التالف</button>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="js/auth-simple.js"></script>
    <script src="js/inventory.js"></script>
</body>
</html>
