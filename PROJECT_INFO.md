# 📋 معلومات نظام إدارة مخبوزات ANW

## 🏗️ **هيكل المشروع**
- **اسم المشروع:** ANW Bakery Management System
- **مسار المشروع:** `C:\ANW_bakery`
- **نوع المشروع:** ASP.NET Core 6.0 + SQL Server 2014
- **اللغة:** C# (Backend) + HTML/CSS/JavaScript (Frontend)
- **التوجه:** RTL (Right-to-Left) للغة العربية

## 📁 **هيكل الملفات**
```
C:\ANW_bakery/
├── Models/                 # نماذج قاعدة البيانات
├── Data/                   # DbContext وإعدادات قاعدة البيانات
├── Repositories/           # طبقة الوصول للبيانات
├── Controllers/            # تحكم API
├── wwwroot/               # ملفات الواجهة الأمامية
│   ├── css/               # ملفات التنسيق
│   ├── js/                # ملفات JavaScript
│   ├── *.html             # صفحات HTML
│   └── index.html         # صفحة تسجيل الدخول
├── Program.cs             # نقطة البداية
└── ANWBakerySystem.csproj # ملف المشروع
```

## 🚀 **أوامر التشغيل**
```bash
# الانتقال لمجلد المشروع
cd C:\ANW_bakery

# استعادة الحزم
dotnet restore

# بناء المشروع
dotnet build

# تشغيل التطبيق
dotnet run --urls "http://localhost:5000"
```

## 🔐 **معلومات المصادقة**
- **نوع المصادقة:** مبسطة (تم تجاوز فحص المصادقة)
- **اسم المستخدم:** admin
- **كلمة المرور:** admin123
- **الدخول المباشر:** متاح عبر رابط "دخول مباشر للنظام"

## 💰 **إعدادات العملة**
- **العملة الوحيدة:** الريال اليمني (ر.ي)
- **رمز العملة:** ر.ي
- **الخانات العشرية:** 3 خانات
- **تنسيق الأرقام:** النظام العربي اليمني

## 📏 **نظام وحدات القياس**
- **نظام هرمي:** وحدة كبرى → متوسطة → صغرى
- **مثال:** كيس (كبرى) → 50 كيلو (متوسطة) → 50000 جرام (صغرى)
- **الغرض:** حسابات التكلفة الدقيقة

## 📊 **الصفحات المكتملة والجاهزة**

### ✅ **dashboard.html - لوحة التحكم**
- **الحالة:** مكتملة وتعمل
- **المميزات:** 
  - إحصائيات مباشرة (فواتير، مبيعات، مشتريات، مخزون منخفض)
  - عمليات سريعة
  - أنشطة حديثة
  - شريط جانبي مع جميع الروابط

### ✅ **journal.html - القيود المحاسبية**
- **الحالة:** مكتملة وتعمل
- **المميزات:**
  - 12 حساب محاسبي تجريبي
  - 7 أنواع قيود (يدوي، مبيعات، مشتريات، رواتب، تالف، إهلاك، تسوية)
  - إضافة أسطر ديناميكية
  - التحقق من توازن القيد تلقائياً
  - 3 قيود تجريبية جاهزة

### ✅ **inventory.html - إدارة المخزون**
- **الحالة:** مكتملة وتعمل
- **المميزات:**
  - 6 أصناف تجريبية (مواد خام، منتجات، مواد تعبئة)
  - إحصائيات مباشرة (إجمالي أصناف، قيمة مخزون، مخزون منخفض/منتهي)
  - تعديل المخزون (زيادة، نقص، تعيين كمية)
  - تسجيل التالف مع أسباب مختلفة
  - تصفية متقدمة (فئة، حالة، بحث)
  - تتبع حالة المخزون تلقائياً

### ✅ **recipes.html - إدارة الوصفات**
- **الحالة:** مكتملة وتعمل
- **المميزات:**
  - 4 وصفات تجريبية (خبز، كيك، معجنات، بسكويت)
  - إدارة المكونات مع حساب التكلفة التلقائي
  - تصفية حسب الفئة والبحث
  - إحصائيات شاملة (إجمالي وصفات، متوسط تكلفة، إجمالي مكونات)
  - إضافة وصفات جديدة مع مكونات متعددة

### ✅ **settings.html - إعدادات النظام**
- **الحالة:** مكتملة وتعمل
- **المميزات:**
  - الإعدادات العامة (اسم الشركة، العنوان، هاتف، إيميل)
  - إعدادات العملة (الريال اليمني فقط، خانات عشرية)
  - إعدادات النظام (لغة، تاريخ، إشعارات، نسخ احتياطي)
  - إعدادات قاعدة البيانات (نسخ احتياطي، تحسين، تصغير، إعادة تعيين)
  - إعدادات الطباعة (طابعة افتراضية، حجم ورق، شعار، باركود)
  - إعدادات الأمان (انتهاء جلسة، سجل عمليات، تغيير كلمة مرور)

## 📄 **الصفحات الأخرى (موجودة لكن تحتاج تطوير)**
- accounts.html - شجرة الحسابات
- banks.html - البنوك والصناديق  
- employees.html - الموظفين
- reports.html - التقارير المالية
- units.html - وحدات القياس
- parties.html - العملاء والموردين
- items.html - المنتجات والخامات
- production.html - الإنتاج
- invoices.html - الفواتير

## 🗄️ **قاعدة البيانات**
- **نوع قاعدة البيانات:** SQL Server 2014
- **DbContext:** ANWBakeryDbContext.cs
- **الجداول الرئيسية:**
  - Users, Permissions, UserPermissions
  - Units, UnitConversions
  - Parties, PartyTransactions
  - Items, ItemCategories
  - Inventories, InventoryMovements, Warehouses
  - Invoices, InvoiceItems
  - Accounts, Banks

## 🎯 **المتطلبات الأساسية للنظام**
1. **نظام محاسبي شامل:** قيود، حسابات، تقارير مالية
2. **إدارة مخزون متقدمة:** تتبع كميات، تكلفة، حركات
3. **نظام فوترة:** مبيعات، مشتريات، مرتجعات
4. **إدارة الإنتاج:** وصفات، تكلفة إنتاج، هامش ربح
5. **إدارة العملاء والموردين:** حسابات جارية، معاملات
6. **تقارير شاملة:** مالية، مخزون، مبيعات، مشتريات
7. **دعم الباركود:** للمنتجات والمواد
8. **طباعة حرارية وعادية:** فواتير، تقارير

## 🔧 **الإعدادات التقنية**
- **المنفذ:** http://localhost:5000
- **اللغة:** العربية RTL
- **إطار العمل:** Bootstrap 5.3.0
- **الأيقونات:** Font Awesome 6.0.0
- **المتصفحات المدعومة:** جميع المتصفحات الحديثة

## 🎉 **تحديث: المرحلة الأولى مكتملة!**

### ✅ **Controllers مكتملة (7/7)**
- ✅ ItemsController - إدارة المنتجات والخامات
- ✅ PartiesController - إدارة العملاء والموردين
- ✅ InvoicesController - نظام الفوترة الموحد
- ✅ InventoryController - إدارة المخزون المتقدمة
- ✅ ProductionController - إدارة الإنتاج والوصفات
- ✅ AccountsController - دليل الحسابات والمحاسبة
- ✅ BanksController - إدارة البنوك والصناديق

### ✅ **النماذج مكتملة (20+ Models)**
- ✅ جميع النماذج الأساسية موجودة ومحدثة
- ✅ النماذج الجديدة: Warehouse, JournalEntry, BankAccount, CashRegister
- ✅ العلاقات والفهارس محدثة في DbContext

### ✅ **APIs جاهزة (35+ Endpoints)**
- ✅ جميع العمليات CRUD مكتملة
- ✅ معالجة الأخطاء المتقدمة
- ✅ التحقق من صحة البيانات
- ✅ دعم الفلترة والبحث والترقيم

### ✅ **Repositories مكتملة (6/6)**
- ✅ جميع Repositories مسجلة في Program.cs
- ✅ دعم العمليات المتقدمة
- ✅ معالجة المعاملات

## 📝 **ملاحظات للمرحلة التالية**
1. **ربط الواجهة الأمامية** بالـ APIs الجديدة
2. **اختبار شامل** لجميع الوظائف
3. **تطوير التقارير** المتقدمة
4. **الطباعة والتصدير**
5. **النسخ الاحتياطي** والاستعادة

## 🚨 **مشاكل تم حلها**
- ✅ Controllers المفقودة (تم إنشاؤها)
- ✅ النماذج المفقودة (تم إنشاؤها)
- ✅ Repositories غير مسجلة (تم تسجيلها)
- ✅ DTOs غير متطابقة (تم تحديثها)
- ✅ CORS للواجهة الأمامية (تم إضافته)

## 📞 **للدعم والتطوير**
- **المطور:** Augment Agent
- **آخر تحديث:** ديسمبر 2024
- **حالة المشروع:** قيد التطوير النشط
- **الإصدار:** 1.0.0 Beta

---
**ملاحظة:** هذا الملف يحتوي على جميع المعلومات المطلوبة للعمل على المشروع بكفاءة ومهنية عالية.
