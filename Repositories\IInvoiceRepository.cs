using ANWBakerySystem.Models;

namespace ANWBakerySystem.Repositories
{
    /// <summary>
    /// واجهة مستودع الفوترة الموحدة
    /// Unified Invoice Repository Interface
    /// </summary>
    public interface IInvoiceRepository
    {
        Task<IEnumerable<Invoice>> GetAllInvoicesAsync();
        Task<IEnumerable<Invoice>> GetInvoicesByTypeAsync(InvoiceType invoiceType);
        Task<IEnumerable<Invoice>> GetInvoicesByPartyAsync(int partyId);
        Task<IEnumerable<Invoice>> GetInvoicesByDateRangeAsync(DateTime fromDate, DateTime toDate);
        Task<IEnumerable<Invoice>> GetInvoicesByStatusAsync(InvoiceStatus status);
        Task<Invoice?> GetInvoiceByIdAsync(int invoiceId);
        Task<Invoice?> GetInvoiceByNumberAsync(string invoiceNumber);
        Task<Invoice> CreateInvoiceAsync(Invoice invoice);
        Task<Invoice> UpdateInvoiceAsync(Invoice invoice);
        Task<bool> DeleteInvoiceAsync(int invoiceId);
        Task<bool> InvoiceExistsAsync(int invoiceId);
        Task<bool> InvoiceNumberExistsAsync(string invoiceNumber);
        
        // أصناف الفواتير
        // Invoice Items
        Task<IEnumerable<InvoiceItem>> GetInvoiceItemsAsync(int invoiceId);
        Task<InvoiceItem> CreateInvoiceItemAsync(InvoiceItem invoiceItem);
        Task<InvoiceItem> UpdateInvoiceItemAsync(InvoiceItem invoiceItem);
        Task<bool> DeleteInvoiceItemAsync(int invoiceItemId);
        
        // عمليات الفوترة
        // Invoice Operations
        Task<bool> ApproveInvoiceAsync(int invoiceId, int userId);
        Task<bool> CancelInvoiceAsync(int invoiceId, int userId, string reason);
        Task<bool> PayInvoiceAsync(int invoiceId, decimal paidAmount, int userId);
        Task<bool> MarkInvoiceAsPrintedAsync(int invoiceId);
        Task<string> GenerateInvoiceNumberAsync(InvoiceType invoiceType);
        
        // حسابات الفوترة
        // Invoice Calculations
        Task<bool> RecalculateInvoiceTotalsAsync(int invoiceId);
        Task<decimal> GetInvoiceSubTotalAsync(int invoiceId);
        Task<decimal> GetInvoiceTotalAsync(int invoiceId);
        Task<decimal> GetInvoiceRemainingAmountAsync(int invoiceId);
        
        // تقارير الفوترة
        // Invoice Reports
        Task<decimal> GetSalesTotalAsync(DateTime fromDate, DateTime toDate);
        Task<decimal> GetPurchasesTotalAsync(DateTime fromDate, DateTime toDate);
        Task<IEnumerable<Invoice>> GetOverdueInvoicesAsync();
        Task<IEnumerable<Invoice>> GetUnpaidInvoicesAsync();
        Task<IEnumerable<Invoice>> GetQuickSalesAsync(DateTime date);
        
        // البيع السريع
        // Quick Sale
        Task<Invoice> CreateQuickSaleAsync(Invoice quickSale);
        Task<IEnumerable<Invoice>> GetTodayQuickSalesAsync();
        Task<decimal> GetQuickSalesTotalAsync(DateTime date);
    }
}
