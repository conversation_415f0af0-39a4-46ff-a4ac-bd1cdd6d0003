using Microsoft.AspNetCore.Mvc;

namespace ANWBakerySystem.Controllers
{
    [ApiController]
    [Route("api/[controller]")]
    public class HomeController : ControllerBase
    {
        [HttpGet]
        public IActionResult Get()
        {
            return Ok(new { 
                message = "مرحباً بك في نظام إدارة مخبوزات ANW",
                messageEn = "Welcome to ANW Bakery Management System",
                version = "1.0.0",
                status = "Running"
            });
        }

        [HttpGet("health")]
        public IActionResult Health()
        {
            return Ok(new { 
                status = "Healthy",
                timestamp = DateTime.Now,
                system = "ANW Bakery Management System"
            });
        }
    }
}
