<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>نظام ANW - إدارة مخبوزات متكامل</title>
    
    <!-- Bootstrap CSS (RTL) -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css" rel="stylesheet">
    
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    
    <!-- Google Fonts - Arabic -->
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
    
    <style>
        body {
            font-family: 'Cairo', sans-serif;
            direction: rtl;
            overflow-x: hidden;
        }
        
        .hero-section {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            color: white;
            position: relative;
        }
        
        .hero-section::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1000 1000"><polygon fill="rgba(255,255,255,0.05)" points="0,1000 1000,0 1000,1000"/></svg>');
            background-size: cover;
        }
        
        .hero-content {
            position: relative;
            z-index: 2;
        }
        
        .hero-title {
            font-size: 4rem;
            font-weight: 700;
            margin-bottom: 20px;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }
        
        .hero-subtitle {
            font-size: 1.5rem;
            margin-bottom: 30px;
            opacity: 0.9;
        }
        
        .hero-description {
            font-size: 1.2rem;
            margin-bottom: 40px;
            line-height: 1.8;
        }
        
        .btn-hero {
            background: rgba(255,255,255,0.2);
            border: 2px solid white;
            color: white;
            padding: 15px 40px;
            font-size: 1.2rem;
            font-weight: 600;
            border-radius: 50px;
            margin: 10px;
            transition: all 0.3s ease;
            backdrop-filter: blur(10px);
        }
        
        .btn-hero:hover {
            background: white;
            color: #667eea;
            transform: translateY(-3px);
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
        }
        
        .btn-hero.primary {
            background: white;
            color: #667eea;
        }
        
        .btn-hero.primary:hover {
            background: rgba(255,255,255,0.9);
            color: #5a6fd8;
        }
        
        .features-section {
            padding: 100px 0;
            background: #f8f9fa;
        }
        
        .feature-card {
            background: white;
            border-radius: 20px;
            padding: 40px 30px;
            text-align: center;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            transition: all 0.3s ease;
            height: 100%;
        }
        
        .feature-card:hover {
            transform: translateY(-10px);
            box-shadow: 0 20px 50px rgba(0,0,0,0.15);
        }
        
        .feature-icon {
            width: 80px;
            height: 80px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 2rem;
            color: white;
            margin: 0 auto 25px;
        }
        
        .feature-title {
            font-size: 1.5rem;
            font-weight: 600;
            margin-bottom: 15px;
            color: #2c3e50;
        }
        
        .feature-description {
            color: #7f8c8d;
            line-height: 1.6;
        }
        
        .stats-section {
            background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);
            color: white;
            padding: 80px 0;
        }
        
        .stat-item {
            text-align: center;
            margin-bottom: 30px;
        }
        
        .stat-number {
            font-size: 3rem;
            font-weight: 700;
            margin-bottom: 10px;
        }
        
        .stat-label {
            font-size: 1.1rem;
            opacity: 0.9;
        }
        
        .footer {
            background: #2c3e50;
            color: white;
            padding: 50px 0 30px;
        }
        
        .developer-section {
            background: #34495e;
            padding: 30px 0;
            text-align: center;
        }
        
        .developer-info {
            font-size: 1.1rem;
        }
        
        .developer-name {
            color: #3498db;
            font-weight: 600;
        }
        
        .developer-phone {
            color: #e74c3c;
            font-weight: 600;
        }
        
        @media (max-width: 768px) {
            .hero-title {
                font-size: 2.5rem;
            }
            
            .hero-subtitle {
                font-size: 1.2rem;
            }
            
            .hero-description {
                font-size: 1rem;
            }
        }
    </style>
</head>
<body>
    <!-- Hero Section -->
    <section class="hero-section">
        <div class="container">
            <div class="row align-items-center">
                <div class="col-lg-6">
                    <div class="hero-content">
                        <h1 class="hero-title">
                            <i class="fas fa-bread-slice me-3"></i>
                            نظام ANW
                        </h1>
                        <h2 class="hero-subtitle">إدارة مخبوزات متكامل</h2>
                        <p class="hero-description">
                            نظام شامل لإدارة المخابز والمطاعم بالريال اليمني، يشمل إدارة المبيعات والمشتريات والمخزون والموظفين والمحاسبة بطريقة سهلة ومتقدمة.
                        </p>
                        <div class="hero-buttons">
                            <a href="login.html" class="btn btn-hero primary">
                                <i class="fas fa-sign-in-alt me-2"></i>
                                دخول النظام
                            </a>
                            <a href="dashboard.html" class="btn btn-hero">
                                <i class="fas fa-eye me-2"></i>
                                معاينة النظام
                            </a>
                        </div>
                    </div>
                </div>
                <div class="col-lg-6">
                    <div class="text-center">
                        <i class="fas fa-store" style="font-size: 15rem; opacity: 0.3;"></i>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Features Section -->
    <section class="features-section">
        <div class="container">
            <div class="row mb-5">
                <div class="col-12 text-center">
                    <h2 class="display-4 fw-bold text-dark mb-3">مميزات النظام</h2>
                    <p class="lead text-muted">نظام متكامل يلبي جميع احتياجات إدارة المخابز والمطاعم</p>
                </div>
            </div>
            
            <div class="row g-4">
                <div class="col-lg-4 col-md-6">
                    <div class="feature-card">
                        <div class="feature-icon" style="background: linear-gradient(135deg, #3498db 0%, #2980b9 100%);">
                            <i class="fas fa-file-invoice"></i>
                        </div>
                        <h3 class="feature-title">إدارة الفواتير</h3>
                        <p class="feature-description">
                            نظام فواتير متقدم يدعم المبيعات والمشتريات والمرتجعات مع طباعة حرارية واعتيادية
                        </p>
                    </div>
                </div>
                
                <div class="col-lg-4 col-md-6">
                    <div class="feature-card">
                        <div class="feature-icon" style="background: linear-gradient(135deg, #27ae60 0%, #229954 100%);">
                            <i class="fas fa-warehouse"></i>
                        </div>
                        <h3 class="feature-title">إدارة المخزون</h3>
                        <p class="feature-description">
                            تتبع دقيق للمخزون مع وحدات قياس هرمية وتنبيهات الحد الأدنى وإدارة الإنتاج
                        </p>
                    </div>
                </div>
                
                <div class="col-lg-4 col-md-6">
                    <div class="feature-card">
                        <div class="feature-icon" style="background: linear-gradient(135deg, #e74c3c 0%, #c0392b 100%);">
                            <i class="fas fa-calculator"></i>
                        </div>
                        <h3 class="feature-title">المحاسبة المتقدمة</h3>
                        <p class="feature-description">
                            نظام محاسبي شامل بالريال اليمني مع إدارة الصناديق والبنوك والقيود التلقائية
                        </p>
                    </div>
                </div>
                
                <div class="col-lg-4 col-md-6">
                    <div class="feature-card">
                        <div class="feature-icon" style="background: linear-gradient(135deg, #9b59b6 0%, #8e44ad 100%);">
                            <i class="fas fa-users"></i>
                        </div>
                        <h3 class="feature-title">إدارة الموظفين</h3>
                        <p class="feature-description">
                            نظام رواتب متكامل مع استحقاقات شهرية وسندات صرف وتتبع الأرصدة
                        </p>
                    </div>
                </div>
                
                <div class="col-lg-4 col-md-6">
                    <div class="feature-card">
                        <div class="feature-icon" style="background: linear-gradient(135deg, #f39c12 0%, #e67e22 100%);">
                            <i class="fas fa-chart-bar"></i>
                        </div>
                        <h3 class="feature-title">التقارير الذكية</h3>
                        <p class="feature-description">
                            تقارير شاملة ومفصلة مع إمكانية التصدير والطباعة لجميع العمليات
                        </p>
                    </div>
                </div>
                
                <div class="col-lg-4 col-md-6">
                    <div class="feature-card">
                        <div class="feature-icon" style="background: linear-gradient(135deg, #1abc9c 0%, #16a085 100%);">
                            <i class="fas fa-mobile-alt"></i>
                        </div>
                        <h3 class="feature-title">متوافق مع الأجهزة</h3>
                        <p class="feature-description">
                            يعمل على جميع الأجهزة والمتصفحات مع واجهة سهلة ومتجاوبة
                        </p>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Stats Section -->
    <section class="stats-section">
        <div class="container">
            <div class="row">
                <div class="col-lg-3 col-md-6">
                    <div class="stat-item">
                        <div class="stat-number">100%</div>
                        <div class="stat-label">باللغة العربية</div>
                    </div>
                </div>
                <div class="col-lg-3 col-md-6">
                    <div class="stat-item">
                        <div class="stat-number">24/7</div>
                        <div class="stat-label">متاح دائماً</div>
                    </div>
                </div>
                <div class="col-lg-3 col-md-6">
                    <div class="stat-item">
                        <div class="stat-number">ر.ي</div>
                        <div class="stat-label">بالريال اليمني</div>
                    </div>
                </div>
                <div class="col-lg-3 col-md-6">
                    <div class="stat-item">
                        <div class="stat-number">∞</div>
                        <div class="stat-label">إمكانيات لا محدودة</div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Footer -->
    <footer class="footer">
        <div class="container">
            <div class="row">
                <div class="col-lg-6">
                    <h5>نظام ANW</h5>
                    <p>نظام إدارة مخبوزات متكامل مصمم خصيصاً للسوق اليمني بالريال اليمني</p>
                </div>
                <div class="col-lg-6 text-end">
                    <h5>روابط سريعة</h5>
                    <ul class="list-unstyled">
                        <li><a href="login.html" class="text-light">تسجيل الدخول</a></li>
                        <li><a href="dashboard.html" class="text-light">لوحة التحكم</a></li>
                    </ul>
                </div>
            </div>
        </div>
    </footer>

    <!-- Developer Section -->
    <section class="developer-section">
        <div class="container">
            <div class="developer-info">
                <i class="fas fa-code text-primary me-2"></i>
                <span>تصميم وتطوير: </span>
                <span class="developer-name">أنور القدمي</span>
                <span class="mx-3">|</span>
                <i class="fas fa-phone text-success me-2"></i>
                <span class="developer-phone">771978523</span>
            </div>
        </div>
    </section>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    
    <script>
        // تأثيرات التحريك
        document.addEventListener('DOMContentLoaded', function() {
            // تحريك الأرقام في قسم الإحصائيات
            const observerOptions = {
                threshold: 0.5
            };
            
            const observer = new IntersectionObserver(function(entries) {
                entries.forEach(entry => {
                    if (entry.isIntersecting) {
                        entry.target.style.opacity = '1';
                        entry.target.style.transform = 'translateY(0)';
                    }
                });
            }, observerOptions);
            
            // مراقبة عناصر البطاقات
            document.querySelectorAll('.feature-card').forEach(card => {
                card.style.opacity = '0';
                card.style.transform = 'translateY(30px)';
                card.style.transition = 'all 0.6s ease';
                observer.observe(card);
            });
        });
    </script>
</body>
</html>
