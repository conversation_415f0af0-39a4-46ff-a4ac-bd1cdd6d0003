<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إدارة الفواتير - نظام إدارة مخبوزات ANW</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="css/style.css" rel="stylesheet">
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary">
        <div class="container-fluid">
            <a class="navbar-brand" href="dashboard.html">
                <i class="fas fa-bread-slice me-2"></i>
                نظام إدارة مخبوزات ANW
            </a>
            <div class="navbar-nav ms-auto">
                <a class="nav-link" href="dashboard.html">
                    <i class="fas fa-home"></i> الرئيسية
                </a>
                <a class="nav-link" href="#" onclick="logout()">
                    <i class="fas fa-sign-out-alt"></i> تسجيل الخروج
                </a>
            </div>
        </div>
    </nav>

    <div class="container-fluid mt-4">
        <div class="row">
            <!-- Sidebar -->
            <div class="col-md-3">
                <div class="card">
                    <div class="card-header">
                        <h5><i class="fas fa-list"></i> القوائم</h5>
                    </div>
                    <div class="list-group list-group-flush">
                        <a href="dashboard.html" class="list-group-item list-group-item-action">
                            <i class="fas fa-tachometer-alt"></i> لوحة التحكم
                        </a>
                        <a href="units.html" class="list-group-item list-group-item-action">
                            <i class="fas fa-balance-scale"></i> وحدات القياس
                        </a>
                        <a href="parties.html" class="list-group-item list-group-item-action">
                            <i class="fas fa-users"></i> العملاء والموردين
                        </a>
                        <a href="items-management.html" class="list-group-item list-group-item-action">
                            <i class="fas fa-boxes"></i> الأصناف والمنتجات
                        </a>
                        <a href="invoices.html" class="list-group-item list-group-item-action active">
                            <i class="fas fa-file-invoice"></i> الفواتير
                        </a>
                    </div>
                </div>
            </div>

            <!-- Main Content -->
            <div class="col-md-9">
                <!-- Invoice Type Selection -->
                <div class="card mb-4">
                    <div class="card-header">
                        <h4><i class="fas fa-file-invoice"></i> إنشاء فاتورة جديدة</h4>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-3">
                                <button class="btn btn-success w-100 mb-2" onclick="createInvoice('sale')">
                                    <i class="fas fa-shopping-cart"></i><br>
                                    فاتورة مبيعات
                                </button>
                            </div>
                            <div class="col-md-3">
                                <button class="btn btn-primary w-100 mb-2" onclick="createInvoice('purchase')">
                                    <i class="fas fa-shopping-bag"></i><br>
                                    فاتورة مشتريات
                                </button>
                            </div>
                            <div class="col-md-2">
                                <button class="btn btn-warning w-100 mb-2" onclick="createInvoice('sale_return')">
                                    <i class="fas fa-undo"></i><br>
                                    مرتجع مبيعات
                                </button>
                            </div>
                            <div class="col-md-2">
                                <button class="btn btn-danger w-100 mb-2" onclick="createInvoice('purchase_return')">
                                    <i class="fas fa-reply"></i><br>
                                    مرتجع مشتريات
                                </button>
                            </div>
                            <div class="col-md-3">
                                <button class="btn btn-info w-100 mb-2" onclick="createInvoice('quick_sale')">
                                    <i class="fas fa-bolt"></i><br>
                                    بيع سريع (نقدي)
                                </button>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Invoice Form -->
                <div class="card mb-4" id="invoiceFormCard" style="display: none;">
                    <div class="card-header d-flex justify-content-between align-items-center">
                        <h5 id="invoiceTitle">فاتورة جديدة</h5>
                        <div>
                            <button class="btn btn-success" onclick="saveInvoice()">
                                <i class="fas fa-save"></i> حفظ الفاتورة
                            </button>
                            <button class="btn btn-secondary" onclick="resetInvoiceForm()">
                                <i class="fas fa-times"></i> إلغاء
                            </button>
                        </div>
                    </div>
                    <div class="card-body">
                        <form id="invoiceForm">
                            <input type="hidden" id="invoiceType">
                            <input type="hidden" id="invoiceId">
                            
                            <!-- Invoice Header -->
                            <div class="row">
                                <div class="col-md-4">
                                    <div class="mb-3">
                                        <label for="invoiceNumber" class="form-label">رقم الفاتورة</label>
                                        <input type="text" class="form-control" id="invoiceNumber" readonly>
                                    </div>
                                </div>
                                <div class="col-md-4">
                                    <div class="mb-3">
                                        <label for="invoiceDate" class="form-label">تاريخ الفاتورة *</label>
                                        <input type="date" class="form-control" id="invoiceDate" required>
                                    </div>
                                </div>
                                <div class="col-md-4">
                                    <div class="mb-3">
                                        <label for="invoiceTime" class="form-label">وقت الفاتورة</label>
                                        <input type="time" class="form-control" id="invoiceTime">
                                    </div>
                                </div>
                            </div>

                            <!-- Party Selection -->
                            <div class="row" id="partySection">
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="partySelect" class="form-label" id="partyLabel">العميل/المورد *</label>
                                        <select class="form-select" id="partySelect" onchange="loadPartyInfo()">
                                            <option value="">-- اختر --</option>
                                        </select>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="partyBalance" class="form-label">الرصيد الحالي</label>
                                        <input type="text" class="form-control" id="partyBalance" readonly>
                                    </div>
                                </div>
                            </div>

                            <!-- Payment Method and Cashbox -->
                            <div class="row">
                                <div class="col-md-4">
                                    <div class="mb-3">
                                        <label for="paymentMethod" class="form-label">طريقة الدفع *</label>
                                        <select class="form-select" id="paymentMethod" onchange="togglePaymentFields()">
                                            <option value="cash">نقدي</option>
                                            <option value="credit">آجل</option>
                                            <option value="partial">دفع جزئي</option>
                                        </select>
                                    </div>
                                </div>
                                <div class="col-md-4" id="cashboxSection">
                                    <div class="mb-3">
                                        <label for="cashboxSelect" class="form-label">الصندوق المتأثر *</label>
                                        <select class="form-select" id="cashboxSelect" required>
                                            <option value="">-- اختر الصندوق --</option>
                                        </select>
                                        <small class="text-muted">الصندوق الذي ستؤثر عليه هذه المعاملة</small>
                                    </div>
                                </div>
                                <div class="col-md-4" id="paidAmountSection" style="display: none;">
                                    <div class="mb-3">
                                        <label for="paidAmount" class="form-label">المبلغ المدفوع (ر.ي)</label>
                                        <input type="number" class="form-control" id="paidAmount" step="0.001" onchange="calculateRemaining()">
                                    </div>
                                </div>
                            </div>

                            <!-- Items Section -->
                            <div class="mb-3">
                                <div class="row mb-3">
                                    <div class="col-md-6">
                                        <label class="form-label">أصناف الفاتورة:</label>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="input-group">
                                            <span class="input-group-text">
                                                <i class="fas fa-search"></i>
                                            </span>
                                            <input type="text" class="form-control" id="itemSearch"
                                                   placeholder="اكتب الباركود أو الكود أو الاسم واضغط Enter..."
                                                   onkeyup="searchAndAddItem(event)"
                                                   autocomplete="off">
                                            <button type="button" class="btn btn-primary" onclick="addInvoiceItem()">
                                                <i class="fas fa-plus"></i> إضافة يدوي
                                            </button>
                                        </div>
                                        <small class="text-muted">
                                            <i class="fas fa-info-circle"></i>
                                            اكتب الباركود أو الكود أو جزء من الاسم واضغط Enter للإضافة التلقائية
                                        </small>
                                        <div class="mt-2">
                                            <small class="text-info">اختبار سريع:</small>
                                            <button type="button" class="btn btn-outline-info btn-sm ms-1" onclick="testSearch('1234567890123')">
                                                باركود خبز
                                            </button>
                                            <button type="button" class="btn btn-outline-info btn-sm ms-1" onclick="testSearch('FLOUR001')">
                                                كود دقيق
                                            </button>
                                            <button type="button" class="btn btn-outline-info btn-sm ms-1" onclick="testSearch('خدمة')">
                                                اسم خدمة
                                            </button>
                                        </div>
                                    </div>
                                </div>
                                
                                <div class="table-responsive">
                                    <table class="table table-bordered">
                                        <thead class="table-dark">
                                            <tr>
                                                <th>الصنف</th>
                                                <th>الكمية</th>
                                                <th>الوحدة</th>
                                                <th>السعر (ر.ي)</th>
                                                <th>الإجمالي (ر.ي)</th>
                                                <th>إجراءات</th>
                                            </tr>
                                        </thead>
                                        <tbody id="invoiceItemsTable">
                                            <!-- Invoice items will be added here -->
                                        </tbody>
                                        <tfoot class="table-warning">
                                            <tr>
                                                <th colspan="4">الإجمالي:</th>
                                                <th id="invoiceTotal">0.000 ر.ي</th>
                                                <th></th>
                                            </tr>
                                        </tfoot>
                                    </table>
                                </div>
                            </div>

                            <!-- Invoice Summary -->
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="discount" class="form-label">الخصم (ر.ي)</label>
                                        <input type="number" class="form-control" id="discount" step="0.001" value="0" onchange="calculateInvoiceTotal()">
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="finalTotal" class="form-label">الإجمالي النهائي (ر.ي)</label>
                                        <input type="number" class="form-control" id="finalTotal" step="0.001" readonly>
                                    </div>
                                </div>
                            </div>

                            <!-- Notes -->
                            <div class="mb-3">
                                <label for="invoiceNotes" class="form-label">ملاحظات</label>
                                <textarea class="form-control" id="invoiceNotes" rows="2"></textarea>
                            </div>
                        </form>
                    </div>
                </div>

                <!-- Invoices List -->
                <div class="card">
                    <div class="card-header d-flex justify-content-between align-items-center">
                        <h5><i class="fas fa-list"></i> قائمة الفواتير</h5>
                        <div>
                            <select class="form-select form-select-sm d-inline-block w-auto" id="invoiceTypeFilter" onchange="filterInvoices()">
                                <option value="">جميع الأنواع</option>
                                <option value="sale">مبيعات</option>
                                <option value="purchase">مشتريات</option>
                                <option value="sale_return">مرتجع مبيعات</option>
                                <option value="purchase_return">مرتجع مشتريات</option>
                                <option value="quick_sale">بيع سريع</option>
                            </select>
                            <button class="btn btn-primary btn-sm" onclick="loadInvoices()">
                                <i class="fas fa-sync"></i> تحديث
                            </button>
                        </div>
                    </div>
                    <div class="card-body">
                        <div class="table-responsive">
                            <table class="table table-hover">
                                <thead class="table-light">
                                    <tr>
                                        <th>رقم الفاتورة</th>
                                        <th>التاريخ</th>
                                        <th>النوع</th>
                                        <th>العميل/المورد</th>
                                        <th>الإجمالي (ر.ي)</th>
                                        <th>طريقة الدفع</th>
                                        <th>الصندوق المتأثر</th>
                                        <th>الحالة</th>
                                        <th>إجراءات</th>
                                    </tr>
                                </thead>
                                <tbody id="invoicesTableBody">
                                    <tr>
                                        <td colspan="8" class="text-center text-muted">
                                            <i class="fas fa-file-invoice fa-2x mb-2"></i>
                                            <p>لا توجد فواتير</p>
                                        </td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="js/auth-simple.js"></script>
    <script src="js/invoices.js"></script>
</body>
</html>
