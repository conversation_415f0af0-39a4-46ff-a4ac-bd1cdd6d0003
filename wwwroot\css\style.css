/* أنماط نظام إدارة مخبوزات ANW */
body {
    font-family: 'Arial', sans-serif;
    direction: rtl;
    background-color: #f8f9fa;
}

/* القائمة الجانبية المرنة */
.sidebar-container {
    position: sticky;
    top: 20px;
    max-height: calc(100vh - 40px);
    overflow-y: auto;
    overflow-x: hidden;
    scrollbar-width: thin;
    scrollbar-color: #888 #f1f1f1;
}

.sidebar-container::-webkit-scrollbar {
    width: 6px;
}

.sidebar-container::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 3px;
}

.sidebar-container::-webkit-scrollbar-thumb {
    background: #888;
    border-radius: 3px;
}

.sidebar-container::-webkit-scrollbar-thumb:hover {
    background: #555;
}

/* تحسين عرض القائمة */
.list-group-item {
    border: none;
    border-bottom: 1px solid #e9ecef;
    transition: all 0.3s ease;
    padding: 12px 15px;
}

.list-group-item:hover {
    background-color: #e3f2fd;
    transform: translateX(-5px);
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.list-group-item.active {
    background-color: #007bff;
    border-color: #007bff;
    transform: translateX(-5px);
    box-shadow: 0 4px 8px rgba(0,123,255,0.3);
}

.list-group-item i {
    width: 20px;
    text-align: center;
    margin-left: 10px;
}

/* تحسين البطاقات */
.card {
    border: none;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    border-radius: 10px;
    transition: all 0.3s ease;
}

.card:hover {
    box-shadow: 0 4px 20px rgba(0,0,0,0.15);
    transform: translateY(-2px);
}

.card-header {
    background: linear-gradient(135deg, #007bff, #0056b3);
    color: white;
    border-radius: 10px 10px 0 0 !important;
    border: none;
}

/* تحسين الأزرار */
.btn {
    border-radius: 8px;
    transition: all 0.3s ease;
}

.btn:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 8px rgba(0,0,0,0.2);
}

/* تحسين الجداول */
.table {
    border-radius: 8px;
    overflow: hidden;
}

.table thead th {
    background: linear-gradient(135deg, #6c757d, #495057);
    color: white;
    border: none;
    font-weight: 600;
}

.table-hover tbody tr:hover {
    background-color: #f8f9fa;
    transform: scale(1.01);
    transition: all 0.2s ease;
}

/* تحسين النماذج */
.form-control, .form-select {
    border-radius: 8px;
    border: 2px solid #e9ecef;
    transition: all 0.3s ease;
}

.form-control:focus, .form-select:focus {
    border-color: #007bff;
    box-shadow: 0 0 0 0.2rem rgba(0,123,255,0.25);
    transform: scale(1.02);
}

/* تحسين النوافذ المنبثقة */
.modal-content {
    border-radius: 15px;
    border: none;
    box-shadow: 0 10px 30px rgba(0,0,0,0.3);
}

.modal-header {
    background: linear-gradient(135deg, #007bff, #0056b3);
    color: white;
    border-radius: 15px 15px 0 0;
    border: none;
}

/* تحسين شجرة الحسابات */
.account-tree {
    font-family: 'Arial', sans-serif;
}

.account-node {
    margin: 5px 0;
    padding: 12px;
    border: 1px solid #e0e0e0;
    border-radius: 8px;
    background: #f8f9fa;
    cursor: pointer;
    transition: all 0.3s ease;
}

.account-node:hover {
    background: #e9ecef;
    border-color: #007bff;
    transform: translateX(-5px);
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.account-node.selected {
    background: linear-gradient(135deg, #007bff, #0056b3);
    color: white;
    border-color: #0056b3;
    transform: translateX(-5px);
    box-shadow: 0 4px 12px rgba(0,123,255,0.3);
}

.account-children {
    margin-right: 20px;
    border-right: 2px solid #dee2e6;
    padding-right: 10px;
    position: relative;
}

.account-children::before {
    content: '';
    position: absolute;
    right: -2px;
    top: 0;
    bottom: 0;
    width: 2px;
    background: linear-gradient(to bottom, #007bff, transparent);
}

.account-toggle {
    width: 24px;
    height: 24px;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    background: linear-gradient(135deg, #6c757d, #495057);
    color: white;
    border-radius: 50%;
    font-size: 12px;
    margin-left: 8px;
    cursor: pointer;
    transition: all 0.3s ease;
}

.account-toggle:hover {
    background: linear-gradient(135deg, #007bff, #0056b3);
    transform: scale(1.1);
}

.account-info {
    display: inline-block;
    margin-right: 10px;
}

.account-balance {
    float: left;
    font-weight: bold;
    color: #28a745;
    background: rgba(40, 167, 69, 0.1);
    padding: 4px 8px;
    border-radius: 4px;
}

/* تحسين الإشعارات */
.alert {
    border-radius: 10px;
    border: none;
    box-shadow: 0 4px 12px rgba(0,0,0,0.1);
}

/* تحسين الشارات */
.badge {
    border-radius: 6px;
    font-weight: 500;
}

/* تحسين أزرار العمليات السريعة */
.quick-action-btn {
    display: block;
    padding: 15px;
    margin-bottom: 10px;
    background: white;
    border: 2px solid #e9ecef;
    border-radius: 10px;
    text-decoration: none;
    color: #495057;
    transition: all 0.3s ease;
    text-align: center;
}

.quick-action-btn:hover {
    background: linear-gradient(135deg, #007bff, #0056b3);
    color: white;
    border-color: #007bff;
    transform: translateY(-2px);
    box-shadow: 0 6px 16px rgba(0,123,255,0.3);
    text-decoration: none;
}

.quick-action-btn i {
    font-size: 1.2em;
    margin-bottom: 5px;
}

/* تحسين الإحصائيات */
.stat-card {
    background: white;
    border-radius: 15px;
    padding: 20px;
    text-align: center;
    border: none;
    box-shadow: 0 4px 15px rgba(0,0,0,0.1);
    transition: all 0.3s ease;
}

.stat-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 25px rgba(0,0,0,0.15);
}

.stat-icon {
    width: 60px;
    height: 60px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 15px;
    font-size: 24px;
    color: white;
}

.stat-icon.bg-primary {
    background: linear-gradient(135deg, #007bff, #0056b3);
}

.stat-icon.bg-success {
    background: linear-gradient(135deg, #28a745, #1e7e34);
}

.stat-icon.bg-warning {
    background: linear-gradient(135deg, #ffc107, #e0a800);
}

.stat-icon.bg-info {
    background: linear-gradient(135deg, #17a2b8, #138496);
}

/* تحسين الاستجابة للشاشات الصغيرة */
@media (max-width: 768px) {
    .sidebar-container {
        position: relative;
        max-height: none;
        margin-bottom: 20px;
    }
    
    .account-children {
        margin-right: 10px;
        padding-right: 5px;
    }
    
    .quick-action-btn {
        padding: 10px;
        font-size: 0.9em;
    }
}

/* تحسين الطباعة */
@media print {
    .sidebar-container,
    .navbar,
    .btn,
    .no-print {
        display: none !important;
    }
    
    .card {
        box-shadow: none;
        border: 1px solid #ddd;
    }
    
    body {
        background: white;
    }
}
