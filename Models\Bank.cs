using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace ANWBakerySystem.Models
{
    /// <summary>
    /// البنوك والحسابات المصرفية
    /// Banks and Bank Accounts
    /// </summary>
    public class Bank
    {
        [Key]
        public int BankId { get; set; }

        [Required]
        [StringLength(20)]
        [Display(Name = "كود البنك")]
        public string BankCode { get; set; } = string.Empty;

        [Required]
        [StringLength(200)]
        [Display(Name = "اسم البنك")]
        public string BankName { get; set; } = string.Empty;

        [StringLength(200)]
        [Display(Name = "الاسم بالإنجليزية")]
        public string? BankNameEn { get; set; }

        [StringLength(500)]
        [Display(Name = "عنوان البنك")]
        public string? Address { get; set; }

        [StringLength(20)]
        [Display(Name = "رقم الهاتف")]
        public string? Phone { get; set; }

        [StringLength(100)]
        [Display(Name = "البريد الإلكتروني")]
        public string? Email { get; set; }

        [StringLength(100)]
        [Display(Name = "الموقع الإلكتروني")]
        public string? Website { get; set; }

        [StringLength(50)]
        [Display(Name = "رمز SWIFT")]
        public string? SwiftCode { get; set; }

        [StringLength(500)]
        [Display(Name = "ملاحظات")]
        public string? Notes { get; set; }

        [Display(Name = "نشط")]
        public bool IsActive { get; set; } = true;

        [Display(Name = "تاريخ الإنشاء")]
        public DateTime CreatedAt { get; set; } = DateTime.Now;

        [Display(Name = "تاريخ التحديث")]
        public DateTime? UpdatedAt { get; set; }

        [Display(Name = "منشئ بواسطة")]
        public int CreatedBy { get; set; }

        [Display(Name = "محدث بواسطة")]
        public int? UpdatedBy { get; set; }

        // Navigation properties
        public virtual User Creator { get; set; } = null!;
        public virtual User? Updater { get; set; }
        public virtual ICollection<BankAccount> BankAccounts { get; set; } = new List<BankAccount>();
    }

    // تم نقل تعريف BankAccount إلى ملف منفصل لتجنب التضارب
    // BankAccount definition moved to separate file to avoid conflicts



    // تم نقل تعريف BankTransaction إلى ملف BankAccount.cs لتجنب التضارب
    // BankTransaction definition moved to BankAccount.cs to avoid conflicts



    // تم نقل تعريفات الـ enums إلى ملف BankAccount.cs لتجنب التضارب
    // Enum definitions moved to BankAccount.cs to avoid conflicts




}
