<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>لوحة التحكم - نظام إدارة مخبوزات ANW</title>
    
    <!-- Bootstrap CSS (RTL) -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css" rel="stylesheet">
    
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    
    <!-- Google Fonts - Arabic -->
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
    
    <style>
        body {
            font-family: 'Cairo', sans-serif;
            background: #f8f9fa;
            direction: rtl;
        }
        
        .sidebar {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            color: white;
            position: fixed;
            top: 0;
            right: 0;
            width: 250px;
            z-index: 1000;
            transition: all 0.3s ease;
        }
        
        .sidebar.collapsed {
            width: 70px;
        }
        
        .sidebar .logo {
            padding: 20px;
            text-align: center;
            border-bottom: 1px solid rgba(255,255,255,0.1);
        }
        
        .sidebar .logo h4 {
            margin: 0;
            font-weight: 700;
        }
        
        .sidebar .nav-link {
            color: rgba(255,255,255,0.8);
            padding: 15px 20px;
            border-radius: 0;
            transition: all 0.3s ease;
        }
        
        .sidebar .nav-link:hover,
        .sidebar .nav-link.active {
            background: rgba(255,255,255,0.1);
            color: white;
        }
        
        .main-content {
            margin-right: 250px;
            transition: all 0.3s ease;
        }
        
        .main-content.expanded {
            margin-right: 70px;
        }
        
        .navbar {
            background: white;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        
        .stats-card {
            background: white;
            border-radius: 15px;
            padding: 25px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.08);
            border: none;
            transition: transform 0.3s ease;
        }
        
        .stats-card:hover {
            transform: translateY(-5px);
        }
        
        .stats-icon {
            width: 60px;
            height: 60px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 24px;
            color: white;
            margin-bottom: 15px;
        }
        
        .stats-number {
            font-size: 2rem;
            font-weight: 700;
            color: #2c3e50;
            margin-bottom: 5px;
        }
        
        .stats-label {
            color: #7f8c8d;
            font-size: 0.9rem;
        }
        
        .currency-badge {
            background: linear-gradient(135deg, #f39c12 0%, #e67e22 100%);
            color: white;
            padding: 8px 15px;
            border-radius: 20px;
            font-weight: 600;
            display: inline-block;
            margin-bottom: 20px;
        }
        
        .quick-actions {
            background: white;
            border-radius: 15px;
            padding: 25px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.08);
        }
        
        .quick-action-btn {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border: none;
            border-radius: 10px;
            padding: 15px;
            color: white;
            text-decoration: none;
            display: block;
            margin-bottom: 10px;
            transition: all 0.3s ease;
        }
        
        .quick-action-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(102, 126, 234, 0.4);
            color: white;
        }
        
        .recent-activities {
            background: white;
            border-radius: 15px;
            padding: 25px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.08);
        }
        
        .activity-item {
            padding: 15px 0;
            border-bottom: 1px solid #ecf0f1;
        }
        
        .activity-item:last-child {
            border-bottom: none;
        }
        
        .activity-icon {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 16px;
            color: white;
            margin-left: 15px;
        }
        
        .toggle-sidebar {
            background: none;
            border: none;
            color: #667eea;
            font-size: 1.2rem;
            padding: 10px;
        }
        
        .user-info {
            background: rgba(102, 126, 234, 0.1);
            border-radius: 10px;
            padding: 15px;
            margin-bottom: 20px;
        }
    </style>
</head>
<body>
    <!-- Sidebar -->
    <div class="sidebar" id="sidebar">
        <div class="logo">
            <div class="d-flex align-items-center justify-content-center">
                <i class="fas fa-bread-slice me-2"></i>
                <h4 class="sidebar-text">ANW</h4>
            </div>
        </div>
        
        <div class="user-info">
            <div class="d-flex align-items-center">
                <i class="fas fa-user-circle fa-2x me-2"></i>
                <div class="sidebar-text">
                    <div class="fw-bold" id="userName">مرحباً</div>
                    <small id="userRole">مدير النظام</small>
                </div>
            </div>
        </div>
        
        <div class="sidebar-container" style="max-height: calc(100vh - 200px); overflow-y: auto;">
            <div class="list-group list-group-flush">
                <a href="dashboard.html" class="list-group-item list-group-item-action active">
                    <i class="fas fa-tachometer-alt"></i> لوحة التحكم
                </a>
                <a href="accounts.html" class="list-group-item list-group-item-action">
                    <i class="fas fa-sitemap"></i> شجرة الحسابات
                </a>
                <a href="banks.html" class="list-group-item list-group-item-action">
                    <i class="fas fa-university"></i> البنوك والصناديق
                </a>
                <a href="employees.html" class="list-group-item list-group-item-action">
                    <i class="fas fa-users"></i> الموظفين
                </a>
                <a href="users.html" class="list-group-item list-group-item-action">
                    <i class="fas fa-users-cog"></i> المستخدمين
                </a>
                <a href="journal.html" class="list-group-item list-group-item-action">
                    <i class="fas fa-book"></i> القيود المحاسبية
                </a>
                <a href="vouchers.html" class="list-group-item list-group-item-action">
                    <i class="fas fa-receipt"></i> سندات القبض والصرف
                </a>
                <a href="owners.html" class="list-group-item list-group-item-action">
                    <i class="fas fa-crown"></i> إدارة الملاك
                </a>
                <a href="reports.html" class="list-group-item list-group-item-action">
                    <i class="fas fa-chart-bar"></i> التقارير المالية
                </a>
                <a href="units.html" class="list-group-item list-group-item-action">
                    <i class="fas fa-balance-scale"></i> وحدات القياس
                </a>
                <a href="parties.html" class="list-group-item list-group-item-action">
                    <i class="fas fa-handshake"></i> العملاء والموردين
                </a>
                <a href="items-management.html" class="list-group-item list-group-item-action">
                    <i class="fas fa-boxes"></i> الأصناف والمنتجات
                </a>
                <a href="invoices.html" class="list-group-item list-group-item-action">
                    <i class="fas fa-file-invoice"></i> الفواتير
                </a>
                <a href="inventory.html" class="list-group-item list-group-item-action">
                    <i class="fas fa-warehouse"></i> المخزون
                </a>
                <a href="settings.html" class="list-group-item list-group-item-action">
                    <i class="fas fa-cog"></i> الإعدادات
                </a>
            </div>
        </div>
        
        <div class="mt-auto p-3">
            <button class="btn btn-outline-light btn-sm w-100" onclick="logout()">
                <i class="fas fa-sign-out-alt me-2"></i>
                <span class="sidebar-text">تسجيل الخروج</span>
            </button>
        </div>
    </div>

    <!-- Main Content -->
    <div class="main-content" id="mainContent">
        <!-- Top Navbar -->
        <nav class="navbar navbar-expand-lg navbar-light">
            <div class="container-fluid">
                <button class="toggle-sidebar" onclick="toggleSidebar()">
                    <i class="fas fa-bars"></i>
                </button>
                
                <div class="currency-badge">
                    <i class="fas fa-coins me-2"></i>
                    العملة الرسمية: الريال اليمني (ر.ي)
                </div>
                
                <div class="navbar-nav ms-auto">
                    <div class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown">
                            <i class="fas fa-bell"></i>
                            <span class="badge bg-danger">3</span>
                        </a>
                        <ul class="dropdown-menu">
                            <li><a class="dropdown-item" href="#">تنبيه: مخزون منخفض</a></li>
                            <li><a class="dropdown-item" href="#">فاتورة جديدة</a></li>
                            <li><a class="dropdown-item" href="#">تحديث النظام</a></li>
                        </ul>
                    </div>
                </div>
            </div>
        </nav>

        <!-- Dashboard Content -->
        <div class="container-fluid p-4" id="dashboardSection">
            <div class="row mb-4">
                <div class="col-12">
                    <h2 class="mb-0">لوحة التحكم الرئيسية</h2>
                    <p class="text-muted">نظام إدارة مخبوزات ANW المتكامل</p>
                </div>
            </div>

            <!-- Statistics Cards -->
            <div class="row mb-4">
                <div class="col-lg-3 col-md-6 mb-3">
                    <div class="stats-card">
                        <div class="stats-icon" style="background: linear-gradient(135deg, #3498db 0%, #2980b9 100%);">
                            <i class="fas fa-file-invoice"></i>
                        </div>
                        <div class="stats-number" id="totalInvoices">0</div>
                        <div class="stats-label">إجمالي الفواتير اليوم</div>
                    </div>
                </div>
                
                <div class="col-lg-3 col-md-6 mb-3">
                    <div class="stats-card">
                        <div class="stats-icon" style="background: linear-gradient(135deg, #27ae60 0%, #229954 100%);">
                            <i class="fas fa-money-bill-wave"></i>
                        </div>
                        <div class="stats-number" id="totalSales">0 ر.ي</div>
                        <div class="stats-label">إجمالي المبيعات اليوم</div>
                    </div>
                </div>
                
                <div class="col-lg-3 col-md-6 mb-3">
                    <div class="stats-card">
                        <div class="stats-icon" style="background: linear-gradient(135deg, #e74c3c 0%, #c0392b 100%);">
                            <i class="fas fa-shopping-cart"></i>
                        </div>
                        <div class="stats-number" id="totalPurchases">0 ر.ي</div>
                        <div class="stats-label">إجمالي المشتريات اليوم</div>
                    </div>
                </div>
                
                <div class="col-lg-3 col-md-6 mb-3">
                    <div class="stats-card">
                        <div class="stats-icon" style="background: linear-gradient(135deg, #f39c12 0%, #e67e22 100%);">
                            <i class="fas fa-boxes"></i>
                        </div>
                        <div class="stats-number" id="lowStockItems">0</div>
                        <div class="stats-label">أصناف مخزون منخفض</div>
                    </div>
                </div>
            </div>

            <div class="row">
                <!-- Quick Actions -->
                <div class="col-lg-4 mb-4">
                    <div class="quick-actions">
                        <h5 class="mb-3">
                            <i class="fas fa-bolt me-2"></i>
                            العمليات السريعة
                        </h5>
                        
                        <a href="invoices.html" class="quick-action-btn">
                            <i class="fas fa-plus me-2"></i>
                            فاتورة بيع جديدة
                        </a>

                        <a href="invoices.html" class="quick-action-btn">
                            <i class="fas fa-shopping-cart me-2"></i>
                            فاتورة شراء جديدة
                        </a>

                        <a href="invoices.html" class="quick-action-btn">
                            <i class="fas fa-cash-register me-2"></i>
                            بيع سريع
                        </a>
                        
                        <a href="items.html" class="quick-action-btn">
                            <i class="fas fa-box me-2"></i>
                            إضافة منتج جديد
                        </a>
                        
                        <a href="parties.html" class="quick-action-btn">
                            <i class="fas fa-user-plus me-2"></i>
                            إضافة عميل/مورد
                        </a>

                        <a href="production.html" class="quick-action-btn">
                            <i class="fas fa-industry me-2"></i>
                            بدء عملية إنتاج
                        </a>

                        <a href="invoices.html" class="quick-action-btn">
                            <i class="fas fa-shopping-cart me-2"></i>
                            فاتورة مبيعات
                        </a>

                        <a href="invoices.html" class="quick-action-btn">
                            <i class="fas fa-bolt me-2"></i>
                            بيع سريع
                        </a>
                    </div>
                </div>

                <!-- Recent Activities -->
                <div class="col-lg-8 mb-4">
                    <div class="recent-activities">
                        <h5 class="mb-3">
                            <i class="fas fa-history me-2"></i>
                            الأنشطة الأخيرة
                        </h5>
                        
                        <div class="activity-item d-flex align-items-center">
                            <div class="activity-icon" style="background: #3498db;">
                                <i class="fas fa-file-invoice"></i>
                            </div>
                            <div>
                                <div class="fw-bold">فاتورة مبيعات جديدة</div>
                                <small class="text-muted">فاتورة رقم SAL-202412-0001 بقيمة 15,000 ر.ي</small>
                            </div>
                            <small class="text-muted ms-auto">منذ 5 دقائق</small>
                        </div>
                        
                        <div class="activity-item d-flex align-items-center">
                            <div class="activity-icon" style="background: #27ae60;">
                                <i class="fas fa-user-plus"></i>
                            </div>
                            <div>
                                <div class="fw-bold">عميل جديد</div>
                                <small class="text-muted">تم إضافة العميل: مخبز الأمل</small>
                            </div>
                            <small class="text-muted ms-auto">منذ 15 دقيقة</small>
                        </div>
                        
                        <div class="activity-item d-flex align-items-center">
                            <div class="activity-icon" style="background: #e74c3c;">
                                <i class="fas fa-exclamation-triangle"></i>
                            </div>
                            <div>
                                <div class="fw-bold">تنبيه مخزون</div>
                                <small class="text-muted">دقيق أبيض - الكمية أقل من الحد الأدنى</small>
                            </div>
                            <small class="text-muted ms-auto">منذ 30 دقيقة</small>
                        </div>
                        
                        <div class="activity-item d-flex align-items-center">
                            <div class="activity-icon" style="background: #f39c12;">
                                <i class="fas fa-box"></i>
                            </div>
                            <div>
                                <div class="fw-bold">منتج جديد</div>
                                <small class="text-muted">تم إضافة: خبز أسمر كامل</small>
                            </div>
                            <small class="text-muted ms-auto">منذ ساعة</small>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- تم حذف الأقسام الداخلية - جميع الصفحات منفصلة الآن -->
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>

    <!-- Bypass Authentication -->
    <script src="js/bypass-auth.js"></script>
    
    <script>
        // متغيرات عامة
        const API_BASE_URL = '/api';
        let authToken = localStorage.getItem('authToken');
        let userInfo = JSON.parse(localStorage.getItem('userInfo') || '{}');

        // التحقق من المصادقة عند تحميل الصفحة - معطل
        document.addEventListener('DOMContentLoaded', function() {
            console.log('🔓 لوحة التحكم - تم تجاوز فحص المصادقة');

            // إنشاء بيانات تلقائية
            const userData = {
                id: 1,
                username: 'admin',
                fullName: 'مدير النظام',
                role: 'Admin'
            };

            localStorage.setItem('authToken', 'dashboard-token-12345');
            localStorage.setItem('userInfo', JSON.stringify(userData));
            localStorage.setItem('user', JSON.stringify(userData));
            localStorage.setItem('token', 'dashboard-token-12345');

            authToken = 'dashboard-token-12345';
            userInfo = userData;

            loadUserInfo();
            loadDashboardData();
        });

        // تحميل معلومات المستخدم
        function loadUserInfo() {
            if (userInfo.fullName) {
                document.getElementById('userName').textContent = `مرحباً، ${userInfo.fullName}`;
                document.getElementById('userRole').textContent = getRoleDisplayName(userInfo.role);
            }
        }

        // الحصول على اسم الدور للعرض
        function getRoleDisplayName(role) {
            const roles = {
                'Admin': 'مدير النظام',
                'Manager': 'مدير',
                'Accountant': 'محاسب',
                'Cashier': 'أمين صندوق',
                'SalesEmployee': 'موظف مبيعات',
                'WarehouseEmployee': 'موظف مخزن',
                'Employee': 'موظف',
                'User': 'مستخدم'
            };
            return roles[role] || 'مستخدم';
        }

        // تحميل بيانات لوحة التحكم
        async function loadDashboardData() {
            try {
                // هنا يمكن إضافة استدعاءات API لجلب البيانات الفعلية
                // مؤقتاً سنستخدم بيانات تجريبية
                
                document.getElementById('totalInvoices').textContent = '25';
                document.getElementById('totalSales').textContent = '125,500 ر.ي';
                document.getElementById('totalPurchases').textContent = '85,200 ر.ي';
                document.getElementById('lowStockItems').textContent = '8';
                
            } catch (error) {
                console.error('خطأ في تحميل بيانات لوحة التحكم:', error);
            }
        }

        // تبديل الشريط الجانبي
        function toggleSidebar() {
            const sidebar = document.getElementById('sidebar');
            const mainContent = document.getElementById('mainContent');
            const sidebarTexts = document.querySelectorAll('.sidebar-text');
            
            sidebar.classList.toggle('collapsed');
            mainContent.classList.toggle('expanded');
            
            sidebarTexts.forEach(text => {
                text.style.display = sidebar.classList.contains('collapsed') ? 'none' : 'inline';
            });
        }

        // تم حذف دالة showSection - جميع الصفحات منفصلة الآن

        // تسجيل الخروج
        async function logout() {
            try {
                await fetch(`${API_BASE_URL}/auth/logout`, {
                    method: 'POST',
                    headers: {
                        'Authorization': `Bearer ${authToken}`,
                        'Content-Type': 'application/json'
                    }
                });
            } catch (error) {
                console.error('خطأ في تسجيل الخروج:', error);
            } finally {
                localStorage.removeItem('authToken');
                localStorage.removeItem('userInfo');
                window.location.href = '/index.html';
            }
        }

        // دالة مساعدة لاستدعاء API
        async function apiCall(endpoint, method = 'GET', data = null) {
            const options = {
                method,
                headers: {
                    'Authorization': `Bearer ${authToken}`,
                    'Content-Type': 'application/json'
                }
            };
            
            if (data) {
                options.body = JSON.stringify(data);
            }
            
            const response = await fetch(`${API_BASE_URL}${endpoint}`, options);
            
            if (response.status === 401) {
                // انتهت صلاحية الرمز المميز
                localStorage.removeItem('authToken');
                localStorage.removeItem('userInfo');
                window.location.href = '/index.html';
                return;
            }
            
            return await response.json();
        }
    </script>
</body>
</html>
