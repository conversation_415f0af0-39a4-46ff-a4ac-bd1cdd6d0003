@echo off
echo ========================================
echo       نظام ANW لإدارة المخبوزات
echo ========================================
echo.

REM إنهاء أي عمليات dotnet قيد التشغيل
echo إنهاء العمليات السابقة...
taskkill /f /im dotnet.exe >nul 2>&1

REM الانتقال إلى مجلد المشروع
cd /d "C:\ANW_bakery"

REM التحقق من وجود المجلد
if not exist "C:\ANW_bakery" (
    echo خطأ: مجلد المشروع غير موجود
    echo يرجى التأكد من وجود المشروع في C:\ANW_bakery
    pause
    exit /b 1
)

echo استعادة الحزم...
dotnet restore

echo بناء المشروع...
dotnet build

echo تشغيل النظام...
echo.
echo ========================================
echo النظام يعمل الآن على:
echo http://localhost:5000/units-simple.html
echo ========================================
echo.
echo اضغط Ctrl+C لإيقاف النظام
echo.

REM تشغيل المشروع وفتح المتصفح
start http://localhost:5000/units-simple.html
dotnet run

pause
