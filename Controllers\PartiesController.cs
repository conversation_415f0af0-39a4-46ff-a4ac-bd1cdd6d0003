using Microsoft.AspNetCore.Mvc;
using ANWBakerySystem.Data;
using ANWBakerySystem.Models;
using Microsoft.EntityFrameworkCore;

namespace ANWBakerySystem.Controllers
{
    [ApiController]
    [Route("api/[controller]")]
    public class PartiesController : ControllerBase
    {
        private readonly ANWBakeryDbContext _context;

        public PartiesController(ANWBakeryDbContext context)
        {
            _context = context;
        }

        // GET: api/parties
        [HttpGet]
        public async Task<ActionResult> GetParties([FromQuery] string? type = null)
        {
            try
            {
                var query = _context.Parties.AsQueryable();

                // تصفية حسب النوع إذا تم تحديده
                if (!string.IsNullOrEmpty(type))
                {
                    if (Enum.TryParse<PartyType>(type, true, out var partyType))
                    {
                        query = query.Where(p => p.PartyType == partyType);
                    }
                }

                var parties = await query
                    .Where(p => p.IsActive)
                    .OrderBy(p => p.PartyName)
                    .Select(p => new
                    {
                        p.PartyId,
                        p.PartyCode,
                        p.PartyName,
                        p.PartyType,
                        p.Phone,
                        p.Email,
                        p.Address,
                        p.CurrentBalance,
                        p.CreditLimit,
                        p.IsActive,
                        p.CreatedAt
                    })
                    .ToListAsync();

                return Ok(new
                {
                    success = true,
                    data = parties,
                    message = "تم جلب العملاء والموردين بنجاح"
                });
            }
            catch (Exception ex)
            {
                return StatusCode(500, new
                {
                    success = false,
                    message = "خطأ في جلب العملاء والموردين",
                    error = ex.Message
                });
            }
        }

        // GET: api/parties/{id}
        [HttpGet("{id}")]
        public async Task<ActionResult> GetParty(int id)
        {
            try
            {
                var party = await _context.Parties
                    .Where(p => p.PartyId == id)
                    .Select(p => new
                    {
                        p.PartyId,
                        p.PartyCode,
                        p.PartyName,
                        p.PartyType,
                        p.Phone,
                        p.Email,
                        p.Address,
                        p.CurrentBalance,
                        p.CreditLimit,
                        PaymentTerms = 0, // سيتم إضافته لاحقاً
                        p.TaxNumber,
                        p.IsActive,
                        p.Notes,
                        p.CreatedAt
                    })
                    .FirstOrDefaultAsync();

                if (party == null)
                {
                    return NotFound(new
                    {
                        success = false,
                        message = "العميل/المورد غير موجود"
                    });
                }

                return Ok(new
                {
                    success = true,
                    data = party,
                    message = "تم جلب العميل/المورد بنجاح"
                });
            }
            catch (Exception ex)
            {
                return StatusCode(500, new
                {
                    success = false,
                    message = "خطأ في جلب العميل/المورد",
                    error = ex.Message
                });
            }
        }

        // POST: api/parties
        [HttpPost]
        public async Task<ActionResult> CreateParty([FromBody] CreatePartyRequest request)
        {
            try
            {
                // التحقق من عدم وجود كود مكرر
                var existingParty = await _context.Parties
                    .FirstOrDefaultAsync(p => p.PartyCode == request.PartyCode);

                if (existingParty != null)
                {
                    return BadRequest(new
                    {
                        success = false,
                        message = "كود العميل/المورد موجود بالفعل"
                    });
                }

                var party = new Party
                {
                    PartyCode = request.PartyCode,
                    PartyName = request.PartyName,
                    PartyType = request.PartyType,
                    Phone = request.Phone,
                    Email = request.Email,
                    Address = request.Address,
                    CurrentBalance = request.CurrentBalance,
                    CreditLimit = request.CreditLimit ?? 0,
                    // PaymentTerms سيتم إضافته لاحقاً
                    TaxNumber = request.TaxNumber,
                    IsActive = request.IsActive,
                    Notes = request.Notes,
                    CreatedAt = DateTime.Now
                };

                _context.Parties.Add(party);
                await _context.SaveChangesAsync();

                return Ok(new
                {
                    success = true,
                    data = new { partyId = party.PartyId },
                    message = "تم إنشاء العميل/المورد بنجاح"
                });
            }
            catch (Exception ex)
            {
                return StatusCode(500, new
                {
                    success = false,
                    message = "خطأ في إنشاء العميل/المورد",
                    error = ex.Message
                });
            }
        }

        // PUT: api/parties/{id}
        [HttpPut("{id}")]
        public async Task<ActionResult> UpdateParty(int id, [FromBody] UpdatePartyRequest request)
        {
            try
            {
                var party = await _context.Parties.FindAsync(id);
                if (party == null)
                {
                    return NotFound(new
                    {
                        success = false,
                        message = "العميل/المورد غير موجود"
                    });
                }

                // التحقق من عدم وجود كود مكرر
                if (request.PartyCode != party.PartyCode)
                {
                    var existingParty = await _context.Parties
                        .FirstOrDefaultAsync(p => p.PartyCode == request.PartyCode && p.PartyId != id);

                    if (existingParty != null)
                    {
                        return BadRequest(new
                        {
                            success = false,
                            message = "كود العميل/المورد موجود بالفعل"
                        });
                    }
                }

                party.PartyCode = request.PartyCode;
                party.PartyName = request.PartyName;
                party.PartyType = request.PartyType;
                party.Phone = request.Phone;
                party.Email = request.Email;
                party.Address = request.Address;
                party.CurrentBalance = request.CurrentBalance;
                party.CreditLimit = request.CreditLimit ?? 0;
                // PaymentTerms سيتم إضافته لاحقاً
                party.TaxNumber = request.TaxNumber;
                party.IsActive = request.IsActive;
                party.Notes = request.Notes;

                await _context.SaveChangesAsync();

                return Ok(new
                {
                    success = true,
                    message = "تم تحديث العميل/المورد بنجاح"
                });
            }
            catch (Exception ex)
            {
                return StatusCode(500, new
                {
                    success = false,
                    message = "خطأ في تحديث العميل/المورد",
                    error = ex.Message
                });
            }
        }

        // DELETE: api/parties/{id}
        [HttpDelete("{id}")]
        public async Task<ActionResult> DeleteParty(int id)
        {
            try
            {
                var party = await _context.Parties.FindAsync(id);
                if (party == null)
                {
                    return NotFound(new
                    {
                        success = false,
                        message = "العميل/المورد غير موجود"
                    });
                }

                // التحقق من عدم وجود فواتير مرتبطة
                var hasInvoices = await _context.Invoices.AnyAsync(i => i.PartyId == id);
                if (hasInvoices)
                {
                    return BadRequest(new
                    {
                        success = false,
                        message = "لا يمكن حذف العميل/المورد لوجود فواتير مرتبطة به"
                    });
                }

                _context.Parties.Remove(party);
                await _context.SaveChangesAsync();

                return Ok(new
                {
                    success = true,
                    message = "تم حذف العميل/المورد بنجاح"
                });
            }
            catch (Exception ex)
            {
                return StatusCode(500, new
                {
                    success = false,
                    message = "خطأ في حذف العميل/المورد",
                    error = ex.Message
                });
            }
        }

        // GET: api/parties/customers
        [HttpGet("customers")]
        public async Task<ActionResult> GetCustomers()
        {
            return await GetParties("Customer");
        }

        // GET: api/parties/suppliers
        [HttpGet("suppliers")]
        public async Task<ActionResult> GetSuppliers()
        {
            return await GetParties("Supplier");
        }
    }

    public class CreatePartyRequest
    {
        public string PartyCode { get; set; } = string.Empty;
        public string PartyName { get; set; } = string.Empty;
        public PartyType PartyType { get; set; }
        public string? Phone { get; set; }
        public string? Email { get; set; }
        public string? Address { get; set; }
        public decimal CurrentBalance { get; set; } = 0;
        public decimal? CreditLimit { get; set; }
        public int? PaymentTerms { get; set; }
        public string? TaxNumber { get; set; }
        public bool IsActive { get; set; } = true;
        public string? Notes { get; set; }
    }

    public class UpdatePartyRequest
    {
        public string PartyCode { get; set; } = string.Empty;
        public string PartyName { get; set; } = string.Empty;
        public PartyType PartyType { get; set; }
        public string? Phone { get; set; }
        public string? Email { get; set; }
        public string? Address { get; set; }
        public decimal CurrentBalance { get; set; }
        public decimal? CreditLimit { get; set; }
        public int? PaymentTerms { get; set; }
        public string? TaxNumber { get; set; }
        public bool IsActive { get; set; }
        public string? Notes { get; set; }
    }
}
