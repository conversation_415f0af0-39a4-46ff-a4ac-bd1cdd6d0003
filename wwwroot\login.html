<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>تسجيل الدخول - نظام ANW</title>
    
    <!-- Bootstrap CSS (RTL) -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css" rel="stylesheet">
    
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    
    <!-- Google Fonts - Arabic -->
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
    
    <style>
        body {
            font-family: 'Cairo', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            direction: rtl;
        }
        
        .login-container {
            background: white;
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            overflow: hidden;
            max-width: 900px;
            width: 100%;
            margin: 20px;
        }
        
        .login-left {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 60px 40px;
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            text-align: center;
        }
        
        .login-right {
            padding: 60px 40px;
        }
        
        .logo {
            font-size: 3rem;
            margin-bottom: 20px;
        }
        
        .welcome-title {
            font-size: 2rem;
            font-weight: 700;
            margin-bottom: 15px;
        }
        
        .welcome-subtitle {
            font-size: 1.1rem;
            opacity: 0.9;
            margin-bottom: 30px;
        }
        
        .feature-item {
            display: flex;
            align-items: center;
            margin-bottom: 15px;
            font-size: 1rem;
        }
        
        .feature-item i {
            margin-left: 15px;
            font-size: 1.2rem;
        }
        
        .login-form {
            max-width: 400px;
        }
        
        .form-title {
            font-size: 2rem;
            font-weight: 700;
            color: #2c3e50;
            margin-bottom: 30px;
            text-align: center;
        }
        
        .form-group {
            margin-bottom: 25px;
        }
        
        .form-label {
            font-weight: 600;
            color: #34495e;
            margin-bottom: 8px;
        }
        
        .form-control {
            border: 2px solid #ecf0f1;
            border-radius: 10px;
            padding: 15px 20px;
            font-size: 1rem;
            transition: all 0.3s ease;
        }
        
        .form-control:focus {
            border-color: #667eea;
            box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
        }
        
        .btn-login {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border: none;
            border-radius: 10px;
            padding: 15px 30px;
            font-size: 1.1rem;
            font-weight: 600;
            color: white;
            width: 100%;
            transition: all 0.3s ease;
        }
        
        .btn-login:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 20px rgba(102, 126, 234, 0.3);
            color: white;
        }
        
        .quick-login {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 20px;
            margin-top: 30px;
        }
        
        .quick-login-btn {
            background: #28a745;
            border: none;
            border-radius: 8px;
            padding: 10px 20px;
            color: white;
            font-weight: 600;
            margin: 5px;
            transition: all 0.3s ease;
        }
        
        .quick-login-btn:hover {
            background: #218838;
            transform: translateY(-1px);
            color: white;
        }
        
        .developer-info {
            position: fixed;
            bottom: 20px;
            left: 20px;
            background: rgba(255,255,255,0.9);
            padding: 15px 20px;
            border-radius: 10px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
            font-size: 0.9rem;
            color: #2c3e50;
        }
        
        .developer-info i {
            color: #667eea;
            margin-left: 8px;
        }
        
        @media (max-width: 768px) {
            .login-left {
                display: none;
            }
            
            .login-right {
                padding: 40px 30px;
            }
        }
    </style>
</head>
<body>
    <div class="login-container">
        <div class="row g-0 h-100">
            <!-- Left Side - Welcome -->
            <div class="col-lg-6 login-left">
                <div class="logo">
                    <i class="fas fa-bread-slice"></i>
                </div>
                <h1 class="welcome-title">مرحباً بك في نظام ANW</h1>
                <p class="welcome-subtitle">نظام إدارة مخبوزات متكامل وسهل الاستخدام</p>
                
                <div class="features">
                    <div class="feature-item">
                        <i class="fas fa-chart-line"></i>
                        <span>إدارة شاملة للمبيعات والمشتريات</span>
                    </div>
                    <div class="feature-item">
                        <i class="fas fa-warehouse"></i>
                        <span>تتبع دقيق للمخزون والمنتجات</span>
                    </div>
                    <div class="feature-item">
                        <i class="fas fa-calculator"></i>
                        <span>محاسبة متقدمة بالريال اليمني</span>
                    </div>
                    <div class="feature-item">
                        <i class="fas fa-users"></i>
                        <span>إدارة الموظفين والرواتب</span>
                    </div>
                    <div class="feature-item">
                        <i class="fas fa-mobile-alt"></i>
                        <span>يعمل على جميع الأجهزة</span>
                    </div>
                </div>
            </div>
            
            <!-- Right Side - Login Form -->
            <div class="col-lg-6 login-right">
                <div class="login-form">
                    <h2 class="form-title">تسجيل الدخول</h2>
                    
                    <form id="loginForm">
                        <div class="form-group">
                            <label class="form-label">اسم المستخدم</label>
                            <input type="text" id="username" class="form-control" placeholder="أدخل اسم المستخدم" required>
                        </div>
                        
                        <div class="form-group">
                            <label class="form-label">كلمة المرور</label>
                            <input type="password" id="password" class="form-control" placeholder="أدخل كلمة المرور" required>
                        </div>
                        
                        <div class="form-check mb-3">
                            <input type="checkbox" id="rememberMe" class="form-check-input">
                            <label class="form-check-label" for="rememberMe">تذكرني</label>
                        </div>
                        
                        <button type="submit" class="btn btn-login">
                            <i class="fas fa-sign-in-alt me-2"></i>
                            دخول
                        </button>
                    </form>
                    
                    <!-- Quick Login -->
                    <div class="quick-login">
                        <h6 class="mb-3">دخول سريع:</h6>
                        <button class="btn quick-login-btn" onclick="quickLogin('admin', 'admin123')">
                            <i class="fas fa-user-shield me-1"></i>مدير النظام
                        </button>
                        <button class="btn quick-login-btn" onclick="quickLogin('accountant', 'acc123')">
                            <i class="fas fa-calculator me-1"></i>محاسب
                        </button>
                        <button class="btn quick-login-btn" onclick="quickLogin('sales', 'sales123')">
                            <i class="fas fa-shopping-cart me-1"></i>مبيعات
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Developer Info -->
    <div class="developer-info">
        <i class="fas fa-code"></i>
        <strong>تصميم: أنور القدمي</strong>
        <br>
        <i class="fas fa-phone"></i>
        *********
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    
    <script>
        // تسجيل الدخول
        document.getElementById('loginForm').addEventListener('submit', function(e) {
            e.preventDefault();
            
            const username = document.getElementById('username').value;
            const password = document.getElementById('password').value;
            
            // التحقق من بيانات الدخول
            if (validateLogin(username, password)) {
                // حفظ بيانات المستخدم
                const userData = getUserData(username);
                localStorage.setItem('authToken', 'anw-token-' + Date.now());
                localStorage.setItem('userInfo', JSON.stringify(userData));
                localStorage.setItem('isLoggedIn', 'true');
                
                // توجيه إلى لوحة التحكم
                window.location.href = 'dashboard.html';
            } else {
                alert('اسم المستخدم أو كلمة المرور غير صحيحة');
            }
        });
        
        // التحقق من بيانات الدخول
        function validateLogin(username, password) {
            const users = {
                'admin': 'admin123',
                'accountant': 'acc123',
                'sales': 'sales123',
                'warehouse': 'warehouse123'
            };
            
            return users[username] === password;
        }
        
        // الحصول على بيانات المستخدم
        function getUserData(username) {
            const usersData = {
                'admin': {
                    id: 1,
                    username: 'admin',
                    fullName: 'مدير النظام',
                    role: 'Admin',
                    permissions: ['all']
                },
                'accountant': {
                    id: 2,
                    username: 'accountant',
                    fullName: 'أحمد المحاسب',
                    role: 'Accountant',
                    permissions: ['invoices', 'reports', 'cash', 'banks']
                },
                'sales': {
                    id: 3,
                    username: 'sales',
                    fullName: 'محمد المبيعات',
                    role: 'Sales',
                    permissions: ['invoices', 'customers', 'items_view']
                },
                'warehouse': {
                    id: 4,
                    username: 'warehouse',
                    fullName: 'علي المخزن',
                    role: 'Warehouse',
                    permissions: ['items', 'inventory', 'invoices_view']
                }
            };
            
            return usersData[username];
        }
        
        // دخول سريع
        function quickLogin(username, password) {
            document.getElementById('username').value = username;
            document.getElementById('password').value = password;
            document.getElementById('loginForm').dispatchEvent(new Event('submit'));
        }
        
        // التحقق من حالة الدخول عند تحميل الصفحة
        document.addEventListener('DOMContentLoaded', function() {
            if (localStorage.getItem('isLoggedIn') === 'true') {
                window.location.href = 'dashboard.html';
            }
        });
    </script>
</body>
</html>
