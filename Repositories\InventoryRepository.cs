using Microsoft.EntityFrameworkCore;
using ANWBakerySystem.Data;
using ANWBakerySystem.Models;

namespace ANWBakerySystem.Repositories
{
    /// <summary>
    /// تطبيق مستودع المخزون
    /// Inventory Repository Implementation
    /// </summary>
    public class InventoryRepository : IInventoryRepository
    {
        private readonly ANWBakeryDbContext _context;

        public InventoryRepository(ANWBakeryDbContext context)
        {
            _context = context;
        }

        #region Inventory Management

        public async Task<IEnumerable<Inventory>> GetAllInventoryAsync()
        {
            return await _context.Inventories
                .Include(i => i.Item)
                .Include(i => i.Warehouse)
                .Include(i => i.Updater)
                .OrderBy(i => i.Item.ItemName)
                .ToListAsync();
        }

        public async Task<IEnumerable<Inventory>> GetInventoryByWarehouseAsync(int warehouseId)
        {
            return await _context.Inventories
                .Include(i => i.Item)
                .Include(i => i.Warehouse)
                .Where(i => i.WarehouseId == warehouseId)
                .OrderBy(i => i.Item.ItemName)
                .ToListAsync();
        }

        public async Task<Inventory?> GetInventoryAsync(int itemId, int warehouseId)
        {
            return await _context.Inventories
                .Include(i => i.Item)
                .Include(i => i.Warehouse)
                .Include(i => i.Updater)
                .FirstOrDefaultAsync(i => i.ItemId == itemId && i.WarehouseId == warehouseId);
        }

        public async Task<Inventory> CreateInventoryAsync(Inventory inventory)
        {
            _context.Inventories.Add(inventory);
            await _context.SaveChangesAsync();
            return inventory;
        }

        public async Task<Inventory> UpdateInventoryAsync(Inventory inventory)
        {
            inventory.UpdatedAt = DateTime.Now;
            _context.Inventories.Update(inventory);
            await _context.SaveChangesAsync();
            return inventory;
        }

        public async Task<bool> DeleteInventoryAsync(int inventoryId)
        {
            var inventory = await _context.Inventories.FindAsync(inventoryId);
            if (inventory == null) return false;

            _context.Inventories.Remove(inventory);
            await _context.SaveChangesAsync();
            return true;
        }

        #endregion

        #region Inventory Movement

        public async Task<InventoryMovement> CreateMovementAsync(InventoryMovement movement)
        {
            _context.InventoryMovements.Add(movement);
            await _context.SaveChangesAsync();
            return movement;
        }

        public async Task<IEnumerable<InventoryMovement>> GetMovementsByItemAsync(int itemId)
        {
            return await _context.InventoryMovements
                .Include(im => im.Item)
                .Include(im => im.Unit)
                .Include(im => im.Warehouse)
                .Include(im => im.Creator)
                .Where(im => im.ItemId == itemId)
                .OrderByDescending(im => im.MovementDate)
                .ToListAsync();
        }

        public async Task<IEnumerable<InventoryMovement>> GetMovementsByWarehouseAsync(int warehouseId)
        {
            return await _context.InventoryMovements
                .Include(im => im.Item)
                .Include(im => im.Unit)
                .Include(im => im.Creator)
                .Where(im => im.WarehouseId == warehouseId)
                .OrderByDescending(im => im.MovementDate)
                .ToListAsync();
        }

        public async Task<IEnumerable<InventoryMovement>> GetMovementsByDateRangeAsync(DateTime fromDate, DateTime toDate)
        {
            return await _context.InventoryMovements
                .Include(im => im.Item)
                .Include(im => im.Unit)
                .Include(im => im.Warehouse)
                .Include(im => im.Creator)
                .Where(im => im.MovementDate >= fromDate && im.MovementDate <= toDate)
                .OrderByDescending(im => im.MovementDate)
                .ToListAsync();
        }

        public async Task<IEnumerable<InventoryMovement>> GetMovementsByTypeAsync(MovementType movementType)
        {
            return await _context.InventoryMovements
                .Include(im => im.Item)
                .Include(im => im.Unit)
                .Include(im => im.Warehouse)
                .Include(im => im.Creator)
                .Where(im => im.MovementType == movementType)
                .OrderByDescending(im => im.MovementDate)
                .ToListAsync();
        }

        #endregion

        #region Inventory Operations

        public async Task<bool> AddStockAsync(int itemId, int warehouseId, decimal quantity, decimal unitCost, 
            MovementType movementType, string? referenceNumber, int userId)
        {
            using var transaction = await _context.Database.BeginTransactionAsync();
            try
            {
                // الحصول على أو إنشاء سجل المخزون
                var inventory = await GetInventoryAsync(itemId, warehouseId);
                if (inventory == null)
                {
                    inventory = new Inventory
                    {
                        ItemId = itemId,
                        WarehouseId = warehouseId,
                        AvailableQuantity = 0,
                        ReservedQuantity = 0,
                        TotalQuantity = 0,
                        AverageCost = 0,
                        TotalValue = 0,
                        UpdatedBy = userId,
                        UpdatedAt = DateTime.Now
                    };
                    await CreateInventoryAsync(inventory);
                }

                // حفظ الرصيد السابق
                var balanceBefore = inventory.AvailableQuantity;

                // حساب متوسط التكلفة الجديد
                var totalValue = inventory.TotalValue + (quantity * unitCost);
                var totalQuantity = inventory.TotalQuantity + quantity;
                inventory.AverageCost = totalQuantity > 0 ? totalValue / totalQuantity : unitCost;

                // تحديث الكميات
                inventory.AvailableQuantity += quantity;
                inventory.UpdateTotalQuantity();
                inventory.UpdateTotalValue();
                inventory.LastMovementDate = DateTime.Now;
                inventory.UpdatedBy = userId;

                await UpdateInventoryAsync(inventory);

                // إنشاء حركة المخزون
                var movement = new InventoryMovement
                {
                    MovementNumber = await GenerateMovementNumberAsync(),
                    MovementDate = DateTime.Now,
                    MovementType = movementType,
                    ItemId = itemId,
                    WarehouseId = warehouseId,
                    UnitId = 1, // افتراضي - يجب تمريره كمعامل
                    UnitType = UnitType.Base,
                    Quantity = quantity,
                    BaseQuantity = quantity,
                    ConversionFactor = 1,
                    UnitCost = unitCost,
                    TotalCost = quantity * unitCost,
                    BalanceBefore = balanceBefore,
                    BalanceAfter = inventory.AvailableQuantity,
                    ReferenceNumber = referenceNumber,
                    CreatedBy = userId
                };

                await CreateMovementAsync(movement);
                await transaction.CommitAsync();
                return true;
            }
            catch
            {
                await transaction.RollbackAsync();
                return false;
            }
        }

        public async Task<bool> RemoveStockAsync(int itemId, int warehouseId, decimal quantity, 
            MovementType movementType, string? referenceNumber, int userId)
        {
            using var transaction = await _context.Database.BeginTransactionAsync();
            try
            {
                var inventory = await GetInventoryAsync(itemId, warehouseId);
                if (inventory == null || inventory.AvailableQuantity < quantity)
                    return false;

                var balanceBefore = inventory.AvailableQuantity;

                // تحديث الكميات
                inventory.AvailableQuantity -= quantity;
                inventory.UpdateTotalQuantity();
                inventory.UpdateTotalValue();
                inventory.LastMovementDate = DateTime.Now;
                inventory.UpdatedBy = userId;

                await UpdateInventoryAsync(inventory);

                // إنشاء حركة المخزون
                var movement = new InventoryMovement
                {
                    MovementNumber = await GenerateMovementNumberAsync(),
                    MovementDate = DateTime.Now,
                    MovementType = movementType,
                    ItemId = itemId,
                    WarehouseId = warehouseId,
                    UnitId = 1, // افتراضي
                    UnitType = UnitType.Base,
                    Quantity = -quantity, // سالب للصادر
                    BaseQuantity = -quantity,
                    ConversionFactor = 1,
                    UnitCost = inventory.AverageCost,
                    TotalCost = quantity * inventory.AverageCost,
                    BalanceBefore = balanceBefore,
                    BalanceAfter = inventory.AvailableQuantity,
                    ReferenceNumber = referenceNumber,
                    CreatedBy = userId
                };

                await CreateMovementAsync(movement);
                await transaction.CommitAsync();
                return true;
            }
            catch
            {
                await transaction.RollbackAsync();
                return false;
            }
        }

        public async Task<bool> TransferStockAsync(int itemId, int fromWarehouseId, int toWarehouseId, 
            decimal quantity, string? referenceNumber, int userId)
        {
            using var transaction = await _context.Database.BeginTransactionAsync();
            try
            {
                // إزالة من المخزن المصدر
                var removeSuccess = await RemoveStockAsync(itemId, fromWarehouseId, quantity, 
                    MovementType.TransferOut, referenceNumber, userId);
                
                if (!removeSuccess) return false;

                // إضافة للمخزن الهدف
                var fromInventory = await GetInventoryAsync(itemId, fromWarehouseId);
                var unitCost = fromInventory?.AverageCost ?? 0;

                var addSuccess = await AddStockAsync(itemId, toWarehouseId, quantity, unitCost,
                    MovementType.TransferIn, referenceNumber, userId);

                if (!addSuccess)
                {
                    await transaction.RollbackAsync();
                    return false;
                }

                await transaction.CommitAsync();
                return true;
            }
            catch
            {
                await transaction.RollbackAsync();
                return false;
            }
        }

        public async Task<bool> AdjustStockAsync(int itemId, int warehouseId, decimal newQuantity, 
            string reason, int userId)
        {
            var inventory = await GetInventoryAsync(itemId, warehouseId);
            if (inventory == null) return false;

            var difference = newQuantity - inventory.AvailableQuantity;
            if (difference == 0) return true;

            var movementType = difference > 0 ? MovementType.AdjustmentIn : MovementType.AdjustmentOut;
            var quantity = Math.Abs(difference);

            if (difference > 0)
                return await AddStockAsync(itemId, warehouseId, quantity, inventory.AverageCost, 
                    movementType, reason, userId);
            else
                return await RemoveStockAsync(itemId, warehouseId, quantity, movementType, reason, userId);
        }

        #endregion

        #region Inventory Queries

        public async Task<decimal> GetAvailableQuantityAsync(int itemId, int warehouseId)
        {
            var inventory = await GetInventoryAsync(itemId, warehouseId);
            return inventory?.GetAvailableForSale() ?? 0;
        }

        public async Task<decimal> GetTotalQuantityAsync(int itemId)
        {
            return await _context.Inventories
                .Where(i => i.ItemId == itemId)
                .SumAsync(i => i.AvailableQuantity);
        }

        public async Task<decimal> GetReservedQuantityAsync(int itemId, int warehouseId)
        {
            var inventory = await GetInventoryAsync(itemId, warehouseId);
            return inventory?.ReservedQuantity ?? 0;
        }

        public async Task<bool> IsQuantityAvailableAsync(int itemId, int warehouseId, decimal requiredQuantity)
        {
            var availableQuantity = await GetAvailableQuantityAsync(itemId, warehouseId);
            return availableQuantity >= requiredQuantity;
        }

        public async Task<decimal> GetAverageCostAsync(int itemId, int warehouseId)
        {
            var inventory = await GetInventoryAsync(itemId, warehouseId);
            return inventory?.AverageCost ?? 0;
        }

        public async Task<decimal> GetInventoryValueAsync(int itemId, int warehouseId)
        {
            var inventory = await GetInventoryAsync(itemId, warehouseId);
            return inventory?.TotalValue ?? 0;
        }

        public async Task<decimal> GetTotalInventoryValueAsync(int warehouseId)
        {
            return await _context.Inventories
                .Where(i => i.WarehouseId == warehouseId)
                .SumAsync(i => i.TotalValue);
        }

        #endregion

        #region Inventory Reports

        public async Task<IEnumerable<Inventory>> GetLowStockItemsAsync()
        {
            return await _context.Inventories
                .Include(i => i.Item)
                .Include(i => i.Warehouse)
                .Where(i => i.AvailableQuantity <= i.Item.MinimumStock)
                .OrderBy(i => i.AvailableQuantity)
                .ToListAsync();
        }

        public async Task<IEnumerable<Inventory>> GetZeroStockItemsAsync()
        {
            return await _context.Inventories
                .Include(i => i.Item)
                .Include(i => i.Warehouse)
                .Where(i => i.AvailableQuantity <= 0)
                .OrderBy(i => i.Item.ItemName)
                .ToListAsync();
        }

        public async Task<IEnumerable<Inventory>> GetOverStockItemsAsync()
        {
            return await _context.Inventories
                .Include(i => i.Item)
                .Include(i => i.Warehouse)
                .Where(i => i.AvailableQuantity > i.Item.MaximumStock)
                .OrderByDescending(i => i.AvailableQuantity - i.Item.MaximumStock)
                .ToListAsync();
        }

        public async Task<IEnumerable<InventoryMovement>> GetSlowMovingItemsAsync(int days)
        {
            var cutoffDate = DateTime.Today.AddDays(-days);
            return await _context.InventoryMovements
                .Include(im => im.Item)
                .Include(im => im.Warehouse)
                .Where(im => im.MovementDate >= cutoffDate)
                .GroupBy(im => new { im.ItemId, im.WarehouseId })
                .Where(g => g.Sum(im => Math.Abs(im.BaseQuantity)) < 10) // أقل من 10 وحدات
                .SelectMany(g => g)
                .OrderBy(im => im.Item.ItemName)
                .ToListAsync();
        }

        public async Task<IEnumerable<InventoryMovement>> GetFastMovingItemsAsync(int days)
        {
            var cutoffDate = DateTime.Today.AddDays(-days);
            return await _context.InventoryMovements
                .Include(im => im.Item)
                .Include(im => im.Warehouse)
                .Where(im => im.MovementDate >= cutoffDate)
                .GroupBy(im => new { im.ItemId, im.WarehouseId })
                .Where(g => g.Sum(im => Math.Abs(im.BaseQuantity)) > 100) // أكثر من 100 وحدة
                .SelectMany(g => g)
                .OrderByDescending(im => im.BaseQuantity)
                .ToListAsync();
        }

        #endregion

        #region Warehouse Management

        public async Task<IEnumerable<Warehouse>> GetAllWarehousesAsync()
        {
            return await _context.Warehouses
                .Include(w => w.Creator)
                .Include(w => w.Updater)
                .OrderBy(w => w.WarehouseName)
                .ToListAsync();
        }

        public async Task<IEnumerable<Warehouse>> GetActiveWarehousesAsync()
        {
            return await _context.Warehouses
                .Where(w => w.IsActive)
                .OrderBy(w => w.WarehouseName)
                .ToListAsync();
        }

        public async Task<Warehouse?> GetWarehouseByIdAsync(int warehouseId)
        {
            return await _context.Warehouses
                .Include(w => w.Creator)
                .Include(w => w.Updater)
                .FirstOrDefaultAsync(w => w.WarehouseId == warehouseId);
        }

        public async Task<Warehouse?> GetWarehouseByCodeAsync(string warehouseCode)
        {
            return await _context.Warehouses
                .FirstOrDefaultAsync(w => w.WarehouseCode == warehouseCode);
        }

        public async Task<Warehouse> CreateWarehouseAsync(Warehouse warehouse)
        {
            _context.Warehouses.Add(warehouse);
            await _context.SaveChangesAsync();
            return warehouse;
        }

        public async Task<Warehouse> UpdateWarehouseAsync(Warehouse warehouse)
        {
            warehouse.UpdatedAt = DateTime.Now;
            _context.Warehouses.Update(warehouse);
            await _context.SaveChangesAsync();
            return warehouse;
        }

        public async Task<bool> DeleteWarehouseAsync(int warehouseId)
        {
            var warehouse = await _context.Warehouses.FindAsync(warehouseId);
            if (warehouse == null) return false;

            warehouse.IsActive = false;
            warehouse.UpdatedAt = DateTime.Now;
            await _context.SaveChangesAsync();
            return true;
        }

        public async Task<bool> WarehouseExistsAsync(int warehouseId)
        {
            return await _context.Warehouses.AnyAsync(w => w.WarehouseId == warehouseId);
        }

        public async Task<bool> WarehouseCodeExistsAsync(string warehouseCode)
        {
            return await _context.Warehouses.AnyAsync(w => w.WarehouseCode == warehouseCode);
        }

        #endregion

        #region Stock Count

        public async Task<bool> StartStockCountAsync(int warehouseId, int userId)
        {
            // يمكن إضافة منطق بدء الجرد هنا
            // مثل إنشاء جدول منفصل لجرد المخزون
            await Task.CompletedTask;
            return true;
        }

        public async Task<bool> UpdateStockCountAsync(int itemId, int warehouseId, decimal countedQuantity, int userId)
        {
            // يمكن إضافة منطق تحديث الجرد هنا
            await Task.CompletedTask;
            return true;
        }

        public async Task<bool> CompleteStockCountAsync(int warehouseId, int userId)
        {
            // يمكن إضافة منطق إنهاء الجرد هنا
            await Task.CompletedTask;
            return true;
        }

        public async Task<IEnumerable<Inventory>> GetStockCountVariancesAsync(int warehouseId)
        {
            // يمكن إضافة منطق حساب الفروقات هنا
            return await GetInventoryByWarehouseAsync(warehouseId);
        }

        #endregion

        #region Private Helper Methods

        private async Task<string> GenerateMovementNumberAsync()
        {
            var today = DateTime.Today;
            var yearMonth = today.ToString("yyyyMM");
            
            var lastMovement = await _context.InventoryMovements
                .Where(im => im.MovementDate.Date == today)
                .OrderByDescending(im => im.MovementNumber)
                .FirstOrDefaultAsync();

            int nextNumber = 1;
            if (lastMovement != null)
            {
                var lastNumberPart = lastMovement.MovementNumber.Split('-').LastOrDefault();
                if (int.TryParse(lastNumberPart, out int lastNumber))
                {
                    nextNumber = lastNumber + 1;
                }
            }

            return $"MOV-{yearMonth}-{nextNumber:D6}";
        }

        #endregion
    }
}
