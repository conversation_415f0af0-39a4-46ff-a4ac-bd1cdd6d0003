// لوحة التحكم الرئيسية
const API_BASE_URL = '/api';

// تحميل البيانات عند تحميل الصفحة
document.addEventListener('DOMContentLoaded', function() {
    checkAuthentication();
    loadDashboardData();
});

// التحقق من المصادقة
function checkAuthentication() {
    const token = localStorage.getItem('authToken');
    const user = localStorage.getItem('currentUser');
    
    if (!token || !user) {
        window.location.href = 'index.html';
        return;
    }
    
    // عرض اسم المستخدم
    const userData = JSON.parse(user);
    document.getElementById('currentUser').textContent = userData.fullName || userData.username;
}

// تحميل بيانات لوحة التحكم
async function loadDashboardData() {
    try {
        await Promise.all([
            loadSalesStatistics(),
            loadRecentInvoices(),
            loadLowStockItems()
        ]);
    } catch (error) {
        console.error('خطأ في تحميل بيانات لوحة التحكم:', error);
    }
}

// تحميل إحصائيات المبيعات
async function loadSalesStatistics() {
    try {
        console.log('🔄 جلب الإحصائيات من قاعدة البيانات...');
        const response = await fetch('/api/dashboard/statistics');

        if (response.ok) {
            const data = await response.json();
            console.log('✅ تم جلب الإحصائيات:', data);

            document.getElementById('todaySales').textContent = formatCurrency(data.todaySales || 0);
            document.getElementById('monthSales').textContent = formatCurrency(data.monthSales || 0);
            document.getElementById('todayInvoices').textContent = data.todayInvoices || 0;

            // إظهار رسالة نجاح
            if (data.todaySales === 0 && data.monthSales === 0 && data.todayInvoices === 0) {
                console.log('📊 قاعدة البيانات فارغة - لا توجد عمليات بعد');
            }
        } else {
            throw new Error(`HTTP ${response.status}`);
        }
    } catch (error) {
        console.error('❌ خطأ في جلب الإحصائيات:', error);
        // عرض أصفار مع رسالة خطأ
        document.getElementById('todaySales').textContent = formatCurrency(0);
        document.getElementById('monthSales').textContent = formatCurrency(0);
        document.getElementById('todayInvoices').textContent = '0';

        // إظهار رسالة خطأ
        showError('فشل في الاتصال بقاعدة البيانات');
    }
}

// تحميل آخر الفواتير
async function loadRecentInvoices() {
    try {
        // جلب البيانات من قاعدة البيانات
        const response = await fetch('/api/dashboard/recent-invoices');
        let recentInvoices = [];

        if (response.ok) {
            recentInvoices = await response.json();
        } else {
            console.error('فشل في جلب الفواتير الأخيرة');
        }

        const tbody = document.getElementById('recentInvoices');

        if (recentInvoices.length === 0) {
            tbody.innerHTML = '<tr><td colspan="5" class="text-center text-muted">لا توجد فواتير حديثة - ابدأ بإنشاء فاتورة جديدة</td></tr>';
            return;
        }

        tbody.innerHTML = recentInvoices.map(invoice => `
            <tr>
                <td>${invoice.invoiceNumber}</td>
                <td>${invoice.partyName}</td>
                <td>
                    <span class="badge ${getInvoiceTypeBadge(invoice.invoiceType)}">
                        ${getInvoiceTypeText(invoice.invoiceType)}
                    </span>
                </td>
                <td>${formatCurrency(invoice.totalAmount)}</td>
                <td>${formatDate(invoice.invoiceDate)}</td>
            </tr>
        `).join('');

    } catch (error) {
        console.error('خطأ في تحميل آخر الفواتير:', error);
        const tbody = document.getElementById('recentInvoices');
        tbody.innerHTML = '<tr><td colspan="5" class="text-center text-muted">لا توجد فواتير حديثة</td></tr>';
    }
}

// تحميل المنتجات المنخفضة
async function loadLowStockItems() {
    try {
        // جلب البيانات من قاعدة البيانات
        const response = await fetch('/api/dashboard/low-stock');
        let lowStockItems = [];

        if (response.ok) {
            lowStockItems = await response.json();
        }

        document.getElementById('lowStockItems').textContent = lowStockItems.length;

        const lowStockList = document.getElementById('lowStockList');

        if (lowStockItems.length === 0) {
            lowStockList.innerHTML = '<p class="text-muted text-center">جميع المنتجات متوفرة بكميات كافية</p>';
            return;
        }

        lowStockList.innerHTML = lowStockItems.map(item => `
            <div class="alert alert-warning py-2 mb-2">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <strong>${item.itemName}</strong><br>
                        <small>الكود: ${item.itemCode}</small><br>
                        <small>المتوفر: ${item.currentStock}</small>
                    </div>
                    <div class="text-end">
                        <small class="text-muted">الحد الأدنى: ${item.minimumStock}</small>
                    </div>
                </div>
            </div>
        `).join('');

    } catch (error) {
        console.error('خطأ في تحميل المنتجات المنخفضة:', error);
        const lowStockList = document.getElementById('lowStockList');
        lowStockList.innerHTML = '<p class="text-muted text-center">لا توجد بيانات</p>';
    }
}

// تنسيق العملة
function formatCurrency(amount) {
    return new Intl.NumberFormat('ar-YE', {
        style: 'currency',
        currency: 'YER',
        minimumFractionDigits: 0,
        maximumFractionDigits: 0
    }).format(amount).replace('YER', 'ر.ي');
}

// تنسيق التاريخ
function formatDate(dateString) {
    const date = new Date(dateString);
    return date.toLocaleDateString('ar-YE', {
        year: 'numeric',
        month: 'short',
        day: 'numeric'
    });
}

// عرض رسالة خطأ
function showError(message) {
    const alertDiv = document.createElement('div');
    alertDiv.className = 'alert alert-danger alert-dismissible fade show position-fixed';
    alertDiv.style.cssText = 'top: 20px; right: 20px; z-index: 9999; min-width: 300px;';
    alertDiv.innerHTML = `
        <i class="fas fa-exclamation-triangle me-2"></i>
        ${message}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    `;
    document.body.appendChild(alertDiv);

    setTimeout(() => {
        if (alertDiv.parentNode) {
            alertDiv.remove();
        }
    }, 5000);
}

// عرض رسالة نجاح
function showSuccess(message) {
    const alertDiv = document.createElement('div');
    alertDiv.className = 'alert alert-success alert-dismissible fade show position-fixed';
    alertDiv.style.cssText = 'top: 20px; right: 20px; z-index: 9999; min-width: 300px;';
    alertDiv.innerHTML = `
        <i class="fas fa-check-circle me-2"></i>
        ${message}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    `;
    document.body.appendChild(alertDiv);

    setTimeout(() => {
        if (alertDiv.parentNode) {
            alertDiv.remove();
        }
    }, 3000);
}

// تسجيل الخروج
function logout() {
    if (confirm('هل أنت متأكد من تسجيل الخروج؟')) {
        localStorage.removeItem('authToken');
        localStorage.removeItem('currentUser');
        window.location.href = 'index.html';
    }
}

// تحديث البيانات كل 5 دقائق
setInterval(loadDashboardData, 5 * 60 * 1000);
