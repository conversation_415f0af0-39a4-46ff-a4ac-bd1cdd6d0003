<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إدارة المستخدمين - نظام ANW</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
</head>
<body>
    <div class="container-fluid">
        <div class="row">
            <!-- Sidebar -->
            <div class="col-md-3 bg-dark text-white p-0">
                <div class="sidebar">
                    <div class="p-3 border-bottom">
                        <h5><i class="fas fa-calculator me-2"></i>نظام ANW</h5>
                        <small>إدارة مخبوزات مبسط</small>
                    </div>
                    <nav class="nav flex-column p-3">
                        <a href="items-simple.html" class="nav-link text-white">
                            <i class="fas fa-boxes me-2"></i>المنتجات والخامات
                        </a>
                        <a href="parties-simple.html" class="nav-link text-white">
                            <i class="fas fa-users me-2"></i>العملاء والموردين
                        </a>
                        <a href="employees-simple.html" class="nav-link text-white">
                            <i class="fas fa-user-tie me-2"></i>الموظفين
                        </a>
                        <a href="invoices-simple.html" class="nav-link text-white">
                            <i class="fas fa-file-invoice me-2"></i>الفواتير
                        </a>
                        <a href="inventory-simple.html" class="nav-link text-white">
                            <i class="fas fa-warehouse me-2"></i>المخزون
                        </a>
                        <a href="cash-simple.html" class="nav-link text-white">
                            <i class="fas fa-cash-register me-2"></i>الصناديق
                        </a>
                        <a href="banks-simple.html" class="nav-link text-white">
                            <i class="fas fa-university me-2"></i>البنوك
                        </a>
                        <a href="owners-simple.html" class="nav-link text-white">
                            <i class="fas fa-crown me-2"></i>الملاك
                        </a>
                        <a href="users-simple.html" class="nav-link text-white active">
                            <i class="fas fa-user-cog me-2"></i>المستخدمين
                        </a>
                        <a href="reports-simple.html" class="nav-link text-white">
                            <i class="fas fa-chart-bar me-2"></i>التقارير
                        </a>
                    </nav>
                </div>
            </div>

            <!-- Main Content -->
            <div class="col-md-9">
                <div class="p-4">
                    <!-- Header -->
                    <div class="d-flex justify-content-between align-items-center mb-4">
                        <h2><i class="fas fa-user-cog text-primary me-2"></i>إدارة المستخدمين</h2>
                        <div>
                            <button class="btn btn-success" onclick="addUser()">
                                <i class="fas fa-plus me-1"></i>إضافة مستخدم
                            </button>
                            <button class="btn btn-info" onclick="manageRoles()">
                                <i class="fas fa-shield-alt me-1"></i>إدارة الأدوار
                            </button>
                        </div>
                    </div>

                    <!-- Users Summary -->
                    <div class="row mb-4">
                        <div class="col-md-12">
                            <div class="card">
                                <div class="card-header">
                                    <h5><i class="fas fa-chart-bar me-2"></i>ملخص المستخدمين</h5>
                                </div>
                                <div class="card-body">
                                    <div id="usersSummary" class="row">
                                        <!-- سيتم ملؤها بـ JavaScript -->
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Tabs -->
                    <ul class="nav nav-tabs mb-3">
                        <li class="nav-item">
                            <a class="nav-link active" onclick="showTab('users')">المستخدمين</a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" onclick="showTab('roles')">الأدوار والصلاحيات</a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" onclick="showTab('sessions')">جلسات العمل</a>
                        </li>
                    </ul>

                    <!-- Users Tab -->
                    <div id="usersTab" class="tab-content">
                        <div class="card">
                            <div class="card-body">
                                <div class="table-responsive">
                                    <table class="table table-striped table-hover">
                                        <thead class="table-dark">
                                            <tr>
                                                <th>اسم المستخدم</th>
                                                <th>الاسم الكامل</th>
                                                <th>البريد الإلكتروني</th>
                                                <th>الدور</th>
                                                <th>آخر دخول</th>
                                                <th>الحالة</th>
                                                <th>الإجراءات</th>
                                            </tr>
                                        </thead>
                                        <tbody id="usersTableBody">
                                            <!-- سيتم ملؤها بـ JavaScript -->
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Roles Tab -->
                    <div id="rolesTab" class="tab-content" style="display: none;">
                        <div class="card">
                            <div class="card-header d-flex justify-content-between">
                                <h6>الأدوار والصلاحيات</h6>
                                <button class="btn btn-sm btn-success" onclick="addRole()">
                                    <i class="fas fa-plus"></i> إضافة دور
                                </button>
                            </div>
                            <div class="card-body">
                                <div id="rolesContainer">
                                    <!-- سيتم ملؤها بـ JavaScript -->
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Sessions Tab -->
                    <div id="sessionsTab" class="tab-content" style="display: none;">
                        <div class="card">
                            <div class="card-body">
                                <div class="table-responsive">
                                    <table class="table table-striped table-hover">
                                        <thead class="table-dark">
                                            <tr>
                                                <th>المستخدم</th>
                                                <th>وقت الدخول</th>
                                                <th>وقت الخروج</th>
                                                <th>المدة</th>
                                                <th>عنوان IP</th>
                                                <th>الحالة</th>
                                            </tr>
                                        </thead>
                                        <tbody id="sessionsTableBody">
                                            <!-- سيتم ملؤها بـ JavaScript -->
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- User Modal -->
    <div class="modal fade" id="userModal" tabindex="-1">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">إضافة مستخدم جديد</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <form id="userForm">
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">اسم المستخدم *</label>
                                    <input type="text" id="username" class="form-control" required>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">كلمة المرور *</label>
                                    <input type="password" id="password" class="form-control" required>
                                </div>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">الاسم الأول *</label>
                                    <input type="text" id="firstName" class="form-control" required>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">اسم العائلة *</label>
                                    <input type="text" id="lastName" class="form-control" required>
                                </div>
                            </div>
                        </div>
                        <div class="mb-3">
                            <label class="form-label">البريد الإلكتروني</label>
                            <input type="email" id="email" class="form-control">
                        </div>
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">رقم الهاتف</label>
                                    <input type="text" id="phone" class="form-control">
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">الدور *</label>
                                    <select id="userRole" class="form-control" required>
                                        <!-- سيتم ملؤها بـ JavaScript -->
                                    </select>
                                </div>
                            </div>
                        </div>
                        <div class="mb-3">
                            <label class="form-label">ملاحظات</label>
                            <textarea id="userNotes" class="form-control" rows="2"></textarea>
                        </div>
                        <div class="form-check mb-3">
                            <input type="checkbox" id="isActive" class="form-check-input" checked>
                            <label class="form-check-label">مستخدم نشط</label>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                    <button type="button" class="btn btn-primary" onclick="saveUser()">حفظ</button>
                </div>
            </div>
        </div>
    </div>

    <!-- Role Modal -->
    <div class="modal fade" id="roleModal" tabindex="-1">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">إضافة دور جديد</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <form id="roleForm">
                        <div class="mb-3">
                            <label class="form-label">اسم الدور *</label>
                            <input type="text" id="roleName" class="form-control" required>
                        </div>
                        <div class="mb-3">
                            <label class="form-label">وصف الدور</label>
                            <textarea id="roleDescription" class="form-control" rows="2"></textarea>
                        </div>
                        <div class="mb-3">
                            <label class="form-label">الصلاحيات</label>
                            <div id="permissionsContainer" class="row">
                                <!-- سيتم ملؤها بـ JavaScript -->
                            </div>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                    <button type="button" class="btn btn-primary" onclick="saveRole()">حفظ</button>
                </div>
            </div>
        </div>
    </div>

    <!-- Success/Error Messages -->
    <div id="messages"></div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // البيانات المحلية
        let users = [];
        let roles = [];
        let sessions = [];
        let nextUserId = 1;
        let nextRoleId = 1;
        let nextSessionId = 1;

        // الصلاحيات المتاحة
        const availablePermissions = [
            {id: 'items_view', name: 'عرض المنتجات والخامات', category: 'المخزون'},
            {id: 'items_add', name: 'إضافة منتجات وخامات', category: 'المخزون'},
            {id: 'items_edit', name: 'تعديل المنتجات والخامات', category: 'المخزون'},
            {id: 'items_delete', name: 'حذف المنتجات والخامات', category: 'المخزون'},
            {id: 'invoices_view', name: 'عرض الفواتير', category: 'المبيعات'},
            {id: 'invoices_add', name: 'إضافة فواتير', category: 'المبيعات'},
            {id: 'invoices_edit', name: 'تعديل الفواتير', category: 'المبيعات'},
            {id: 'invoices_delete', name: 'حذف الفواتير', category: 'المبيعات'},
            {id: 'cash_view', name: 'عرض الصناديق', category: 'المالية'},
            {id: 'cash_add', name: 'إضافة سندات قبض وصرف', category: 'المالية'},
            {id: 'banks_view', name: 'عرض البنوك', category: 'المالية'},
            {id: 'banks_add', name: 'إضافة معاملات بنكية', category: 'المالية'},
            {id: 'reports_view', name: 'عرض التقارير', category: 'التقارير'},
            {id: 'reports_export', name: 'تصدير التقارير', category: 'التقارير'},
            {id: 'users_view', name: 'عرض المستخدمين', category: 'الإدارة'},
            {id: 'users_manage', name: 'إدارة المستخدمين', category: 'الإدارة'},
            {id: 'system_settings', name: 'إعدادات النظام', category: 'الإدارة'}
        ];

        // تحميل البيانات عند بدء الصفحة
        document.addEventListener('DOMContentLoaded', function() {
            loadAllData();
        });

        // تحميل جميع البيانات
        function loadAllData() {
            loadRoles();
            loadUsers();
            loadSessions();
            displayUsersSummary();
            displayUsers();
            displayRoles();
            displaySessions();
            loadRoleOptions();
        }

        // تحميل الأدوار
        function loadRoles() {
            try {
                const savedRoles = localStorage.getItem('anw_roles');
                if (savedRoles && savedRoles !== 'null') {
                    roles = JSON.parse(savedRoles);
                    if (roles.length > 0) {
                        nextRoleId = Math.max(...roles.map(r => r.id), 0) + 1;
                    }
                } else {
                    // إنشاء أدوار افتراضية
                    roles = [
                        {
                            id: 1,
                            name: 'مدير النظام',
                            description: 'صلاحيات كاملة على النظام',
                            permissions: availablePermissions.map(p => p.id)
                        },
                        {
                            id: 2,
                            name: 'محاسب',
                            description: 'صلاحيات المحاسبة والتقارير المالية',
                            permissions: ['invoices_view', 'invoices_add', 'cash_view', 'cash_add', 'banks_view', 'reports_view']
                        },
                        {
                            id: 3,
                            name: 'موظف مبيعات',
                            description: 'صلاحيات المبيعات والعملاء',
                            permissions: ['items_view', 'invoices_view', 'invoices_add', 'cash_view']
                        },
                        {
                            id: 4,
                            name: 'موظف مخزون',
                            description: 'صلاحيات إدارة المخزون',
                            permissions: ['items_view', 'items_add', 'items_edit', 'invoices_view']
                        }
                    ];
                    nextRoleId = 5;
                    saveRoles();
                }
            } catch (error) {
                console.error('خطأ في تحميل الأدوار:', error);
                showMessage('خطأ في تحميل البيانات', 'danger');
            }
        }

        // تحميل المستخدمين
        function loadUsers() {
            try {
                const savedUsers = localStorage.getItem('anw_users');
                if (savedUsers && savedUsers !== 'null') {
                    users = JSON.parse(savedUsers);
                    if (users.length > 0) {
                        nextUserId = Math.max(...users.map(u => u.id), 0) + 1;
                    }
                } else {
                    // إنشاء مستخدمين افتراضيين
                    users = [
                        {
                            id: 1,
                            username: 'admin',
                            password: 'admin123',
                            firstName: 'مدير',
                            lastName: 'النظام',
                            email: '<EMAIL>',
                            phone: '777-000000',
                            roleId: 1,
                            active: true,
                            lastLogin: '2024-01-20T10:30:00',
                            notes: 'مدير النظام الرئيسي'
                        },
                        {
                            id: 2,
                            username: 'accountant',
                            password: 'acc123',
                            firstName: 'أحمد',
                            lastName: 'المحاسب',
                            email: '<EMAIL>',
                            phone: '777-111111',
                            roleId: 2,
                            active: true,
                            lastLogin: '2024-01-19T14:15:00',
                            notes: 'محاسب رئيسي'
                        }
                    ];
                    nextUserId = 3;
                    saveUsers();
                }
            } catch (error) {
                console.error('خطأ في تحميل المستخدمين:', error);
                showMessage('خطأ في تحميل البيانات', 'danger');
            }
        }

        // تحميل الجلسات
        function loadSessions() {
            try {
                const savedSessions = localStorage.getItem('anw_sessions');
                if (savedSessions && savedSessions !== 'null') {
                    sessions = JSON.parse(savedSessions);
                    if (sessions.length > 0) {
                        nextSessionId = Math.max(...sessions.map(s => s.id), 0) + 1;
                    }
                } else {
                    // إنشاء جلسات افتراضية
                    sessions = [
                        {id: 1, userId: 1, loginTime: '2024-01-20T10:30:00', logoutTime: '2024-01-20T18:45:00', ipAddress: '*************', status: 'completed'},
                        {id: 2, userId: 2, loginTime: '2024-01-19T14:15:00', logoutTime: '2024-01-19T22:30:00', ipAddress: '*************', status: 'completed'},
                        {id: 3, userId: 1, loginTime: '2024-01-21T09:00:00', logoutTime: null, ipAddress: '*************', status: 'active'}
                    ];
                    nextSessionId = 4;
                    saveSessions();
                }
            } catch (error) {
                console.error('خطأ في تحميل الجلسات:', error);
                showMessage('خطأ في تحميل البيانات', 'danger');
            }
        }

        // حفظ البيانات
        function saveUsers() {
            localStorage.setItem('anw_users', JSON.stringify(users));
        }

        function saveRoles() {
            localStorage.setItem('anw_roles', JSON.stringify(roles));
        }

        function saveSessions() {
            localStorage.setItem('anw_sessions', JSON.stringify(sessions));
        }
        // عرض ملخص المستخدمين
        function displayUsersSummary() {
            const summaryDiv = document.getElementById('usersSummary');

            const totalUsers = users.length;
            const activeUsers = users.filter(u => u.active).length;
            const activeSessions = sessions.filter(s => s.status === 'active').length;
            const totalRoles = roles.length;

            summaryDiv.innerHTML = `
                <div class="col-md-3">
                    <div class="card border-primary">
                        <div class="card-body text-center">
                            <h5 class="card-title text-primary">${totalUsers}</h5>
                            <p class="card-text">إجمالي المستخدمين</p>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card border-success">
                        <div class="card-body text-center">
                            <h5 class="card-title text-success">${activeUsers}</h5>
                            <p class="card-text">المستخدمين النشطين</p>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card border-info">
                        <div class="card-body text-center">
                            <h5 class="card-title text-info">${activeSessions}</h5>
                            <p class="card-text">الجلسات النشطة</p>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card border-warning">
                        <div class="card-body text-center">
                            <h5 class="card-title text-warning">${totalRoles}</h5>
                            <p class="card-text">الأدوار المتاحة</p>
                        </div>
                    </div>
                </div>
            `;
        }

        // عرض المستخدمين
        function displayUsers() {
            const tbody = document.getElementById('usersTableBody');

            if (users.length === 0) {
                tbody.innerHTML = `
                    <tr>
                        <td colspan="7" class="text-center text-muted">
                            لا توجد مستخدمين
                            <br>
                            <button class="btn btn-primary mt-2" onclick="addUser()">إضافة مستخدم جديد</button>
                        </td>
                    </tr>
                `;
                return;
            }

            let html = '';
            users.forEach(user => {
                const role = roles.find(r => r.id === user.roleId);
                const statusBadge = user.active ?
                    '<span class="badge bg-success">نشط</span>' :
                    '<span class="badge bg-secondary">غير نشط</span>';

                const lastLogin = user.lastLogin ?
                    new Date(user.lastLogin).toLocaleDateString('ar-YE') + ' ' +
                    new Date(user.lastLogin).toLocaleTimeString('ar-YE', {hour: '2-digit', minute: '2-digit'}) :
                    'لم يدخل بعد';

                html += `
                    <tr>
                        <td>${user.username}</td>
                        <td>${user.firstName} ${user.lastName}</td>
                        <td>${user.email || '-'}</td>
                        <td><span class="badge bg-info">${role ? role.name : 'غير محدد'}</span></td>
                        <td>${lastLogin}</td>
                        <td>${statusBadge}</td>
                        <td>
                            <button class="btn btn-sm btn-warning" onclick="editUser(${user.id})">
                                <i class="fas fa-edit"></i>
                            </button>
                            <button class="btn btn-sm btn-info" onclick="resetPassword(${user.id})">
                                <i class="fas fa-key"></i>
                            </button>
                            <button class="btn btn-sm btn-danger" onclick="deleteUser(${user.id})">
                                <i class="fas fa-trash"></i>
                            </button>
                        </td>
                    </tr>
                `;
            });

            tbody.innerHTML = html;
        }

        // عرض الأدوار
        function displayRoles() {
            const container = document.getElementById('rolesContainer');
            let html = '';

            roles.forEach(role => {
                const permissionsCount = role.permissions ? role.permissions.length : 0;

                html += `
                    <div class="col-md-6 mb-3">
                        <div class="card">
                            <div class="card-body">
                                <div class="d-flex justify-content-between align-items-center">
                                    <div>
                                        <h6 class="card-title">${role.name}</h6>
                                        <p class="card-text text-muted">${role.description}</p>
                                        <small class="text-info">${permissionsCount} صلاحية</small>
                                    </div>
                                    <div>
                                        <button class="btn btn-sm btn-warning" onclick="editRole(${role.id})">
                                            <i class="fas fa-edit"></i>
                                        </button>
                                        <button class="btn btn-sm btn-danger" onclick="deleteRole(${role.id})">
                                            <i class="fas fa-trash"></i>
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                `;
            });

            container.innerHTML = html;
        }

        // عرض الجلسات
        function displaySessions() {
            const tbody = document.getElementById('sessionsTableBody');

            if (sessions.length === 0) {
                tbody.innerHTML = `
                    <tr>
                        <td colspan="6" class="text-center text-muted">لا توجد جلسات</td>
                    </tr>
                `;
                return;
            }

            let html = '';
            sessions.forEach(session => {
                const user = users.find(u => u.id === session.userId);
                const loginTime = new Date(session.loginTime).toLocaleString('ar-YE');
                const logoutTime = session.logoutTime ? new Date(session.logoutTime).toLocaleString('ar-YE') : '-';

                let duration = '-';
                if (session.logoutTime) {
                    const diff = new Date(session.logoutTime) - new Date(session.loginTime);
                    const hours = Math.floor(diff / (1000 * 60 * 60));
                    const minutes = Math.floor((diff % (1000 * 60 * 60)) / (1000 * 60));
                    duration = `${hours}س ${minutes}د`;
                }

                const statusBadge = session.status === 'active' ?
                    '<span class="badge bg-success">نشطة</span>' :
                    '<span class="badge bg-secondary">منتهية</span>';

                html += `
                    <tr>
                        <td>${user ? user.firstName + ' ' + user.lastName : 'غير محدد'}</td>
                        <td>${loginTime}</td>
                        <td>${logoutTime}</td>
                        <td>${duration}</td>
                        <td>${session.ipAddress}</td>
                        <td>${statusBadge}</td>
                    </tr>
                `;
            });

            tbody.innerHTML = html;
        }

        // تحميل خيارات الأدوار
        function loadRoleOptions() {
            const select = document.getElementById('userRole');

            let html = '<option value="">اختر الدور</option>';

            roles.forEach(role => {
                html += `<option value="${role.id}">${role.name}</option>`;
            });

            select.innerHTML = html;
        }

        // إضافة مستخدم جديد
        function addUser() {
            clearUserForm();
            document.querySelector('#userModal .modal-title').textContent = 'إضافة مستخدم جديد';
            new bootstrap.Modal(document.getElementById('userModal')).show();
        }

        // إضافة دور جديد
        function addRole() {
            clearRoleForm();
            loadPermissions();
            document.querySelector('#roleModal .modal-title').textContent = 'إضافة دور جديد';
            new bootstrap.Modal(document.getElementById('roleModal')).show();
        }

        // تحميل الصلاحيات
        function loadPermissions() {
            const container = document.getElementById('permissionsContainer');
            const categories = [...new Set(availablePermissions.map(p => p.category))];

            let html = '';
            categories.forEach(category => {
                html += `
                    <div class="col-md-6 mb-3">
                        <h6>${category}</h6>
                        ${availablePermissions.filter(p => p.category === category).map(permission => `
                            <div class="form-check">
                                <input type="checkbox" class="form-check-input" id="perm_${permission.id}" value="${permission.id}">
                                <label class="form-check-label" for="perm_${permission.id}">${permission.name}</label>
                            </div>
                        `).join('')}
                    </div>
                `;
            });

            container.innerHTML = html;
        }

        // حفظ المستخدم
        function saveUser() {
            const form = document.getElementById('userForm');
            const editId = form.dataset.editId;

            const userData = {
                username: document.getElementById('username').value,
                password: document.getElementById('password').value,
                firstName: document.getElementById('firstName').value,
                lastName: document.getElementById('lastName').value,
                email: document.getElementById('email').value,
                phone: document.getElementById('phone').value,
                roleId: parseInt(document.getElementById('userRole').value),
                notes: document.getElementById('userNotes').value,
                active: document.getElementById('isActive').checked
            };

            if (!userData.username || !userData.password || !userData.firstName || !userData.lastName || !userData.roleId) {
                showMessage('يرجى ملء جميع الحقول المطلوبة', 'danger');
                return;
            }

            // التحقق من عدم تكرار اسم المستخدم
            const existingUser = users.find(u => u.username === userData.username && u.id != editId);
            if (existingUser) {
                showMessage('اسم المستخدم موجود بالفعل', 'danger');
                return;
            }

            if (editId) {
                const userIndex = users.findIndex(u => u.id == editId);
                users[userIndex] = { ...users[userIndex], ...userData };
                showMessage('تم تحديث المستخدم بنجاح', 'success');
            } else {
                userData.id = nextUserId++;
                userData.lastLogin = null;
                users.push(userData);
                showMessage('تم إضافة المستخدم بنجاح', 'success');
            }

            saveUsers();
            loadAllData();
            bootstrap.Modal.getInstance(document.getElementById('userModal')).hide();
        }

        // حفظ الدور
        function saveRole() {
            const roleData = {
                name: document.getElementById('roleName').value,
                description: document.getElementById('roleDescription').value,
                permissions: []
            };

            // جمع الصلاحيات المحددة
            document.querySelectorAll('#permissionsContainer input[type="checkbox"]:checked').forEach(checkbox => {
                roleData.permissions.push(checkbox.value);
            });

            if (!roleData.name) {
                showMessage('يرجى إدخال اسم الدور', 'danger');
                return;
            }

            roleData.id = nextRoleId++;
            roles.push(roleData);

            saveRoles();
            loadAllData();
            bootstrap.Modal.getInstance(document.getElementById('roleModal')).hide();
            showMessage('تم إضافة الدور بنجاح', 'success');
        }

        // تنظيف النماذج
        function clearUserForm() {
            document.getElementById('userForm').reset();
            document.getElementById('userForm').removeAttribute('data-edit-id');
            document.getElementById('isActive').checked = true;
        }

        function clearRoleForm() {
            document.getElementById('roleForm').reset();
        }

        // تبديل التبويبات
        function showTab(tabName) {
            document.getElementById('usersTab').style.display = 'none';
            document.getElementById('rolesTab').style.display = 'none';
            document.getElementById('sessionsTab').style.display = 'none';

            document.querySelectorAll('.nav-link').forEach(link => {
                link.classList.remove('active');
            });

            if (tabName === 'users') {
                document.getElementById('usersTab').style.display = 'block';
            } else if (tabName === 'roles') {
                document.getElementById('rolesTab').style.display = 'block';
            } else if (tabName === 'sessions') {
                document.getElementById('sessionsTab').style.display = 'block';
            }

            event.target.classList.add('active');
        }

        // عرض رسالة
        function showMessage(message, type) {
            const messagesDiv = document.getElementById('messages');
            const alertHtml = `
                <div class="alert alert-${type} alert-dismissible fade show position-fixed"
                     style="top: 20px; left: 20px; z-index: 9999; min-width: 300px;">
                    ${message}
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
            `;
            messagesDiv.innerHTML = alertHtml;

            setTimeout(() => {
                const alert = messagesDiv.querySelector('.alert');
                if (alert) {
                    bootstrap.Alert.getOrCreateInstance(alert).close();
                }
            }, 3000);
        }

        console.log('✅ تم تحميل نظام إدارة المستخدمين بنجاح');
    </script>

    <style>
        .sidebar {
            height: 100vh;
            position: fixed;
            width: 25%;
        }
        .nav-link.active {
            background-color: rgba(255,255,255,0.1);
            border-radius: 5px;
        }
        .table th {
            background-color: #343a40;
            color: white;
        }
        .tab-content {
            animation: fadeIn 0.3s;
        }
        @keyframes fadeIn {
            from { opacity: 0; }
            to { opacity: 1; }
        }
    </style>
</body>
</html>
