using System.ComponentModel.DataAnnotations;

namespace ANWBakerySystem.Models
{
    /// <summary>
    /// نموذج الصلاحيات
    /// Permissions model
    /// </summary>
    public class Permission
    {
        [Key]
        public int PermissionId { get; set; }

        [Required]
        [StringLength(100)]
        [Display(Name = "اسم الصلاحية")]
        public string PermissionName { get; set; } = string.Empty;

        [Required]
        [StringLength(200)]
        [Display(Name = "عرض الصلاحية")]
        public string DisplayName { get; set; } = string.Empty;

        [StringLength(500)]
        [Display(Name = "الوصف")]
        public string? Description { get; set; }

        [Required]
        [StringLength(100)]
        [Display(Name = "اسم الوحدة")]
        public string ModuleName { get; set; } = string.Empty;

        [Required]
        [StringLength(200)]
        [Display(Name = "عرض الوحدة")]
        public string ModuleDisplayName { get; set; } = string.Empty;

        [Display(Name = "ترتيب العرض")]
        public int DisplayOrder { get; set; } = 0;

        [Display(Name = "نشط")]
        public bool IsActive { get; set; } = true;

        [Display(Name = "تاريخ الإنشاء")]
        public DateTime CreatedAt { get; set; } = DateTime.Now;

        // Navigation properties
        public virtual ICollection<UserPermission> UserPermissions { get; set; } = new List<UserPermission>();
    }

    // تم نقل تعريف UserPermission إلى ملف منفصل لتجنب التضارب
    // UserPermission definition moved to separate file to avoid conflicts
}
