<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>الفواتير - نظام ANW</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
</head>
<body>
    <div class="container-fluid">
        <div class="row">
            <!-- Sidebar -->
            <div class="col-md-3 bg-dark text-white p-0">
                <div class="sidebar">
                    <div class="p-3 border-bottom">
                        <h5><i class="fas fa-calculator me-2"></i>نظام ANW</h5>
                        <small>إدارة مخبوزات مبسط</small>
                    </div>
                    <nav class="nav flex-column p-3">
                        <a href="items-simple.html" class="nav-link text-white">
                            <i class="fas fa-boxes me-2"></i>المنتجات والخامات
                        </a>
                        <a href="parties-simple.html" class="nav-link text-white">
                            <i class="fas fa-users me-2"></i>العملاء والموردين
                        </a>
                        <a href="employees-simple.html" class="nav-link text-white">
                            <i class="fas fa-user-tie me-2"></i>الموظفين
                        </a>
                        <a href="invoices-simple.html" class="nav-link text-white active">
                            <i class="fas fa-file-invoice me-2"></i>الفواتير
                        </a>
                        <a href="inventory-simple.html" class="nav-link text-white">
                            <i class="fas fa-warehouse me-2"></i>المخزون
                        </a>
                        <a href="cash-simple.html" class="nav-link text-white">
                            <i class="fas fa-cash-register me-2"></i>الصناديق
                        </a>
                        <a href="banks-simple.html" class="nav-link text-white">
                            <i class="fas fa-university me-2"></i>البنوك
                        </a>
                        <a href="owners-simple.html" class="nav-link text-white">
                            <i class="fas fa-crown me-2"></i>الملاك
                        </a>
                        <a href="users-simple.html" class="nav-link text-white">
                            <i class="fas fa-user-cog me-2"></i>المستخدمين
                        </a>
                        <a href="reports-simple.html" class="nav-link text-white">
                            <i class="fas fa-chart-bar me-2"></i>التقارير
                        </a>
                    </nav>
                </div>
            </div>

            <!-- Main Content -->
            <div class="col-md-9">
                <div class="p-4">
                    <!-- Header -->
                    <div class="d-flex justify-content-between align-items-center mb-4">
                        <h2><i class="fas fa-file-invoice text-primary me-2"></i>إدارة الفواتير</h2>
                        <div>
                            <button class="btn btn-success" onclick="addSalesInvoice()">
                                <i class="fas fa-plus me-1"></i>فاتورة مبيعات
                            </button>
                            <button class="btn btn-primary" onclick="addPurchaseInvoice()">
                                <i class="fas fa-plus me-1"></i>فاتورة مشتريات
                            </button>
                            <button class="btn btn-warning" onclick="addReturnInvoice()">
                                <i class="fas fa-undo me-1"></i>فاتورة مرتجع
                            </button>
                        </div>
                    </div>

                    <!-- Filter Tabs -->
                    <ul class="nav nav-tabs mb-3">
                        <li class="nav-item">
                            <a class="nav-link active" onclick="filterInvoices('all')">جميع الفواتير</a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" onclick="filterInvoices('sales')">المبيعات</a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" onclick="filterInvoices('purchase')">المشتريات</a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" onclick="filterInvoices('return')">المرتجعات</a>
                        </li>
                    </ul>

                    <!-- Search and Filter -->
                    <div class="row mb-3">
                        <div class="col-md-4">
                            <input type="text" id="searchInput" class="form-control" placeholder="البحث برقم الفاتورة أو اسم العميل..." onkeyup="searchInvoices()">
                        </div>
                        <div class="col-md-3">
                            <select id="partyFilter" class="form-control" onchange="filterInvoices()">
                                <option value="">جميع الأطراف</option>
                            </select>
                        </div>
                        <div class="col-md-2">
                            <input type="date" id="dateFromFilter" class="form-control" onchange="filterInvoices()">
                        </div>
                        <div class="col-md-2">
                            <input type="date" id="dateToFilter" class="form-control" onchange="filterInvoices()">
                        </div>
                        <div class="col-md-1">
                            <button class="btn btn-secondary" onclick="clearFilters()">
                                <i class="fas fa-times"></i>
                            </button>
                        </div>
                    </div>

                    <!-- Invoices Table -->
                    <div class="card">
                        <div class="card-body">
                            <div class="table-responsive">
                                <table class="table table-striped table-hover">
                                    <thead class="table-dark">
                                        <tr>
                                            <th>رقم الفاتورة</th>
                                            <th>النوع</th>
                                            <th>التاريخ</th>
                                            <th>العميل/المورد</th>
                                            <th>الإجمالي</th>
                                            <th>المدفوع</th>
                                            <th>المتبقي</th>
                                            <th>الحالة</th>
                                            <th>الإجراءات</th>
                                        </tr>
                                    </thead>
                                    <tbody id="invoicesTableBody">
                                        <tr>
                                            <td colspan="9" class="text-center">
                                                <div class="spinner-border text-primary" role="status">
                                                    <span class="visually-hidden">جاري التحميل...</span>
                                                </div>
                                                <p class="mt-2">جاري تحميل الفواتير...</p>
                                            </td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Invoice Modal -->
    <div class="modal fade" id="invoiceModal" tabindex="-1">
        <div class="modal-dialog modal-xl">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">فاتورة جديدة</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <form id="invoiceForm">
                        <!-- Invoice Header -->
                        <div class="row mb-3">
                            <div class="col-md-3">
                                <label class="form-label">نوع الفاتورة *</label>
                                <select id="invoiceType" class="form-control" required onchange="updatePartyOptions()">
                                    <option value="">اختر النوع</option>
                                    <option value="sales">مبيعات</option>
                                    <option value="purchase">مشتريات</option>
                                    <option value="return">مرتجع</option>
                                </select>
                            </div>
                            <div class="col-md-3">
                                <label class="form-label">العميل/المورد *</label>
                                <select id="invoiceParty" class="form-control" required>
                                    <option value="">اختر الطرف</option>
                                </select>
                            </div>
                            <div class="col-md-3">
                                <label class="form-label">التاريخ *</label>
                                <input type="date" id="invoiceDate" class="form-control" required>
                            </div>
                            <div class="col-md-3">
                                <label class="form-label">طريقة الدفع</label>
                                <select id="paymentMethod" class="form-control">
                                    <option value="cash">نقدي</option>
                                    <option value="credit">آجل</option>
                                    <option value="partial">جزئي</option>
                                </select>
                            </div>
                        </div>

                        <!-- Invoice Items -->
                        <div class="card mb-3">
                            <div class="card-header d-flex justify-content-between">
                                <h6>أصناف الفاتورة</h6>
                                <button type="button" class="btn btn-sm btn-success" onclick="addInvoiceItem()">
                                    <i class="fas fa-plus"></i> إضافة صنف
                                </button>
                            </div>
                            <div class="card-body">
                                <div class="table-responsive">
                                    <table class="table table-sm">
                                        <thead>
                                            <tr>
                                                <th>الصنف</th>
                                                <th>الكمية</th>
                                                <th>الوحدة</th>
                                                <th>السعر</th>
                                                <th>الإجمالي</th>
                                                <th>حذف</th>
                                            </tr>
                                        </thead>
                                        <tbody id="invoiceItemsTable">
                                            <!-- سيتم ملؤها بـ JavaScript -->
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                        </div>

                        <!-- Invoice Totals -->
                        <div class="row">
                            <div class="col-md-8">
                                <div class="mb-3">
                                    <label class="form-label">ملاحظات</label>
                                    <textarea id="invoiceNotes" class="form-control" rows="2"></textarea>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="card">
                                    <div class="card-body">
                                        <div class="d-flex justify-content-between">
                                            <span>المجموع الفرعي:</span>
                                            <span id="subtotal">0.00 ر.ي</span>
                                        </div>
                                        <div class="d-flex justify-content-between">
                                            <span>الخصم:</span>
                                            <input type="number" id="discountAmount" class="form-control form-control-sm" value="0" step="0.01" onchange="calculateTotals()">
                                        </div>
                                        <hr>
                                        <div class="d-flex justify-content-between fw-bold">
                                            <span>الإجمالي:</span>
                                            <span id="totalAmount">0.00 ر.ي</span>
                                        </div>
                                        <div class="d-flex justify-content-between mt-2">
                                            <span>المدفوع:</span>
                                            <input type="number" id="paidAmount" class="form-control form-control-sm" value="0" step="0.01" onchange="calculateTotals()">
                                        </div>
                                        <div class="d-flex justify-content-between text-danger">
                                            <span>المتبقي:</span>
                                            <span id="remainingAmount">0.00 ر.ي</span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                    <button type="button" class="btn btn-info" onclick="printInvoice()">طباعة</button>
                    <button type="button" class="btn btn-primary" onclick="saveInvoice()">حفظ</button>
                </div>
            </div>
        </div>
    </div>

    <!-- Payment Modal -->
    <div class="modal fade" id="paymentModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">تسديد فاتورة</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <form id="paymentForm">
                        <div class="mb-3">
                            <label class="form-label">رقم الفاتورة</label>
                            <input type="text" id="paymentInvoiceNumber" class="form-control" readonly>
                        </div>
                        <div class="mb-3">
                            <label class="form-label">المبلغ المتبقي</label>
                            <input type="text" id="paymentRemainingAmount" class="form-control" readonly>
                        </div>
                        <div class="mb-3">
                            <label class="form-label">المبلغ المدفوع *</label>
                            <input type="number" id="paymentAmount" class="form-control" required step="0.01">
                        </div>
                        <div class="mb-3">
                            <label class="form-label">الصندوق *</label>
                            <select id="paymentCashBox" class="form-control" required>
                                <!-- سيتم ملؤها بـ JavaScript -->
                            </select>
                        </div>
                        <div class="mb-3">
                            <label class="form-label">ملاحظات</label>
                            <textarea id="paymentNotes" class="form-control" rows="2"></textarea>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                    <button type="button" class="btn btn-primary" onclick="savePayment()">تسديد</button>
                </div>
            </div>
        </div>
    </div>

    <!-- Success/Error Messages -->
    <div id="messages"></div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // البيانات المحلية
        let invoices = [];
        let parties = [];
        let items = [];
        let cashBoxes = [];
        let availableUnits = [];
        let nextInvoiceId = 1;
        let currentInvoiceItems = [];
        let currentFilter = 'all';

        // تحميل البيانات عند بدء الصفحة
        document.addEventListener('DOMContentLoaded', function() {
            loadAllData();
            document.getElementById('invoiceDate').value = new Date().toISOString().split('T')[0];
        });

        // تحميل جميع البيانات
        function loadAllData() {
            loadParties();
            loadItems();
            loadCashBoxes();
            loadUnits();
            loadInvoices();
            loadPartyOptions();
            loadCashBoxOptions();
            displayInvoices();
        }

        // تحميل وحدات القياس
        function loadUnits() {
            try {
                const savedUnits = localStorage.getItem('anw_units');
                if (savedUnits && savedUnits !== 'null') {
                    availableUnits = JSON.parse(savedUnits).filter(u => u.active);
                } else {
                    availableUnits = [];
                }
            } catch (error) {
                console.error('خطأ في تحميل وحدات القياس:', error);
                availableUnits = [];
            }
        }

        // تحميل الأطراف
        function loadParties() {
            const savedParties = localStorage.getItem('anw_parties');
            if (savedParties) {
                parties = JSON.parse(savedParties);
            }
        }

        // تحميل الأصناف
        function loadItems() {
            const savedItems = localStorage.getItem('anw_items');
            if (savedItems) {
                items = JSON.parse(savedItems);
            }
        }

        // تحميل الصناديق
        function loadCashBoxes() {
            const savedCashBoxes = localStorage.getItem('anw_cashboxes');
            if (savedCashBoxes) {
                cashBoxes = JSON.parse(savedCashBoxes);
            }
        }

        // تحميل الفواتير
        function loadInvoices() {
            try {
                const savedInvoices = localStorage.getItem('anw_invoices');
                if (savedInvoices && savedInvoices !== 'null') {
                    invoices = JSON.parse(savedInvoices);
                    if (invoices.length > 0) {
                        nextInvoiceId = Math.max(...invoices.map(i => i.id), 0) + 1;
                    }
                } else {
                    // إنشاء فواتير افتراضية
                    invoices = [
                        {
                            id: 1,
                            number: 'S001',
                            type: 'sales',
                            date: '2024-01-15',
                            partyId: 1,
                            subtotal: 100000,
                            discount: 5000,
                            total: 95000,
                            paid: 50000,
                            remaining: 45000,
                            status: 'partial',
                            items: [
                                {itemId: 1, quantity: 100, price: 75, total: 7500},
                                {itemId: 2, quantity: 50, price: 1750, total: 87500}
                            ],
                            notes: 'فاتورة مبيعات للعميل الأول'
                        }
                    ];
                    nextInvoiceId = 2;
                    saveInvoices();
                }
                displayInvoices();
            } catch (error) {
                console.error('خطأ في تحميل الفواتير:', error);
                showMessage('خطأ في تحميل البيانات', 'danger');
            }
        }

        // حفظ الفواتير
        function saveInvoices() {
            localStorage.setItem('anw_invoices', JSON.stringify(invoices));
        }

        // عرض الفواتير
        function displayInvoices() {
            const tbody = document.getElementById('invoicesTableBody');
            let filteredInvoices = invoices;

            // تطبيق الفلتر
            if (currentFilter !== 'all') {
                filteredInvoices = invoices.filter(inv => inv.type === currentFilter);
            }

            if (filteredInvoices.length === 0) {
                tbody.innerHTML = `
                    <tr>
                        <td colspan="9" class="text-center text-muted">
                            لا توجد فواتير
                            <br>
                            <button class="btn btn-primary mt-2" onclick="addSalesInvoice()">إضافة فاتورة مبيعات</button>
                        </td>
                    </tr>
                `;
                return;
            }

            let html = '';
            filteredInvoices.forEach(invoice => {
                const party = parties.find(p => p.id === invoice.partyId);
                const typeLabel = getInvoiceTypeLabel(invoice.type);
                const typeBadge = getInvoiceTypeBadge(invoice.type);
                const statusBadge = getStatusBadge(invoice.status);

                html += `
                    <tr>
                        <td>${invoice.number}</td>
                        <td><span class="badge ${typeBadge}">${typeLabel}</span></td>
                        <td>${new Date(invoice.date).toLocaleDateString('ar-YE')}</td>
                        <td>${party ? party.name : 'غير محدد'}</td>
                        <td class="fw-bold">${invoice.total.toLocaleString()} ر.ي</td>
                        <td class="text-success">${invoice.paid.toLocaleString()} ر.ي</td>
                        <td class="text-danger">${invoice.remaining.toLocaleString()} ر.ي</td>
                        <td>${statusBadge}</td>
                        <td>
                            <button class="btn btn-sm btn-info" onclick="viewInvoice(${invoice.id})">
                                <i class="fas fa-eye"></i>
                            </button>
                            <button class="btn btn-sm btn-success" onclick="payInvoice(${invoice.id})" ${invoice.remaining <= 0 ? 'disabled' : ''}>
                                <i class="fas fa-money-bill"></i>
                            </button>
                            <button class="btn btn-sm btn-warning" onclick="printInvoiceById(${invoice.id})">
                                <i class="fas fa-print"></i>
                            </button>
                            <button class="btn btn-sm btn-danger" onclick="deleteInvoice(${invoice.id})">
                                <i class="fas fa-trash"></i>
                            </button>
                        </td>
                    </tr>
                `;
            });

            tbody.innerHTML = html;
        }

        // الحصول على تسمية نوع الفاتورة
        function getInvoiceTypeLabel(type) {
            const types = {
                'sales': 'مبيعات',
                'purchase': 'مشتريات',
                'return': 'مرتجع'
            };
            return types[type] || type;
        }

        // الحصول على شارة نوع الفاتورة
        function getInvoiceTypeBadge(type) {
            const badges = {
                'sales': 'bg-success',
                'purchase': 'bg-primary',
                'return': 'bg-warning'
            };
            return badges[type] || 'bg-secondary';
        }

        // الحصول على شارة الحالة
        function getStatusBadge(status) {
            if (status === 'paid') {
                return '<span class="badge bg-success">مدفوعة</span>';
            } else if (status === 'partial') {
                return '<span class="badge bg-warning">جزئية</span>';
            } else {
                return '<span class="badge bg-danger">غير مدفوعة</span>';
            }
        }
        // تحميل خيارات الأطراف
        function loadPartyOptions() {
            const select = document.getElementById('invoiceParty');
            const filterSelect = document.getElementById('partyFilter');

            let html = '<option value="">اختر الطرف</option>';
            let filterHtml = '<option value="">جميع الأطراف</option>';

            parties.forEach(party => {
                html += `<option value="${party.id}">${party.name}</option>`;
                filterHtml += `<option value="${party.id}">${party.name}</option>`;
            });

            select.innerHTML = html;
            filterSelect.innerHTML = filterHtml;
        }

        // تحميل خيارات الصناديق
        function loadCashBoxOptions() {
            const select = document.getElementById('paymentCashBox');

            let html = '<option value="">اختر الصندوق</option>';

            cashBoxes.filter(c => c.active).forEach(cashBox => {
                html += `<option value="${cashBox.id}">${cashBox.name}</option>`;
            });

            select.innerHTML = html;
        }

        // إضافة فاتورة مبيعات
        function addSalesInvoice() {
            clearInvoiceForm();
            document.getElementById('invoiceType').value = 'sales';
            updatePartyOptions();
            document.querySelector('#invoiceModal .modal-title').textContent = 'فاتورة مبيعات جديدة';
            new bootstrap.Modal(document.getElementById('invoiceModal')).show();
        }

        // إضافة فاتورة مشتريات
        function addPurchaseInvoice() {
            clearInvoiceForm();
            document.getElementById('invoiceType').value = 'purchase';
            updatePartyOptions();
            document.querySelector('#invoiceModal .modal-title').textContent = 'فاتورة مشتريات جديدة';
            new bootstrap.Modal(document.getElementById('invoiceModal')).show();
        }

        // إضافة فاتورة مرتجع
        function addReturnInvoice() {
            clearInvoiceForm();
            document.getElementById('invoiceType').value = 'return';
            updatePartyOptions();
            document.querySelector('#invoiceModal .modal-title').textContent = 'فاتورة مرتجع جديدة';
            new bootstrap.Modal(document.getElementById('invoiceModal')).show();
        }

        // تحديث خيارات الأطراف حسب نوع الفاتورة
        function updatePartyOptions() {
            const invoiceType = document.getElementById('invoiceType').value;
            const select = document.getElementById('invoiceParty');

            let html = '<option value="">اختر الطرف</option>';

            parties.forEach(party => {
                let canAdd = false;
                if (invoiceType === 'sales' && (party.type === 'customer' || party.type === 'both')) {
                    canAdd = true;
                } else if (invoiceType === 'purchase' && (party.type === 'supplier' || party.type === 'both')) {
                    canAdd = true;
                } else if (invoiceType === 'return') {
                    canAdd = true;
                }

                if (canAdd) {
                    html += `<option value="${party.id}">${party.name}</option>`;
                }
            });

            select.innerHTML = html;
        }

        // إضافة صنف للفاتورة
        function addInvoiceItem() {
            const tbody = document.getElementById('invoiceItemsTable');
            const rowIndex = currentInvoiceItems.length;

            const row = `
                <tr data-index="${rowIndex}">
                    <td>
                        <select class="form-control item-select" onchange="updateItemPrice(${rowIndex})">
                            <option value="">اختر الصنف</option>
                            ${items.map(item => `<option value="${item.id}" data-price="${item.salePrice}">${item.name}</option>`).join('')}
                        </select>
                    </td>
                    <td>
                        <input type="number" class="form-control quantity-input" value="1" min="1" step="0.01" onchange="calculateItemTotal(${rowIndex})">
                    </td>
                    <td>
                        <select class="form-control unit-select" onchange="updateUnitPrice(${rowIndex})" style="display: none;">
                            <option value="">اختر الوحدة</option>
                        </select>
                        <span class="unit-label">قطعة</span>
                    </td>
                    <td>
                        <input type="number" class="form-control price-input" value="0" step="0.01" onchange="calculateItemTotal(${rowIndex})">
                    </td>
                    <td>
                        <span class="item-total">0.00 ر.ي</span>
                    </td>
                    <td>
                        <button type="button" class="btn btn-sm btn-danger" onclick="removeInvoiceItem(${rowIndex})">
                            <i class="fas fa-trash"></i>
                        </button>
                    </td>
                </tr>
            `;

            tbody.insertAdjacentHTML('beforeend', row);
            currentInvoiceItems.push({itemId: null, quantity: 1, unit: '', unitName: '', price: 0, total: 0});
        }

        // تحديث سعر الصنف
        function updateItemPrice(index) {
            const row = document.querySelector(`tr[data-index="${index}"]`);
            const itemSelect = row.querySelector('.item-select');
            const unitSelect = row.querySelector('.unit-select');
            const unitLabel = row.querySelector('.unit-label');
            const priceInput = row.querySelector('.price-input');

            if (itemSelect.value) {
                const item = items.find(i => i.id == itemSelect.value);
                if (item) {
                    currentInvoiceItems[index].itemId = parseInt(itemSelect.value);

                    // إذا كانت خامة مع وحدات قياس
                    if (item.type === 'material' && item.unitGroupId && item.unitPrices) {
                        const unit = availableUnits.find(u => u.id === item.unitGroupId);
                        if (unit) {
                            // إظهار قائمة الوحدات
                            unitSelect.style.display = 'block';
                            unitLabel.style.display = 'none';

                            // تحديث خيارات الوحدات
                            let unitsHtml = '<option value="">اختر الوحدة</option>';
                            unitsHtml += `<option value="major" data-price="${item.unitPrices.majorUnitPrice}">${unit.majorUnit} - ${item.unitPrices.majorUnitPrice.toLocaleString()} ر.ي</option>`;
                            unitsHtml += `<option value="medium" data-price="${item.unitPrices.mediumUnitPrice}">${unit.mediumUnit} - ${item.unitPrices.mediumUnitPrice.toLocaleString()} ر.ي</option>`;
                            unitsHtml += `<option value="minor" data-price="${item.unitPrices.minorUnitPrice}">${unit.minorUnit} - ${item.unitPrices.minorUnitPrice.toLocaleString()} ر.ي</option>`;

                            unitSelect.innerHTML = unitsHtml;
                            priceInput.value = '';

                            return;
                        }
                    }

                    // للمنتجات والخدمات أو الخامات بدون وحدات
                    unitSelect.style.display = 'none';
                    unitLabel.style.display = 'inline';

                    const invoiceType = document.getElementById('invoiceType').value;
                    const price = invoiceType === 'sale' ? item.salePrice : item.purchasePrice;

                    priceInput.value = price;
                    unitLabel.textContent = getUnitLabel(item.unit);

                    currentInvoiceItems[index].price = price;
                    currentInvoiceItems[index].unit = '';
                    currentInvoiceItems[index].unitName = getUnitLabel(item.unit);

                    calculateItemTotal(index);
                }
            }
        }

        // تحديث سعر الوحدة
        function updateUnitPrice(index) {
            const row = document.querySelector(`tr[data-index="${index}"]`);
            const unitSelect = row.querySelector('.unit-select');
            const priceInput = row.querySelector('.price-input');

            if (unitSelect.value) {
                const selectedOption = unitSelect.options[unitSelect.selectedIndex];
                const unitPrice = parseFloat(selectedOption.getAttribute('data-price')) || 0;

                priceInput.value = unitPrice;

                currentInvoiceItems[index].unit = unitSelect.value;
                currentInvoiceItems[index].unitName = selectedOption.text.split(' - ')[0];
                currentInvoiceItems[index].price = unitPrice;

                calculateItemTotal(index);
            }
        }

        // حساب إجمالي الصنف
        function calculateItemTotal(index) {
            const row = document.querySelector(`tr[data-index="${index}"]`);
            const quantity = parseFloat(row.querySelector('.quantity-input').value) || 0;
            const price = parseFloat(row.querySelector('.price-input').value) || 0;
            const total = quantity * price;

            row.querySelector('.item-total').textContent = total.toLocaleString() + ' ر.ي';

            currentInvoiceItems[index].quantity = quantity;
            currentInvoiceItems[index].price = price;
            currentInvoiceItems[index].total = total;

            calculateTotals();
        }

        // حذف صنف من الفاتورة
        function removeInvoiceItem(index) {
            const row = document.querySelector(`tr[data-index="${index}"]`);
            row.remove();
            currentInvoiceItems.splice(index, 1);
            calculateTotals();
        }

        // حساب الإجماليات
        function calculateTotals() {
            const subtotal = currentInvoiceItems.reduce((sum, item) => sum + item.total, 0);
            const discount = parseFloat(document.getElementById('discountAmount').value) || 0;
            const total = subtotal - discount;
            const paid = parseFloat(document.getElementById('paidAmount').value) || 0;
            const remaining = total - paid;

            document.getElementById('subtotal').textContent = subtotal.toLocaleString() + ' ر.ي';
            document.getElementById('totalAmount').textContent = total.toLocaleString() + ' ر.ي';
            document.getElementById('remainingAmount').textContent = remaining.toLocaleString() + ' ر.ي';
        }

        // الحصول على تسمية الوحدة
        function getUnitLabel(unit) {
            const units = {
                'piece': 'قطعة',
                'kg': 'كيلوجرام',
                'bag': 'كيس',
                'box': 'صندوق',
                'liter': 'لتر'
            };
            return units[unit] || unit;
        }
        // حفظ الفاتورة
        function saveInvoice() {
            const invoiceData = {
                type: document.getElementById('invoiceType').value,
                partyId: parseInt(document.getElementById('invoiceParty').value),
                date: document.getElementById('invoiceDate').value,
                paymentMethod: document.getElementById('paymentMethod').value,
                notes: document.getElementById('invoiceNotes').value,
                items: currentInvoiceItems.filter(item => item.itemId),
                subtotal: currentInvoiceItems.reduce((sum, item) => sum + item.total, 0),
                discount: parseFloat(document.getElementById('discountAmount').value) || 0,
                paid: parseFloat(document.getElementById('paidAmount').value) || 0
            };

            // التحقق من البيانات
            if (!invoiceData.type || !invoiceData.partyId || invoiceData.items.length === 0) {
                showMessage('يرجى ملء جميع الحقول المطلوبة وإضافة أصناف', 'danger');
                return;
            }

            // حساب الإجماليات
            invoiceData.total = invoiceData.subtotal - invoiceData.discount;
            invoiceData.remaining = invoiceData.total - invoiceData.paid;

            // تحديد الحالة
            if (invoiceData.remaining <= 0) {
                invoiceData.status = 'paid';
            } else if (invoiceData.paid > 0) {
                invoiceData.status = 'partial';
            } else {
                invoiceData.status = 'unpaid';
            }

            // إنشاء رقم الفاتورة
            const prefix = invoiceData.type === 'sales' ? 'S' : invoiceData.type === 'purchase' ? 'P' : 'R';
            const count = invoices.filter(i => i.type === invoiceData.type).length + 1;
            invoiceData.number = `${prefix}${count.toString().padStart(3, '0')}`;
            invoiceData.id = nextInvoiceId++;

            // تحديث رصيد العميل/المورد
            updatePartyBalance(invoiceData);

            // تحديث المخزون
            updateInventory(invoiceData);

            // إنشاء سند قبض/صرف إذا كان هناك مبلغ مدفوع
            if (invoiceData.paid > 0) {
                createVoucherForInvoice(invoiceData);
            }

            invoices.push(invoiceData);
            saveInvoices();
            displayInvoices();
            bootstrap.Modal.getInstance(document.getElementById('invoiceModal')).hide();
            showMessage('تم حفظ الفاتورة بنجاح', 'success');
        }

        // تحديث رصيد العميل/المورد
        function updatePartyBalance(invoice) {
            const party = parties.find(p => p.id === invoice.partyId);
            if (party) {
                if (invoice.type === 'sales') {
                    party.balance += invoice.remaining; // العميل مدين
                } else if (invoice.type === 'purchase') {
                    party.balance -= invoice.remaining; // المورد دائن
                }
                localStorage.setItem('anw_parties', JSON.stringify(parties));
            }
        }

        // تحديث المخزون
        function updateInventory(invoice) {
            invoice.items.forEach(invoiceItem => {
                const item = items.find(i => i.id === invoiceItem.itemId);
                if (item) {
                    if (invoice.type === 'sales') {
                        item.stock -= invoiceItem.quantity; // خروج من المخزون
                    } else if (invoice.type === 'purchase') {
                        item.stock += invoiceItem.quantity; // دخول للمخزون
                    } else if (invoice.type === 'return') {
                        item.stock += invoiceItem.quantity; // إرجاع للمخزون
                    }
                }
            });
            localStorage.setItem('anw_items', JSON.stringify(items));
        }

        // إنشاء سند قبض/صرف للفاتورة
        function createVoucherForInvoice(invoice) {
            const vouchers = JSON.parse(localStorage.getItem('anw_vouchers') || '[]');
            const party = parties.find(p => p.id === invoice.partyId);

            const voucher = {
                id: Date.now(),
                number: `${invoice.type === 'sales' ? 'R' : 'P'}${(vouchers.length + 1).toString().padStart(3, '0')}`,
                type: invoice.type === 'sales' ? 'receipt' : 'payment',
                date: invoice.date,
                cashBoxId: 1, // الصندوق الافتراضي
                amount: invoice.paid,
                description: `${invoice.type === 'sales' ? 'تحصيل' : 'دفع'} فاتورة ${invoice.number}`,
                party: party ? party.name : 'غير محدد'
            };

            vouchers.push(voucher);
            localStorage.setItem('anw_vouchers', JSON.stringify(vouchers));

            // تحديث رصيد الصندوق
            if (cashBoxes.length > 0) {
                if (voucher.type === 'receipt') {
                    cashBoxes[0].balance += voucher.amount;
                } else {
                    cashBoxes[0].balance -= voucher.amount;
                }
                localStorage.setItem('anw_cashboxes', JSON.stringify(cashBoxes));
            }
        }

        // تسديد فاتورة
        function payInvoice(invoiceId) {
            const invoice = invoices.find(i => i.id === invoiceId);
            if (invoice && invoice.remaining > 0) {
                document.getElementById('paymentInvoiceNumber').value = invoice.number;
                document.getElementById('paymentRemainingAmount').value = invoice.remaining.toLocaleString() + ' ر.ي';
                document.getElementById('paymentAmount').value = invoice.remaining;

                document.getElementById('paymentForm').dataset.invoiceId = invoiceId;
                new bootstrap.Modal(document.getElementById('paymentModal')).show();
            }
        }

        // حفظ التسديد
        function savePayment() {
            const invoiceId = parseInt(document.getElementById('paymentForm').dataset.invoiceId);
            const paymentAmount = parseFloat(document.getElementById('paymentAmount').value);
            const cashBoxId = parseInt(document.getElementById('paymentCashBox').value);
            const notes = document.getElementById('paymentNotes').value;

            if (!paymentAmount || !cashBoxId) {
                showMessage('يرجى ملء جميع الحقول المطلوبة', 'danger');
                return;
            }

            const invoice = invoices.find(i => i.id === invoiceId);
            if (invoice) {
                // تحديث الفاتورة
                invoice.paid += paymentAmount;
                invoice.remaining -= paymentAmount;

                if (invoice.remaining <= 0) {
                    invoice.status = 'paid';
                } else {
                    invoice.status = 'partial';
                }

                // تحديث رصيد العميل/المورد
                const party = parties.find(p => p.id === invoice.partyId);
                if (party) {
                    if (invoice.type === 'sales') {
                        party.balance -= paymentAmount;
                    } else if (invoice.type === 'purchase') {
                        party.balance += paymentAmount;
                    }
                }

                // إنشاء سند قبض/صرف
                const vouchers = JSON.parse(localStorage.getItem('anw_vouchers') || '[]');
                const voucher = {
                    id: Date.now(),
                    number: `${invoice.type === 'sales' ? 'R' : 'P'}${(vouchers.length + 1).toString().padStart(3, '0')}`,
                    type: invoice.type === 'sales' ? 'receipt' : 'payment',
                    date: new Date().toISOString().split('T')[0],
                    cashBoxId: cashBoxId,
                    amount: paymentAmount,
                    description: `تسديد فاتورة ${invoice.number}`,
                    party: party ? party.name : 'غير محدد'
                };

                vouchers.push(voucher);
                localStorage.setItem('anw_vouchers', JSON.stringify(vouchers));

                // تحديث رصيد الصندوق
                const cashBox = cashBoxes.find(c => c.id === cashBoxId);
                if (cashBox) {
                    if (voucher.type === 'receipt') {
                        cashBox.balance += paymentAmount;
                    } else {
                        cashBox.balance -= paymentAmount;
                    }
                }

                // حفظ التحديثات
                saveInvoices();
                localStorage.setItem('anw_parties', JSON.stringify(parties));
                localStorage.setItem('anw_cashboxes', JSON.stringify(cashBoxes));

                displayInvoices();
                bootstrap.Modal.getInstance(document.getElementById('paymentModal')).hide();
                showMessage('تم التسديد بنجاح', 'success');
            }
        }
        // فلترة الفواتير
        function filterInvoices(filter) {
            if (filter) {
                currentFilter = filter;
                // تحديث التبويبات
                document.querySelectorAll('.nav-link').forEach(link => {
                    link.classList.remove('active');
                });
                event.target.classList.add('active');
            }
            displayInvoices();
        }

        // البحث في الفواتير
        function searchInvoices() {
            const searchTerm = document.getElementById('searchInput').value.toLowerCase();
            const tbody = document.getElementById('invoicesTableBody');

            let filteredInvoices = invoices.filter(invoice => {
                const party = parties.find(p => p.id === invoice.partyId);
                return invoice.number.toLowerCase().includes(searchTerm) ||
                       (party && party.name.toLowerCase().includes(searchTerm));
            });

            if (currentFilter !== 'all') {
                filteredInvoices = filteredInvoices.filter(inv => inv.type === currentFilter);
            }

            // عرض النتائج
            if (filteredInvoices.length === 0) {
                tbody.innerHTML = `<tr><td colspan="9" class="text-center text-muted">لا توجد نتائج للبحث</td></tr>`;
                return;
            }

            let html = '';
            filteredInvoices.forEach(invoice => {
                const party = parties.find(p => p.id === invoice.partyId);
                const typeLabel = getInvoiceTypeLabel(invoice.type);
                const typeBadge = getInvoiceTypeBadge(invoice.type);
                const statusBadge = getStatusBadge(invoice.status);

                html += `
                    <tr>
                        <td>${invoice.number}</td>
                        <td><span class="badge ${typeBadge}">${typeLabel}</span></td>
                        <td>${new Date(invoice.date).toLocaleDateString('ar-YE')}</td>
                        <td>${party ? party.name : 'غير محدد'}</td>
                        <td class="fw-bold">${invoice.total.toLocaleString()} ر.ي</td>
                        <td class="text-success">${invoice.paid.toLocaleString()} ر.ي</td>
                        <td class="text-danger">${invoice.remaining.toLocaleString()} ر.ي</td>
                        <td>${statusBadge}</td>
                        <td>
                            <button class="btn btn-sm btn-info" onclick="viewInvoice(${invoice.id})">
                                <i class="fas fa-eye"></i>
                            </button>
                            <button class="btn btn-sm btn-success" onclick="payInvoice(${invoice.id})" ${invoice.remaining <= 0 ? 'disabled' : ''}>
                                <i class="fas fa-money-bill"></i>
                            </button>
                            <button class="btn btn-sm btn-warning" onclick="printInvoiceById(${invoice.id})">
                                <i class="fas fa-print"></i>
                            </button>
                            <button class="btn btn-sm btn-danger" onclick="deleteInvoice(${invoice.id})">
                                <i class="fas fa-trash"></i>
                            </button>
                        </td>
                    </tr>
                `;
            });

            tbody.innerHTML = html;
        }

        // طباعة فاتورة
        function printInvoiceById(invoiceId) {
            const invoice = invoices.find(i => i.id === invoiceId);
            const party = parties.find(p => p.id === invoice.partyId);

            if (invoice) {
                let itemsHtml = '';
                invoice.items.forEach(item => {
                    const itemData = items.find(i => i.id === item.itemId);
                    const unitDisplay = item.unitName || (itemData ? getUnitLabel(itemData.unit) : 'قطعة');

                    itemsHtml += `
                        <tr>
                            <td>${itemData ? itemData.name : 'غير محدد'}</td>
                            <td>${item.quantity} ${unitDisplay}</td>
                            <td>${item.price.toLocaleString()}</td>
                            <td>${item.total.toLocaleString()}</td>
                        </tr>
                    `;
                });

                const printContent = `
                    <div style="text-align: center; font-family: Arial;">
                        <h2>فاتورة ${getInvoiceTypeLabel(invoice.type)}</h2>
                        <p>رقم الفاتورة: ${invoice.number}</p>
                        <p>التاريخ: ${new Date(invoice.date).toLocaleDateString('ar-YE')}</p>
                        <p>العميل/المورد: ${party ? party.name : 'غير محدد'}</p>
                        <hr>
                        <table style="width: 100%; border-collapse: collapse;">
                            <thead>
                                <tr style="background: #f8f9fa;">
                                    <th style="border: 1px solid #ddd; padding: 8px;">الصنف</th>
                                    <th style="border: 1px solid #ddd; padding: 8px;">الكمية</th>
                                    <th style="border: 1px solid #ddd; padding: 8px;">السعر</th>
                                    <th style="border: 1px solid #ddd; padding: 8px;">الإجمالي</th>
                                </tr>
                            </thead>
                            <tbody>
                                ${itemsHtml}
                            </tbody>
                        </table>
                        <hr>
                        <div style="text-align: right;">
                            <p>المجموع الفرعي: ${invoice.subtotal.toLocaleString()} ر.ي</p>
                            <p>الخصم: ${invoice.discount.toLocaleString()} ر.ي</p>
                            <p><strong>الإجمالي: ${invoice.total.toLocaleString()} ر.ي</strong></p>
                            <p>المدفوع: ${invoice.paid.toLocaleString()} ر.ي</p>
                            <p>المتبقي: ${invoice.remaining.toLocaleString()} ر.ي</p>
                        </div>
                        ${invoice.notes ? `<p>ملاحظات: ${invoice.notes}</p>` : ''}
                    </div>
                `;

                const printWindow = window.open('', '_blank');
                printWindow.document.write(printContent);
                printWindow.document.close();
                printWindow.print();
            }
        }

        // حذف فاتورة
        function deleteInvoice(invoiceId) {
            if (confirm('هل أنت متأكد من حذف هذه الفاتورة؟ سيتم إلغاء جميع التأثيرات المحاسبية.')) {
                const invoice = invoices.find(i => i.id === invoiceId);
                if (invoice) {
                    // إعادة تعديل رصيد العميل/المورد
                    const party = parties.find(p => p.id === invoice.partyId);
                    if (party) {
                        if (invoice.type === 'sales') {
                            party.balance -= invoice.remaining;
                        } else if (invoice.type === 'purchase') {
                            party.balance += invoice.remaining;
                        }
                    }

                    // إعادة تعديل المخزون
                    invoice.items.forEach(invoiceItem => {
                        const item = items.find(i => i.id === invoiceItem.itemId);
                        if (item) {
                            if (invoice.type === 'sales') {
                                item.stock += invoiceItem.quantity;
                            } else if (invoice.type === 'purchase') {
                                item.stock -= invoiceItem.quantity;
                            } else if (invoice.type === 'return') {
                                item.stock -= invoiceItem.quantity;
                            }
                        }
                    });

                    // حذف الفاتورة
                    invoices = invoices.filter(i => i.id !== invoiceId);

                    // حفظ التحديثات
                    saveInvoices();
                    localStorage.setItem('anw_parties', JSON.stringify(parties));
                    localStorage.setItem('anw_items', JSON.stringify(items));

                    displayInvoices();
                    showMessage('تم حذف الفاتورة بنجاح', 'success');
                }
            }
        }

        // تنظيف النموذج
        function clearInvoiceForm() {
            document.getElementById('invoiceForm').reset();
            document.getElementById('invoiceDate').value = new Date().toISOString().split('T')[0];
            document.getElementById('invoiceItemsTable').innerHTML = '';
            currentInvoiceItems = [];
            calculateTotals();
        }

        // مسح الفلاتر
        function clearFilters() {
            document.getElementById('searchInput').value = '';
            document.getElementById('partyFilter').value = '';
            document.getElementById('dateFromFilter').value = '';
            document.getElementById('dateToFilter').value = '';
            displayInvoices();
        }

        // عرض رسالة
        function showMessage(message, type) {
            const messagesDiv = document.getElementById('messages');
            const alertHtml = `
                <div class="alert alert-${type} alert-dismissible fade show position-fixed"
                     style="top: 20px; left: 20px; z-index: 9999; min-width: 300px;">
                    ${message}
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
            `;
            messagesDiv.innerHTML = alertHtml;

            setTimeout(() => {
                const alert = messagesDiv.querySelector('.alert');
                if (alert) {
                    bootstrap.Alert.getOrCreateInstance(alert).close();
                }
            }, 3000);
        }

        console.log('✅ تم تحميل نظام الفواتير المترابط بنجاح');
    </script>

    <style>
        .sidebar {
            height: 100vh;
            position: fixed;
            width: 25%;
        }
        .nav-link.active {
            background-color: rgba(255,255,255,0.1);
            border-radius: 5px;
        }
        .table th {
            background-color: #343a40;
            color: white;
        }
    </style>
</body>
</html>
