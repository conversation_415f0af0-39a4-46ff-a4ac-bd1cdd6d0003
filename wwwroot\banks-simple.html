<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إدارة البنوك - نظام ANW</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
</head>
<body>
    <div class="container-fluid">
        <div class="row">
            <!-- Sidebar -->
            <div class="col-md-3 bg-dark text-white p-0">
                <div class="sidebar">
                    <div class="p-3 border-bottom">
                        <h5><i class="fas fa-calculator me-2"></i>نظام ANW</h5>
                        <small>إدارة مخبوزات مبسط</small>
                    </div>
                    <nav class="nav flex-column p-3">
                        <a href="items-simple.html" class="nav-link text-white">
                            <i class="fas fa-boxes me-2"></i>المنتجات والخامات
                        </a>
                        <a href="parties-simple.html" class="nav-link text-white">
                            <i class="fas fa-users me-2"></i>العملاء والموردين
                        </a>
                        <a href="employees-simple.html" class="nav-link text-white">
                            <i class="fas fa-user-tie me-2"></i>الموظفين
                        </a>
                        <a href="invoices-simple.html" class="nav-link text-white">
                            <i class="fas fa-file-invoice me-2"></i>الفواتير
                        </a>
                        <a href="inventory-simple.html" class="nav-link text-white">
                            <i class="fas fa-warehouse me-2"></i>المخزون
                        </a>
                        <a href="cash-simple.html" class="nav-link text-white">
                            <i class="fas fa-cash-register me-2"></i>الصناديق
                        </a>
                        <a href="banks-simple.html" class="nav-link text-white active">
                            <i class="fas fa-university me-2"></i>البنوك
                        </a>
                        <a href="owners-simple.html" class="nav-link text-white">
                            <i class="fas fa-crown me-2"></i>الملاك
                        </a>
                        <a href="users-simple.html" class="nav-link text-white">
                            <i class="fas fa-user-cog me-2"></i>المستخدمين
                        </a>
                        <a href="reports-simple.html" class="nav-link text-white">
                            <i class="fas fa-chart-bar me-2"></i>التقارير
                        </a>
                    </nav>
                </div>
            </div>

            <!-- Main Content -->
            <div class="col-md-9">
                <div class="p-4">
                    <!-- Header -->
                    <div class="d-flex justify-content-between align-items-center mb-4">
                        <h2><i class="fas fa-university text-primary me-2"></i>إدارة البنوك</h2>
                        <div>
                            <button class="btn btn-success" onclick="addBank()">
                                <i class="fas fa-plus me-1"></i>إضافة بنك
                            </button>
                            <button class="btn btn-primary" onclick="addDeposit()">
                                <i class="fas fa-arrow-down me-1"></i>إيداع
                            </button>
                            <button class="btn btn-warning" onclick="addWithdrawal()">
                                <i class="fas fa-arrow-up me-1"></i>سحب
                            </button>
                            <button class="btn btn-info" onclick="addTransfer()">
                                <i class="fas fa-exchange-alt me-1"></i>تحويل
                            </button>
                        </div>
                    </div>

                    <!-- Banks Summary -->
                    <div class="row mb-4">
                        <div class="col-md-12">
                            <div class="card">
                                <div class="card-header">
                                    <h5><i class="fas fa-university me-2"></i>الحسابات البنكية</h5>
                                </div>
                                <div class="card-body">
                                    <div id="banksSummary" class="row">
                                        <!-- سيتم ملؤها بـ JavaScript -->
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Tabs -->
                    <ul class="nav nav-tabs mb-3">
                        <li class="nav-item">
                            <a class="nav-link active" onclick="showTab('transactions')">المعاملات البنكية</a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" onclick="showTab('banks')">إدارة البنوك</a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" onclick="showTab('reconciliation')">مطابقة البنك</a>
                        </li>
                    </ul>

                    <!-- Transactions Tab -->
                    <div id="transactionsTab" class="tab-content">
                        <!-- Filter -->
                        <div class="row mb-3">
                            <div class="col-md-3">
                                <select id="transactionTypeFilter" class="form-control" onchange="filterTransactions()">
                                    <option value="">جميع المعاملات</option>
                                    <option value="deposit">إيداع</option>
                                    <option value="withdrawal">سحب</option>
                                    <option value="transfer">تحويل</option>
                                </select>
                            </div>
                            <div class="col-md-3">
                                <select id="bankFilter" class="form-control" onchange="filterTransactions()">
                                    <option value="">جميع البنوك</option>
                                </select>
                            </div>
                            <div class="col-md-6">
                                <input type="text" id="transactionSearchInput" class="form-control" placeholder="البحث في المعاملات..." onkeyup="searchTransactions()">
                            </div>
                        </div>

                        <!-- Transactions Table -->
                        <div class="card">
                            <div class="card-body">
                                <div class="table-responsive">
                                    <table class="table table-striped table-hover">
                                        <thead class="table-dark">
                                            <tr>
                                                <th>التاريخ</th>
                                                <th>النوع</th>
                                                <th>البنك</th>
                                                <th>المبلغ</th>
                                                <th>البيان</th>
                                                <th>المرجع</th>
                                                <th>الإجراءات</th>
                                            </tr>
                                        </thead>
                                        <tbody id="transactionsTableBody">
                                            <!-- سيتم ملؤها بـ JavaScript -->
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Banks Tab -->
                    <div id="banksTab" class="tab-content" style="display: none;">
                        <div class="card">
                            <div class="card-body">
                                <div class="table-responsive">
                                    <table class="table table-striped table-hover">
                                        <thead class="table-dark">
                                            <tr>
                                                <th>اسم البنك</th>
                                                <th>رقم الحساب</th>
                                                <th>نوع الحساب</th>
                                                <th>الرصيد الحالي</th>
                                                <th>الحالة</th>
                                                <th>الإجراءات</th>
                                            </tr>
                                        </thead>
                                        <tbody id="banksTableBody">
                                            <!-- سيتم ملؤها بـ JavaScript -->
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Reconciliation Tab -->
                    <div id="reconciliationTab" class="tab-content" style="display: none;">
                        <div class="card">
                            <div class="card-header">
                                <h6>مطابقة كشف البنك</h6>
                            </div>
                            <div class="card-body">
                                <div class="row mb-3">
                                    <div class="col-md-4">
                                        <label class="form-label">البنك</label>
                                        <select id="reconciliationBank" class="form-control">
                                            <option value="">اختر البنك</option>
                                        </select>
                                    </div>
                                    <div class="col-md-3">
                                        <label class="form-label">من تاريخ</label>
                                        <input type="date" id="reconciliationFromDate" class="form-control">
                                    </div>
                                    <div class="col-md-3">
                                        <label class="form-label">إلى تاريخ</label>
                                        <input type="date" id="reconciliationToDate" class="form-control">
                                    </div>
                                    <div class="col-md-2">
                                        <label class="form-label">&nbsp;</label>
                                        <button class="btn btn-primary d-block" onclick="generateReconciliation()">
                                            <i class="fas fa-search"></i> مطابقة
                                        </button>
                                    </div>
                                </div>
                                <div id="reconciliationResult">
                                    <!-- سيتم ملؤها بـ JavaScript -->
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Bank Modal -->
    <div class="modal fade" id="bankModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">إضافة بنك جديد</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <form id="bankForm">
                        <div class="mb-3">
                            <label class="form-label">اسم البنك *</label>
                            <input type="text" id="bankName" class="form-control" required>
                        </div>
                        <div class="mb-3">
                            <label class="form-label">رقم الحساب *</label>
                            <input type="text" id="accountNumber" class="form-control" required>
                        </div>
                        <div class="mb-3">
                            <label class="form-label">نوع الحساب</label>
                            <select id="accountType" class="form-control">
                                <option value="current">جاري</option>
                                <option value="savings">توفير</option>
                                <option value="fixed">وديعة ثابتة</option>
                            </select>
                        </div>
                        <div class="mb-3">
                            <label class="form-label">الرصيد الافتتاحي</label>
                            <input type="number" id="openingBalance" class="form-control" value="0" step="0.01">
                        </div>
                        <div class="mb-3">
                            <label class="form-label">فرع البنك</label>
                            <input type="text" id="bankBranch" class="form-control">
                        </div>
                        <div class="mb-3">
                            <label class="form-label">ملاحظات</label>
                            <textarea id="bankNotes" class="form-control" rows="2"></textarea>
                        </div>
                        <div class="form-check mb-3">
                            <input type="checkbox" id="isActive" class="form-check-input" checked>
                            <label class="form-check-label">حساب نشط</label>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                    <button type="button" class="btn btn-primary" onclick="saveBank()">حفظ</button>
                </div>
            </div>
        </div>
    </div>

    <!-- Transaction Modal -->
    <div class="modal fade" id="transactionModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">معاملة بنكية</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <form id="transactionForm">
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">نوع المعاملة *</label>
                                    <select id="transactionType" class="form-control" required onchange="updateTransactionForm()">
                                        <option value="">اختر النوع</option>
                                        <option value="deposit">إيداع</option>
                                        <option value="withdrawal">سحب</option>
                                        <option value="transfer">تحويل</option>
                                    </select>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">التاريخ *</label>
                                    <input type="date" id="transactionDate" class="form-control" required>
                                </div>
                            </div>
                        </div>
                        
                        <div class="mb-3">
                            <label class="form-label">البنك *</label>
                            <select id="transactionBank" class="form-control" required>
                                <!-- سيتم ملؤها بـ JavaScript -->
                            </select>
                        </div>

                        <div id="transferToBank" class="mb-3" style="display: none;">
                            <label class="form-label">البنك المحول إليه *</label>
                            <select id="transferToBankId" class="form-control">
                                <!-- سيتم ملؤها بـ JavaScript -->
                            </select>
                        </div>

                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">المبلغ *</label>
                                    <input type="number" id="transactionAmount" class="form-control" required step="0.01">
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">رقم المرجع</label>
                                    <input type="text" id="referenceNumber" class="form-control">
                                </div>
                            </div>
                        </div>

                        <div class="mb-3">
                            <label class="form-label">البيان *</label>
                            <textarea id="transactionDescription" class="form-control" rows="3" required></textarea>
                        </div>

                        <div id="cashBoxSection" class="mb-3">
                            <label class="form-label">الصندوق المرتبط</label>
                            <select id="linkedCashBox" class="form-control">
                                <option value="">لا يوجد</option>
                                <!-- سيتم ملؤها بـ JavaScript -->
                            </select>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                    <button type="button" class="btn btn-primary" onclick="saveTransaction()">حفظ</button>
                </div>
            </div>
        </div>
    </div>

    <!-- Success/Error Messages -->
    <div id="messages"></div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // البيانات المحلية
        let banks = [];
        let bankTransactions = [];
        let cashBoxes = [];
        let nextBankId = 1;
        let nextTransactionId = 1;
        let currentFilter = '';

        // تحميل البيانات عند بدء الصفحة
        document.addEventListener('DOMContentLoaded', function() {
            loadAllData();
            document.getElementById('transactionDate').value = new Date().toISOString().split('T')[0];
            document.getElementById('reconciliationFromDate').value = new Date(new Date().getFullYear(), new Date().getMonth(), 1).toISOString().split('T')[0];
            document.getElementById('reconciliationToDate').value = new Date().toISOString().split('T')[0];
        });

        // تحميل جميع البيانات
        function loadAllData() {
            loadBanks();
            loadBankTransactions();
            loadCashBoxes();
            displayBanksSummary();
            displayBankTransactions();
            displayBanks();
            loadBankOptions();
            loadCashBoxOptions();
        }

        // تحميل البنوك
        function loadBanks() {
            try {
                const savedBanks = localStorage.getItem('anw_banks');
                if (savedBanks && savedBanks !== 'null') {
                    banks = JSON.parse(savedBanks);
                    if (banks.length > 0) {
                        nextBankId = Math.max(...banks.map(b => b.id), 0) + 1;
                    }
                } else {
                    // إنشاء بنوك افتراضية
                    banks = [
                        {id: 1, name: 'البنك الأهلي اليمني', accountNumber: '*********', accountType: 'current', balance: 1500000, branch: 'فرع صنعاء الرئيسي', notes: 'الحساب الرئيسي للشركة', active: true},
                        {id: 2, name: 'بنك سبأ الإسلامي', accountNumber: '*********', accountType: 'savings', balance: 750000, branch: 'فرع الزبيري', notes: 'حساب توفير للطوارئ', active: true}
                    ];
                    nextBankId = 3;
                    saveBanks();
                }
            } catch (error) {
                console.error('خطأ في تحميل البنوك:', error);
                showMessage('خطأ في تحميل البيانات', 'danger');
            }
        }

        // تحميل المعاملات البنكية
        function loadBankTransactions() {
            try {
                const savedTransactions = localStorage.getItem('anw_bank_transactions');
                if (savedTransactions && savedTransactions !== 'null') {
                    bankTransactions = JSON.parse(savedTransactions);
                    if (bankTransactions.length > 0) {
                        nextTransactionId = Math.max(...bankTransactions.map(t => t.id), 0) + 1;
                    }
                } else {
                    // إنشاء معاملات افتراضية
                    bankTransactions = [
                        {id: 1, date: '2024-01-15', type: 'deposit', bankId: 1, amount: 500000, description: 'إيداع من مبيعات نقدية', reference: 'DEP001', cashBoxId: 1},
                        {id: 2, date: '2024-01-16', type: 'withdrawal', bankId: 1, amount: 200000, description: 'سحب لدفع رواتب الموظفين', reference: 'WTH001', cashBoxId: null},
                        {id: 3, date: '2024-01-17', type: 'transfer', bankId: 1, amount: 100000, description: 'تحويل إلى حساب التوفير', reference: 'TRF001', transferToBankId: 2}
                    ];
                    nextTransactionId = 4;
                    saveBankTransactions();
                }
            } catch (error) {
                console.error('خطأ في تحميل المعاملات البنكية:', error);
                showMessage('خطأ في تحميل البيانات', 'danger');
            }
        }

        // تحميل الصناديق
        function loadCashBoxes() {
            const savedCashBoxes = localStorage.getItem('anw_cashboxes');
            if (savedCashBoxes) {
                cashBoxes = JSON.parse(savedCashBoxes);
            }
        }

        // حفظ البيانات
        function saveBanks() {
            localStorage.setItem('anw_banks', JSON.stringify(banks));
        }

        function saveBankTransactions() {
            localStorage.setItem('anw_bank_transactions', JSON.stringify(bankTransactions));
        }

        function saveCashBoxes() {
            localStorage.setItem('anw_cashboxes', JSON.stringify(cashBoxes));
        }

        // عرض ملخص البنوك
        function displayBanksSummary() {
            const summaryDiv = document.getElementById('banksSummary');
            let html = '';

            banks.forEach(bank => {
                const statusClass = bank.active ? 'success' : 'secondary';
                const accountTypeLabel = getAccountTypeLabel(bank.accountType);

                html += `
                    <div class="col-md-4 mb-3">
                        <div class="card border-${statusClass}">
                            <div class="card-body text-center">
                                <h5 class="card-title">${bank.name}</h5>
                                <h3 class="text-${statusClass}">${bank.balance.toLocaleString()} ر.ي</h3>
                                <p class="card-text">
                                    <small class="text-muted">رقم الحساب: ${bank.accountNumber}</small><br>
                                    <small class="text-muted">نوع الحساب: ${accountTypeLabel}</small><br>
                                    <small class="text-muted">الفرع: ${bank.branch}</small>
                                </p>
                            </div>
                        </div>
                    </div>
                `;
            });

            summaryDiv.innerHTML = html;
        }

        // عرض المعاملات البنكية
        function displayBankTransactions() {
            const tbody = document.getElementById('transactionsTableBody');
            let filteredTransactions = bankTransactions;

            // تطبيق الفلتر
            if (currentFilter) {
                filteredTransactions = bankTransactions.filter(t => t.type === currentFilter);
            }

            if (filteredTransactions.length === 0) {
                tbody.innerHTML = `
                    <tr>
                        <td colspan="7" class="text-center text-muted">
                            لا توجد معاملات بنكية
                            <br>
                            <button class="btn btn-primary mt-2" onclick="addDeposit()">إضافة معاملة جديدة</button>
                        </td>
                    </tr>
                `;
                return;
            }

            let html = '';
            filteredTransactions.forEach(transaction => {
                const bank = banks.find(b => b.id === transaction.bankId);
                const typeLabel = getTransactionTypeLabel(transaction.type);
                const typeBadge = getTransactionTypeBadge(transaction.type);
                const amountClass = transaction.type === 'deposit' ? 'text-success' : 'text-danger';

                html += `
                    <tr>
                        <td>${new Date(transaction.date).toLocaleDateString('ar-YE')}</td>
                        <td><span class="badge ${typeBadge}">${typeLabel}</span></td>
                        <td>${bank ? bank.name : 'غير محدد'}</td>
                        <td class="${amountClass} fw-bold">${transaction.amount.toLocaleString()} ر.ي</td>
                        <td>${transaction.description}</td>
                        <td>${transaction.reference || '-'}</td>
                        <td>
                            <button class="btn btn-sm btn-info" onclick="printTransaction(${transaction.id})">
                                <i class="fas fa-print"></i>
                            </button>
                            <button class="btn btn-sm btn-danger" onclick="deleteTransaction(${transaction.id})">
                                <i class="fas fa-trash"></i>
                            </button>
                        </td>
                    </tr>
                `;
            });

            tbody.innerHTML = html;
        }

        // عرض البنوك
        function displayBanks() {
            const tbody = document.getElementById('banksTableBody');

            if (banks.length === 0) {
                tbody.innerHTML = `
                    <tr>
                        <td colspan="6" class="text-center text-muted">
                            لا توجد بنوك
                            <br>
                            <button class="btn btn-primary mt-2" onclick="addBank()">إضافة بنك جديد</button>
                        </td>
                    </tr>
                `;
                return;
            }

            let html = '';
            banks.forEach(bank => {
                const statusBadge = bank.active ?
                    '<span class="badge bg-success">نشط</span>' :
                    '<span class="badge bg-secondary">غير نشط</span>';

                const accountTypeLabel = getAccountTypeLabel(bank.accountType);

                html += `
                    <tr>
                        <td>${bank.name}</td>
                        <td>${bank.accountNumber}</td>
                        <td><span class="badge bg-info">${accountTypeLabel}</span></td>
                        <td class="fw-bold">${bank.balance.toLocaleString()} ر.ي</td>
                        <td>${statusBadge}</td>
                        <td>
                            <button class="btn btn-sm btn-warning" onclick="editBank(${bank.id})">
                                <i class="fas fa-edit"></i>
                            </button>
                            <button class="btn btn-sm btn-info" onclick="viewBankStatement(${bank.id})">
                                <i class="fas fa-file-alt"></i>
                            </button>
                            <button class="btn btn-sm btn-danger" onclick="deleteBank(${bank.id})">
                                <i class="fas fa-trash"></i>
                            </button>
                        </td>
                    </tr>
                `;
            });

            tbody.innerHTML = html;
        }
        // تحميل خيارات البنوك
        function loadBankOptions() {
            const selects = [
                document.getElementById('transactionBank'),
                document.getElementById('transferToBankId'),
                document.getElementById('bankFilter'),
                document.getElementById('reconciliationBank')
            ];

            let html = '<option value="">اختر البنك</option>';
            let filterHtml = '<option value="">جميع البنوك</option>';

            banks.filter(b => b.active).forEach(bank => {
                const option = `<option value="${bank.id}">${bank.name} - ${bank.accountNumber}</option>`;
                html += option;
                filterHtml += option;
            });

            selects[0].innerHTML = html; // transactionBank
            selects[1].innerHTML = html; // transferToBankId
            selects[2].innerHTML = filterHtml; // bankFilter
            selects[3].innerHTML = html; // reconciliationBank
        }

        // تحميل خيارات الصناديق
        function loadCashBoxOptions() {
            const select = document.getElementById('linkedCashBox');

            let html = '<option value="">لا يوجد</option>';

            cashBoxes.filter(c => c.active).forEach(cashBox => {
                html += `<option value="${cashBox.id}">${cashBox.name}</option>`;
            });

            select.innerHTML = html;
        }

        // الحصول على تسمية نوع الحساب
        function getAccountTypeLabel(type) {
            const types = {
                'current': 'جاري',
                'savings': 'توفير',
                'fixed': 'وديعة ثابتة'
            };
            return types[type] || type;
        }

        // الحصول على تسمية نوع المعاملة
        function getTransactionTypeLabel(type) {
            const types = {
                'deposit': 'إيداع',
                'withdrawal': 'سحب',
                'transfer': 'تحويل'
            };
            return types[type] || type;
        }

        // الحصول على شارة نوع المعاملة
        function getTransactionTypeBadge(type) {
            const badges = {
                'deposit': 'bg-success',
                'withdrawal': 'bg-danger',
                'transfer': 'bg-info'
            };
            return badges[type] || 'bg-secondary';
        }

        // إضافة بنك جديد
        function addBank() {
            clearBankForm();
            document.querySelector('#bankModal .modal-title').textContent = 'إضافة بنك جديد';
            new bootstrap.Modal(document.getElementById('bankModal')).show();
        }

        // إضافة إيداع
        function addDeposit() {
            clearTransactionForm();
            document.getElementById('transactionType').value = 'deposit';
            updateTransactionForm();
            document.querySelector('#transactionModal .modal-title').textContent = 'إيداع بنكي';
            new bootstrap.Modal(document.getElementById('transactionModal')).show();
        }

        // إضافة سحب
        function addWithdrawal() {
            clearTransactionForm();
            document.getElementById('transactionType').value = 'withdrawal';
            updateTransactionForm();
            document.querySelector('#transactionModal .modal-title').textContent = 'سحب بنكي';
            new bootstrap.Modal(document.getElementById('transactionModal')).show();
        }

        // إضافة تحويل
        function addTransfer() {
            clearTransactionForm();
            document.getElementById('transactionType').value = 'transfer';
            updateTransactionForm();
            document.querySelector('#transactionModal .modal-title').textContent = 'تحويل بنكي';
            new bootstrap.Modal(document.getElementById('transactionModal')).show();
        }

        // تحديث نموذج المعاملة
        function updateTransactionForm() {
            const type = document.getElementById('transactionType').value;
            const transferSection = document.getElementById('transferToBank');
            const cashBoxSection = document.getElementById('cashBoxSection');

            if (type === 'transfer') {
                transferSection.style.display = 'block';
                cashBoxSection.style.display = 'none';
            } else {
                transferSection.style.display = 'none';
                cashBoxSection.style.display = 'block';
            }
        }

        // حفظ البنك
        function saveBank() {
            const form = document.getElementById('bankForm');
            const editId = form.dataset.editId;

            const bankData = {
                name: document.getElementById('bankName').value,
                accountNumber: document.getElementById('accountNumber').value,
                accountType: document.getElementById('accountType').value,
                balance: parseFloat(document.getElementById('openingBalance').value) || 0,
                branch: document.getElementById('bankBranch').value,
                notes: document.getElementById('bankNotes').value,
                active: document.getElementById('isActive').checked
            };

            if (!bankData.name || !bankData.accountNumber) {
                showMessage('يرجى ملء جميع الحقول المطلوبة', 'danger');
                return;
            }

            if (editId) {
                const bankIndex = banks.findIndex(b => b.id == editId);
                banks[bankIndex] = { ...banks[bankIndex], ...bankData };
                showMessage('تم تحديث البنك بنجاح', 'success');
            } else {
                bankData.id = nextBankId++;
                banks.push(bankData);
                showMessage('تم إضافة البنك بنجاح', 'success');
            }

            saveBanks();
            loadAllData();
            bootstrap.Modal.getInstance(document.getElementById('bankModal')).hide();
        }

        // حفظ المعاملة البنكية
        function saveTransaction() {
            const transactionData = {
                type: document.getElementById('transactionType').value,
                bankId: parseInt(document.getElementById('transactionBank').value),
                amount: parseFloat(document.getElementById('transactionAmount').value),
                date: document.getElementById('transactionDate').value,
                description: document.getElementById('transactionDescription').value,
                reference: document.getElementById('referenceNumber').value,
                cashBoxId: document.getElementById('linkedCashBox').value ? parseInt(document.getElementById('linkedCashBox').value) : null,
                transferToBankId: document.getElementById('transferToBankId').value ? parseInt(document.getElementById('transferToBankId').value) : null
            };

            if (!transactionData.type || !transactionData.bankId || !transactionData.amount || !transactionData.description) {
                showMessage('يرجى ملء جميع الحقول المطلوبة', 'danger');
                return;
            }

            if (transactionData.type === 'transfer' && !transactionData.transferToBankId) {
                showMessage('يرجى اختيار البنك المحول إليه', 'danger');
                return;
            }

            transactionData.id = nextTransactionId++;

            // تحديث رصيد البنك
            const bank = banks.find(b => b.id === transactionData.bankId);
            if (bank) {
                if (transactionData.type === 'deposit') {
                    bank.balance += transactionData.amount;
                } else if (transactionData.type === 'withdrawal' || transactionData.type === 'transfer') {
                    bank.balance -= transactionData.amount;
                }
            }

            // في حالة التحويل، تحديث رصيد البنك المحول إليه
            if (transactionData.type === 'transfer' && transactionData.transferToBankId) {
                const toBank = banks.find(b => b.id === transactionData.transferToBankId);
                if (toBank) {
                    toBank.balance += transactionData.amount;
                }
            }

            // تحديث رصيد الصندوق المرتبط
            if (transactionData.cashBoxId) {
                const cashBox = cashBoxes.find(c => c.id === transactionData.cashBoxId);
                if (cashBox) {
                    if (transactionData.type === 'deposit') {
                        cashBox.balance -= transactionData.amount; // خروج من الصندوق
                    } else if (transactionData.type === 'withdrawal') {
                        cashBox.balance += transactionData.amount; // دخول للصندوق
                    }
                    saveCashBoxes();
                }
            }

            bankTransactions.push(transactionData);
            saveBankTransactions();
            saveBanks();
            loadAllData();
            bootstrap.Modal.getInstance(document.getElementById('transactionModal')).hide();
            showMessage('تم حفظ المعاملة البنكية بنجاح', 'success');
        }
        // مطابقة البنك
        function generateReconciliation() {
            const bankId = parseInt(document.getElementById('reconciliationBank').value);
            const fromDate = document.getElementById('reconciliationFromDate').value;
            const toDate = document.getElementById('reconciliationToDate').value;

            if (!bankId || !fromDate || !toDate) {
                showMessage('يرجى ملء جميع الحقول المطلوبة', 'danger');
                return;
            }

            const bank = banks.find(b => b.id === bankId);
            const transactions = bankTransactions.filter(t =>
                (t.bankId === bankId || t.transferToBankId === bankId) &&
                t.date >= fromDate &&
                t.date <= toDate
            );

            let openingBalance = bank.balance;
            let totalDeposits = 0;
            let totalWithdrawals = 0;

            // حساب الرصيد الافتتاحي
            bankTransactions.forEach(t => {
                if (t.date < fromDate) {
                    if (t.bankId === bankId) {
                        if (t.type === 'deposit') {
                            openingBalance -= t.amount;
                        } else if (t.type === 'withdrawal' || t.type === 'transfer') {
                            openingBalance += t.amount;
                        }
                    }
                    if (t.transferToBankId === bankId) {
                        openingBalance -= t.amount;
                    }
                }
            });

            // حساب الإجماليات
            transactions.forEach(t => {
                if (t.bankId === bankId) {
                    if (t.type === 'deposit') {
                        totalDeposits += t.amount;
                    } else if (t.type === 'withdrawal' || t.type === 'transfer') {
                        totalWithdrawals += t.amount;
                    }
                }
                if (t.transferToBankId === bankId) {
                    totalDeposits += t.amount;
                }
            });

            const closingBalance = openingBalance + totalDeposits - totalWithdrawals;

            let transactionsHtml = '';
            transactions.forEach(t => {
                const typeLabel = getTransactionTypeLabel(t.type);
                const amountClass = (t.bankId === bankId && (t.type === 'withdrawal' || t.type === 'transfer')) ? 'text-danger' : 'text-success';
                const amount = (t.bankId === bankId && (t.type === 'withdrawal' || t.type === 'transfer')) ? `-${t.amount.toLocaleString()}` : `+${t.amount.toLocaleString()}`;

                transactionsHtml += `
                    <tr>
                        <td>${new Date(t.date).toLocaleDateString('ar-YE')}</td>
                        <td>${typeLabel}</td>
                        <td>${t.description}</td>
                        <td class="${amountClass}">${amount} ر.ي</td>
                        <td>${t.reference || '-'}</td>
                    </tr>
                `;
            });

            const resultHtml = `
                <div class="card">
                    <div class="card-header">
                        <h6>كشف حساب ${bank.name} من ${new Date(fromDate).toLocaleDateString('ar-YE')} إلى ${new Date(toDate).toLocaleDateString('ar-YE')}</h6>
                    </div>
                    <div class="card-body">
                        <div class="row mb-3">
                            <div class="col-md-3">
                                <div class="card border-info">
                                    <div class="card-body text-center">
                                        <h6 class="text-info">الرصيد الافتتاحي</h6>
                                        <h5>${openingBalance.toLocaleString()} ر.ي</h5>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="card border-success">
                                    <div class="card-body text-center">
                                        <h6 class="text-success">إجمالي الإيداعات</h6>
                                        <h5>${totalDeposits.toLocaleString()} ر.ي</h5>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="card border-danger">
                                    <div class="card-body text-center">
                                        <h6 class="text-danger">إجمالي السحوبات</h6>
                                        <h5>${totalWithdrawals.toLocaleString()} ر.ي</h5>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="card border-primary">
                                    <div class="card-body text-center">
                                        <h6 class="text-primary">الرصيد الختامي</h6>
                                        <h5>${closingBalance.toLocaleString()} ر.ي</h5>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="table-responsive">
                            <table class="table table-striped">
                                <thead class="table-dark">
                                    <tr>
                                        <th>التاريخ</th>
                                        <th>النوع</th>
                                        <th>البيان</th>
                                        <th>المبلغ</th>
                                        <th>المرجع</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    ${transactionsHtml}
                                </tbody>
                            </table>
                        </div>

                        <div class="text-center mt-3">
                            <button class="btn btn-primary" onclick="printReconciliation()">
                                <i class="fas fa-print"></i> طباعة الكشف
                            </button>
                        </div>
                    </div>
                </div>
            `;

            document.getElementById('reconciliationResult').innerHTML = resultHtml;
        }

        // طباعة كشف البنك
        function printReconciliation() {
            const content = document.getElementById('reconciliationResult').innerHTML;
            const printWindow = window.open('', '_blank');
            printWindow.document.write(`
                <html>
                    <head>
                        <title>كشف حساب بنكي</title>
                        <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
                        <style>
                            body { font-family: Arial, sans-serif; direction: rtl; }
                            @media print { .btn { display: none; } }
                        </style>
                    </head>
                    <body>
                        <div class="container">
                            ${content}
                        </div>
                    </body>
                </html>
            `);
            printWindow.document.close();
            printWindow.print();
        }

        // تبديل التبويبات
        function showTab(tabName) {
            // إخفاء جميع التبويبات
            document.getElementById('transactionsTab').style.display = 'none';
            document.getElementById('banksTab').style.display = 'none';
            document.getElementById('reconciliationTab').style.display = 'none';

            // إزالة active من جميع الروابط
            document.querySelectorAll('.nav-link').forEach(link => {
                link.classList.remove('active');
            });

            // إظهار التبويب المحدد
            if (tabName === 'transactions') {
                document.getElementById('transactionsTab').style.display = 'block';
            } else if (tabName === 'banks') {
                document.getElementById('banksTab').style.display = 'block';
            } else if (tabName === 'reconciliation') {
                document.getElementById('reconciliationTab').style.display = 'block';
            }

            // إضافة active للرابط المحدد
            event.target.classList.add('active');
        }

        // تنظيف النماذج
        function clearBankForm() {
            document.getElementById('bankForm').reset();
            document.getElementById('bankForm').removeAttribute('data-edit-id');
            document.getElementById('openingBalance').value = '0';
            document.getElementById('isActive').checked = true;
        }

        function clearTransactionForm() {
            document.getElementById('transactionForm').reset();
            document.getElementById('transactionDate').value = new Date().toISOString().split('T')[0];
            updateTransactionForm();
        }

        // عرض رسالة
        function showMessage(message, type) {
            const messagesDiv = document.getElementById('messages');
            const alertHtml = `
                <div class="alert alert-${type} alert-dismissible fade show position-fixed"
                     style="top: 20px; left: 20px; z-index: 9999; min-width: 300px;">
                    ${message}
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
            `;
            messagesDiv.innerHTML = alertHtml;

            setTimeout(() => {
                const alert = messagesDiv.querySelector('.alert');
                if (alert) {
                    bootstrap.Alert.getOrCreateInstance(alert).close();
                }
            }, 3000);
        }

        console.log('✅ تم تحميل نظام إدارة البنوك المتكامل بنجاح');
    </script>

    <style>
        .sidebar {
            height: 100vh;
            position: fixed;
            width: 25%;
        }
        .nav-link.active {
            background-color: rgba(255,255,255,0.1);
            border-radius: 5px;
        }
        .table th {
            background-color: #343a40;
            color: white;
        }
        .tab-content {
            animation: fadeIn 0.3s;
        }
        @keyframes fadeIn {
            from { opacity: 0; }
            to { opacity: 1; }
        }
    </style>
</body>
</html>
