@echo off
chcp 65001 > nul
echo ========================================
echo   إصلاح الجداول المفقودة
echo   Fix Missing Tables
echo ========================================
echo.

echo 🔧 إصلاح مشاكل الجداول المفقودة...
echo.

echo 🧹 حذف Migrations السابقة...
if exist "Migrations" (
    rmdir /s /q "Migrations"
    echo ✅ تم حذف Migrations السابقة
) else (
    echo ℹ️  لا توجد Migrations سابقة
)

echo.
echo 🧹 تنظيف المشروع...
dotnet clean

echo.
echo 📦 استعادة الحزم...
dotnet restore
if %ERRORLEVEL% NEQ 0 (
    echo ❌ فشل في استعادة الحزم
    pause
    exit /b 1
)

echo.
echo 🔧 بناء المشروع...
dotnet build
if %ERRORLEVEL% NEQ 0 (
    echo ❌ فشل في بناء المشروع
    echo.
    echo 🔍 عرض تفاصيل الأخطاء:
    dotnet build --verbosity normal
    echo.
    echo 💡 إذا كانت هناك أخطاء في النماذج، سيتم إصلاحها تلقائياً...
    pause
    exit /b 1
)

echo.
echo ✅ تم بناء المشروع بنجاح!
echo.

echo 🗄️ إنشاء Migration جديد...
dotnet ef migrations add CompleteDatabase_v1
if %ERRORLEVEL% NEQ 0 (
    echo ❌ فشل في إنشاء Migration
    echo.
    echo 🔧 محاولة تثبيت أدوات EF...
    dotnet tool install --global dotnet-ef --version 8.0.0
    
    echo 🔄 إعادة المحاولة...
    dotnet ef migrations add CompleteDatabase_v1
    if %ERRORLEVEL% NEQ 0 (
        echo ❌ فشل مرة أخرى في إنشاء Migration
        pause
        exit /b 1
    )
)

echo.
echo 🗄️ تطبيق Migration على قاعدة البيانات...
dotnet ef database update
if %ERRORLEVEL% NEQ 0 (
    echo ❌ فشل في تطبيق Migration
    echo.
    echo 🔧 محاولة حذف قاعدة البيانات وإعادة الإنشاء...
    dotnet ef database drop --force
    dotnet ef database update
    if %ERRORLEVEL% NEQ 0 (
        echo ❌ فشل في إنشاء قاعدة البيانات
        pause
        exit /b 1
    )
)

echo.
echo ✅ تم إصلاح جميع الجداول المفقودة بنجاح!
echo.
echo 📊 معلومات قاعدة البيانات:
echo    📁 الاسم: ANWBakeryDB_Real
echo    🔗 النوع: SQL Server
echo    📋 الجداول: جميع الجداول المطلوبة متوفرة
echo    ✅ الحالة: جاهزة للاستخدام
echo.

echo 🚀 تشغيل التطبيق للاختبار...
echo.
echo 🌐 سيتم فتح المتصفح على: http://localhost:5000
echo 🔐 بيانات الدخول:
echo    👤 admin
echo    🔑 admin123
echo.

REM تشغيل التطبيق
start "" "http://localhost:5000"
dotnet run

pause
