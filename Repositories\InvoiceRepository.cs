using Microsoft.EntityFrameworkCore;
using ANWBakerySystem.Data;
using ANWBakerySystem.Models;

namespace ANWBakerySystem.Repositories
{
    /// <summary>
    /// تطبيق مستودع الفوترة الموحدة
    /// Unified Invoice Repository Implementation
    /// </summary>
    public class InvoiceRepository : IInvoiceRepository
    {
        private readonly ANWBakeryDbContext _context;

        public InvoiceRepository(ANWBakeryDbContext context)
        {
            _context = context;
        }

        public async Task<IEnumerable<Invoice>> GetAllInvoicesAsync()
        {
            return await _context.Invoices
                .Include(i => i.Party)
                .Include(i => i.Creator)
                .Include(i => i.Updater)
                .Include(i => i.InvoiceItems)
                .OrderByDescending(i => i.InvoiceDate)
                .ToListAsync();
        }

        public async Task<IEnumerable<Invoice>> GetInvoicesByTypeAsync(InvoiceType invoiceType)
        {
            return await _context.Invoices
                .Include(i => i.Party)
                .Include(i => i.Creator)
                .Where(i => i.InvoiceType == invoiceType)
                .OrderByDescending(i => i.InvoiceDate)
                .ToListAsync();
        }

        public async Task<IEnumerable<Invoice>> GetInvoicesByPartyAsync(int partyId)
        {
            return await _context.Invoices
                .Include(i => i.Creator)
                .Include(i => i.InvoiceItems)
                .Where(i => i.PartyId == partyId)
                .OrderByDescending(i => i.InvoiceDate)
                .ToListAsync();
        }

        public async Task<IEnumerable<Invoice>> GetInvoicesByDateRangeAsync(DateTime fromDate, DateTime toDate)
        {
            return await _context.Invoices
                .Include(i => i.Party)
                .Include(i => i.Creator)
                .Where(i => i.InvoiceDate >= fromDate && i.InvoiceDate <= toDate)
                .OrderByDescending(i => i.InvoiceDate)
                .ToListAsync();
        }

        public async Task<IEnumerable<Invoice>> GetInvoicesByStatusAsync(InvoiceStatus status)
        {
            return await _context.Invoices
                .Include(i => i.Party)
                .Include(i => i.Creator)
                .Where(i => i.Status == status)
                .OrderByDescending(i => i.InvoiceDate)
                .ToListAsync();
        }

        public async Task<Invoice?> GetInvoiceByIdAsync(int invoiceId)
        {
            return await _context.Invoices
                .Include(i => i.Party)
                .Include(i => i.Creator)
                .Include(i => i.Updater)
                .Include(i => i.InvoiceItems)
                    .ThenInclude(ii => ii.Item)
                .Include(i => i.InvoiceItems)
                    .ThenInclude(ii => ii.Unit)
                .FirstOrDefaultAsync(i => i.InvoiceId == invoiceId);
        }

        public async Task<Invoice?> GetInvoiceByNumberAsync(string invoiceNumber)
        {
            return await _context.Invoices
                .Include(i => i.Party)
                .Include(i => i.InvoiceItems)
                .FirstOrDefaultAsync(i => i.InvoiceNumber == invoiceNumber);
        }

        public async Task<Invoice> CreateInvoiceAsync(Invoice invoice)
        {
            _context.Invoices.Add(invoice);
            await _context.SaveChangesAsync();
            return invoice;
        }

        public async Task<Invoice> UpdateInvoiceAsync(Invoice invoice)
        {
            invoice.UpdatedAt = DateTime.Now;
            _context.Invoices.Update(invoice);
            await _context.SaveChangesAsync();
            return invoice;
        }

        public async Task<bool> DeleteInvoiceAsync(int invoiceId)
        {
            var invoice = await _context.Invoices.FindAsync(invoiceId);
            if (invoice == null) return false;

            invoice.Status = InvoiceStatus.Cancelled;
            invoice.UpdatedAt = DateTime.Now;
            await _context.SaveChangesAsync();
            return true;
        }

        public async Task<bool> InvoiceExistsAsync(int invoiceId)
        {
            return await _context.Invoices.AnyAsync(i => i.InvoiceId == invoiceId);
        }

        public async Task<bool> InvoiceNumberExistsAsync(string invoiceNumber)
        {
            return await _context.Invoices.AnyAsync(i => i.InvoiceNumber == invoiceNumber);
        }

        #region Invoice Items

        public async Task<IEnumerable<InvoiceItem>> GetInvoiceItemsAsync(int invoiceId)
        {
            return await _context.InvoiceItems
                .Include(ii => ii.Item)
                .Include(ii => ii.Unit)
                .Where(ii => ii.InvoiceId == invoiceId)
                .OrderBy(ii => ii.LineOrder)
                .ToListAsync();
        }

        public async Task<InvoiceItem> CreateInvoiceItemAsync(InvoiceItem invoiceItem)
        {
            _context.InvoiceItems.Add(invoiceItem);
            await _context.SaveChangesAsync();
            return invoiceItem;
        }

        public async Task<InvoiceItem> UpdateInvoiceItemAsync(InvoiceItem invoiceItem)
        {
            _context.InvoiceItems.Update(invoiceItem);
            await _context.SaveChangesAsync();
            return invoiceItem;
        }

        public async Task<bool> DeleteInvoiceItemAsync(int invoiceItemId)
        {
            var invoiceItem = await _context.InvoiceItems.FindAsync(invoiceItemId);
            if (invoiceItem == null) return false;

            _context.InvoiceItems.Remove(invoiceItem);
            await _context.SaveChangesAsync();
            return true;
        }

        #endregion

        #region Invoice Operations

        public async Task<bool> ApproveInvoiceAsync(int invoiceId, int userId)
        {
            var invoice = await _context.Invoices.FindAsync(invoiceId);
            if (invoice == null) return false;

            invoice.Status = InvoiceStatus.Approved;
            invoice.UpdatedAt = DateTime.Now;
            invoice.UpdatedBy = userId;
            await _context.SaveChangesAsync();
            return true;
        }

        public async Task<bool> CancelInvoiceAsync(int invoiceId, int userId, string reason)
        {
            var invoice = await _context.Invoices.FindAsync(invoiceId);
            if (invoice == null) return false;

            invoice.Status = InvoiceStatus.Cancelled;
            invoice.Notes = $"{invoice.Notes}\nملغية: {reason}";
            invoice.UpdatedAt = DateTime.Now;
            invoice.UpdatedBy = userId;
            await _context.SaveChangesAsync();
            return true;
        }

        public async Task<bool> PayInvoiceAsync(int invoiceId, decimal paidAmount, int userId)
        {
            var invoice = await _context.Invoices.FindAsync(invoiceId);
            if (invoice == null) return false;

            invoice.PaidAmount += paidAmount;
            invoice.RemainingAmount = invoice.TotalAmount - invoice.PaidAmount;
            
            if (invoice.RemainingAmount <= 0)
                invoice.Status = InvoiceStatus.Paid;
            else if (invoice.PaidAmount > 0)
                invoice.Status = InvoiceStatus.PartiallyPaid;

            invoice.UpdatedAt = DateTime.Now;
            invoice.UpdatedBy = userId;
            await _context.SaveChangesAsync();
            return true;
        }

        public async Task<bool> MarkInvoiceAsPrintedAsync(int invoiceId)
        {
            var invoice = await _context.Invoices.FindAsync(invoiceId);
            if (invoice == null) return false;

            invoice.IsPrinted = true;
            invoice.PrintedAt = DateTime.Now;
            invoice.PrintCount++;
            await _context.SaveChangesAsync();
            return true;
        }

        public async Task<string> GenerateInvoiceNumberAsync(InvoiceType invoiceType)
        {
            var prefix = invoiceType switch
            {
                InvoiceType.Sales => "SAL",
                InvoiceType.Purchase => "PUR",
                InvoiceType.SalesReturn => "SRT",
                InvoiceType.PurchaseReturn => "PRT",
                InvoiceType.QuickSale => "QSL",
                _ => "INV"
            };

            var today = DateTime.Today;
            var yearMonth = today.ToString("yyyyMM");
            
            var lastInvoice = await _context.Invoices
                .Where(i => i.InvoiceType == invoiceType && 
                           i.InvoiceDate.Date == today)
                .OrderByDescending(i => i.InvoiceNumber)
                .FirstOrDefaultAsync();

            int nextNumber = 1;
            if (lastInvoice != null)
            {
                var lastNumberPart = lastInvoice.InvoiceNumber.Split('-').LastOrDefault();
                if (int.TryParse(lastNumberPart, out int lastNumber))
                {
                    nextNumber = lastNumber + 1;
                }
            }

            return $"{prefix}-{yearMonth}-{nextNumber:D4}";
        }

        #endregion

        #region Invoice Calculations

        public async Task<bool> RecalculateInvoiceTotalsAsync(int invoiceId)
        {
            var invoice = await GetInvoiceByIdAsync(invoiceId);
            if (invoice == null) return false;

            invoice.CalculateTotals();
            await _context.SaveChangesAsync();
            return true;
        }

        public async Task<decimal> GetInvoiceSubTotalAsync(int invoiceId)
        {
            return await _context.InvoiceItems
                .Where(ii => ii.InvoiceId == invoiceId)
                .SumAsync(ii => ii.SubTotal);
        }

        public async Task<decimal> GetInvoiceTotalAsync(int invoiceId)
        {
            return await _context.InvoiceItems
                .Where(ii => ii.InvoiceId == invoiceId)
                .SumAsync(ii => ii.TotalAmount);
        }

        public async Task<decimal> GetInvoiceRemainingAmountAsync(int invoiceId)
        {
            var invoice = await _context.Invoices.FindAsync(invoiceId);
            return invoice?.RemainingAmount ?? 0;
        }

        #endregion

        #region Invoice Reports

        public async Task<decimal> GetSalesTotalAsync(DateTime fromDate, DateTime toDate)
        {
            return await _context.Invoices
                .Where(i => i.InvoiceType == InvoiceType.Sales && 
                           i.InvoiceDate >= fromDate && 
                           i.InvoiceDate <= toDate &&
                           i.Status != InvoiceStatus.Cancelled)
                .SumAsync(i => i.TotalAmount);
        }

        public async Task<decimal> GetPurchasesTotalAsync(DateTime fromDate, DateTime toDate)
        {
            return await _context.Invoices
                .Where(i => i.InvoiceType == InvoiceType.Purchase && 
                           i.InvoiceDate >= fromDate && 
                           i.InvoiceDate <= toDate &&
                           i.Status != InvoiceStatus.Cancelled)
                .SumAsync(i => i.TotalAmount);
        }

        public async Task<IEnumerable<Invoice>> GetOverdueInvoicesAsync()
        {
            var today = DateTime.Today;
            return await _context.Invoices
                .Include(i => i.Party)
                .Where(i => i.DueDate < today && 
                           i.RemainingAmount > 0 &&
                           i.Status != InvoiceStatus.Cancelled)
                .OrderBy(i => i.DueDate)
                .ToListAsync();
        }

        public async Task<IEnumerable<Invoice>> GetUnpaidInvoicesAsync()
        {
            return await _context.Invoices
                .Include(i => i.Party)
                .Where(i => i.RemainingAmount > 0 &&
                           i.Status != InvoiceStatus.Cancelled)
                .OrderByDescending(i => i.InvoiceDate)
                .ToListAsync();
        }

        public async Task<IEnumerable<Invoice>> GetQuickSalesAsync(DateTime date)
        {
            return await _context.Invoices
                .Where(i => i.IsQuickSale && 
                           i.InvoiceDate.Date == date.Date)
                .OrderByDescending(i => i.InvoiceDate)
                .ToListAsync();
        }

        #endregion

        #region Quick Sale

        public async Task<Invoice> CreateQuickSaleAsync(Invoice quickSale)
        {
            quickSale.IsQuickSale = true;
            quickSale.InvoiceType = InvoiceType.QuickSale;
            quickSale.PaymentMethod = PaymentMethod.Cash;
            quickSale.Status = InvoiceStatus.Paid;
            quickSale.PaidAmount = quickSale.TotalAmount;
            quickSale.RemainingAmount = 0;

            return await CreateInvoiceAsync(quickSale);
        }

        public async Task<IEnumerable<Invoice>> GetTodayQuickSalesAsync()
        {
            var today = DateTime.Today;
            return await GetQuickSalesAsync(today);
        }

        public async Task<decimal> GetQuickSalesTotalAsync(DateTime date)
        {
            return await _context.Invoices
                .Where(i => i.IsQuickSale && 
                           i.InvoiceDate.Date == date.Date &&
                           i.Status != InvoiceStatus.Cancelled)
                .SumAsync(i => i.TotalAmount);
        }

        #endregion
    }
}
