<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إدارة الملاك - نظام ANW</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
</head>
<body>
    <div class="container-fluid">
        <div class="row">
            <!-- Sidebar -->
            <div class="col-md-3 bg-dark text-white p-0">
                <div class="sidebar">
                    <div class="p-3 border-bottom">
                        <h5><i class="fas fa-calculator me-2"></i>نظام ANW</h5>
                        <small>إدارة مخبوزات مبسط</small>
                    </div>
                    <nav class="nav flex-column p-3">
                        <a href="items-simple.html" class="nav-link text-white">
                            <i class="fas fa-boxes me-2"></i>المنتجات والخامات
                        </a>
                        <a href="parties-simple.html" class="nav-link text-white">
                            <i class="fas fa-users me-2"></i>العملاء والموردين
                        </a>
                        <a href="employees-simple.html" class="nav-link text-white">
                            <i class="fas fa-user-tie me-2"></i>الموظفين
                        </a>
                        <a href="invoices-simple.html" class="nav-link text-white">
                            <i class="fas fa-file-invoice me-2"></i>الفواتير
                        </a>
                        <a href="inventory-simple.html" class="nav-link text-white">
                            <i class="fas fa-warehouse me-2"></i>المخزون
                        </a>
                        <a href="cash-simple.html" class="nav-link text-white">
                            <i class="fas fa-cash-register me-2"></i>الصناديق
                        </a>
                        <a href="banks-simple.html" class="nav-link text-white">
                            <i class="fas fa-university me-2"></i>البنوك
                        </a>
                        <a href="owners-simple.html" class="nav-link text-white active">
                            <i class="fas fa-crown me-2"></i>الملاك
                        </a>
                        <a href="users-simple.html" class="nav-link text-white">
                            <i class="fas fa-user-cog me-2"></i>المستخدمين
                        </a>
                        <a href="reports-simple.html" class="nav-link text-white">
                            <i class="fas fa-chart-bar me-2"></i>التقارير
                        </a>
                    </nav>
                </div>
            </div>

            <!-- Main Content -->
            <div class="col-md-9">
                <div class="p-4">
                    <!-- Header -->
                    <div class="d-flex justify-content-between align-items-center mb-4">
                        <h2><i class="fas fa-crown text-primary me-2"></i>إدارة الملاك</h2>
                        <div>
                            <button class="btn btn-success" onclick="addOwner()">
                                <i class="fas fa-plus me-1"></i>إضافة مالك
                            </button>
                            <button class="btn btn-primary" onclick="addCapitalInvestment()">
                                <i class="fas fa-arrow-down me-1"></i>استثمار رأس مال
                            </button>
                            <button class="btn btn-warning" onclick="addWithdrawal()">
                                <i class="fas fa-arrow-up me-1"></i>سحوبات
                            </button>
                            <button class="btn btn-info" onclick="distributeProfits()">
                                <i class="fas fa-chart-pie me-1"></i>توزيع أرباح
                            </button>
                        </div>
                    </div>

                    <!-- Capital Summary -->
                    <div class="row mb-4">
                        <div class="col-md-12">
                            <div class="card">
                                <div class="card-header">
                                    <h5><i class="fas fa-chart-bar me-2"></i>ملخص رأس المال</h5>
                                </div>
                                <div class="card-body">
                                    <div id="capitalSummary" class="row">
                                        <!-- سيتم ملؤها بـ JavaScript -->
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Owners Cards -->
                    <div class="row mb-4">
                        <div class="col-md-12">
                            <div class="card">
                                <div class="card-header">
                                    <h5><i class="fas fa-users me-2"></i>الملاك والشركاء</h5>
                                </div>
                                <div class="card-body">
                                    <div id="ownersCards" class="row">
                                        <!-- سيتم ملؤها بـ JavaScript -->
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Tabs -->
                    <ul class="nav nav-tabs mb-3">
                        <li class="nav-item">
                            <a class="nav-link active" onclick="showTab('transactions')">معاملات رأس المال</a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" onclick="showTab('owners')">إدارة الملاك</a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" onclick="showTab('profits')">توزيع الأرباح</a>
                        </li>
                    </ul>

                    <!-- Transactions Tab -->
                    <div id="transactionsTab" class="tab-content">
                        <div class="card">
                            <div class="card-body">
                                <div class="table-responsive">
                                    <table class="table table-striped table-hover">
                                        <thead class="table-dark">
                                            <tr>
                                                <th>التاريخ</th>
                                                <th>النوع</th>
                                                <th>المالك</th>
                                                <th>المبلغ</th>
                                                <th>البيان</th>
                                                <th>الإجراءات</th>
                                            </tr>
                                        </thead>
                                        <tbody id="transactionsTableBody">
                                            <!-- سيتم ملؤها بـ JavaScript -->
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Owners Tab -->
                    <div id="ownersTab" class="tab-content" style="display: none;">
                        <div class="card">
                            <div class="card-body">
                                <div class="table-responsive">
                                    <table class="table table-striped table-hover">
                                        <thead class="table-dark">
                                            <tr>
                                                <th>اسم المالك</th>
                                                <th>نسبة الملكية</th>
                                                <th>رأس المال المستثمر</th>
                                                <th>السحوبات</th>
                                                <th>صافي رأس المال</th>
                                                <th>الهاتف</th>
                                                <th>الإجراءات</th>
                                            </tr>
                                        </thead>
                                        <tbody id="ownersTableBody">
                                            <!-- سيتم ملؤها بـ JavaScript -->
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Profits Tab -->
                    <div id="profitsTab" class="tab-content" style="display: none;">
                        <div class="card">
                            <div class="card-header">
                                <h6>توزيع الأرباح على الملاك</h6>
                            </div>
                            <div class="card-body">
                                <div class="row mb-3">
                                    <div class="col-md-4">
                                        <label class="form-label">إجمالي الأرباح المراد توزيعها</label>
                                        <input type="number" id="totalProfitsToDistribute" class="form-control" step="0.01" onchange="calculateProfitDistribution()">
                                    </div>
                                    <div class="col-md-4">
                                        <label class="form-label">تاريخ التوزيع</label>
                                        <input type="date" id="distributionDate" class="form-control">
                                    </div>
                                    <div class="col-md-4">
                                        <label class="form-label">&nbsp;</label>
                                        <button class="btn btn-success d-block" onclick="executeProfitDistribution()">
                                            <i class="fas fa-check"></i> تنفيذ التوزيع
                                        </button>
                                    </div>
                                </div>
                                <div id="profitDistributionTable">
                                    <!-- سيتم ملؤها بـ JavaScript -->
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Owner Modal -->
    <div class="modal fade" id="ownerModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">إضافة مالك جديد</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <form id="ownerForm">
                        <div class="mb-3">
                            <label class="form-label">اسم المالك *</label>
                            <input type="text" id="ownerName" class="form-control" required>
                        </div>
                        <div class="mb-3">
                            <label class="form-label">نسبة الملكية (%) *</label>
                            <input type="number" id="ownershipPercentage" class="form-control" required min="0" max="100" step="0.01">
                        </div>
                        <div class="mb-3">
                            <label class="form-label">رأس المال الأولي</label>
                            <input type="number" id="initialCapital" class="form-control" value="0" step="0.01">
                        </div>
                        <div class="mb-3">
                            <label class="form-label">رقم الهاتف</label>
                            <input type="text" id="ownerPhone" class="form-control">
                        </div>
                        <div class="mb-3">
                            <label class="form-label">العنوان</label>
                            <textarea id="ownerAddress" class="form-control" rows="2"></textarea>
                        </div>
                        <div class="mb-3">
                            <label class="form-label">ملاحظات</label>
                            <textarea id="ownerNotes" class="form-control" rows="2"></textarea>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                    <button type="button" class="btn btn-primary" onclick="saveOwner()">حفظ</button>
                </div>
            </div>
        </div>
    </div>

    <!-- Capital Transaction Modal -->
    <div class="modal fade" id="capitalModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">معاملة رأس مال</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <form id="capitalForm">
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">نوع المعاملة *</label>
                                    <select id="transactionType" class="form-control" required>
                                        <option value="">اختر النوع</option>
                                        <option value="investment">استثمار رأس مال</option>
                                        <option value="withdrawal">سحوبات</option>
                                    </select>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">التاريخ *</label>
                                    <input type="date" id="transactionDate" class="form-control" required>
                                </div>
                            </div>
                        </div>
                        <div class="mb-3">
                            <label class="form-label">المالك *</label>
                            <select id="transactionOwner" class="form-control" required>
                                <!-- سيتم ملؤها بـ JavaScript -->
                            </select>
                        </div>
                        <div class="mb-3">
                            <label class="form-label">المبلغ *</label>
                            <input type="number" id="transactionAmount" class="form-control" required step="0.01">
                        </div>
                        <div class="mb-3">
                            <label class="form-label">البيان *</label>
                            <textarea id="transactionDescription" class="form-control" rows="3" required></textarea>
                        </div>
                        <div class="mb-3">
                            <label class="form-label">الصندوق المرتبط</label>
                            <select id="linkedCashBox" class="form-control">
                                <!-- سيتم ملؤها بـ JavaScript -->
                            </select>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                    <button type="button" class="btn btn-primary" onclick="saveCapitalTransaction()">حفظ</button>
                </div>
            </div>
        </div>
    </div>

    <!-- Success/Error Messages -->
    <div id="messages"></div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // البيانات المحلية
        let owners = [];
        let capitalTransactions = [];
        let cashBoxes = [];
        let nextOwnerId = 1;
        let nextTransactionId = 1;

        // تحميل البيانات عند بدء الصفحة
        document.addEventListener('DOMContentLoaded', function() {
            loadAllData();
            document.getElementById('transactionDate').value = new Date().toISOString().split('T')[0];
            document.getElementById('distributionDate').value = new Date().toISOString().split('T')[0];
        });

        // تحميل جميع البيانات
        function loadAllData() {
            loadOwners();
            loadCapitalTransactions();
            loadCashBoxes();
            displayCapitalSummary();
            displayOwnersCards();
            displayCapitalTransactions();
            displayOwners();
            loadOwnerOptions();
            loadCashBoxOptions();
        }

        // تحميل الملاك
        function loadOwners() {
            try {
                const savedOwners = localStorage.getItem('anw_owners');
                if (savedOwners && savedOwners !== 'null') {
                    owners = JSON.parse(savedOwners);
                    if (owners.length > 0) {
                        nextOwnerId = Math.max(...owners.map(o => o.id), 0) + 1;
                    }
                } else {
                    // إنشاء ملاك افتراضيين
                    owners = [
                        {
                            id: 1,
                            name: 'أحمد محمد الشريف',
                            ownershipPercentage: 60,
                            capitalInvested: 1000000,
                            withdrawals: 50000,
                            phone: '777-111111',
                            address: 'صنعاء - حي السبعين',
                            notes: 'الشريك الأول والمؤسس'
                        },
                        {
                            id: 2,
                            name: 'فاطمة علي السالم',
                            ownershipPercentage: 40,
                            capitalInvested: 500000,
                            withdrawals: 20000,
                            phone: '777-222222',
                            address: 'صنعاء - شارع الزبيري',
                            notes: 'الشريك الثاني'
                        }
                    ];
                    nextOwnerId = 3;
                    saveOwners();
                }
            } catch (error) {
                console.error('خطأ في تحميل الملاك:', error);
                showMessage('خطأ في تحميل البيانات', 'danger');
            }
        }

        // تحميل معاملات رأس المال
        function loadCapitalTransactions() {
            try {
                const savedTransactions = localStorage.getItem('anw_capital_transactions');
                if (savedTransactions && savedTransactions !== 'null') {
                    capitalTransactions = JSON.parse(savedTransactions);
                    if (capitalTransactions.length > 0) {
                        nextTransactionId = Math.max(...capitalTransactions.map(t => t.id), 0) + 1;
                    }
                } else {
                    // إنشاء معاملات افتراضية
                    capitalTransactions = [
                        {id: 1, date: '2024-01-01', type: 'investment', ownerId: 1, amount: 1000000, description: 'رأس المال الأولي للشريك الأول', cashBoxId: 1},
                        {id: 2, date: '2024-01-01', type: 'investment', ownerId: 2, amount: 500000, description: 'رأس المال الأولي للشريك الثاني', cashBoxId: 1},
                        {id: 3, date: '2024-01-15', type: 'withdrawal', ownerId: 1, amount: 50000, description: 'سحوبات شخصية', cashBoxId: 1}
                    ];
                    nextTransactionId = 4;
                    saveCapitalTransactions();
                }
            } catch (error) {
                console.error('خطأ في تحميل معاملات رأس المال:', error);
                showMessage('خطأ في تحميل البيانات', 'danger');
            }
        }

        // تحميل الصناديق
        function loadCashBoxes() {
            const savedCashBoxes = localStorage.getItem('anw_cashboxes');
            if (savedCashBoxes) {
                cashBoxes = JSON.parse(savedCashBoxes);
            }
        }

        // حفظ البيانات
        function saveOwners() {
            localStorage.setItem('anw_owners', JSON.stringify(owners));
        }

        function saveCapitalTransactions() {
            localStorage.setItem('anw_capital_transactions', JSON.stringify(capitalTransactions));
        }

        function saveCashBoxes() {
            localStorage.setItem('anw_cashboxes', JSON.stringify(cashBoxes));
        }

        // عرض ملخص رأس المال
        function displayCapitalSummary() {
            const summaryDiv = document.getElementById('capitalSummary');

            const totalCapitalInvested = owners.reduce((sum, owner) => sum + owner.capitalInvested, 0);
            const totalWithdrawals = owners.reduce((sum, owner) => sum + owner.withdrawals, 0);
            const netCapital = totalCapitalInvested - totalWithdrawals;
            const totalOwners = owners.length;

            summaryDiv.innerHTML = `
                <div class="col-md-3">
                    <div class="card border-primary">
                        <div class="card-body text-center">
                            <h5 class="card-title text-primary">${totalOwners}</h5>
                            <p class="card-text">عدد الملاك</p>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card border-success">
                        <div class="card-body text-center">
                            <h5 class="card-title text-success">${totalCapitalInvested.toLocaleString()} ر.ي</h5>
                            <p class="card-text">إجمالي رأس المال المستثمر</p>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card border-danger">
                        <div class="card-body text-center">
                            <h5 class="card-title text-danger">${totalWithdrawals.toLocaleString()} ر.ي</h5>
                            <p class="card-text">إجمالي السحوبات</p>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card border-info">
                        <div class="card-body text-center">
                            <h5 class="card-title text-info">${netCapital.toLocaleString()} ر.ي</h5>
                            <p class="card-text">صافي رأس المال</p>
                        </div>
                    </div>
                </div>
            `;
        }

        // عرض بطاقات الملاك
        function displayOwnersCards() {
            const cardsDiv = document.getElementById('ownersCards');
            let html = '';

            owners.forEach(owner => {
                const netCapital = owner.capitalInvested - owner.withdrawals;
                const statusClass = netCapital >= 0 ? 'success' : 'danger';

                html += `
                    <div class="col-md-6 mb-3">
                        <div class="card border-${statusClass}">
                            <div class="card-body">
                                <div class="d-flex justify-content-between align-items-center">
                                    <div>
                                        <h5 class="card-title">${owner.name}</h5>
                                        <p class="card-text">
                                            <span class="badge bg-primary">${owner.ownershipPercentage}% ملكية</span>
                                        </p>
                                    </div>
                                    <div class="text-end">
                                        <h4 class="text-${statusClass}">${netCapital.toLocaleString()} ر.ي</h4>
                                        <small class="text-muted">صافي رأس المال</small>
                                    </div>
                                </div>
                                <div class="row mt-2">
                                    <div class="col-6">
                                        <small class="text-muted">المستثمر: ${owner.capitalInvested.toLocaleString()} ر.ي</small>
                                    </div>
                                    <div class="col-6">
                                        <small class="text-muted">السحوبات: ${owner.withdrawals.toLocaleString()} ر.ي</small>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                `;
            });

            cardsDiv.innerHTML = html;
        }
        // عرض معاملات رأس المال
        function displayCapitalTransactions() {
            const tbody = document.getElementById('transactionsTableBody');

            if (capitalTransactions.length === 0) {
                tbody.innerHTML = `
                    <tr>
                        <td colspan="6" class="text-center text-muted">
                            لا توجد معاملات رأس مال
                            <br>
                            <button class="btn btn-primary mt-2" onclick="addCapitalInvestment()">إضافة معاملة جديدة</button>
                        </td>
                    </tr>
                `;
                return;
            }

            let html = '';
            capitalTransactions.forEach(transaction => {
                const owner = owners.find(o => o.id === transaction.ownerId);
                const typeLabel = transaction.type === 'investment' ? 'استثمار' : 'سحوبات';
                const typeBadge = transaction.type === 'investment' ? 'bg-success' : 'bg-danger';
                const amountClass = transaction.type === 'investment' ? 'text-success' : 'text-danger';

                html += `
                    <tr>
                        <td>${new Date(transaction.date).toLocaleDateString('ar-YE')}</td>
                        <td><span class="badge ${typeBadge}">${typeLabel}</span></td>
                        <td>${owner ? owner.name : 'غير محدد'}</td>
                        <td class="${amountClass} fw-bold">${transaction.amount.toLocaleString()} ر.ي</td>
                        <td>${transaction.description}</td>
                        <td>
                            <button class="btn btn-sm btn-info" onclick="printTransaction(${transaction.id})">
                                <i class="fas fa-print"></i>
                            </button>
                            <button class="btn btn-sm btn-danger" onclick="deleteTransaction(${transaction.id})">
                                <i class="fas fa-trash"></i>
                            </button>
                        </td>
                    </tr>
                `;
            });

            tbody.innerHTML = html;
        }

        // عرض الملاك
        function displayOwners() {
            const tbody = document.getElementById('ownersTableBody');

            if (owners.length === 0) {
                tbody.innerHTML = `
                    <tr>
                        <td colspan="7" class="text-center text-muted">
                            لا توجد ملاك
                            <br>
                            <button class="btn btn-primary mt-2" onclick="addOwner()">إضافة مالك جديد</button>
                        </td>
                    </tr>
                `;
                return;
            }

            let html = '';
            owners.forEach(owner => {
                const netCapital = owner.capitalInvested - owner.withdrawals;
                const netCapitalClass = netCapital >= 0 ? 'text-success' : 'text-danger';

                html += `
                    <tr>
                        <td>${owner.name}</td>
                        <td><span class="badge bg-primary">${owner.ownershipPercentage}%</span></td>
                        <td class="text-success fw-bold">${owner.capitalInvested.toLocaleString()} ر.ي</td>
                        <td class="text-danger">${owner.withdrawals.toLocaleString()} ر.ي</td>
                        <td class="${netCapitalClass} fw-bold">${netCapital.toLocaleString()} ر.ي</td>
                        <td>${owner.phone || '-'}</td>
                        <td>
                            <button class="btn btn-sm btn-warning" onclick="editOwner(${owner.id})">
                                <i class="fas fa-edit"></i>
                            </button>
                            <button class="btn btn-sm btn-info" onclick="viewOwnerStatement(${owner.id})">
                                <i class="fas fa-file-alt"></i>
                            </button>
                            <button class="btn btn-sm btn-danger" onclick="deleteOwner(${owner.id})">
                                <i class="fas fa-trash"></i>
                            </button>
                        </td>
                    </tr>
                `;
            });

            tbody.innerHTML = html;
        }

        // تحميل خيارات الملاك
        function loadOwnerOptions() {
            const select = document.getElementById('transactionOwner');

            let html = '<option value="">اختر المالك</option>';

            owners.forEach(owner => {
                html += `<option value="${owner.id}">${owner.name}</option>`;
            });

            select.innerHTML = html;
        }

        // تحميل خيارات الصناديق
        function loadCashBoxOptions() {
            const select = document.getElementById('linkedCashBox');

            let html = '<option value="">اختر الصندوق</option>';

            cashBoxes.filter(c => c.active).forEach(cashBox => {
                html += `<option value="${cashBox.id}">${cashBox.name}</option>`;
            });

            select.innerHTML = html;
        }

        // إضافة مالك جديد
        function addOwner() {
            clearOwnerForm();
            document.querySelector('#ownerModal .modal-title').textContent = 'إضافة مالك جديد';
            new bootstrap.Modal(document.getElementById('ownerModal')).show();
        }

        // إضافة استثمار رأس مال
        function addCapitalInvestment() {
            clearCapitalForm();
            document.getElementById('transactionType').value = 'investment';
            document.querySelector('#capitalModal .modal-title').textContent = 'استثمار رأس مال';
            new bootstrap.Modal(document.getElementById('capitalModal')).show();
        }

        // إضافة سحوبات
        function addWithdrawal() {
            clearCapitalForm();
            document.getElementById('transactionType').value = 'withdrawal';
            document.querySelector('#capitalModal .modal-title').textContent = 'سحوبات مالك';
            new bootstrap.Modal(document.getElementById('capitalModal')).show();
        }

        // حفظ المالك
        function saveOwner() {
            const form = document.getElementById('ownerForm');
            const editId = form.dataset.editId;

            const ownerData = {
                name: document.getElementById('ownerName').value,
                ownershipPercentage: parseFloat(document.getElementById('ownershipPercentage').value),
                capitalInvested: parseFloat(document.getElementById('initialCapital').value) || 0,
                withdrawals: 0,
                phone: document.getElementById('ownerPhone').value,
                address: document.getElementById('ownerAddress').value,
                notes: document.getElementById('ownerNotes').value
            };

            if (!ownerData.name || !ownerData.ownershipPercentage) {
                showMessage('يرجى ملء جميع الحقول المطلوبة', 'danger');
                return;
            }

            // التحقق من مجموع نسب الملكية
            const totalPercentage = owners.reduce((sum, owner) => {
                return owner.id == editId ? sum : sum + owner.ownershipPercentage;
            }, 0) + ownerData.ownershipPercentage;

            if (totalPercentage > 100) {
                showMessage('مجموع نسب الملكية لا يمكن أن يتجاوز 100%', 'danger');
                return;
            }

            if (editId) {
                const ownerIndex = owners.findIndex(o => o.id == editId);
                owners[ownerIndex] = { ...owners[ownerIndex], ...ownerData };
                showMessage('تم تحديث المالك بنجاح', 'success');
            } else {
                ownerData.id = nextOwnerId++;
                owners.push(ownerData);
                showMessage('تم إضافة المالك بنجاح', 'success');
            }

            saveOwners();
            loadAllData();
            bootstrap.Modal.getInstance(document.getElementById('ownerModal')).hide();
        }

        // حفظ معاملة رأس المال
        function saveCapitalTransaction() {
            const transactionData = {
                type: document.getElementById('transactionType').value,
                ownerId: parseInt(document.getElementById('transactionOwner').value),
                amount: parseFloat(document.getElementById('transactionAmount').value),
                date: document.getElementById('transactionDate').value,
                description: document.getElementById('transactionDescription').value,
                cashBoxId: document.getElementById('linkedCashBox').value ? parseInt(document.getElementById('linkedCashBox').value) : null
            };

            if (!transactionData.type || !transactionData.ownerId || !transactionData.amount || !transactionData.description) {
                showMessage('يرجى ملء جميع الحقول المطلوبة', 'danger');
                return;
            }

            transactionData.id = nextTransactionId++;

            // تحديث رصيد المالك
            const owner = owners.find(o => o.id === transactionData.ownerId);
            if (owner) {
                if (transactionData.type === 'investment') {
                    owner.capitalInvested += transactionData.amount;
                } else {
                    owner.withdrawals += transactionData.amount;
                }
            }

            // تحديث رصيد الصندوق
            if (transactionData.cashBoxId) {
                const cashBox = cashBoxes.find(c => c.id === transactionData.cashBoxId);
                if (cashBox) {
                    if (transactionData.type === 'investment') {
                        cashBox.balance += transactionData.amount;
                    } else {
                        cashBox.balance -= transactionData.amount;
                    }
                    saveCashBoxes();
                }
            }

            capitalTransactions.push(transactionData);
            saveCapitalTransactions();
            saveOwners();
            loadAllData();
            bootstrap.Modal.getInstance(document.getElementById('capitalModal')).hide();
            showMessage('تم حفظ المعاملة بنجاح', 'success');
        }

        // تنظيف النماذج
        function clearOwnerForm() {
            document.getElementById('ownerForm').reset();
            document.getElementById('ownerForm').removeAttribute('data-edit-id');
            document.getElementById('initialCapital').value = '0';
        }

        function clearCapitalForm() {
            document.getElementById('capitalForm').reset();
            document.getElementById('transactionDate').value = new Date().toISOString().split('T')[0];
        }

        // تبديل التبويبات
        function showTab(tabName) {
            document.getElementById('transactionsTab').style.display = 'none';
            document.getElementById('ownersTab').style.display = 'none';
            document.getElementById('profitsTab').style.display = 'none';

            document.querySelectorAll('.nav-link').forEach(link => {
                link.classList.remove('active');
            });

            if (tabName === 'transactions') {
                document.getElementById('transactionsTab').style.display = 'block';
            } else if (tabName === 'owners') {
                document.getElementById('ownersTab').style.display = 'block';
            } else if (tabName === 'profits') {
                document.getElementById('profitsTab').style.display = 'block';
            }

            event.target.classList.add('active');
        }

        // عرض رسالة
        function showMessage(message, type) {
            const messagesDiv = document.getElementById('messages');
            const alertHtml = `
                <div class="alert alert-${type} alert-dismissible fade show position-fixed"
                     style="top: 20px; left: 20px; z-index: 9999; min-width: 300px;">
                    ${message}
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
            `;
            messagesDiv.innerHTML = alertHtml;

            setTimeout(() => {
                const alert = messagesDiv.querySelector('.alert');
                if (alert) {
                    bootstrap.Alert.getOrCreateInstance(alert).close();
                }
            }, 3000);
        }

        console.log('✅ تم تحميل نظام إدارة الملاك بنجاح');
    </script>

    <style>
        .sidebar {
            height: 100vh;
            position: fixed;
            width: 25%;
        }
        .nav-link.active {
            background-color: rgba(255,255,255,0.1);
            border-radius: 5px;
        }
        .table th {
            background-color: #343a40;
            color: white;
        }
        .tab-content {
            animation: fadeIn 0.3s;
        }
        @keyframes fadeIn {
            from { opacity: 0; }
            to { opacity: 1; }
        }
    </style>
</body>
</html>
