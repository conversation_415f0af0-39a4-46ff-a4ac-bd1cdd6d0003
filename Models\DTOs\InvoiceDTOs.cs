using System.ComponentModel.DataAnnotations;

namespace ANWBakerySystem.Models
{
    /// <summary>
    /// DTO لعرض بيانات الفاتورة
    /// Invoice Data Transfer Object for display
    /// </summary>
    public class InvoiceDto
    {
        public int InvoiceId { get; set; }
        public string InvoiceNumber { get; set; } = string.Empty;
        public InvoiceType InvoiceType { get; set; }
        public DateTime InvoiceDate { get; set; }
        public DateTime? DueDate { get; set; }
        public int? PartyId { get; set; }
        public string? PartyName { get; set; }
        public string? PartyAddress { get; set; }
        public string? PartyPhone { get; set; }
        public decimal SubTotal { get; set; }
        public decimal DiscountAmount { get; set; }
        public decimal DiscountPercentage { get; set; }
        public decimal TaxAmount { get; set; }
        public decimal TaxPercentage { get; set; }
        public decimal TotalAmount { get; set; }
        public decimal PaidAmount { get; set; }
        public decimal RemainingAmount { get; set; }
        public PaymentMethod PaymentMethod { get; set; }
        public InvoiceStatus Status { get; set; }
        public string? Notes { get; set; }
        public string? ReferenceNumber { get; set; }
        public bool IsQuickSale { get; set; }
        public bool IsPrinted { get; set; }
        public DateTime? PrintedAt { get; set; }
        public int PrintCount { get; set; }
        public DateTime CreatedAt { get; set; }
        public DateTime? UpdatedAt { get; set; }
        public List<InvoiceItemDto> Items { get; set; } = new List<InvoiceItemDto>();
    }

    /// <summary>
    /// DTO لعرض بيانات صنف الفاتورة
    /// Invoice Item Data Transfer Object for display
    /// </summary>
    public class InvoiceItemDto
    {
        public int InvoiceItemId { get; set; }
        public int ItemId { get; set; }
        public string ItemName { get; set; } = string.Empty;
        public string? ItemCode { get; set; }
        public string? Barcode { get; set; }
        public int UnitId { get; set; }
        public string UnitName { get; set; } = string.Empty;
        public decimal Quantity { get; set; }
        public decimal UnitPrice { get; set; }
        public decimal DiscountAmount { get; set; }
        public decimal DiscountPercentage { get; set; }
        public decimal TaxAmount { get; set; }
        public decimal TaxPercentage { get; set; }
        public decimal TotalAmount { get; set; }
        public string? Notes { get; set; }
    }

    /// <summary>
    /// طلب إنشاء فاتورة جديدة
    /// Create Invoice Request
    /// </summary>
    public class CreateInvoiceRequest
    {
        [StringLength(50, ErrorMessage = "رقم الفاتورة يجب أن يكون أقل من 50 حرف")]
        public string? InvoiceNumber { get; set; }

        [Required(ErrorMessage = "نوع الفاتورة مطلوب")]
        public InvoiceType InvoiceType { get; set; }

        public DateTime? InvoiceDate { get; set; }

        public DateTime? DueDate { get; set; }

        public int? PartyId { get; set; }

        [StringLength(200, ErrorMessage = "اسم الطرف يجب أن يكون أقل من 200 حرف")]
        public string? PartyName { get; set; }

        [StringLength(500, ErrorMessage = "عنوان الطرف يجب أن يكون أقل من 500 حرف")]
        public string? PartyAddress { get; set; }

        [StringLength(100, ErrorMessage = "هاتف الطرف يجب أن يكون أقل من 100 حرف")]
        public string? PartyPhone { get; set; }

        public PaymentMethod? PaymentMethod { get; set; }

        [StringLength(500, ErrorMessage = "الملاحظات يجب أن تكون أقل من 500 حرف")]
        public string? Notes { get; set; }

        [StringLength(100, ErrorMessage = "رقم المرجع يجب أن يكون أقل من 100 حرف")]
        public string? ReferenceNumber { get; set; }

        public bool? IsQuickSale { get; set; }

        public List<CreateInvoiceItemRequest> Items { get; set; } = new List<CreateInvoiceItemRequest>();
    }

    /// <summary>
    /// طلب إنشاء صنف فاتورة جديد
    /// Create Invoice Item Request
    /// </summary>
    public class CreateInvoiceItemRequest
    {
        [Required(ErrorMessage = "معرف الصنف مطلوب")]
        public int ItemId { get; set; }

        [Required(ErrorMessage = "معرف الوحدة مطلوب")]
        public int UnitId { get; set; }

        [Required(ErrorMessage = "الكمية مطلوبة")]
        [Range(0.001, double.MaxValue, ErrorMessage = "الكمية يجب أن تكون أكبر من صفر")]
        public decimal Quantity { get; set; }

        [Required(ErrorMessage = "سعر الوحدة مطلوب")]
        [Range(0, double.MaxValue, ErrorMessage = "سعر الوحدة يجب أن يكون أكبر من أو يساوي صفر")]
        public decimal UnitPrice { get; set; }

        [Range(0, double.MaxValue, ErrorMessage = "مبلغ الخصم يجب أن يكون أكبر من أو يساوي صفر")]
        public decimal? DiscountAmount { get; set; }

        [Range(0, 100, ErrorMessage = "نسبة الخصم يجب أن تكون بين 0 و 100")]
        public decimal? DiscountPercentage { get; set; }

        [Range(0, 100, ErrorMessage = "نسبة الضريبة يجب أن تكون بين 0 و 100")]
        public decimal? TaxPercentage { get; set; }

        [StringLength(500, ErrorMessage = "الملاحظات يجب أن تكون أقل من 500 حرف")]
        public string? Notes { get; set; }
    }

    /// <summary>
    /// طلب تحديث فاتورة موجودة
    /// Update Invoice Request
    /// </summary>
    public class UpdateInvoiceRequest
    {
        [Required(ErrorMessage = "رقم الفاتورة مطلوب")]
        [StringLength(50, ErrorMessage = "رقم الفاتورة يجب أن يكون أقل من 50 حرف")]
        public string InvoiceNumber { get; set; } = string.Empty;

        [Required(ErrorMessage = "نوع الفاتورة مطلوب")]
        public InvoiceType InvoiceType { get; set; }

        [Required(ErrorMessage = "تاريخ الفاتورة مطلوب")]
        public DateTime InvoiceDate { get; set; }

        public DateTime? DueDate { get; set; }

        public int? PartyId { get; set; }

        [StringLength(200, ErrorMessage = "اسم الطرف يجب أن يكون أقل من 200 حرف")]
        public string? PartyName { get; set; }

        [StringLength(500, ErrorMessage = "عنوان الطرف يجب أن يكون أقل من 500 حرف")]
        public string? PartyAddress { get; set; }

        [StringLength(100, ErrorMessage = "هاتف الطرف يجب أن يكون أقل من 100 حرف")]
        public string? PartyPhone { get; set; }

        public PaymentMethod PaymentMethod { get; set; }

        [StringLength(500, ErrorMessage = "الملاحظات يجب أن تكون أقل من 500 حرف")]
        public string? Notes { get; set; }

        [StringLength(100, ErrorMessage = "رقم المرجع يجب أن يكون أقل من 100 حرف")]
        public string? ReferenceNumber { get; set; }

        public List<UpdateInvoiceItemRequest> Items { get; set; } = new List<UpdateInvoiceItemRequest>();
    }

    /// <summary>
    /// طلب تحديث صنف فاتورة موجود
    /// Update Invoice Item Request
    /// </summary>
    public class UpdateInvoiceItemRequest
    {
        public int? InvoiceItemId { get; set; } // null للأصناف الجديدة

        [Required(ErrorMessage = "معرف الصنف مطلوب")]
        public int ItemId { get; set; }

        [Required(ErrorMessage = "معرف الوحدة مطلوب")]
        public int UnitId { get; set; }

        [Required(ErrorMessage = "الكمية مطلوبة")]
        [Range(0.001, double.MaxValue, ErrorMessage = "الكمية يجب أن تكون أكبر من صفر")]
        public decimal Quantity { get; set; }

        [Required(ErrorMessage = "سعر الوحدة مطلوب")]
        [Range(0, double.MaxValue, ErrorMessage = "سعر الوحدة يجب أن يكون أكبر من أو يساوي صفر")]
        public decimal UnitPrice { get; set; }

        [Range(0, double.MaxValue, ErrorMessage = "مبلغ الخصم يجب أن يكون أكبر من أو يساوي صفر")]
        public decimal? DiscountAmount { get; set; }

        [Range(0, 100, ErrorMessage = "نسبة الخصم يجب أن تكون بين 0 و 100")]
        public decimal? DiscountPercentage { get; set; }

        [Range(0, 100, ErrorMessage = "نسبة الضريبة يجب أن تكون بين 0 و 100")]
        public decimal? TaxPercentage { get; set; }

        [StringLength(500, ErrorMessage = "الملاحظات يجب أن تكون أقل من 500 حرف")]
        public string? Notes { get; set; }
    }

    /// <summary>
    /// نتيجة عملية الفاتورة الواحدة
    /// Single Invoice Result
    /// </summary>
    public class InvoiceResult
    {
        public bool Success { get; set; }
        public string Message { get; set; } = string.Empty;
        public InvoiceDto? Invoice { get; set; }
        public List<string>? Errors { get; set; }
    }

    /// <summary>
    /// نتيجة عملية الفواتير المتعددة
    /// Multiple Invoices Result
    /// </summary>
    public class InvoicesResult
    {
        public bool Success { get; set; }
        public string Message { get; set; } = string.Empty;
        public List<InvoiceDto>? Invoices { get; set; }
        public int TotalCount { get; set; }
        public int Page { get; set; }
        public int PageSize { get; set; }
        public int TotalPages { get; set; }
        public List<string>? Errors { get; set; }
    }

    /// <summary>
    /// طلب طباعة الفاتورة
    /// Print Invoice Request
    /// </summary>
    public class PrintInvoiceRequest
    {
        [Required(ErrorMessage = "معرف الفاتورة مطلوب")]
        public int InvoiceId { get; set; }

        [Required(ErrorMessage = "نوع الطباعة مطلوب")]
        public PrintType PrintType { get; set; }

        public int? Copies { get; set; } = 1;
    }

    /// <summary>
    /// أنواع الطباعة
    /// Print Types
    /// </summary>
    public enum PrintType
    {
        [Display(Name = "طباعة عادية")]
        Normal = 1,
        [Display(Name = "طباعة حرارية")]
        Thermal = 2
    }
}
