using Microsoft.EntityFrameworkCore;
using ANWBakerySystem.Data;
using ANWBakerySystem.Services;
using ANWBakerySystem.Repositories;
using ANWBakerySystem.Models;

var builder = WebApplication.CreateBuilder(args);

// Add services to the container.
builder.Services.AddControllers();

// إضافة قاعدة البيانات SQLite
builder.Services.AddDbContext<ANWBakeryDbContext>(options =>
    options.UseSqlite("Data Source=ANWBakery.db"));

// إضافة الخدمات والمستودعات
builder.Services.AddScoped<IAuthService, AuthService>();
builder.Services.AddScoped<IUserRepository, UserRepository>();
builder.Services.AddScoped<IUnitRepository, UnitRepository>();
builder.Services.AddScoped<IItemRepository, ItemRepository>();
builder.Services.AddScoped<IPartyRepository, PartyRepository>();
builder.Services.AddScoped<IInvoiceRepository, InvoiceRepository>();
builder.Services.AddScoped<IInventoryRepository, InventoryRepository>();

// إضافة CORS للسماح بالوصول من الواجهة الأمامية
builder.Services.AddCors(options =>
{
    options.AddPolicy("AllowAll", policy =>
    {
        policy.AllowAnyOrigin()
              .AllowAnyMethod()
              .AllowAnyHeader();
    });
});

var app = builder.Build();

// إنشاء قاعدة البيانات الحقيقية تلقائياً
using (var scope = app.Services.CreateScope())
{
    var context = scope.ServiceProvider.GetRequiredService<ANWBakeryDbContext>();
    try
    {
        // إنشاء قاعدة البيانات إذا لم تكن موجودة
        bool created = context.Database.EnsureCreated();

        if (created)
        {
            Console.WriteLine("✅ تم إنشاء قاعدة البيانات SQLite الجديدة بنجاح");
            Console.WriteLine("📁 مسار قاعدة البيانات: ANWBakery.db");
        }
        else
        {
            Console.WriteLine("✅ قاعدة البيانات SQLite موجودة ومتصلة");
        }

        // التحقق من وجود بيانات
        var userCount = context.Users.Count();
        Console.WriteLine($"📊 عدد المستخدمين الحاليين: {userCount}");

        // إضافة مستخدم افتراضي إذا لم يكن موجود
        if (userCount == 0)
        {
            var adminUser = new User
            {
                Username = "admin",
                FullName = "مدير النظام",
                FullNameEn = "System Administrator",
                Email = "<EMAIL>",
                Phone = "+967-1-234567",
                Role = UserRole.Admin,
                IsActive = true,
                CreatedAt = DateTime.Now,
                PasswordHash = BCrypt.Net.BCrypt.HashPassword("admin123"),
                MustChangePassword = false
            };

            context.Users.Add(adminUser);
            Console.WriteLine("👤 تم إنشاء المستخدم الافتراضي: admin / admin123");
        }

        // تم إزالة إنشاء الحسابات - نظام مبسط بدون محاسبة
        Console.WriteLine("📋 نظام مبسط - لا توجد حسابات محاسبية");

        context.SaveChanges();

    }
    catch (Exception ex)
    {
        Console.WriteLine($"❌ خطأ في إنشاء قاعدة البيانات: {ex.Message}");
        Console.WriteLine("💡 تأكد من صلاحيات الكتابة في مجلد التطبيق");
    }
}

// استخدام CORS
app.UseCors("AllowAll");

// إضافة Controllers
app.MapControllers();

// Serve static files for frontend
app.UseStaticFiles();

// توجيه الصفحة الرئيسية مباشرة لصفحة المنتجات
app.MapGet("/", () => Results.Redirect("/items-simple.html"));

// Fallback to items page for SPA routing
app.MapFallbackToFile("items-simple.html");

// Add health check endpoint
app.MapGet("/health", () => new {
    Status = "Healthy",
    Timestamp = DateTime.Now,
    Version = "1.0.0",
    System = "ANW Bakery Management System - SQLite Database",
    Database = "SQLite Connected",
    DataPersistence = "File-based (ANWBakery.db)"
});

Console.WriteLine("========================================");
Console.WriteLine("   نظام إدارة مخبوزات ANW - قاعدة بيانات SQLite");
Console.WriteLine("========================================");
Console.WriteLine("✅ الخادم يعمل على: http://localhost:5000");
Console.WriteLine("✅ قاعدة البيانات SQLite متصلة");
Console.WriteLine("✅ البيانات محفوظة في ملف ANWBakery.db");
Console.WriteLine("✅ جميع الوظائف متاحة");
Console.WriteLine("========================================");
Console.WriteLine("📝 بيانات تسجيل الدخول:");
Console.WriteLine("   👤 اسم المستخدم: admin");
Console.WriteLine("   🔑 كلمة المرور: admin123");
Console.WriteLine("========================================");

app.Run("http://localhost:5000");
