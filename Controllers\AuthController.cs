using Microsoft.AspNetCore.Mvc;
using ANWBakerySystem.Data;
using ANWBakerySystem.Models;
using Microsoft.IdentityModel.Tokens;
using System.IdentityModel.Tokens.Jwt;
using System.Security.Claims;
using System.Text;
using Microsoft.EntityFrameworkCore;

namespace ANWBakerySystem.Controllers
{
    [ApiController]
    [Route("api/[controller]")]
    public class AuthController : ControllerBase
    {
        private readonly ANWBakeryDbContext _context;
        private readonly IConfiguration _configuration;

        public AuthController(ANWBakeryDbContext context, IConfiguration configuration)
        {
            _context = context;
            _configuration = configuration;
        }

        [HttpPost("login")]
        public IActionResult Login([FromBody] LoginRequest request)
        {
            try
            {
                // التحقق من البيانات المرسلة
                if (string.IsNullOrEmpty(request.Username) || string.IsNullOrEmpty(request.Password))
                {
                    return BadRequest(new { 
                        success = false, 
                        message = "يرجى إدخال اسم المستخدم وكلمة المرور" 
                    });
                }

                // مستخدم تجريبي للاختبار
                if (request.Username == "admin" && request.Password == "admin123")
                {
                    return Ok(new { 
                        success = true, 
                        message = "تم تسجيل الدخول بنجاح",
                        user = new {
                            id = 1,
                            username = "admin",
                            fullName = "مدير النظام",
                            role = "Admin"
                        },
                        token = "demo-token-12345"
                    });
                }

                // التحقق من قاعدة البيانات
                var user = _context.Users.FirstOrDefault(u => 
                    u.Username == request.Username && u.IsActive);

                if (user == null)
                {
                    return Unauthorized(new { 
                        success = false, 
                        message = "اسم المستخدم غير صحيح" 
                    });
                }

                // التحقق من كلمة المرور (مبسط للاختبار)
                bool passwordValid = false;

                // جرب كلمة المرور مباشرة أولاً
                if (user.PasswordHash == request.Password)
                {
                    passwordValid = true;
                }
                // ثم جرب BCrypt إذا كان متاحاً
                else
                {
                    try
                    {
                        passwordValid = BCrypt.Net.BCrypt.Verify(request.Password, user.PasswordHash);
                    }
                    catch
                    {
                        // إذا فشل BCrypt، استخدم مقارنة مباشرة
                        passwordValid = (user.PasswordHash == request.Password);
                    }
                }

                if (!passwordValid)
                {
                    return Unauthorized(new {
                        success = false,
                        message = "كلمة المرور غير صحيحة"
                    });
                }

                // إنشاء JWT Token
                var token = GenerateJwtToken(user);

                return Ok(new {
                    success = true,
                    message = "تم تسجيل الدخول بنجاح",
                    user = new {
                        id = user.UserId,
                        username = user.Username,
                        fullName = user.FullName,
                        role = user.Role.ToString()
                    },
                    token = token
                });
            }
            catch (Exception ex)
            {
                return StatusCode(500, new { 
                    success = false, 
                    message = "خطأ في الخادم: " + ex.Message 
                });
            }
        }

        [HttpPost("logout")]
        public IActionResult Logout()
        {
            return Ok(new { 
                success = true, 
                message = "تم تسجيل الخروج بنجاح" 
            });
        }

        [HttpPost("validate")]
        public IActionResult ValidateToken([FromBody] ValidateTokenRequest request)
        {
            try
            {
                // هنا يمكن إضافة منطق التحقق من صحة الرمز
                // للآن سنعتبر أي رمز صالح
                return Ok(new {
                    success = true,
                    message = "الرمز صالح"
                });
            }
            catch
            {
                return BadRequest(new {
                    success = false,
                    message = "رمز غير صالح"
                });
            }
        }

        [HttpGet("test")]
        public IActionResult Test()
        {
            return Ok(new {
                message = "API يعمل بشكل صحيح",
                timestamp = DateTime.Now,
                server = "ANW Bakery System"
            });
        }

        private string GenerateJwtToken(User user)
        {
            var securityKey = new SymmetricSecurityKey(Encoding.UTF8.GetBytes(_configuration["JwtSettings:SecretKey"]));
            var credentials = new SigningCredentials(securityKey, SecurityAlgorithms.HmacSha256);

            var claims = new[]
            {
                new Claim(ClaimTypes.NameIdentifier, user.UserId.ToString()),
                new Claim(ClaimTypes.Name, user.Username),
                new Claim(ClaimTypes.GivenName, user.FullName),
                new Claim(ClaimTypes.Role, user.Role.ToString())
            };

            var token = new JwtSecurityToken(
                issuer: _configuration["JwtSettings:Issuer"],
                audience: _configuration["JwtSettings:Audience"],
                claims: claims,
                expires: DateTime.Now.AddHours(Convert.ToDouble(_configuration["JwtSettings:ExpiryInHours"])),
                signingCredentials: credentials
            );

            return new JwtSecurityTokenHandler().WriteToken(token);
        }
    }

    public class LoginRequest
    {
        public string Username { get; set; } = string.Empty;
        public string Password { get; set; } = string.Empty;
    }

    public class ValidateTokenRequest
    {
        public string Token { get; set; } = string.Empty;
    }
}
