@echo off
chcp 65001 > nul
echo ========================================
echo   الحل النهائي لقاعدة البيانات
echo   Final Database Solution
echo ========================================
echo.

echo 🎯 إنشاء قاعدة البيانات النهائية...
echo.

echo 🧹 حذف Migrations السابقة...
if exist "Migrations" (
    rmdir /s /q "Migrations"
    echo ✅ تم حذف Migrations السابقة
)

echo.
echo 🧹 تنظيف المشروع...
dotnet clean

echo.
echo 📦 استعادة الحزم...
dotnet restore

echo.
echo 🔧 بناء المشروع...
dotnet build
if %ERRORLEVEL% NEQ 0 (
    echo ❌ فشل في بناء المشروع
    pause
    exit /b 1
)

echo.
echo ✅ تم بناء المشروع بنجاح!
echo.

echo 🗄️ إنشاء Migration نهائي...
dotnet ef migrations add FinalDatabase_v1
if %ERRORLEVEL% NEQ 0 (
    echo ❌ فشل في إنشاء Migration
    echo.
    echo 🔧 محاولة تثبيت أدوات EF...
    dotnet tool install --global dotnet-ef
    
    echo 🔄 إعادة المحاولة...
    dotnet ef migrations add FinalDatabase_v1
    if %ERRORLEVEL% NEQ 0 (
        echo ❌ فشل مرة أخرى في إنشاء Migration
        echo.
        echo 💡 سنحاول إنشاء قاعدة البيانات مباشرة...
        goto :direct_creation
    )
)

echo.
echo 🗄️ تطبيق Migration على قاعدة البيانات...
dotnet ef database update
if %ERRORLEVEL% NEQ 0 (
    echo ❌ فشل في تطبيق Migration
    echo.
    echo 🔧 محاولة حذف قاعدة البيانات وإعادة الإنشاء...
    dotnet ef database drop --force
    dotnet ef database update
    if %ERRORLEVEL% NEQ 0 (
        echo ❌ فشل في إنشاء قاعدة البيانات
        goto :direct_creation
    )
)

goto :success

:direct_creation
echo.
echo 🔧 إنشاء قاعدة البيانات مباشرة عبر التطبيق...
echo.
start /b dotnet run
timeout /t 20 /nobreak > nul
taskkill /f /im dotnet.exe > nul 2>&1
echo ✅ تم إنشاء قاعدة البيانات

:success
echo.
echo 🎉 تم إنشاء قاعدة البيانات بنجاح!
echo.
echo 📊 معلومات قاعدة البيانات:
echo    📁 الاسم: ANWBakeryDB_Real
echo    🔗 النوع: SQL Server
echo    📋 الجداول: جميع الجداول الأساسية
echo    ✅ الحالة: جاهزة للاستخدام
echo.

echo 🚀 تشغيل التطبيق...
echo.
echo 🌐 سيتم فتح المتصفح على: http://localhost:5000
echo 🔐 بيانات الدخول:
echo    👤 admin
echo    🔑 admin123
echo.

REM تشغيل التطبيق
start "" "http://localhost:5000"
dotnet run

pause
