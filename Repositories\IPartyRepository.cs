using ANWBakerySystem.Models;

namespace ANWBakerySystem.Repositories
{
    /// <summary>
    /// واجهة مستودع الأطراف (عملاء وموردين)
    /// Party Repository Interface (Customers and Suppliers)
    /// </summary>
    public interface IPartyRepository
    {
        Task<IEnumerable<Party>> GetAllPartiesAsync();
        Task<IEnumerable<Party>> GetActivePartiesAsync();
        Task<IEnumerable<Party>> GetCustomersAsync();
        Task<IEnumerable<Party>> GetSuppliersAsync();
        Task<IEnumerable<Party>> GetEmployeesAsync();
        Task<Party?> GetPartyByIdAsync(int partyId);
        Task<Party?> GetPartyByCodeAsync(string partyCode);
        Task<Party> CreatePartyAsync(Party party);
        Task<Party> UpdatePartyAsync(Party party);
        Task<bool> DeletePartyAsync(int partyId);
        Task<bool> PartyExistsAsync(int partyId);
        Task<bool> PartyCodeExistsAsync(string partyCode);
        
        // إدارة الأرصدة
        // Balance Management
        Task<decimal> GetPartyBalanceAsync(int partyId);
        Task<bool> UpdatePartyBalanceAsync(int partyId, decimal amount, bool isDebit);
        Task<bool> CheckCreditLimitAsync(int partyId, decimal amount);
        
        // معاملات الأطراف
        // Party Transactions
        Task<PartyTransaction> CreateTransactionAsync(PartyTransaction transaction);
        Task<IEnumerable<PartyTransaction>> GetPartyTransactionsAsync(int partyId);
        Task<IEnumerable<PartyTransaction>> GetPartyTransactionsAsync(int partyId, DateTime fromDate, DateTime toDate);
        Task<decimal> GetPartyBalanceAtDateAsync(int partyId, DateTime date);
        
        // تقارير الأطراف
        // Party Reports
        Task<IEnumerable<Party>> GetPartiesWithBalanceAsync();
        Task<IEnumerable<Party>> GetPartiesOverCreditLimitAsync();
        Task<IEnumerable<Party>> GetPartiesWithOverdueInvoicesAsync();
        Task<decimal> GetTotalCustomersBalanceAsync();
        Task<decimal> GetTotalSuppliersBalanceAsync();
    }
}
