// إدارة الفواتير - متصل بـ APIs
const API_BASE_URL = '/api';
let invoices = [];
let filteredInvoices = [];
let parties = [];
let items = [];
let invoiceItems = [];
let itemCounter = 0;
let currentInvoiceType = '';
let cashboxes = [];

// تحميل الصفحة
document.addEventListener('DOMContentLoaded', function() {
    console.log('🔄 تحميل صفحة الفواتير...');
    initializePage();
});

// تهيئة الصفحة
async function initializePage() {
    try {
        // تعيين التاريخ والوقت الحالي
        const now = new Date();
        document.getElementById('invoiceDate').value = now.toISOString().split('T')[0];
        document.getElementById('invoiceTime').value = now.toTimeString().slice(0, 5);

        await loadCashboxes();
        await loadParties();
        await loadItems();
        await loadInvoices();

        console.log('✅ تم تحميل صفحة الفواتير بنجاح');
    } catch (error) {
        console.error('❌ خطأ في تحميل صفحة الفواتير:', error);
        showError('خطأ في تحميل البيانات. يرجى إعادة تحميل الصفحة.');
    }
}

// تحميل الصناديق والبنوك
async function loadCashboxes() {
    console.log('🔄 تحميل الصناديق والبنوك...');

    try {
        const response = await fetch(`${API_BASE_URL}/cashboxes`);
        if (response.ok) {
            const result = await response.json();
            if (result.success) {
                cashboxes = result.data || [];
                populateCashboxDropdown();
                console.log('✅ تم تحميل الصناديق:', cashboxes.length);
                return;
            }
        }

        // في حالة فشل API، استخدم بيانات تجريبية
        console.warn('⚠️ فشل تحميل الصناديق من API، استخدام بيانات تجريبية');
        loadSampleCashboxes();

    } catch (error) {
        console.error('❌ خطأ في تحميل الصناديق:', error);
        loadSampleCashboxes();
    }
}

// تحميل بيانات تجريبية للصناديق
function loadSampleCashboxes() {
    cashboxes = [
        {
            cashboxId: 1,
            cashboxName: 'الصندوق الرئيسي',
            cashboxType: 'نقدي',
            currentBalance: 150000.000,
            isActive: true
        },
        {
            cashboxId: 2,
            cashboxName: 'بنك الكريمي',
            cashboxType: 'بنكي',
            accountNumber: '*********',
            currentBalance: 500000.000,
            isActive: true
        },
        {
            cashboxId: 3,
            cashboxName: 'صندوق المبيعات',
            cashboxType: 'نقدي',
            currentBalance: 75000.000,
            isActive: true
        },
        {
            cashboxId: 4,
            cashboxName: 'بنك سبأ',
            cashboxType: 'بنكي',
            accountNumber: '*********',
            currentBalance: 250000.000,
            isActive: true
        }
    ];

    populateCashboxDropdown();
    console.log('📦 تم تحميل الصناديق التجريبية:', cashboxes.length);
}

// ملء قائمة الصناديق
function populateCashboxDropdown() {
    const cashboxSelect = document.getElementById('cashboxSelect');
    if (!cashboxSelect) return;

    cashboxSelect.innerHTML = '<option value="">-- اختر الصندوق --</option>';

    cashboxes.filter(cb => cb.isActive).forEach(cashbox => {
        const option = document.createElement('option');
        option.value = cashbox.cashboxId;

        const typeIcon = cashbox.cashboxType === 'نقدي' ? '💰' : '🏦';
        const balanceText = formatCurrency(cashbox.currentBalance);

        option.textContent = `${typeIcon} ${cashbox.cashboxName} (${balanceText})`;
        option.dataset.balance = cashbox.currentBalance;
        option.dataset.type = cashbox.cashboxType;

        cashboxSelect.appendChild(option);
    });
}

// تحميل العملاء والموردين من API
async function loadParties() {
    console.log('🔄 تحميل العملاء والموردين...');

    try {
        const response = await fetch(`${API_BASE_URL}/parties`);
        if (response.ok) {
            const result = await response.json();
            if (result.success) {
                parties = result.data || [];
                console.log('✅ تم تحميل العملاء والموردين:', parties.length);
                return;
            }
        }

        // في حالة فشل API، استخدم بيانات تجريبية
        console.warn('⚠️ فشل تحميل العملاء والموردين من API، استخدام بيانات تجريبية');
        loadSampleParties();

    } catch (error) {
        console.error('❌ خطأ في تحميل العملاء والموردين:', error);
        loadSampleParties();
    }
}

// تحميل بيانات تجريبية للعملاء والموردين
function loadSampleParties() {
    parties = [
        {
            partyId: 1,
            partyName: 'مخبز الأمل',
            partyType: 1, // Customer
            currentBalance: 15000.000,
            creditLimit: 50000.000
        },
        {
            partyId: 2,
            partyName: 'شركة الدقيق الذهبي',
            partyType: 2, // Supplier
            currentBalance: -25000.000,
            creditLimit: 0
        },
        {
            partyId: 3,
            partyName: 'محمد أحمد التجاري',
            partyType: 3, // Both
            currentBalance: 5000.000,
            creditLimit: 30000.000
        }
    ];

    console.log('📦 تم تحميل العملاء والموردين التجريبية:', parties.length);
}

// تحميل المنتجات من API
async function loadItems() {
    console.log('🔄 تحميل المنتجات...');

    try {
        const response = await fetch(`${API_BASE_URL}/items/active`);
        if (response.ok) {
            const result = await response.json();
            if (result.success) {
                items = result.data || [];
                console.log('✅ تم تحميل المنتجات:', items.length);
                return;
            }
        }

        // في حالة فشل API، استخدم بيانات تجريبية
        console.warn('⚠️ فشل تحميل المنتجات من API، استخدام بيانات تجريبية');
        loadSampleItems();

    } catch (error) {
        console.error('❌ خطأ في تحميل المنتجات:', error);
        loadSampleItems();
    }
}

// تحميل بيانات تجريبية للمنتجات
function loadSampleItems() {
    items = [
        {
            itemId: 1,
            itemName: 'خبز أبيض عادي',
            itemCode: 'BREAD001',
            barcode: '*********0123',
            majorUnit: { unitName: 'قطعة' },
            salePrice: 75.000,
            purchasePrice: 45.000,
            currentStock: 150,
            itemType: 1 // Product
        },
        {
            itemId: 2,
            itemName: 'دقيق أبيض فاخر',
            itemCode: 'FLOUR001',
            barcode: '2345678901234',
            majorUnit: { unitName: 'كيلوجرام' },
            salePrice: 950.000,
            purchasePrice: 800.000,
            currentStock: 25.5,
            itemType: 2 // RawMaterial
        },
        {
            itemId: 3,
            itemName: 'خدمة توصيل',
            itemCode: 'SERV001',
            barcode: '4567890123456',
            majorUnit: { unitName: 'مرة' },
            salePrice: 500.000,
            purchasePrice: 0,
            currentStock: 0,
            itemType: 3 // Service
        }
    ];

    console.log('📦 تم تحميل المنتجات التجريبية:', items.length);

    // طباعة قائمة الأصناف للمساعدة في الاختبار
    console.log('قائمة الأصناف المتاحة:');
    items.forEach(item => {
        console.log(`- ${item.itemName} | كود: ${item.itemCode} | باركود: ${item.barcode}`);
    });
}

// إنشاء فاتورة جديدة
function createInvoice(type) {
    console.log('إنشاء فاتورة نوع:', type);
    
    currentInvoiceType = type;
    invoiceItems = [];
    itemCounter = 0;
    
    // إظهار نموذج الفاتورة
    document.getElementById('invoiceFormCard').style.display = 'block';
    document.getElementById('invoiceType').value = type;
    
    // تعيين عنوان الفاتورة
    const titles = {
        'sale': 'فاتورة مبيعات',
        'purchase': 'فاتورة مشتريات',
        'sale_return': 'مرتجع مبيعات',
        'purchase_return': 'مرتجع مشتريات',
        'quick_sale': 'بيع سريع (نقدي)'
    };

    document.getElementById('invoiceTitle').textContent = titles[type];

    // توليد رقم فاتورة
    const prefix = {
        'sale': 'SAL',
        'purchase': 'PUR',
        'sale_return': 'SRT',
        'purchase_return': 'PRT',
        'quick_sale': 'QSL'
    };
    
    const invoiceNumber = prefix[type] + '-' + Date.now().toString().slice(-6);
    document.getElementById('invoiceNumber').value = invoiceNumber;
    
    // تحديد نوع الطرف
    const partyLabel = document.getElementById('partyLabel');
    const partySection = document.getElementById('partySection');
    
    if (type === 'quick_sale') {
        // البيع السريع لا يحتاج عميل محدد
        partySection.style.display = 'none';
        document.getElementById('paymentMethod').value = 'cash';
        document.getElementById('paymentMethod').disabled = true;
    } else {
        partySection.style.display = 'block';
        document.getElementById('paymentMethod').disabled = false;
        
        // جميع الفواتير تحتمل عميل أو مورد
        partyLabel.textContent = 'العميل/المورد *';
        populatePartiesDropdown(type);
    }
    
    // تعيين الصندوق الافتراضي
    setDefaultCashbox(type);

    // تحديث جدول الأصناف
    updateInvoiceItemsTable();

    // التمرير للنموذج
    document.getElementById('invoiceFormCard').scrollIntoView({ behavior: 'smooth' });

    // تركيز على مربع البحث
    setTimeout(() => {
        const searchInput = document.getElementById('itemSearch');
        if (searchInput) {
            searchInput.focus();
            console.log('تم التركيز على مربع البحث');
        }
    }, 500);
}

// ملء قائمة العملاء/الموردين حسب نوع الفاتورة
function populatePartiesDropdown(invoiceType) {
    const partySelect = document.getElementById('partySelect');
    partySelect.innerHTML = '<option value="">-- اختر العميل/المورد --</option>';

    // تصفية الأطراف حسب نوع الفاتورة
    const filteredParties = parties.filter(party => {
        if (invoiceType === 'sale' || invoiceType === 'sale_return') {
            // فواتير المبيعات - عملاء فقط
            return party.partyType === 1 || party.partyType === 3; // Customer or Both
        } else if (invoiceType === 'purchase' || invoiceType === 'purchase_return') {
            // فواتير المشتريات - موردين فقط
            return party.partyType === 2 || party.partyType === 3; // Supplier or Both
        }
        return true; // جميع الأطراف للأنواع الأخرى
    });

    filteredParties.forEach(party => {
        const option = document.createElement('option');
        option.value = party.partyId;

        // تحديد نوع الطرف
        let partyTypeText = '';
        switch(party.partyType) {
            case 1: partyTypeText = ' (عميل)'; break;
            case 2: partyTypeText = ' (مورد)'; break;
            case 3: partyTypeText = ' (عميل ومورد)'; break;
        }

        option.textContent = party.partyName + partyTypeText;
        option.dataset.balance = party.currentBalance || 0;
        option.dataset.creditLimit = party.creditLimit || 0;
        option.dataset.partyType = party.partyType;
        partySelect.appendChild(option);
    });
}

// تعيين الصندوق الافتراضي حسب نوع الفاتورة
function setDefaultCashbox(invoiceType) {
    const cashboxSelect = document.getElementById('cashboxSelect');
    if (!cashboxSelect || cashboxes.length === 0) return;

    let defaultCashboxId = null;

    // تحديد الصندوق الافتراضي حسب نوع الفاتورة
    if (invoiceType === 'sale' || invoiceType === 'quick_sale' || invoiceType === 'purchase_return') {
        // للمبيعات ومرتجع المشتريات - الصندوق الرئيسي أو صندوق المبيعات
        const salesCashbox = cashboxes.find(cb => cb.cashboxName.includes('مبيعات') || cb.cashboxName.includes('الرئيسي'));
        defaultCashboxId = salesCashbox ? salesCashbox.cashboxId : cashboxes[0].cashboxId;
    } else if (invoiceType === 'purchase' || invoiceType === 'sale_return') {
        // للمشتريات ومرتجع المبيعات - أي صندوق متاح
        defaultCashboxId = cashboxes[0].cashboxId;
    }

    if (defaultCashboxId) {
        cashboxSelect.value = defaultCashboxId;
    }
}

// تحميل معلومات الطرف
function loadPartyInfo() {
    const partySelect = document.getElementById('partySelect');
    const selectedOption = partySelect.options[partySelect.selectedIndex];
    
    if (selectedOption.value) {
        const balance = parseFloat(selectedOption.dataset.balance);
        document.getElementById('partyBalance').value = formatCurrency(balance);
    } else {
        document.getElementById('partyBalance').value = '';
    }
}

// تبديل حقول الدفع
function togglePaymentFields() {
    const paymentMethod = document.getElementById('paymentMethod').value;
    const paidAmountSection = document.getElementById('paidAmountSection');
    const cashboxSection = document.getElementById('cashboxSection');
    const cashboxSelect = document.getElementById('cashboxSelect');

    if (paymentMethod === 'partial') {
        paidAmountSection.style.display = 'block';
    } else {
        paidAmountSection.style.display = 'none';
        document.getElementById('paidAmount').value = '';
    }

    // إظهار/إخفاء الصندوق حسب طريقة الدفع
    if (paymentMethod === 'credit') {
        // الدفع الآجل لا يحتاج صندوق
        cashboxSection.style.display = 'none';
        cashboxSelect.required = false;
    } else {
        // النقدي والدفع الجزئي يحتاجان صندوق
        cashboxSection.style.display = 'block';
        cashboxSelect.required = true;
    }
}

// البحث وإضافة صنف بالباركود أو الكود أو الاسم
function searchAndAddItem(event) {
    console.log('البحث - المفتاح المضغوط:', event.key, 'القيمة:', event.target.value);

    if (event.key === 'Enter') {
        event.preventDefault(); // منع إرسال النموذج

        const searchValue = event.target.value.trim();
        console.log('البحث عن:', searchValue);

        if (!searchValue) {
            alert('يرجى إدخال باركود أو كود أو اسم الصنف');
            return;
        }

        // التحقق من وجود نوع فاتورة
        if (!currentInvoiceType) {
            alert('يرجى اختيار نوع الفاتورة أولاً');
            return;
        }

        // البحث في جميع الأصناف (منتجات، خامات، خدمات)
        console.log('البحث في', items.length, 'صنف...');

        const foundItem = items.find(item => {
            const barcodeMatch = item.barcode && item.barcode.toString() === searchValue;
            const codeMatch = item.itemCode && item.itemCode.toLowerCase() === searchValue.toLowerCase();
            const nameMatch = item.itemName && item.itemName.toLowerCase().includes(searchValue.toLowerCase());

            console.log('فحص الصنف:', item.itemName, {
                barcode: item.barcode,
                code: item.itemCode,
                barcodeMatch,
                codeMatch,
                nameMatch
            });

            return barcodeMatch || codeMatch || nameMatch;
        });

        if (foundItem) {
            console.log('تم العثور على الصنف:', foundItem);

            // التحقق من وجود الصنف في الفاتورة
            const existingItem = invoiceItems.find(inv => inv.itemId === foundItem.itemId);

            if (existingItem) {
                // زيادة الكمية إذا كان موجود
                existingItem.quantity += 1;
                existingItem.total = existingItem.quantity * existingItem.price;
                console.log('تم زيادة كمية الصنف:', foundItem.itemName, 'الكمية الجديدة:', existingItem.quantity);

                // إظهار رسالة تأكيد
                showItemAddedMessage(`تم زيادة كمية ${foundItem.itemName} إلى ${existingItem.quantity}`);
            } else {
                // تحديد السعر حسب نوع الفاتورة
                let itemPrice = foundItem.salePrice; // افتراضي للمبيعات

                if (currentInvoiceType === 'purchase' || currentInvoiceType === 'purchase_return') {
                    itemPrice = foundItem.purchasePrice;
                } else if (currentInvoiceType === 'sale' || currentInvoiceType === 'sale_return' || currentInvoiceType === 'quick_sale') {
                    itemPrice = foundItem.salePrice;
                }

                // إضافة صنف جديد
                const newItem = {
                    id: ++itemCounter,
                    itemId: foundItem.itemId,
                    itemName: foundItem.itemName,
                    itemCode: foundItem.itemCode,
                    barcode: foundItem.barcode,
                    quantity: 1,
                    unitName: foundItem.unitName,
                    price: itemPrice,
                    total: itemPrice,
                    type: foundItem.type
                };

                invoiceItems.push(newItem);
                console.log('تم إضافة صنف جديد:', foundItem.itemName, 'بسعر:', itemPrice);

                // إظهار رسالة تأكيد
                const itemTypeText = getItemTypeText(foundItem.type);
                showItemAddedMessage(`تم إضافة ${itemTypeText}: ${foundItem.itemName}`);
            }

            updateInvoiceItemsTable();
            event.target.value = ''; // مسح البحث
            event.target.focus(); // العودة للبحث

        } else {
            console.log('لم يتم العثور على الصنف:', searchValue);
            console.log('الأصناف المتاحة:', items.map(item => ({
                name: item.itemName,
                code: item.itemCode,
                barcode: item.barcode
            })));

            alert(`لم يتم العثور على الصنف: "${searchValue}"\n\nيمكنك البحث بـ:\n• الباركود (مثال: *********0123)\n• الكود (مثال: BREAD001)\n• الاسم (مثال: خبز أبيض)\n\nتأكد من كتابة البيانات بشكل صحيح.`);
        }
    }
}

// إظهار رسالة تأكيد إضافة الصنف
function showItemAddedMessage(message) {
    // إنشاء عنصر الرسالة
    const alertDiv = document.createElement('div');
    alertDiv.className = 'alert alert-success alert-dismissible fade show position-fixed';
    alertDiv.style.cssText = 'top: 20px; right: 20px; z-index: 9999; min-width: 300px;';
    alertDiv.innerHTML = `
        <i class="fas fa-check-circle"></i> ${message}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    `;

    // إضافة الرسالة للصفحة
    document.body.appendChild(alertDiv);

    // إزالة الرسالة تلقائياً بعد 3 ثوان
    setTimeout(() => {
        if (alertDiv.parentNode) {
            alertDiv.parentNode.removeChild(alertDiv);
        }
    }, 3000);
}

// دالة اختبار البحث السريع
function testSearch(searchValue) {
    const searchInput = document.getElementById('itemSearch');
    if (searchInput) {
        searchInput.value = searchValue;
        searchInput.focus();

        // محاكاة ضغط Enter
        const event = new KeyboardEvent('keyup', {
            key: 'Enter',
            code: 'Enter',
            keyCode: 13
        });
        searchAndAddItem(event);
    }
}

// إضافة صنف للفاتورة يدوياً
function addInvoiceItem() {
    const item = {
        id: ++itemCounter,
        itemId: '',
        itemName: '',
        quantity: 1,
        unitName: '',
        price: 0,
        total: 0
    };

    invoiceItems.push(item);
    updateInvoiceItemsTable();
}

// تحديث جدول أصناف الفاتورة
function updateInvoiceItemsTable() {
    const tbody = document.getElementById('invoiceItemsTable');
    
    if (invoiceItems.length === 0) {
        tbody.innerHTML = `
            <tr>
                <td colspan="6" class="text-center text-muted">
                    لا توجد أصناف مضافة
                    <br><button type="button" class="btn btn-sm btn-primary mt-2" onclick="addInvoiceItem()">
                        <i class="fas fa-plus"></i> إضافة أول صنف
                    </button>
                </td>
            </tr>
        `;
        calculateInvoiceTotal();
        return;
    }
    
    tbody.innerHTML = invoiceItems.map(item => `
        <tr>
            <td>
                <select class="form-select form-select-sm" onchange="updateItemInfo(${item.id}, this.value)">
                    <option value="">اختر الصنف (منتج/خامة/خدمة)</option>
                    ${items.map(product => `<option value="${product.itemId}" ${product.itemId == item.itemId ? 'selected' : ''}>
                        ${product.itemName} (${product.majorUnit?.unitName || 'وحدة'}) - ${getItemTypeText(product.itemType)} - ${product.itemCode || 'بدون كود'}
                    </option>`).join('')}
                </select>
                ${item.itemId ? `<small class="text-muted">باركود: ${items.find(p => p.itemId == item.itemId)?.barcode || 'غير محدد'}</small>` : ''}
            </td>
            <td>
                <input type="number" class="form-control form-control-sm" 
                       value="${item.quantity}" step="0.001" min="0.001"
                       onchange="updateItemQuantity(${item.id}, this.value)">
            </td>
            <td>
                <span class="badge bg-info">${item.unitName || 'وحدة'}</span>
            </td>
            <td>
                <strong>${formatCurrency(item.price)}</strong>
                <br><small class="text-muted">سعر مسجل</small>
            </td>
            <td>
                <strong>${formatCurrency(item.total)}</strong>
            </td>
            <td>
                <button type="button" class="btn btn-sm btn-outline-danger" 
                        onclick="removeInvoiceItem(${item.id})" title="حذف">
                    <i class="fas fa-trash"></i>
                </button>
            </td>
        </tr>
    `).join('');
    
    calculateInvoiceTotal();
}

// تحديث معلومات الصنف عند الاختيار اليدوي
function updateItemInfo(itemId, productId) {
    const item = invoiceItems.find(inv => inv.id === itemId);
    const product = items.find(prod => prod.itemId == productId);

    if (item && product) {
        item.itemId = productId;
        item.itemName = product.itemName;
        item.itemCode = product.itemCode;
        item.barcode = product.barcode;
        item.unitName = product.majorUnit?.unitName || 'وحدة';
        item.type = product.type;

        // تحديد السعر حسب نوع الفاتورة لجميع أنواع الأصناف
        let itemPrice = product.salePrice; // افتراضي

        if (currentInvoiceType === 'purchase' || currentInvoiceType === 'purchase_return') {
            itemPrice = product.purchasePrice;
        } else if (currentInvoiceType === 'sale' || currentInvoiceType === 'sale_return' || currentInvoiceType === 'quick_sale') {
            itemPrice = product.salePrice;
        }

        item.price = itemPrice;
        item.total = item.quantity * item.price;

        updateInvoiceItemsTable();

        console.log('تم تحديث الصنف:', product.itemName, 'بسعر:', itemPrice, 'لفاتورة:', currentInvoiceType);
    }
}

// تحديث كمية الصنف
function updateItemQuantity(itemId, quantity) {
    const item = invoiceItems.find(inv => inv.id === itemId);
    if (item) {
        item.quantity = parseFloat(quantity) || 0;
        item.total = item.quantity * item.price;
        updateInvoiceItemsTable();
    }
}

// ملاحظة: تم إزالة دالة تحديث السعر - الأسعار ثابتة من تسجيل الصنف

// حذف صنف من الفاتورة
function removeInvoiceItem(itemId) {
    invoiceItems = invoiceItems.filter(item => item.id !== itemId);
    updateInvoiceItemsTable();
}

// حساب إجمالي الفاتورة
function calculateInvoiceTotal() {
    const subtotal = invoiceItems.reduce((sum, item) => sum + item.total, 0);
    const discount = parseFloat(document.getElementById('discount').value) || 0;
    const finalTotal = subtotal - discount;
    
    document.getElementById('invoiceTotal').textContent = formatCurrency(subtotal);
    document.getElementById('finalTotal').value = finalTotal.toFixed(3);
    
    calculateRemaining();
}

// حساب المبلغ المتبقي
function calculateRemaining() {
    const finalTotal = parseFloat(document.getElementById('finalTotal').value) || 0;
    const paidAmount = parseFloat(document.getElementById('paidAmount').value) || 0;
    const paymentMethod = document.getElementById('paymentMethod').value;
    
    if (paymentMethod === 'partial' && paidAmount > 0) {
        const remaining = finalTotal - paidAmount;
        // يمكن إضافة عرض المبلغ المتبقي هنا
    }
}

// تنسيق العملة
function formatCurrency(amount) {
    return new Intl.NumberFormat('ar-YE', {
        style: 'currency',
        currency: 'YER',
        minimumFractionDigits: 3,
        maximumFractionDigits: 3
    }).format(amount).replace('YER', 'ر.ي');
}

// حفظ الفاتورة
function saveInvoice() {
    console.log('حفظ الفاتورة...');
    
    try {
        // التحقق من البيانات المطلوبة
        if (invoiceItems.length === 0) {
            alert('يرجى إضافة أصناف للفاتورة');
            return;
        }

        const paymentMethod = document.getElementById('paymentMethod').value;
        const cashboxId = document.getElementById('cashboxSelect').value;

        // التحقق من الصندوق للمعاملات النقدية
        if (paymentMethod !== 'credit' && !cashboxId) {
            alert('يرجى اختيار الصندوق المتأثر للمعاملات النقدية');
            return;
        }

        // الحصول على معلومات الصندوق
        let cashboxInfo = null;
        if (cashboxId) {
            const cashbox = cashboxes.find(cb => cb.cashboxId == cashboxId);
            if (cashbox) {
                cashboxInfo = {
                    cashboxId: cashbox.cashboxId,
                    cashboxName: cashbox.cashboxName,
                    cashboxType: cashbox.cashboxType
                };
            }
        }

        const invoiceData = {
            invoiceId: Date.now(),
            invoiceNumber: document.getElementById('invoiceNumber').value,
            invoiceType: document.getElementById('invoiceType').value,
            date: document.getElementById('invoiceDate').value,
            time: document.getElementById('invoiceTime').value,
            partyId: document.getElementById('partySelect').value || null,
            partyName: document.getElementById('partySelect').selectedOptions[0]?.text || 'عميل نقدي',
            paymentMethod: paymentMethod,
            cashbox: cashboxInfo,
            paidAmount: parseFloat(document.getElementById('paidAmount').value) || 0,
            discount: parseFloat(document.getElementById('discount').value) || 0,
            finalTotal: parseFloat(document.getElementById('finalTotal').value) || 0,
            notes: document.getElementById('invoiceNotes').value,
            items: [...invoiceItems],
            status: 'completed',
            createdAt: new Date().toISOString()
        };
        
        // إضافة الفاتورة للقائمة
        invoices.unshift(invoiceData);
        
        alert('تم حفظ الفاتورة بنجاح!');
        resetInvoiceForm();
        displayInvoices();
        
        console.log('تم حفظ الفاتورة:', invoiceData);
        
    } catch (error) {
        console.error('خطأ في حفظ الفاتورة:', error);
        alert('خطأ في حفظ الفاتورة: ' + error.message);
    }
}

// إعادة تعيين نموذج الفاتورة
function resetInvoiceForm() {
    document.getElementById('invoiceFormCard').style.display = 'none';
    document.getElementById('invoiceForm').reset();
    invoiceItems = [];
    itemCounter = 0;
    currentInvoiceType = '';
    
    // إعادة تعيين التاريخ والوقت
    const now = new Date();
    document.getElementById('invoiceDate').value = now.toISOString().split('T')[0];
    document.getElementById('invoiceTime').value = now.toTimeString().slice(0, 5);
}

// تحميل قائمة الفواتير
function loadInvoices() {
    console.log('تحميل قائمة الفواتير...');
    
    // بيانات تجريبية
    if (invoices.length === 0) {
        const today = new Date().toISOString().split('T')[0];
        invoices = [
            {
                invoiceId: 1,
                invoiceNumber: 'SAL-001234',
                invoiceType: 'sale',
                date: today,
                partyName: 'مخبز الأمل',
                finalTotal: 3750.000,
                paymentMethod: 'cash',
                status: 'completed'
            },
            {
                invoiceId: 2,
                invoiceNumber: 'PUR-001235',
                invoiceType: 'purchase',
                date: today,
                partyName: 'شركة الدقيق الذهبي',
                finalTotal: 47500.000,
                paymentMethod: 'credit',
                status: 'completed'
            }
        ];
    }
    
    filteredInvoices = [...invoices];
    displayInvoices();
}

// عرض قائمة الفواتير
function displayInvoices() {
    const tbody = document.getElementById('invoicesTableBody');
    
    if (filteredInvoices.length === 0) {
        tbody.innerHTML = `
            <tr>
                <td colspan="9" class="text-center text-muted">
                    <i class="fas fa-file-invoice fa-2x mb-2"></i>
                    <p>لا توجد فواتير</p>
                </td>
            </tr>
        `;
        return;
    }
    
    tbody.innerHTML = filteredInvoices.map(invoice => {
        const typeText = getInvoiceTypeText(invoice.invoiceType);
        const typeBadge = getInvoiceTypeBadge(invoice.invoiceType);
        const paymentText = getPaymentMethodText(invoice.paymentMethod);
        
        // معلومات الصندوق
        let cashboxInfo = '';
        if (invoice.cashbox) {
            const typeIcon = invoice.cashbox.cashboxType === 'نقدي' ? '💰' : '🏦';
            cashboxInfo = `${typeIcon} ${invoice.cashbox.cashboxName}`;
        } else if (invoice.paymentMethod === 'credit') {
            cashboxInfo = '<span class="text-muted">آجل</span>';
        } else {
            cashboxInfo = '<span class="text-muted">غير محدد</span>';
        }

        return `
            <tr>
                <td><strong>${invoice.invoiceNumber}</strong></td>
                <td>${invoice.date}</td>
                <td><span class="badge ${typeBadge}">${typeText}</span></td>
                <td>${invoice.partyName}</td>
                <td><strong>${formatCurrency(invoice.finalTotal)}</strong></td>
                <td>${paymentText}</td>
                <td>${cashboxInfo}</td>
                <td>
                    <span class="badge ${invoice.status === 'completed' ? 'bg-success' : 'bg-warning'}">
                        ${invoice.status === 'completed' ? 'مكتملة' : 'قيد المعالجة'}
                    </span>
                </td>
                <td>
                    <div class="btn-group btn-group-sm">
                        <button class="btn btn-outline-info" onclick="viewInvoice(${invoice.invoiceId})" title="عرض">
                            <i class="fas fa-eye"></i>
                        </button>
                        <button class="btn btn-outline-primary" onclick="printInvoice(${invoice.invoiceId})" title="طباعة">
                            <i class="fas fa-print"></i>
                        </button>
                        <button class="btn btn-outline-warning" onclick="printThermal(${invoice.invoiceId})" title="طباعة حرارية">
                            <i class="fas fa-receipt"></i>
                        </button>
                    </div>
                </td>
            </tr>
        `;
    }).join('');
}

// تحديد نص نوع الفاتورة
function getInvoiceTypeText(type) {
    const types = {
        'sale': 'مبيعات',
        'purchase': 'مشتريات',
        'sale_return': 'مرتجع مبيعات',
        'quick_sale': 'بيع سريع'
    };
    return types[type] || 'غير محدد';
}

// تحديد لون شارة نوع الفاتورة
function getInvoiceTypeBadge(type) {
    const badges = {
        'sale': 'bg-success',
        'purchase': 'bg-primary',
        'sale_return': 'bg-warning',
        'quick_sale': 'bg-info'
    };
    return badges[type] || 'bg-secondary';
}

// تحديد نص طريقة الدفع
function getPaymentMethodText(method) {
    const methods = {
        'cash': 'نقدي',
        'credit': 'آجل',
        'partial': 'دفع جزئي'
    };
    return methods[method] || 'غير محدد';
}

// تصفية الفواتير
function filterInvoices() {
    const typeFilter = document.getElementById('invoiceTypeFilter').value;
    
    if (typeFilter) {
        filteredInvoices = invoices.filter(invoice => invoice.invoiceType === typeFilter);
    } else {
        filteredInvoices = [...invoices];
    }
    
    displayInvoices();
}

// تحديد نوع الصنف
function getItemTypeText(type) {
    const types = {
        'product': 'منتج',
        'material': 'خامة',
        'service': 'خدمة'
    };
    return types[type] || 'غير محدد';
}

// تحديث نصوص أنواع الفواتير
function getInvoiceTypeText(type) {
    const types = {
        'sale': 'مبيعات',
        'purchase': 'مشتريات',
        'sale_return': 'مرتجع مبيعات',
        'purchase_return': 'مرتجع مشتريات',
        'quick_sale': 'بيع سريع'
    };
    return types[type] || 'غير محدد';
}

// تحديث ألوان شارات أنواع الفواتير
function getInvoiceTypeBadge(type) {
    const badges = {
        'sale': 'bg-success',
        'purchase': 'bg-primary',
        'sale_return': 'bg-warning',
        'purchase_return': 'bg-danger',
        'quick_sale': 'bg-info'
    };
    return badges[type] || 'bg-secondary';
}

// عرض الفاتورة
function viewInvoice(invoiceId) {
    const invoice = invoices.find(inv => inv.invoiceId === invoiceId);
    if (!invoice) {
        alert('لم يتم العثور على الفاتورة');
        return;
    }

    const typeText = getInvoiceTypeText(invoice.invoiceType);
    const paymentText = getPaymentMethodText(invoice.paymentMethod);

    let itemsHtml = '';
    if (invoice.items && invoice.items.length > 0) {
        itemsHtml = `
            <table class="table table-striped">
                <thead>
                    <tr>
                        <th>الصنف</th>
                        <th>الكمية</th>
                        <th>السعر</th>
                        <th>الإجمالي</th>
                    </tr>
                </thead>
                <tbody>
                    ${invoice.items.map(item => `
                        <tr>
                            <td>${item.itemName}</td>
                            <td>${item.quantity} ${item.unitName}</td>
                            <td>${formatCurrency(item.price)}</td>
                            <td>${formatCurrency(item.total)}</td>
                        </tr>
                    `).join('')}
                </tbody>
            </table>
        `;
    }

    const content = `
        <div class="text-center mb-4">
            <h4>${typeText} - ${invoice.invoiceNumber}</h4>
            <p class="text-muted">التاريخ: ${invoice.date} - الوقت: ${invoice.time || ''}</p>
        </div>

        <div class="row mb-3">
            <div class="col-md-6">
                <strong>العميل/المورد:</strong> ${invoice.partyName}<br>
                <strong>طريقة الدفع:</strong> ${paymentText}<br>
                ${invoice.cashbox ? `<strong>الصندوق المتأثر:</strong> ${invoice.cashbox.cashboxName} (${invoice.cashbox.cashboxType})<br>` : ''}
                <strong>الحالة:</strong> ${invoice.status === 'completed' ? 'مكتملة' : 'قيد المعالجة'}
            </div>
            <div class="col-md-6">
                <strong>الإجمالي:</strong> ${formatCurrency(invoice.finalTotal)}<br>
                <strong>الخصم:</strong> ${formatCurrency(invoice.discount || 0)}<br>
                <strong>المبلغ المدفوع:</strong> ${formatCurrency(invoice.paidAmount || 0)}
            </div>
        </div>

        ${itemsHtml}

        ${invoice.notes ? `<div class="mt-3"><strong>ملاحظات:</strong> ${invoice.notes}</div>` : ''}
    `;

    // فتح نافذة جديدة لعرض الفاتورة
    const printWindow = window.open('', '_blank');
    printWindow.document.write(`
        <html>
        <head>
            <title>عرض الفاتورة - ${invoice.invoiceNumber}</title>
            <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
            <style>
                body { font-family: 'Arial', sans-serif; direction: rtl; }
                @media print { .no-print { display: none; } }
            </style>
        </head>
        <body>
            <div class="container">
                ${content}
                <div class="text-center mt-4 no-print">
                    <button class="btn btn-primary" onclick="window.print()">
                        <i class="fas fa-print"></i> طباعة
                    </button>
                    <button class="btn btn-secondary" onclick="window.close()">
                        <i class="fas fa-times"></i> إغلاق
                    </button>
                </div>
            </div>
        </body>
        </html>
    `);
}

// طباعة الفاتورة العادية
function printInvoice(invoiceId) {
    const invoice = invoices.find(inv => inv.invoiceId === invoiceId);
    if (!invoice) {
        alert('لم يتم العثور على الفاتورة');
        return;
    }

    const typeText = getInvoiceTypeText(invoice.invoiceType);

    let itemsHtml = '';
    if (invoice.items && invoice.items.length > 0) {
        itemsHtml = invoice.items.map(item => `
            <tr>
                <td>${item.itemName}</td>
                <td>${item.quantity}</td>
                <td>${item.unitName}</td>
                <td>${formatCurrency(item.price)}</td>
                <td>${formatCurrency(item.total)}</td>
            </tr>
        `).join('');
    }

    const printContent = `
        <div class="text-center mb-4">
            <h2>نظام إدارة مخبوزات ANW</h2>
            <h4>${typeText}</h4>
            <p>رقم الفاتورة: ${invoice.invoiceNumber}</p>
            <p>التاريخ: ${invoice.date} - الوقت: ${invoice.time || ''}</p>
        </div>

        <div class="row mb-4">
            <div class="col-4">
                <strong>العميل/المورد:</strong> ${invoice.partyName}
            </div>
            <div class="col-4">
                <strong>طريقة الدفع:</strong> ${getPaymentMethodText(invoice.paymentMethod)}
            </div>
            <div class="col-4">
                ${invoice.cashbox ? `<strong>الصندوق:</strong> ${invoice.cashbox.cashboxName}` : ''}
            </div>
        </div>

        <table class="table table-bordered">
            <thead>
                <tr>
                    <th>الصنف</th>
                    <th>الكمية</th>
                    <th>الوحدة</th>
                    <th>السعر</th>
                    <th>الإجمالي</th>
                </tr>
            </thead>
            <tbody>
                ${itemsHtml}
            </tbody>
            <tfoot>
                <tr>
                    <th colspan="4">الإجمالي:</th>
                    <th>${formatCurrency(invoice.finalTotal)}</th>
                </tr>
            </tfoot>
        </table>

        ${invoice.notes ? `<div class="mt-3"><strong>ملاحظات:</strong> ${invoice.notes}</div>` : ''}

        <div class="text-center mt-4">
            <p>شكراً لتعاملكم معنا</p>
        </div>
    `;

    const printWindow = window.open('', '_blank');
    printWindow.document.write(`
        <html>
        <head>
            <title>طباعة فاتورة - ${invoice.invoiceNumber}</title>
            <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
            <style>
                body { font-family: 'Arial', sans-serif; direction: rtl; }
                @media print {
                    .no-print { display: none; }
                    body { margin: 0; }
                }
            </style>
        </head>
        <body>
            <div class="container">
                ${printContent}
            </div>
            <script>window.print();</script>
        </body>
        </html>
    `);
}

// طباعة حرارية
function printThermal(invoiceId) {
    const invoice = invoices.find(inv => inv.invoiceId === invoiceId);
    if (!invoice) {
        alert('لم يتم العثور على الفاتورة');
        return;
    }

    const typeText = getInvoiceTypeText(invoice.invoiceType);

    let itemsText = '';
    if (invoice.items && invoice.items.length > 0) {
        itemsText = invoice.items.map(item =>
            `${item.itemName}\n${item.quantity} ${item.unitName} × ${item.price.toFixed(3)} = ${item.total.toFixed(3)} ر.ي`
        ).join('\n' + '-'.repeat(32) + '\n');
    }

    const thermalContent = `
        <div style="width: 300px; font-family: 'Courier New', monospace; font-size: 12px; line-height: 1.2;">
            <div style="text-align: center; margin-bottom: 10px;">
                <strong>نظام إدارة مخبوزات ANW</strong><br>
                ${typeText}<br>
                رقم: ${invoice.invoiceNumber}<br>
                ${invoice.date} ${invoice.time || ''}
            </div>

            <div style="border-top: 1px dashed #000; border-bottom: 1px dashed #000; padding: 5px 0; margin: 10px 0;">
                العميل/المورد: ${invoice.partyName}<br>
                طريقة الدفع: ${getPaymentMethodText(invoice.paymentMethod)}<br>
                ${invoice.cashbox ? `الصندوق: ${invoice.cashbox.cashboxName}` : ''}
            </div>

            <div style="margin: 10px 0;">
                ${itemsText.replace(/\n/g, '<br>')}
            </div>

            <div style="border-top: 1px dashed #000; padding: 5px 0; margin: 10px 0;">
                <div style="text-align: right;">
                    الإجمالي: <strong>${invoice.finalTotal.toFixed(3)} ر.ي</strong>
                </div>
                ${invoice.discount > 0 ? `<div style="text-align: right;">الخصم: ${invoice.discount.toFixed(3)} ر.ي</div>` : ''}
                ${invoice.paidAmount > 0 ? `<div style="text-align: right;">المدفوع: ${invoice.paidAmount.toFixed(3)} ر.ي</div>` : ''}
            </div>

            <div style="text-align: center; margin-top: 15px;">
                شكراً لتعاملكم معنا<br>
                ${new Date().toLocaleString('ar-YE')}
            </div>
        </div>
    `;

    const printWindow = window.open('', '_blank');
    printWindow.document.write(`
        <html>
        <head>
            <title>طباعة حرارية - ${invoice.invoiceNumber}</title>
            <style>
                body {
                    font-family: 'Courier New', monospace;
                    direction: rtl;
                    margin: 0;
                    padding: 10px;
                }
                @media print {
                    body { margin: 0; padding: 5px; }
                    .no-print { display: none; }
                }
            </style>
        </head>
        <body>
            ${thermalContent}
            <div class="no-print" style="text-align: center; margin-top: 20px;">
                <button onclick="window.print()" style="padding: 10px 20px;">طباعة</button>
                <button onclick="window.close()" style="padding: 10px 20px; margin-left: 10px;">إغلاق</button>
            </div>
            <script>window.print();</script>
        </body>
        </html>
    `);
}

// وظائف المساعدة والرسائل
function showError(message) {
    const alertDiv = document.createElement('div');
    alertDiv.className = 'alert alert-danger alert-dismissible fade show position-fixed';
    alertDiv.style.cssText = 'top: 20px; right: 20px; z-index: 9999; min-width: 300px;';
    alertDiv.innerHTML = `
        <i class="fas fa-exclamation-triangle me-2"></i>
        ${message}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    `;
    document.body.appendChild(alertDiv);

    setTimeout(() => {
        if (alertDiv.parentNode) {
            alertDiv.remove();
        }
    }, 5000);
}

function showSuccess(message) {
    const alertDiv = document.createElement('div');
    alertDiv.className = 'alert alert-success alert-dismissible fade show position-fixed';
    alertDiv.style.cssText = 'top: 20px; right: 20px; z-index: 9999; min-width: 300px;';
    alertDiv.innerHTML = `
        <i class="fas fa-check-circle me-2"></i>
        ${message}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    `;
    document.body.appendChild(alertDiv);

    setTimeout(() => {
        if (alertDiv.parentNode) {
            alertDiv.remove();
        }
    }, 3000);
}

function showSaveLoading() {
    const saveBtn = document.querySelector('#saveInvoiceBtn');
    if (saveBtn) {
        saveBtn.disabled = true;
        saveBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>جاري الحفظ...';
    }
}

function hideSaveLoading() {
    const saveBtn = document.querySelector('#saveInvoiceBtn');
    if (saveBtn) {
        saveBtn.disabled = false;
        saveBtn.innerHTML = '<i class="fas fa-save me-2"></i>حفظ الفاتورة';
    }
}

// تسجيل الخروج
function logout() {
    if (confirm('هل أنت متأكد من تسجيل الخروج؟')) {
        localStorage.removeItem('isLoggedIn');
        window.location.href = 'login.html';
    }
}
