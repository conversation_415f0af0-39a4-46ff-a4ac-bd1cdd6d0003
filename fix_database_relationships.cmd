@echo off
chcp 65001 > nul
echo ========================================
echo   إصلاح مشاكل علاقات قاعدة البيانات
echo   Fix Database Relationships Issues
echo ========================================
echo.

echo 🔧 إصلاح مشاكل Entity Framework...
echo.

echo 🧹 حذف Migrations السابقة...
if exist "Migrations" (
    rmdir /s /q "Migrations"
    echo ✅ تم حذف Migrations السابقة
) else (
    echo ℹ️  لا توجد Migrations سابقة
)

echo.
echo 🧹 تنظيف المشروع...
dotnet clean

echo.
echo 📦 استعادة الحزم...
dotnet restore
if %ERRORLEVEL% NEQ 0 (
    echo ❌ فشل في استعادة الحزم
    pause
    exit /b 1
)

echo.
echo 🔧 بناء المشروع...
dotnet build
if %ERRORLEVEL% NEQ 0 (
    echo ❌ فشل في بناء المشروع
    echo.
    echo 🔍 عرض تفاصيل الأخطاء:
    dotnet build --verbosity detailed
    pause
    exit /b 1
)

echo.
echo 🗄️ إنشاء Migration جديد...
dotnet ef migrations add FixedRelationships_v1
if %ERRORLEVEL% NEQ 0 (
    echo ❌ فشل في إنشاء Migration
    echo.
    echo 💡 الأسباب المحتملة:
    echo    1. مشاكل في تكوين العلاقات
    echo    2. خصائص Navigation غير صحيحة
    echo    3. مفاتيح خارجية مفقودة
    echo.
    echo 🔧 محاولة إصلاح تلقائي...
    
    REM محاولة إضافة أدوات EF إذا لم تكن موجودة
    dotnet tool install --global dotnet-ef --version 8.0.0
    
    echo 🔄 إعادة المحاولة...
    dotnet ef migrations add FixedRelationships_v1
    if %ERRORLEVEL% NEQ 0 (
        echo ❌ فشل مرة أخرى في إنشاء Migration
        echo.
        echo 📋 يرجى التحقق من:
        echo    1. تكوين العلاقات في DbContext
        echo    2. خصائص Navigation في النماذج
        echo    3. المفاتيح الخارجية
        echo.
        pause
        exit /b 1
    )
)

echo.
echo 🗄️ تطبيق Migration على قاعدة البيانات...
dotnet ef database update
if %ERRORLEVEL% NEQ 0 (
    echo ❌ فشل في تطبيق Migration
    echo.
    echo 🔧 محاولة حذف قاعدة البيانات وإعادة الإنشاء...
    dotnet ef database drop --force
    dotnet ef database update
    if %ERRORLEVEL% NEQ 0 (
        echo ❌ فشل في إنشاء قاعدة البيانات
        pause
        exit /b 1
    )
)

echo.
echo ✅ تم إصلاح مشاكل قاعدة البيانات بنجاح!
echo.
echo 📊 معلومات قاعدة البيانات:
echo    📁 الاسم: ANWBakeryDB_Real
echo    🔗 النوع: SQL Server
echo    ✅ الحالة: جاهزة للاستخدام
echo.

echo 🚀 تشغيل التطبيق للاختبار...
echo.
echo 🌐 سيتم فتح المتصفح على: http://localhost:5000
echo 🔐 بيانات الدخول:
echo    👤 admin
echo    🔑 admin123
echo.

REM تشغيل التطبيق
start "" "http://localhost:5000"
dotnet run

pause
