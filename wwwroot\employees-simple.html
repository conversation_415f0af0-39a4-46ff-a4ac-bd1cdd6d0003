<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>الموظفين - نظام ANW</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
</head>
<body>
    <div class="container-fluid">
        <div class="row">
            <!-- Sidebar -->
            <div class="col-md-3 bg-dark text-white p-0">
                <div class="sidebar">
                    <div class="p-3 border-bottom">
                        <h5><i class="fas fa-calculator me-2"></i>نظام ANW</h5>
                        <small>إدارة مخبوزات مبسط</small>
                    </div>
                    <nav class="nav flex-column p-3">
                        <a href="items-simple.html" class="nav-link text-white">
                            <i class="fas fa-boxes me-2"></i>المنتجات والخامات
                        </a>
                        <a href="parties-simple.html" class="nav-link text-white">
                            <i class="fas fa-users me-2"></i>العملاء والموردين
                        </a>
                        <a href="employees-simple.html" class="nav-link text-white active">
                            <i class="fas fa-user-tie me-2"></i>الموظفين
                        </a>
                        <a href="invoices-simple.html" class="nav-link text-white">
                            <i class="fas fa-file-invoice me-2"></i>الفواتير
                        </a>
                        <a href="inventory-simple.html" class="nav-link text-white">
                            <i class="fas fa-warehouse me-2"></i>المخزون
                        </a>
                        <a href="cash-simple.html" class="nav-link text-white">
                            <i class="fas fa-cash-register me-2"></i>الصناديق
                        </a>
                        <a href="banks-simple.html" class="nav-link text-white">
                            <i class="fas fa-university me-2"></i>البنوك
                        </a>
                        <a href="owners-simple.html" class="nav-link text-white">
                            <i class="fas fa-crown me-2"></i>الملاك
                        </a>
                        <a href="users-simple.html" class="nav-link text-white">
                            <i class="fas fa-user-cog me-2"></i>المستخدمين
                        </a>
                        <a href="reports-simple.html" class="nav-link text-white">
                            <i class="fas fa-chart-bar me-2"></i>التقارير
                        </a>
                    </nav>
                </div>
            </div>

            <!-- Main Content -->
            <div class="col-md-9">
                <div class="p-4">
                    <!-- Header -->
                    <div class="d-flex justify-content-between align-items-center mb-4">
                        <h2><i class="fas fa-user-tie text-primary me-2"></i>إدارة الموظفين</h2>
                        <div>
                            <button class="btn btn-success" onclick="addEmployee()">
                                <i class="fas fa-plus me-1"></i>إضافة موظف
                            </button>
                            <button class="btn btn-warning" onclick="calculateSalaries()">
                                <i class="fas fa-calculator me-1"></i>حساب الرواتب
                            </button>
                            <button class="btn btn-info" onclick="exportEmployees()">
                                <i class="fas fa-download me-1"></i>تصدير
                            </button>
                        </div>
                    </div>

                    <!-- Filter Tabs -->
                    <ul class="nav nav-tabs mb-3">
                        <li class="nav-item">
                            <a class="nav-link active" onclick="filterEmployees('all')">الكل</a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" onclick="filterEmployees('active')">النشطين</a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" onclick="filterEmployees('inactive')">غير النشطين</a>
                        </li>
                    </ul>

                    <!-- Search -->
                    <div class="row mb-3">
                        <div class="col-md-6">
                            <input type="text" id="searchInput" class="form-control" placeholder="البحث بالاسم أو الهاتف..." onkeyup="searchEmployees()">
                        </div>
                    </div>

                    <!-- Employees Table -->
                    <div class="card">
                        <div class="card-body">
                            <div class="table-responsive">
                                <table class="table table-striped table-hover">
                                    <thead class="table-dark">
                                        <tr>
                                            <th>الاسم</th>
                                            <th>المنصب</th>
                                            <th>الهاتف</th>
                                            <th>الراتب الأساسي</th>
                                            <th>تاريخ التوظيف</th>
                                            <th>الحالة</th>
                                            <th>الإجراءات</th>
                                        </tr>
                                    </thead>
                                    <tbody id="employeesTableBody">
                                        <tr>
                                            <td colspan="7" class="text-center">
                                                <div class="spinner-border text-primary" role="status">
                                                    <span class="visually-hidden">جاري التحميل...</span>
                                                </div>
                                                <p class="mt-2">جاري تحميل الموظفين...</p>
                                            </td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Add/Edit Employee Modal -->
    <div class="modal fade" id="employeeModal" tabindex="-1">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">إضافة موظف جديد</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <form id="employeeForm">
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">الاسم الكامل *</label>
                                    <input type="text" id="employeeName" class="form-control" required>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">المنصب *</label>
                                    <select id="position" class="form-control" required>
                                        <option value="">اختر المنصب</option>
                                        <option value="baker">خباز</option>
                                        <option value="assistant">مساعد خباز</option>
                                        <option value="cashier">أمين صندوق</option>
                                        <option value="manager">مدير</option>
                                        <option value="cleaner">عامل نظافة</option>
                                        <option value="driver">سائق</option>
                                        <option value="security">حارس</option>
                                    </select>
                                </div>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">رقم الهاتف</label>
                                    <input type="text" id="phone" class="form-control">
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">تاريخ التوظيف</label>
                                    <input type="date" id="hireDate" class="form-control">
                                </div>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-md-4">
                                <div class="mb-3">
                                    <label class="form-label">الراتب الأساسي</label>
                                    <input type="number" id="basicSalary" class="form-control" value="0" step="0.01">
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="mb-3">
                                    <label class="form-label">بدل المواصلات</label>
                                    <input type="number" id="transportAllowance" class="form-control" value="0" step="0.01">
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="mb-3">
                                    <label class="form-label">بدلات أخرى</label>
                                    <input type="number" id="otherAllowances" class="form-control" value="0" step="0.01">
                                </div>
                            </div>
                        </div>
                        <div class="mb-3">
                            <label class="form-label">العنوان</label>
                            <textarea id="address" class="form-control" rows="2"></textarea>
                        </div>
                        <div class="mb-3">
                            <label class="form-label">ملاحظات</label>
                            <textarea id="notes" class="form-control" rows="2"></textarea>
                        </div>
                        <div class="form-check mb-3">
                            <input type="checkbox" id="isActive" class="form-check-input" checked>
                            <label class="form-check-label">موظف نشط</label>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                    <button type="button" class="btn btn-primary" onclick="saveEmployee()">حفظ</button>
                </div>
            </div>
        </div>
    </div>

    <!-- Success/Error Messages -->
    <div id="messages"></div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // البيانات المحلية
        let employees = [];
        let nextId = 1;
        let currentFilter = 'all';

        // تحميل البيانات عند بدء الصفحة
        document.addEventListener('DOMContentLoaded', function() {
            loadEmployees();
            // تعيين تاريخ اليوم كافتراضي
            document.getElementById('hireDate').value = new Date().toISOString().split('T')[0];
        });

        // تحميل الموظفين من localStorage
        function loadEmployees() {
            try {
                const savedEmployees = localStorage.getItem('anw_employees');
                if (savedEmployees && savedEmployees !== 'null') {
                    employees = JSON.parse(savedEmployees);
                    if (employees.length > 0) {
                        nextId = Math.max(...employees.map(e => e.id), 0) + 1;
                    }
                } else {
                    // إنشاء موظفين افتراضيين
                    employees = [
                        {id: 1, name: 'أحمد محمد علي', position: 'baker', phone: '777-123456', hireDate: '2023-01-15', basicSalary: 150000, transportAllowance: 20000, otherAllowances: 10000, address: 'صنعاء - حي السبعين', notes: 'خباز ماهر', active: true},
                        {id: 2, name: 'فاطمة أحمد سالم', position: 'cashier', phone: '777-234567', hireDate: '2023-03-01', basicSalary: 120000, transportAllowance: 15000, otherAllowances: 5000, address: 'صنعاء - شارع الزبيري', notes: 'أمينة صندوق موثوقة', active: true},
                        {id: 3, name: 'محمد عبدالله حسن', position: 'manager', phone: '777-345678', hireDate: '2022-06-01', basicSalary: 250000, transportAllowance: 30000, otherAllowances: 20000, address: 'صنعاء - حي الحصبة', notes: 'مدير خبير', active: true}
                    ];
                    nextId = 4;
                    saveEmployees();
                }
                displayEmployees();
            } catch (error) {
                console.error('خطأ في تحميل الموظفين:', error);
                showMessage('خطأ في تحميل البيانات', 'danger');
            }
        }

        // حفظ الموظفين في localStorage
        function saveEmployees() {
            localStorage.setItem('anw_employees', JSON.stringify(employees));
        }

        // عرض الموظفين
        function displayEmployees() {
            const tbody = document.getElementById('employeesTableBody');
            let filteredEmployees = employees;

            // تطبيق الفلتر
            if (currentFilter === 'active') {
                filteredEmployees = employees.filter(emp => emp.active);
            } else if (currentFilter === 'inactive') {
                filteredEmployees = employees.filter(emp => !emp.active);
            }

            if (filteredEmployees.length === 0) {
                tbody.innerHTML = `
                    <tr>
                        <td colspan="7" class="text-center text-muted">
                            لا توجد موظفين
                            <br>
                            <button class="btn btn-primary mt-2" onclick="addEmployee()">إضافة موظف جديد</button>
                        </td>
                    </tr>
                `;
                return;
            }

            let html = '';
            filteredEmployees.forEach(employee => {
                const positionLabel = getPositionLabel(employee.position);
                const statusBadge = employee.active ? 
                    '<span class="badge bg-success">نشط</span>' : 
                    '<span class="badge bg-secondary">غير نشط</span>';
                
                const totalSalary = employee.basicSalary + employee.transportAllowance + employee.otherAllowances;

                html += `
                    <tr>
                        <td>${employee.name}</td>
                        <td><span class="badge bg-info">${positionLabel}</span></td>
                        <td>${employee.phone || '-'}</td>
                        <td>${totalSalary.toLocaleString()} ر.ي</td>
                        <td>${new Date(employee.hireDate).toLocaleDateString('ar-YE')}</td>
                        <td>${statusBadge}</td>
                        <td>
                            <button class="btn btn-sm btn-warning" onclick="editEmployee(${employee.id})">
                                <i class="fas fa-edit"></i>
                            </button>
                            <button class="btn btn-sm btn-danger" onclick="deleteEmployee(${employee.id})">
                                <i class="fas fa-trash"></i>
                            </button>
                        </td>
                    </tr>
                `;
            });

            tbody.innerHTML = html;
        }

        // الحصول على تسمية المنصب
        function getPositionLabel(position) {
            const positions = {
                'baker': 'خباز',
                'assistant': 'مساعد خباز',
                'cashier': 'أمين صندوق',
                'manager': 'مدير',
                'cleaner': 'عامل نظافة',
                'driver': 'سائق',
                'security': 'حارس'
            };
            return positions[position] || position;
        }

        // فلترة الموظفين
        function filterEmployees(filter) {
            currentFilter = filter;
            
            // تحديث التبويبات
            document.querySelectorAll('.nav-link').forEach(link => {
                link.classList.remove('active');
            });
            event.target.classList.add('active');
            
            displayEmployees();
        }

        // البحث في الموظفين
        function searchEmployees() {
            const searchTerm = document.getElementById('searchInput').value.toLowerCase();
            const tbody = document.getElementById('employeesTableBody');
            
            let filteredEmployees = employees.filter(employee => {
                return employee.name.toLowerCase().includes(searchTerm) ||
                       (employee.phone && employee.phone.toLowerCase().includes(searchTerm));
            });

            // تطبيق فلتر الحالة أيضاً
            if (currentFilter === 'active') {
                filteredEmployees = filteredEmployees.filter(emp => emp.active);
            } else if (currentFilter === 'inactive') {
                filteredEmployees = filteredEmployees.filter(emp => !emp.active);
            }

            if (filteredEmployees.length === 0) {
                tbody.innerHTML = `
                    <tr>
                        <td colspan="7" class="text-center text-muted">لا توجد نتائج للبحث</td>
                    </tr>
                `;
                return;
            }

            let html = '';
            filteredEmployees.forEach(employee => {
                const positionLabel = getPositionLabel(employee.position);
                const statusBadge = employee.active ? 
                    '<span class="badge bg-success">نشط</span>' : 
                    '<span class="badge bg-secondary">غير نشط</span>';
                
                const totalSalary = employee.basicSalary + employee.transportAllowance + employee.otherAllowances;

                html += `
                    <tr>
                        <td>${employee.name}</td>
                        <td><span class="badge bg-info">${positionLabel}</span></td>
                        <td>${employee.phone || '-'}</td>
                        <td>${totalSalary.toLocaleString()} ر.ي</td>
                        <td>${new Date(employee.hireDate).toLocaleDateString('ar-YE')}</td>
                        <td>${statusBadge}</td>
                        <td>
                            <button class="btn btn-sm btn-warning" onclick="editEmployee(${employee.id})">
                                <i class="fas fa-edit"></i>
                            </button>
                            <button class="btn btn-sm btn-danger" onclick="deleteEmployee(${employee.id})">
                                <i class="fas fa-trash"></i>
                            </button>
                        </td>
                    </tr>
                `;
            });

            tbody.innerHTML = html;
        }

        // إضافة موظف جديد
        function addEmployee() {
            clearForm();
            document.querySelector('.modal-title').textContent = 'إضافة موظف جديد';
            new bootstrap.Modal(document.getElementById('employeeModal')).show();
        }

        // تحرير موظف
        function editEmployee(employeeId) {
            const employee = employees.find(e => e.id === employeeId);
            if (employee) {
                document.getElementById('employeeName').value = employee.name;
                document.getElementById('position').value = employee.position;
                document.getElementById('phone').value = employee.phone || '';
                document.getElementById('hireDate').value = employee.hireDate;
                document.getElementById('basicSalary').value = employee.basicSalary;
                document.getElementById('transportAllowance').value = employee.transportAllowance;
                document.getElementById('otherAllowances').value = employee.otherAllowances;
                document.getElementById('address').value = employee.address || '';
                document.getElementById('notes').value = employee.notes || '';
                document.getElementById('isActive').checked = employee.active;
                
                document.getElementById('employeeForm').dataset.editId = employeeId;
                document.querySelector('.modal-title').textContent = 'تحرير الموظف';
                
                new bootstrap.Modal(document.getElementById('employeeModal')).show();
            }
        }

        // حذف موظف
        function deleteEmployee(employeeId) {
            if (confirm('هل أنت متأكد من حذف هذا الموظف؟')) {
                employees = employees.filter(e => e.id !== employeeId);
                saveEmployees();
                displayEmployees();
                showMessage('تم حذف الموظف بنجاح', 'success');
            }
        }

        // حفظ الموظف
        function saveEmployee() {
            const form = document.getElementById('employeeForm');
            const editId = form.dataset.editId;
            
            const employeeData = {
                name: document.getElementById('employeeName').value,
                position: document.getElementById('position').value,
                phone: document.getElementById('phone').value,
                hireDate: document.getElementById('hireDate').value,
                basicSalary: parseFloat(document.getElementById('basicSalary').value) || 0,
                transportAllowance: parseFloat(document.getElementById('transportAllowance').value) || 0,
                otherAllowances: parseFloat(document.getElementById('otherAllowances').value) || 0,
                address: document.getElementById('address').value,
                notes: document.getElementById('notes').value,
                active: document.getElementById('isActive').checked
            };

            // التحقق من البيانات
            if (!employeeData.name || !employeeData.position) {
                showMessage('يرجى ملء جميع الحقول المطلوبة', 'danger');
                return;
            }

            if (editId) {
                // تحديث موظف موجود
                const employeeIndex = employees.findIndex(e => e.id == editId);
                employees[employeeIndex] = { ...employees[employeeIndex], ...employeeData };
                showMessage('تم تحديث الموظف بنجاح', 'success');
            } else {
                // إضافة موظف جديد
                employeeData.id = nextId++;
                employees.push(employeeData);
                showMessage('تم إضافة الموظف بنجاح', 'success');
            }

            saveEmployees();
            displayEmployees();
            bootstrap.Modal.getInstance(document.getElementById('employeeModal')).hide();
        }

        // تنظيف النموذج
        function clearForm() {
            document.getElementById('employeeForm').reset();
            document.getElementById('employeeForm').removeAttribute('data-edit-id');
            document.getElementById('basicSalary').value = '0';
            document.getElementById('transportAllowance').value = '0';
            document.getElementById('otherAllowances').value = '0';
            document.getElementById('isActive').checked = true;
            document.getElementById('hireDate').value = new Date().toISOString().split('T')[0];
        }

        // حساب الرواتب
        function calculateSalaries() {
            const activeEmployees = employees.filter(e => e.active);
            const totalSalaries = activeEmployees.reduce((total, emp) => {
                return total + emp.basicSalary + emp.transportAllowance + emp.otherAllowances;
            }, 0);

            alert(`إجمالي رواتب الموظفين النشطين: ${totalSalaries.toLocaleString()} ر.ي\nعدد الموظفين: ${activeEmployees.length}`);
        }

        // تصدير الموظفين
        function exportEmployees() {
            const csvContent = "data:text/csv;charset=utf-8," 
                + "الاسم,المنصب,الهاتف,تاريخ التوظيف,الراتب الأساسي,بدل المواصلات,بدلات أخرى,الإجمالي,الحالة\n"
                + employees.map(emp => {
                    const total = emp.basicSalary + emp.transportAllowance + emp.otherAllowances;
                    return `${emp.name},${getPositionLabel(emp.position)},${emp.phone || ''},${emp.hireDate},${emp.basicSalary},${emp.transportAllowance},${emp.otherAllowances},${total},${emp.active ? 'نشط' : 'غير نشط'}`;
                }).join("\n");

            const encodedUri = encodeURI(csvContent);
            const link = document.createElement("a");
            link.setAttribute("href", encodedUri);
            link.setAttribute("download", "employees.csv");
            document.body.appendChild(link);
            link.click();
            document.body.removeChild(link);
            
            showMessage('تم تصدير الموظفين بنجاح', 'success');
        }

        // عرض رسالة
        function showMessage(message, type) {
            const messagesDiv = document.getElementById('messages');
            const alertHtml = `
                <div class="alert alert-${type} alert-dismissible fade show position-fixed" 
                     style="top: 20px; left: 20px; z-index: 9999; min-width: 300px;">
                    ${message}
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
            `;
            messagesDiv.innerHTML = alertHtml;
            
            setTimeout(() => {
                const alert = messagesDiv.querySelector('.alert');
                if (alert) {
                    bootstrap.Alert.getOrCreateInstance(alert).close();
                }
            }, 3000);
        }

        console.log('✅ تم تحميل نظام الموظفين بنجاح');
    </script>

    <style>
        .sidebar {
            height: 100vh;
            position: fixed;
            width: 25%;
        }
        .nav-link.active {
            background-color: rgba(255,255,255,0.1);
            border-radius: 5px;
        }
        .table th {
            background-color: #343a40;
            color: white;
        }
    </style>
</body>
</html>
