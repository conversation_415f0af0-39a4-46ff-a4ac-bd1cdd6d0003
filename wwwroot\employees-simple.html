<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>الموظفين - نظام ANW</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
</head>
<body>
    <div class="container-fluid">
        <div class="row">
            <!-- Sidebar -->
            <div class="col-md-3 bg-dark text-white p-0">
                <div class="sidebar">
                    <div class="p-3 border-bottom">
                        <h5><i class="fas fa-calculator me-2"></i>نظام ANW</h5>
                        <small>إدارة مخبوزات مبسط</small>
                    </div>
                    <nav class="nav flex-column p-3">
                        <a href="items-simple.html" class="nav-link text-white">
                            <i class="fas fa-boxes me-2"></i>المنتجات والخامات
                        </a>
                        <a href="parties-simple.html" class="nav-link text-white">
                            <i class="fas fa-users me-2"></i>العملاء والموردين
                        </a>
                        <a href="employees-simple.html" class="nav-link text-white active">
                            <i class="fas fa-user-tie me-2"></i>الموظفين
                        </a>
                        <a href="invoices-simple.html" class="nav-link text-white">
                            <i class="fas fa-file-invoice me-2"></i>الفواتير
                        </a>
                        <a href="inventory-simple.html" class="nav-link text-white">
                            <i class="fas fa-warehouse me-2"></i>المخزون
                        </a>
                        <a href="cash-simple.html" class="nav-link text-white">
                            <i class="fas fa-cash-register me-2"></i>الصناديق
                        </a>
                        <a href="banks-simple.html" class="nav-link text-white">
                            <i class="fas fa-university me-2"></i>البنوك
                        </a>
                        <a href="owners-simple.html" class="nav-link text-white">
                            <i class="fas fa-crown me-2"></i>الملاك
                        </a>
                        <a href="users-simple.html" class="nav-link text-white">
                            <i class="fas fa-user-cog me-2"></i>المستخدمين
                        </a>
                        <a href="reports-simple.html" class="nav-link text-white">
                            <i class="fas fa-chart-bar me-2"></i>التقارير
                        </a>
                    </nav>
                </div>
            </div>

            <!-- Main Content -->
            <div class="col-md-9">
                <div class="p-4">
                    <!-- Header -->
                    <div class="d-flex justify-content-between align-items-center mb-4">
                        <h2><i class="fas fa-user-tie text-primary me-2"></i>إدارة الموظفين</h2>
                        <div>
                            <button class="btn btn-success" onclick="addEmployee()">
                                <i class="fas fa-plus me-1"></i>إضافة موظف
                            </button>
                            <button class="btn btn-primary" onclick="processMonthlyAccruals()">
                                <i class="fas fa-calendar-plus me-1"></i>قيد استحقاقات شهرية
                            </button>
                            <button class="btn btn-warning" onclick="showMissingAccruals()">
                                <i class="fas fa-exclamation-triangle me-1"></i>فترات مفقودة
                            </button>
                            <button class="btn btn-secondary" onclick="showPayrollReport()">
                                <i class="fas fa-chart-bar me-1"></i>تقرير الرواتب
                            </button>
                            <button class="btn btn-info" onclick="exportEmployees()">
                                <i class="fas fa-download me-1"></i>تصدير
                            </button>
                        </div>
                    </div>

                    <!-- Main Tabs -->
                    <ul class="nav nav-tabs mb-3" id="mainTabs">
                        <li class="nav-item">
                            <a class="nav-link active" onclick="showMainTab('employees')">الموظفين</a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" onclick="showMainTab('accruals')">الاستحقاقات الشهرية</a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" onclick="showMainTab('balances')">أرصدة الموظفين</a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" onclick="showMainTab('vouchers')">سندات القبض والصرف</a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" onclick="showMainTab('alerts')">التنبيهات</a>
                        </li>
                    </ul>

                    <!-- Employees Tab -->
                    <div id="employeesMainTab" class="main-tab-content">
                        <!-- Filter Tabs -->
                        <ul class="nav nav-tabs mb-3">
                            <li class="nav-item">
                                <a class="nav-link active" onclick="filterEmployees('all')">الكل</a>
                            </li>
                            <li class="nav-item">
                                <a class="nav-link" onclick="filterEmployees('active')">النشطين</a>
                            </li>
                            <li class="nav-item">
                                <a class="nav-link" onclick="filterEmployees('inactive')">غير النشطين</a>
                            </li>
                        </ul>

                    <!-- Search -->
                    <div class="row mb-3">
                        <div class="col-md-6">
                            <input type="text" id="searchInput" class="form-control" placeholder="البحث بالاسم أو الهاتف..." onkeyup="searchEmployees()">
                        </div>
                    </div>

                    <!-- Employees Table -->
                    <div class="card">
                        <div class="card-body">
                            <div class="table-responsive">
                                <table class="table table-striped table-hover">
                                    <thead class="table-dark">
                                        <tr>
                                            <th>الاسم</th>
                                            <th>المنصب</th>
                                            <th>الهاتف</th>
                                            <th>الراتب الأساسي</th>
                                            <th>تاريخ التوظيف</th>
                                            <th>الرصيد الحالي</th>
                                            <th>آخر استحقاق</th>
                                            <th>الحالة</th>
                                            <th>الإجراءات</th>
                                        </tr>
                                    </thead>
                                    <tbody id="employeesTableBody">
                                        <tr>
                                            <td colspan="9" class="text-center">
                                                <div class="spinner-border text-primary" role="status">
                                                    <span class="visually-hidden">جاري التحميل...</span>
                                                </div>
                                                <p class="mt-2">جاري تحميل الموظفين...</p>
                                            </td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                    </div>

                    <!-- Accruals Tab -->
                    <div id="accrualsMainTab" class="main-tab-content" style="display: none;">
                        <div class="row mb-3">
                            <div class="col-md-4">
                                <select id="accrualYear" class="form-control" onchange="loadAccruals()">
                                    <option value="">اختر السنة</option>
                                </select>
                            </div>
                            <div class="col-md-4">
                                <select id="accrualMonth" class="form-control" onchange="loadAccruals()">
                                    <option value="">اختر الشهر</option>
                                    <option value="1">يناير</option>
                                    <option value="2">فبراير</option>
                                    <option value="3">مارس</option>
                                    <option value="4">أبريل</option>
                                    <option value="5">مايو</option>
                                    <option value="6">يونيو</option>
                                    <option value="7">يوليو</option>
                                    <option value="8">أغسطس</option>
                                    <option value="9">سبتمبر</option>
                                    <option value="10">أكتوبر</option>
                                    <option value="11">نوفمبر</option>
                                    <option value="12">ديسمبر</option>
                                </select>
                            </div>
                            <div class="col-md-4">
                                <button class="btn btn-primary w-100" onclick="processAccrualsForMonth()">
                                    <i class="fas fa-plus"></i> قيد استحقاقات الشهر
                                </button>
                            </div>
                        </div>

                        <div class="card">
                            <div class="card-header">
                                <h5><i class="fas fa-calendar-alt"></i> الاستحقاقات الشهرية</h5>
                            </div>
                            <div class="card-body">
                                <div class="table-responsive">
                                    <table class="table table-striped">
                                        <thead class="table-dark">
                                            <tr>
                                                <th>الموظف</th>
                                                <th>الشهر/السنة</th>
                                                <th>الراتب الأساسي</th>
                                                <th>البدلات</th>
                                                <th>الخصومات</th>
                                                <th>صافي الاستحقاق</th>
                                                <th>تاريخ القيد</th>
                                                <th>الإجراءات</th>
                                            </tr>
                                        </thead>
                                        <tbody id="accrualsTableBody">
                                            <tr>
                                                <td colspan="8" class="text-center text-muted">لا توجد استحقاقات</td>
                                            </tr>
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Balances Tab -->
                    <div id="balancesMainTab" class="main-tab-content" style="display: none;">
                        <div class="card">
                            <div class="card-header">
                                <h5><i class="fas fa-balance-scale"></i> أرصدة الموظفين</h5>
                            </div>
                            <div class="card-body">
                                <div class="table-responsive">
                                    <table class="table table-striped">
                                        <thead class="table-dark">
                                            <tr>
                                                <th>الموظف</th>
                                                <th>إجمالي الاستحقاقات</th>
                                                <th>إجمالي المدفوع</th>
                                                <th>الرصيد المتبقي</th>
                                                <th>آخر دفعة</th>
                                                <th>الإجراءات</th>
                                            </tr>
                                        </thead>
                                        <tbody id="balancesTableBody">
                                            <tr>
                                                <td colspan="6" class="text-center text-muted">لا توجد أرصدة</td>
                                            </tr>
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Vouchers Tab -->
                    <div id="vouchersMainTab" class="main-tab-content" style="display: none;">
                        <div class="row mb-3">
                            <div class="col-md-6">
                                <button class="btn btn-success" onclick="addPaymentVoucher()">
                                    <i class="fas fa-money-bill-wave"></i> سند صرف راتب
                                </button>
                            </div>
                            <div class="col-md-6">
                                <button class="btn btn-primary" onclick="addReceiptVoucher()">
                                    <i class="fas fa-receipt"></i> سند قبض من موظف
                                </button>
                            </div>
                        </div>

                        <div class="card">
                            <div class="card-header">
                                <h5><i class="fas fa-file-invoice"></i> سندات القبض والصرف</h5>
                            </div>
                            <div class="card-body">
                                <div class="table-responsive">
                                    <table class="table table-striped">
                                        <thead class="table-dark">
                                            <tr>
                                                <th>رقم السند</th>
                                                <th>النوع</th>
                                                <th>الموظف</th>
                                                <th>المبلغ</th>
                                                <th>التاريخ</th>
                                                <th>البيان</th>
                                                <th>الإجراءات</th>
                                            </tr>
                                        </thead>
                                        <tbody id="vouchersTableBody">
                                            <tr>
                                                <td colspan="7" class="text-center text-muted">لا توجد سندات</td>
                                            </tr>
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Alerts Tab -->
                    <div id="alertsMainTab" class="main-tab-content" style="display: none;">
                        <div class="card">
                            <div class="card-header">
                                <h5><i class="fas fa-exclamation-triangle text-warning"></i> التنبيهات والفترات المفقودة</h5>
                            </div>
                            <div class="card-body" id="alertsContent">
                                <div class="text-center text-muted">
                                    <i class="fas fa-spinner fa-spin"></i> جاري تحميل التنبيهات...
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Add/Edit Employee Modal -->
    <div class="modal fade" id="employeeModal" tabindex="-1">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">إضافة موظف جديد</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <form id="employeeForm">
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">الاسم الكامل *</label>
                                    <input type="text" id="employeeName" class="form-control" required>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">المنصب *</label>
                                    <select id="position" class="form-control" required>
                                        <option value="">اختر المنصب</option>
                                        <option value="baker">خباز</option>
                                        <option value="assistant">مساعد خباز</option>
                                        <option value="cashier">أمين صندوق</option>
                                        <option value="manager">مدير</option>
                                        <option value="cleaner">عامل نظافة</option>
                                        <option value="driver">سائق</option>
                                        <option value="security">حارس</option>
                                    </select>
                                </div>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">رقم الهاتف</label>
                                    <input type="text" id="phone" class="form-control">
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">تاريخ التوظيف</label>
                                    <input type="date" id="hireDate" class="form-control">
                                </div>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-md-4">
                                <div class="mb-3">
                                    <label class="form-label">الراتب الأساسي</label>
                                    <input type="number" id="basicSalary" class="form-control" value="0" step="0.01">
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="mb-3">
                                    <label class="form-label">بدل المواصلات</label>
                                    <input type="number" id="transportAllowance" class="form-control" value="0" step="0.01">
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="mb-3">
                                    <label class="form-label">بدلات أخرى</label>
                                    <input type="number" id="otherAllowances" class="form-control" value="0" step="0.01">
                                </div>
                            </div>
                        </div>
                        <div class="mb-3">
                            <label class="form-label">العنوان</label>
                            <textarea id="address" class="form-control" rows="2"></textarea>
                        </div>
                        <div class="mb-3">
                            <label class="form-label">ملاحظات</label>
                            <textarea id="notes" class="form-control" rows="2"></textarea>
                        </div>
                        <div class="form-check mb-3">
                            <input type="checkbox" id="isActive" class="form-check-input" checked>
                            <label class="form-check-label">موظف نشط</label>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                    <button type="button" class="btn btn-primary" onclick="saveEmployee()">حفظ</button>
                </div>
            </div>
        </div>
    </div>

    <!-- Accrual Modal -->
    <div class="modal fade" id="accrualModal" tabindex="-1">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">قيد استحقاق شهري</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <form id="accrualForm">
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">الموظف *</label>
                                    <select id="accrualEmployee" class="form-control" required>
                                        <option value="">اختر الموظف</option>
                                    </select>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="mb-3">
                                    <label class="form-label">الشهر *</label>
                                    <select id="accrualMonthSelect" class="form-control" required>
                                        <option value="1">يناير</option>
                                        <option value="2">فبراير</option>
                                        <option value="3">مارس</option>
                                        <option value="4">أبريل</option>
                                        <option value="5">مايو</option>
                                        <option value="6">يونيو</option>
                                        <option value="7">يوليو</option>
                                        <option value="8">أغسطس</option>
                                        <option value="9">سبتمبر</option>
                                        <option value="10">أكتوبر</option>
                                        <option value="11">نوفمبر</option>
                                        <option value="12">ديسمبر</option>
                                    </select>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="mb-3">
                                    <label class="form-label">السنة *</label>
                                    <input type="number" id="accrualYearInput" class="form-control" required>
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">الراتب الأساسي</label>
                                    <input type="number" id="accrualBasicSalary" class="form-control" readonly>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">البدلات</label>
                                    <input type="number" id="accrualAllowances" class="form-control" value="0" step="0.01" onchange="calculateAccrualTotal()">
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">الخصومات والجزاءات</label>
                                    <input type="number" id="accrualDeductions" class="form-control" value="0" step="0.01" onchange="calculateAccrualTotal()">
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">صافي الاستحقاق</label>
                                    <input type="number" id="accrualNetAmount" class="form-control" readonly>
                                </div>
                            </div>
                        </div>

                        <div class="mb-3">
                            <label class="form-label">ملاحظات</label>
                            <textarea id="accrualNotes" class="form-control" rows="2"></textarea>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                    <button type="button" class="btn btn-primary" onclick="saveAccrual()">حفظ الاستحقاق</button>
                </div>
            </div>
        </div>
    </div>

    <!-- Voucher Modal -->
    <div class="modal fade" id="voucherModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">سند قبض/صرف</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <form id="voucherForm">
                        <input type="hidden" id="voucherType">
                        <div class="mb-3">
                            <label class="form-label">الموظف *</label>
                            <select id="voucherEmployee" class="form-control" required onchange="updateEmployeeBalance()">
                                <option value="">اختر الموظف</option>
                            </select>
                        </div>
                        <div class="mb-3">
                            <label class="form-label">المبلغ *</label>
                            <input type="number" id="voucherAmount" class="form-control" required step="0.01" min="0.01">
                        </div>
                        <div class="mb-3">
                            <label class="form-label">التاريخ *</label>
                            <input type="date" id="voucherDate" class="form-control" required>
                        </div>
                        <div class="mb-3">
                            <label class="form-label">البيان *</label>
                            <textarea id="voucherDescription" class="form-control" rows="3" required></textarea>
                        </div>
                        <div class="alert alert-info">
                            <strong>رصيد الموظف الحالي: </strong>
                            <span id="currentEmployeeBalance">0.00 ر.ي</span>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                    <button type="button" class="btn btn-primary" onclick="saveVoucher()">حفظ السند</button>
                </div>
            </div>
        </div>
    </div>

    <!-- Success/Error Messages -->
    <div id="messages"></div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // البيانات المحلية
        let employees = [];
        let accruals = [];
        let employeeVouchers = [];
        let nextId = 1;
        let nextAccrualId = 1;
        let nextVoucherId = 1;
        let currentFilter = 'all';
        let currentMainTab = 'employees';

        // تحميل البيانات عند بدء الصفحة
        document.addEventListener('DOMContentLoaded', function() {
            loadAllData();
            initializeYearOptions();
            setCurrentMonthYear();
            // تعيين تاريخ اليوم كافتراضي
            if (document.getElementById('hireDate')) {
                document.getElementById('hireDate').value = new Date().toISOString().split('T')[0];
            }
        });

        // تحميل جميع البيانات
        function loadAllData() {
            loadEmployees();
            loadAccruals();
            loadEmployeeVouchers();
            displayEmployees();
            displayAccruals();
            displayBalances();
            displayVouchers();
            checkMissingAccruals();
        }

        // تحميل الاستحقاقات
        function loadAccruals() {
            try {
                const savedAccruals = localStorage.getItem('anw_employee_accruals');
                if (savedAccruals && savedAccruals !== 'null') {
                    accruals = JSON.parse(savedAccruals);
                    if (accruals.length > 0) {
                        nextAccrualId = Math.max(...accruals.map(a => a.id), 0) + 1;
                    }
                } else {
                    accruals = [];
                }
            } catch (error) {
                console.error('خطأ في تحميل الاستحقاقات:', error);
                accruals = [];
            }
        }

        // تحميل سندات الموظفين
        function loadEmployeeVouchers() {
            try {
                const savedVouchers = localStorage.getItem('anw_employee_vouchers');
                if (savedVouchers && savedVouchers !== 'null') {
                    employeeVouchers = JSON.parse(savedVouchers);
                    if (employeeVouchers.length > 0) {
                        nextVoucherId = Math.max(...employeeVouchers.map(v => v.id), 0) + 1;
                    }
                } else {
                    employeeVouchers = [];
                }
            } catch (error) {
                console.error('خطأ في تحميل سندات الموظفين:', error);
                employeeVouchers = [];
            }
        }

        // حفظ البيانات
        function saveAccruals() {
            localStorage.setItem('anw_employee_accruals', JSON.stringify(accruals));
        }

        function saveEmployeeVouchers() {
            localStorage.setItem('anw_employee_vouchers', JSON.stringify(employeeVouchers));
        }

        // تحميل الموظفين من localStorage
        function loadEmployees() {
            try {
                const savedEmployees = localStorage.getItem('anw_employees');
                if (savedEmployees && savedEmployees !== 'null') {
                    employees = JSON.parse(savedEmployees);
                    if (employees.length > 0) {
                        nextId = Math.max(...employees.map(e => e.id), 0) + 1;
                    }
                } else {
                    // إنشاء موظفين افتراضيين
                    employees = [
                        {id: 1, name: 'أحمد محمد علي', position: 'baker', phone: '777-123456', hireDate: '2023-01-15', basicSalary: 150000, transportAllowance: 20000, otherAllowances: 10000, address: 'صنعاء - حي السبعين', notes: 'خباز ماهر', active: true},
                        {id: 2, name: 'فاطمة أحمد سالم', position: 'cashier', phone: '777-234567', hireDate: '2023-03-01', basicSalary: 120000, transportAllowance: 15000, otherAllowances: 5000, address: 'صنعاء - شارع الزبيري', notes: 'أمينة صندوق موثوقة', active: true},
                        {id: 3, name: 'محمد عبدالله حسن', position: 'manager', phone: '777-345678', hireDate: '2022-06-01', basicSalary: 250000, transportAllowance: 30000, otherAllowances: 20000, address: 'صنعاء - حي الحصبة', notes: 'مدير خبير', active: true}
                    ];
                    nextId = 4;
                    saveEmployees();
                }
                displayEmployees();
            } catch (error) {
                console.error('خطأ في تحميل الموظفين:', error);
                showMessage('خطأ في تحميل البيانات', 'danger');
            }
        }

        // حفظ الموظفين في localStorage
        function saveEmployees() {
            localStorage.setItem('anw_employees', JSON.stringify(employees));
        }

        // عرض الموظفين
        function displayEmployees() {
            const tbody = document.getElementById('employeesTableBody');
            let filteredEmployees = employees;

            // تطبيق الفلتر
            if (currentFilter === 'active') {
                filteredEmployees = employees.filter(emp => emp.active);
            } else if (currentFilter === 'inactive') {
                filteredEmployees = employees.filter(emp => !emp.active);
            }

            if (filteredEmployees.length === 0) {
                tbody.innerHTML = `
                    <tr>
                        <td colspan="9" class="text-center text-muted">
                            لا توجد موظفين
                            <br>
                            <button class="btn btn-primary mt-2" onclick="addEmployee()">إضافة موظف جديد</button>
                        </td>
                    </tr>
                `;
                return;
            }

            let html = '';
            filteredEmployees.forEach(employee => {
                const positionLabel = getPositionLabel(employee.position);
                const statusBadge = employee.active ? 
                    '<span class="badge bg-success">نشط</span>' : 
                    '<span class="badge bg-secondary">غير نشط</span>';
                
                const totalSalary = employee.basicSalary + employee.transportAllowance + employee.otherAllowances;

                // حساب رصيد الموظف
                const balance = calculateEmployeeBalance(employee.id);
                const balanceClass = balance > 0 ? 'text-success' : balance < 0 ? 'text-danger' : 'text-muted';

                // آخر استحقاق
                const lastAccrual = getLastAccrual(employee.id);
                const lastAccrualText = lastAccrual ?
                    `${lastAccrual.month}/${lastAccrual.year}` :
                    '<span class="text-warning">لا يوجد</span>';

                html += `
                    <tr>
                        <td>${employee.name}</td>
                        <td><span class="badge bg-info">${positionLabel}</span></td>
                        <td>${employee.phone || '-'}</td>
                        <td>${totalSalary.toLocaleString()} ر.ي</td>
                        <td>${new Date(employee.hireDate).toLocaleDateString('ar-YE')}</td>
                        <td class="${balanceClass} fw-bold">${balance.toLocaleString()} ر.ي</td>
                        <td>${lastAccrualText}</td>
                        <td>${statusBadge}</td>
                        <td>
                            <button class="btn btn-sm btn-info" onclick="viewEmployeeStatement(${employee.id})" title="كشف حساب">
                                <i class="fas fa-file-alt"></i>
                            </button>
                            <button class="btn btn-sm btn-warning" onclick="editEmployee(${employee.id})" title="تحرير">
                                <i class="fas fa-edit"></i>
                            </button>
                            <button class="btn btn-sm btn-danger" onclick="deleteEmployee(${employee.id})" title="حذف">
                                <i class="fas fa-trash"></i>
                            </button>
                        </td>
                    </tr>
                `;
            });

            tbody.innerHTML = html;
        }

        // حساب رصيد الموظف
        function calculateEmployeeBalance(employeeId) {
            // إجمالي الاستحقاقات
            const totalAccruals = accruals
                .filter(a => a.employeeId === employeeId)
                .reduce((sum, a) => sum + a.netAmount, 0);

            // إجمالي المدفوع (سندات الصرف) والمقبوض (سندات القبض)
            const totalPaid = employeeVouchers
                .filter(v => v.employeeId === employeeId && v.type === 'payment')
                .reduce((sum, v) => sum + v.amount, 0);

            const totalReceived = employeeVouchers
                .filter(v => v.employeeId === employeeId && v.type === 'receipt')
                .reduce((sum, v) => sum + v.amount, 0);

            // الرصيد = الاستحقاقات - المدفوع + المقبوض
            return totalAccruals - totalPaid + totalReceived;
        }

        // الحصول على آخر استحقاق
        function getLastAccrual(employeeId) {
            const employeeAccruals = accruals
                .filter(a => a.employeeId === employeeId)
                .sort((a, b) => new Date(b.year, b.month - 1) - new Date(a.year, a.month - 1));

            return employeeAccruals.length > 0 ? employeeAccruals[0] : null;
        }

        // الحصول على تسمية المنصب
        function getPositionLabel(position) {
            const positions = {
                'baker': 'خباز',
                'assistant': 'مساعد خباز',
                'cashier': 'أمين صندوق',
                'manager': 'مدير',
                'cleaner': 'عامل نظافة',
                'driver': 'سائق',
                'security': 'حارس'
            };
            return positions[position] || position;
        }

        // فلترة الموظفين
        function filterEmployees(filter) {
            currentFilter = filter;
            
            // تحديث التبويبات
            document.querySelectorAll('.nav-link').forEach(link => {
                link.classList.remove('active');
            });
            event.target.classList.add('active');
            
            displayEmployees();
        }

        // البحث في الموظفين
        function searchEmployees() {
            const searchTerm = document.getElementById('searchInput').value.toLowerCase();
            const tbody = document.getElementById('employeesTableBody');
            
            let filteredEmployees = employees.filter(employee => {
                return employee.name.toLowerCase().includes(searchTerm) ||
                       (employee.phone && employee.phone.toLowerCase().includes(searchTerm));
            });

            // تطبيق فلتر الحالة أيضاً
            if (currentFilter === 'active') {
                filteredEmployees = filteredEmployees.filter(emp => emp.active);
            } else if (currentFilter === 'inactive') {
                filteredEmployees = filteredEmployees.filter(emp => !emp.active);
            }

            if (filteredEmployees.length === 0) {
                tbody.innerHTML = `
                    <tr>
                        <td colspan="7" class="text-center text-muted">لا توجد نتائج للبحث</td>
                    </tr>
                `;
                return;
            }

            let html = '';
            filteredEmployees.forEach(employee => {
                const positionLabel = getPositionLabel(employee.position);
                const statusBadge = employee.active ? 
                    '<span class="badge bg-success">نشط</span>' : 
                    '<span class="badge bg-secondary">غير نشط</span>';
                
                const totalSalary = employee.basicSalary + employee.transportAllowance + employee.otherAllowances;

                html += `
                    <tr>
                        <td>${employee.name}</td>
                        <td><span class="badge bg-info">${positionLabel}</span></td>
                        <td>${employee.phone || '-'}</td>
                        <td>${totalSalary.toLocaleString()} ر.ي</td>
                        <td>${new Date(employee.hireDate).toLocaleDateString('ar-YE')}</td>
                        <td>${statusBadge}</td>
                        <td>
                            <button class="btn btn-sm btn-warning" onclick="editEmployee(${employee.id})">
                                <i class="fas fa-edit"></i>
                            </button>
                            <button class="btn btn-sm btn-danger" onclick="deleteEmployee(${employee.id})">
                                <i class="fas fa-trash"></i>
                            </button>
                        </td>
                    </tr>
                `;
            });

            tbody.innerHTML = html;
        }

        // إضافة موظف جديد
        function addEmployee() {
            clearForm();
            document.querySelector('.modal-title').textContent = 'إضافة موظف جديد';
            new bootstrap.Modal(document.getElementById('employeeModal')).show();
        }

        // تحرير موظف
        function editEmployee(employeeId) {
            const employee = employees.find(e => e.id === employeeId);
            if (employee) {
                document.getElementById('employeeName').value = employee.name;
                document.getElementById('position').value = employee.position;
                document.getElementById('phone').value = employee.phone || '';
                document.getElementById('hireDate').value = employee.hireDate;
                document.getElementById('basicSalary').value = employee.basicSalary;
                document.getElementById('transportAllowance').value = employee.transportAllowance;
                document.getElementById('otherAllowances').value = employee.otherAllowances;
                document.getElementById('address').value = employee.address || '';
                document.getElementById('notes').value = employee.notes || '';
                document.getElementById('isActive').checked = employee.active;
                
                document.getElementById('employeeForm').dataset.editId = employeeId;
                document.querySelector('.modal-title').textContent = 'تحرير الموظف';
                
                new bootstrap.Modal(document.getElementById('employeeModal')).show();
            }
        }

        // حذف موظف
        function deleteEmployee(employeeId) {
            if (confirm('هل أنت متأكد من حذف هذا الموظف؟')) {
                employees = employees.filter(e => e.id !== employeeId);
                saveEmployees();
                displayEmployees();
                showMessage('تم حذف الموظف بنجاح', 'success');
            }
        }

        // حفظ الموظف
        function saveEmployee() {
            const form = document.getElementById('employeeForm');
            const editId = form.dataset.editId;
            
            const employeeData = {
                name: document.getElementById('employeeName').value,
                position: document.getElementById('position').value,
                phone: document.getElementById('phone').value,
                hireDate: document.getElementById('hireDate').value,
                basicSalary: parseFloat(document.getElementById('basicSalary').value) || 0,
                transportAllowance: parseFloat(document.getElementById('transportAllowance').value) || 0,
                otherAllowances: parseFloat(document.getElementById('otherAllowances').value) || 0,
                address: document.getElementById('address').value,
                notes: document.getElementById('notes').value,
                active: document.getElementById('isActive').checked
            };

            // التحقق من البيانات
            if (!employeeData.name || !employeeData.position) {
                showMessage('يرجى ملء جميع الحقول المطلوبة', 'danger');
                return;
            }

            if (editId) {
                // تحديث موظف موجود
                const employeeIndex = employees.findIndex(e => e.id == editId);
                employees[employeeIndex] = { ...employees[employeeIndex], ...employeeData };
                showMessage('تم تحديث الموظف بنجاح', 'success');
            } else {
                // إضافة موظف جديد
                employeeData.id = nextId++;
                employees.push(employeeData);
                showMessage('تم إضافة الموظف بنجاح', 'success');
            }

            saveEmployees();
            displayEmployees();
            bootstrap.Modal.getInstance(document.getElementById('employeeModal')).hide();
        }

        // تنظيف النموذج
        function clearForm() {
            document.getElementById('employeeForm').reset();
            document.getElementById('employeeForm').removeAttribute('data-edit-id');
            document.getElementById('basicSalary').value = '0';
            document.getElementById('transportAllowance').value = '0';
            document.getElementById('otherAllowances').value = '0';
            document.getElementById('isActive').checked = true;
            document.getElementById('hireDate').value = new Date().toISOString().split('T')[0];
        }

        // حساب الرواتب
        function calculateSalaries() {
            const activeEmployees = employees.filter(e => e.active);
            const totalSalaries = activeEmployees.reduce((total, emp) => {
                return total + emp.basicSalary + emp.transportAllowance + emp.otherAllowances;
            }, 0);

            alert(`إجمالي رواتب الموظفين النشطين: ${totalSalaries.toLocaleString()} ر.ي\nعدد الموظفين: ${activeEmployees.length}`);
        }

        // تصدير الموظفين
        function exportEmployees() {
            const csvContent = "data:text/csv;charset=utf-8," 
                + "الاسم,المنصب,الهاتف,تاريخ التوظيف,الراتب الأساسي,بدل المواصلات,بدلات أخرى,الإجمالي,الحالة\n"
                + employees.map(emp => {
                    const total = emp.basicSalary + emp.transportAllowance + emp.otherAllowances;
                    return `${emp.name},${getPositionLabel(emp.position)},${emp.phone || ''},${emp.hireDate},${emp.basicSalary},${emp.transportAllowance},${emp.otherAllowances},${total},${emp.active ? 'نشط' : 'غير نشط'}`;
                }).join("\n");

            const encodedUri = encodeURI(csvContent);
            const link = document.createElement("a");
            link.setAttribute("href", encodedUri);
            link.setAttribute("download", "employees.csv");
            document.body.appendChild(link);
            link.click();
            document.body.removeChild(link);
            
            showMessage('تم تصدير الموظفين بنجاح', 'success');
        }

        // عرض رسالة
        function showMessage(message, type) {
            const messagesDiv = document.getElementById('messages');
            const alertHtml = `
                <div class="alert alert-${type} alert-dismissible fade show position-fixed" 
                     style="top: 20px; left: 20px; z-index: 9999; min-width: 300px;">
                    ${message}
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
            `;
            messagesDiv.innerHTML = alertHtml;
            
            setTimeout(() => {
                const alert = messagesDiv.querySelector('.alert');
                if (alert) {
                    bootstrap.Alert.getOrCreateInstance(alert).close();
                }
            }, 3000);
        }

        // تبديل التبويبات الرئيسية
        function showMainTab(tabName) {
            // إخفاء جميع التبويبات
            document.querySelectorAll('.main-tab-content').forEach(tab => {
                tab.style.display = 'none';
            });

            // إزالة active من جميع الروابط
            document.querySelectorAll('#mainTabs .nav-link').forEach(link => {
                link.classList.remove('active');
            });

            // إظهار التبويب المحدد
            document.getElementById(tabName + 'MainTab').style.display = 'block';

            // إضافة active للرابط المحدد
            event.target.classList.add('active');

            currentMainTab = tabName;

            // تحديث البيانات حسب التبويب
            if (tabName === 'accruals') {
                displayAccruals();
            } else if (tabName === 'balances') {
                displayBalances();
            } else if (tabName === 'vouchers') {
                displayVouchers();
            } else if (tabName === 'alerts') {
                checkMissingAccruals();
            }
        }

        // قيد استحقاقات شهرية
        function processMonthlyAccruals() {
            const currentDate = new Date();
            const currentMonth = currentDate.getMonth() + 1;
            const currentYear = currentDate.getFullYear();

            // تعيين الشهر والسنة الحالية
            document.getElementById('accrualMonthSelect').value = currentMonth;
            document.getElementById('accrualYearInput').value = currentYear;

            // تحميل خيارات الموظفين
            loadEmployeeOptions();

            new bootstrap.Modal(document.getElementById('accrualModal')).show();
        }

        // عرض الفترات المفقودة
        function showMissingAccruals() {
            showMainTab('alerts');
        }

        // تحميل خيارات الموظفين
        function loadEmployeeOptions() {
            const accrualSelect = document.getElementById('accrualEmployee');
            const voucherSelect = document.getElementById('voucherEmployee');

            let html = '<option value="">اختر الموظف</option>';
            employees.filter(emp => emp.active).forEach(employee => {
                html += `<option value="${employee.id}">${employee.name} - ${employee.position}</option>`;
            });

            if (accrualSelect) accrualSelect.innerHTML = html;
            if (voucherSelect) voucherSelect.innerHTML = html;
        }

        // تهيئة خيارات السنوات
        function initializeYearOptions() {
            const yearSelect = document.getElementById('accrualYear');
            const currentYear = new Date().getFullYear();

            let html = '<option value="">اختر السنة</option>';
            for (let year = currentYear - 2; year <= currentYear + 1; year++) {
                html += `<option value="${year}">${year}</option>`;
            }

            if (yearSelect) yearSelect.innerHTML = html;
        }

        // تعيين الشهر والسنة الحالية
        function setCurrentMonthYear() {
            const currentDate = new Date();
            const currentMonth = currentDate.getMonth() + 1;
            const currentYear = currentDate.getFullYear();

            const monthSelect = document.getElementById('accrualMonth');
            const yearSelect = document.getElementById('accrualYear');

            if (monthSelect) monthSelect.value = currentMonth;
            if (yearSelect) yearSelect.value = currentYear;
        }

        // عرض الاستحقاقات
        function displayAccruals() {
            const tbody = document.getElementById('accrualsTableBody');
            const year = document.getElementById('accrualYear').value;
            const month = document.getElementById('accrualMonth').value;

            let filteredAccruals = accruals;

            if (year) {
                filteredAccruals = filteredAccruals.filter(a => a.year == year);
            }
            if (month) {
                filteredAccruals = filteredAccruals.filter(a => a.month == month);
            }

            if (filteredAccruals.length === 0) {
                tbody.innerHTML = `
                    <tr>
                        <td colspan="8" class="text-center text-muted">
                            لا توجد استحقاقات للفترة المحددة
                            <br>
                            <button class="btn btn-primary mt-2" onclick="processMonthlyAccruals()">إضافة استحقاق جديد</button>
                        </td>
                    </tr>
                `;
                return;
            }

            let html = '';
            filteredAccruals.sort((a, b) => new Date(b.year, b.month - 1) - new Date(a.year, a.month - 1)).forEach(accrual => {
                const employee = employees.find(e => e.id === accrual.employeeId);
                const monthNames = ['', 'يناير', 'فبراير', 'مارس', 'أبريل', 'مايو', 'يونيو',
                                  'يوليو', 'أغسطس', 'سبتمبر', 'أكتوبر', 'نوفمبر', 'ديسمبر'];

                html += `
                    <tr>
                        <td>${employee ? employee.name : 'غير محدد'}</td>
                        <td>${monthNames[accrual.month]} ${accrual.year}</td>
                        <td>${accrual.basicSalary.toLocaleString()} ر.ي</td>
                        <td>${accrual.allowances.toLocaleString()} ر.ي</td>
                        <td>${accrual.deductions.toLocaleString()} ر.ي</td>
                        <td class="fw-bold text-success">${accrual.netAmount.toLocaleString()} ر.ي</td>
                        <td>${new Date(accrual.createdAt).toLocaleDateString('ar-YE')}</td>
                        <td>
                            <button class="btn btn-sm btn-info" onclick="printAccrual(${accrual.id})" title="طباعة">
                                <i class="fas fa-print"></i>
                            </button>
                            <button class="btn btn-sm btn-danger" onclick="deleteAccrual(${accrual.id})" title="حذف">
                                <i class="fas fa-trash"></i>
                            </button>
                        </td>
                    </tr>
                `;
            });

            tbody.innerHTML = html;
        }

        // عرض الأرصدة
        function displayBalances() {
            const tbody = document.getElementById('balancesTableBody');

            if (employees.length === 0) {
                tbody.innerHTML = `
                    <tr>
                        <td colspan="6" class="text-center text-muted">
                            لا يوجد موظفين
                            <br>
                            <button class="btn btn-primary mt-2" onclick="addEmployee()">إضافة موظف جديد</button>
                        </td>
                    </tr>
                `;
                return;
            }

            let html = '';
            employees.filter(emp => emp.active).forEach(employee => {
                const totalAccruals = accruals
                    .filter(a => a.employeeId === employee.id)
                    .reduce((sum, a) => sum + a.netAmount, 0);

                const totalPaid = employeeVouchers
                    .filter(v => v.employeeId === employee.id && v.type === 'payment')
                    .reduce((sum, v) => sum + v.amount, 0);

                const totalReceived = employeeVouchers
                    .filter(v => v.employeeId === employee.id && v.type === 'receipt')
                    .reduce((sum, v) => sum + v.amount, 0);

                const balance = totalAccruals - totalPaid + totalReceived;
                const balanceClass = balance > 0 ? 'text-success' : balance < 0 ? 'text-danger' : 'text-muted';

                const lastPayment = employeeVouchers
                    .filter(v => v.employeeId === employee.id && v.type === 'payment')
                    .sort((a, b) => new Date(b.date) - new Date(a.date))[0];

                const lastPaymentText = lastPayment ?
                    new Date(lastPayment.date).toLocaleDateString('ar-YE') :
                    'لا يوجد';

                html += `
                    <tr>
                        <td>${employee.name}</td>
                        <td>${totalAccruals.toLocaleString()} ر.ي</td>
                        <td>${totalPaid.toLocaleString()} ر.ي</td>
                        <td class="${balanceClass} fw-bold">${balance.toLocaleString()} ر.ي</td>
                        <td>${lastPaymentText}</td>
                        <td>
                            <button class="btn btn-sm btn-info" onclick="viewEmployeeStatement(${employee.id})" title="كشف حساب">
                                <i class="fas fa-file-alt"></i>
                            </button>
                            ${balance > 0 ? `
                                <button class="btn btn-sm btn-success" onclick="payEmployee(${employee.id})" title="صرف راتب">
                                    <i class="fas fa-money-bill-wave"></i>
                                </button>
                            ` : ''}
                        </td>
                    </tr>
                `;
            });

            tbody.innerHTML = html;
        }

        // عرض السندات
        function displayVouchers() {
            const tbody = document.getElementById('vouchersTableBody');

            if (employeeVouchers.length === 0) {
                tbody.innerHTML = `
                    <tr>
                        <td colspan="7" class="text-center text-muted">
                            لا توجد سندات
                            <br>
                            <button class="btn btn-success mt-2" onclick="addPaymentVoucher()">إضافة سند صرف</button>
                        </td>
                    </tr>
                `;
                return;
            }

            let html = '';
            employeeVouchers.sort((a, b) => new Date(b.date) - new Date(a.date)).forEach(voucher => {
                const employee = employees.find(e => e.id === voucher.employeeId);
                const typeLabel = voucher.type === 'payment' ? 'سند صرف' : 'سند قبض';
                const typeBadge = voucher.type === 'payment' ? 'bg-danger' : 'bg-success';

                html += `
                    <tr>
                        <td>${voucher.number}</td>
                        <td><span class="badge ${typeBadge}">${typeLabel}</span></td>
                        <td>${employee ? employee.name : 'غير محدد'}</td>
                        <td class="fw-bold">${voucher.amount.toLocaleString()} ر.ي</td>
                        <td>${new Date(voucher.date).toLocaleDateString('ar-YE')}</td>
                        <td>${voucher.description}</td>
                        <td>
                            <button class="btn btn-sm btn-info" onclick="printVoucher(${voucher.id})" title="طباعة">
                                <i class="fas fa-print"></i>
                            </button>
                            <button class="btn btn-sm btn-danger" onclick="deleteVoucher(${voucher.id})" title="حذف">
                                <i class="fas fa-trash"></i>
                            </button>
                        </td>
                    </tr>
                `;
            });

            tbody.innerHTML = html;
        }

        // فحص الفترات المفقودة
        function checkMissingAccruals() {
            const alertsContent = document.getElementById('alertsContent');
            const currentDate = new Date();
            const currentYear = currentDate.getFullYear();
            const currentMonth = currentDate.getMonth() + 1;

            let alertsHtml = '';
            let totalAlerts = 0;

            // فحص كل موظف نشط
            employees.filter(emp => emp.active).forEach(employee => {
                const hireDate = new Date(employee.hireDate);
                const hireYear = hireDate.getFullYear();
                const hireMonth = hireDate.getMonth() + 1;

                const missingPeriods = [];

                // فحص من تاريخ التعيين حتى الشهر الحالي
                for (let year = hireYear; year <= currentYear; year++) {
                    const startMonth = year === hireYear ? hireMonth : 1;
                    const endMonth = year === currentYear ? currentMonth - 1 : 12; // الشهر الحالي لا يُعتبر مفقود

                    for (let month = startMonth; month <= endMonth; month++) {
                        const hasAccrual = accruals.some(a =>
                            a.employeeId === employee.id &&
                            a.year === year &&
                            a.month === month
                        );

                        if (!hasAccrual) {
                            const monthNames = ['', 'يناير', 'فبراير', 'مارس', 'أبريل', 'مايو', 'يونيو',
                                              'يوليو', 'أغسطس', 'سبتمبر', 'أكتوبر', 'نوفمبر', 'ديسمبر'];
                            missingPeriods.push(`${monthNames[month]} ${year}`);
                        }
                    }
                }

                if (missingPeriods.length > 0) {
                    totalAlerts++;
                    alertsHtml += `
                        <div class="alert alert-warning">
                            <h6><i class="fas fa-exclamation-triangle"></i> ${employee.name}</h6>
                            <p><strong>المنصب:</strong> ${employee.position}</p>
                            <p><strong>تاريخ التعيين:</strong> ${new Date(employee.hireDate).toLocaleDateString('ar-YE')}</p>
                            <p><strong>الفترات المفقودة:</strong></p>
                            <ul class="mb-2">
                                ${missingPeriods.map(period => `<li>${period}</li>`).join('')}
                            </ul>
                            <button class="btn btn-sm btn-primary" onclick="addAccrualForEmployee(${employee.id})">
                                <i class="fas fa-plus"></i> قيد استحقاقات
                            </button>
                        </div>
                    `;
                }
            });

            // فحص الموظفين بأرصدة مرتفعة
            employees.filter(emp => emp.active).forEach(employee => {
                const balance = calculateEmployeeBalance(employee.id);
                if (balance > employee.salary * 2) { // إذا كان الرصيد أكثر من راتبين
                    totalAlerts++;
                    alertsHtml += `
                        <div class="alert alert-info">
                            <h6><i class="fas fa-info-circle"></i> رصيد مرتفع - ${employee.name}</h6>
                            <p><strong>الرصيد الحالي:</strong> ${balance.toLocaleString()} ر.ي</p>
                            <p><strong>الراتب الشهري:</strong> ${employee.salary.toLocaleString()} ر.ي</p>
                            <p>يُنصح بصرف راتب للموظف</p>
                            <button class="btn btn-sm btn-success" onclick="payEmployee(${employee.id})">
                                <i class="fas fa-money-bill-wave"></i> صرف راتب
                            </button>
                        </div>
                    `;
                }
            });

            if (totalAlerts === 0) {
                alertsHtml = `
                    <div class="alert alert-success text-center">
                        <i class="fas fa-check-circle fa-3x mb-3"></i>
                        <h5>ممتاز! لا توجد تنبيهات</h5>
                        <p>جميع الموظفين لديهم استحقاقات محدثة ولا توجد مشاكل</p>
                    </div>
                `;
            } else {
                alertsHtml = `
                    <div class="alert alert-warning mb-3">
                        <h5><i class="fas fa-exclamation-triangle"></i> يوجد ${totalAlerts} تنبيه يحتاج انتباه</h5>
                    </div>
                ` + alertsHtml;
            }

            alertsContent.innerHTML = alertsHtml;
        }

        // إضافة استحقاق لموظف محدد
        function addAccrualForEmployee(employeeId) {
            const employee = employees.find(e => e.id === employeeId);
            if (employee) {
                document.getElementById('accrualEmployee').value = employeeId;
                document.getElementById('accrualBasicSalary').value = employee.salary;

                // تعيين الشهر والسنة الحالية
                const currentDate = new Date();
                document.getElementById('accrualMonthSelect').value = currentDate.getMonth() + 1;
                document.getElementById('accrualYearInput').value = currentDate.getFullYear();

                calculateAccrualTotal();
                new bootstrap.Modal(document.getElementById('accrualModal')).show();
            }
        }

        // صرف راتب لموظف
        function payEmployee(employeeId) {
            const employee = employees.find(e => e.id === employeeId);
            if (employee) {
                const balance = calculateEmployeeBalance(employeeId);

                document.getElementById('voucherEmployee').value = employeeId;
                document.getElementById('voucherAmount').value = Math.min(balance, employee.salary);
                document.getElementById('voucherDate').value = new Date().toISOString().split('T')[0];
                document.getElementById('voucherDescription').value = `صرف راتب ${employee.name}`;
                document.getElementById('voucherType').value = 'payment';

                updateEmployeeBalance();
                new bootstrap.Modal(document.getElementById('voucherModal')).show();
            }
        }

        // عرض كشف حساب موظف
        function viewEmployeeStatement(employeeId) {
            const employee = employees.find(e => e.id === employeeId);
            if (!employee) return;

            const employeeAccruals = accruals.filter(a => a.employeeId === employeeId);
            const employeeVouchersFiltered = employeeVouchers.filter(v => v.employeeId === employeeId);

            let statementHtml = `
                <div style="font-family: Arial; direction: rtl; text-align: right;">
                    <div style="text-align: center; margin-bottom: 20px;">
                        <h2>كشف حساب الموظف</h2>
                        <h3>${employee.name}</h3>
                        <p>المنصب: ${employee.position} | تاريخ التعيين: ${new Date(employee.hireDate).toLocaleDateString('ar-YE')}</p>
                    </div>

                    <h4>الاستحقاقات:</h4>
                    <table style="width: 100%; border-collapse: collapse; margin-bottom: 20px;">
                        <thead>
                            <tr style="background: #f8f9fa;">
                                <th style="border: 1px solid #ddd; padding: 8px;">التاريخ</th>
                                <th style="border: 1px solid #ddd; padding: 8px;">الشهر/السنة</th>
                                <th style="border: 1px solid #ddd; padding: 8px;">الراتب الأساسي</th>
                                <th style="border: 1px solid #ddd; padding: 8px;">البدلات</th>
                                <th style="border: 1px solid #ddd; padding: 8px;">الخصومات</th>
                                <th style="border: 1px solid #ddd; padding: 8px;">صافي الاستحقاق</th>
                            </tr>
                        </thead>
                        <tbody>
            `;

            let totalAccruals = 0;
            employeeAccruals.forEach(accrual => {
                const monthNames = ['', 'يناير', 'فبراير', 'مارس', 'أبريل', 'مايو', 'يونيو',
                                  'يوليو', 'أغسطس', 'سبتمبر', 'أكتوبر', 'نوفمبر', 'ديسمبر'];
                totalAccruals += accrual.netAmount;

                statementHtml += `
                    <tr>
                        <td style="border: 1px solid #ddd; padding: 8px;">${new Date(accrual.createdAt).toLocaleDateString('ar-YE')}</td>
                        <td style="border: 1px solid #ddd; padding: 8px;">${monthNames[accrual.month]} ${accrual.year}</td>
                        <td style="border: 1px solid #ddd; padding: 8px;">${accrual.basicSalary.toLocaleString()} ر.ي</td>
                        <td style="border: 1px solid #ddd; padding: 8px;">${accrual.allowances.toLocaleString()} ر.ي</td>
                        <td style="border: 1px solid #ddd; padding: 8px;">${accrual.deductions.toLocaleString()} ر.ي</td>
                        <td style="border: 1px solid #ddd; padding: 8px;">${accrual.netAmount.toLocaleString()} ر.ي</td>
                    </tr>
                `;
            });

            statementHtml += `
                        </tbody>
                    </table>

                    <h4>السندات:</h4>
                    <table style="width: 100%; border-collapse: collapse; margin-bottom: 20px;">
                        <thead>
                            <tr style="background: #f8f9fa;">
                                <th style="border: 1px solid #ddd; padding: 8px;">التاريخ</th>
                                <th style="border: 1px solid #ddd; padding: 8px;">رقم السند</th>
                                <th style="border: 1px solid #ddd; padding: 8px;">النوع</th>
                                <th style="border: 1px solid #ddd; padding: 8px;">المبلغ</th>
                                <th style="border: 1px solid #ddd; padding: 8px;">البيان</th>
                            </tr>
                        </thead>
                        <tbody>
            `;

            let totalPaid = 0;
            let totalReceived = 0;
            employeeVouchersFiltered.forEach(voucher => {
                const typeLabel = voucher.type === 'payment' ? 'سند صرف' : 'سند قبض';
                if (voucher.type === 'payment') {
                    totalPaid += voucher.amount;
                } else {
                    totalReceived += voucher.amount;
                }

                statementHtml += `
                    <tr>
                        <td style="border: 1px solid #ddd; padding: 8px;">${new Date(voucher.date).toLocaleDateString('ar-YE')}</td>
                        <td style="border: 1px solid #ddd; padding: 8px;">${voucher.number}</td>
                        <td style="border: 1px solid #ddd; padding: 8px;">${typeLabel}</td>
                        <td style="border: 1px solid #ddd; padding: 8px;">${voucher.amount.toLocaleString()} ر.ي</td>
                        <td style="border: 1px solid #ddd; padding: 8px;">${voucher.description}</td>
                    </tr>
                `;
            });

            const finalBalance = totalAccruals - totalPaid + totalReceived;

            statementHtml += `
                        </tbody>
                    </table>

                    <div style="background: #f8f9fa; padding: 15px; border: 1px solid #ddd;">
                        <h4>ملخص الحساب:</h4>
                        <p><strong>إجمالي الاستحقاقات:</strong> ${totalAccruals.toLocaleString()} ر.ي</p>
                        <p><strong>إجمالي المدفوع:</strong> ${totalPaid.toLocaleString()} ر.ي</p>
                        <p><strong>إجمالي المقبوض:</strong> ${totalReceived.toLocaleString()} ر.ي</p>
                        <hr>
                        <p style="font-size: 18px;"><strong>الرصيد النهائي: ${finalBalance.toLocaleString()} ر.ي</strong></p>
                    </div>
                </div>
            `;

            const printWindow = window.open('', '_blank');
            printWindow.document.write(statementHtml);
            printWindow.document.close();
            printWindow.print();
        }

        // إضافة سند صرف
        function addPaymentVoucher() {
            document.getElementById('voucherType').value = 'payment';
            document.getElementById('voucherDate').value = new Date().toISOString().split('T')[0];
            document.querySelector('#voucherModal .modal-title').textContent = 'سند صرف راتب';
            loadEmployeeOptions();
            new bootstrap.Modal(document.getElementById('voucherModal')).show();
        }

        // إضافة سند قبض
        function addReceiptVoucher() {
            document.getElementById('voucherType').value = 'receipt';
            document.getElementById('voucherDate').value = new Date().toISOString().split('T')[0];
            document.querySelector('#voucherModal .modal-title').textContent = 'سند قبض من موظف';
            loadEmployeeOptions();
            new bootstrap.Modal(document.getElementById('voucherModal')).show();
        }

        // تحديث رصيد الموظف في النموذج
        function updateEmployeeBalance() {
            const employeeId = parseInt(document.getElementById('voucherEmployee').value);
            if (employeeId) {
                const balance = calculateEmployeeBalance(employeeId);
                document.getElementById('currentEmployeeBalance').textContent = balance.toLocaleString() + ' ر.ي';
            } else {
                document.getElementById('currentEmployeeBalance').textContent = '0.00 ر.ي';
            }
        }

        // عرض تقرير الرواتب
        function showPayrollReport() {
            const currentDate = new Date();
            const currentYear = currentDate.getFullYear();
            const currentMonth = currentDate.getMonth() + 1;

            // حساب إحصائيات الرواتب
            const monthlyAccruals = accruals.filter(a => a.year === currentYear && a.month === currentMonth);
            const totalSalaries = monthlyAccruals.reduce((sum, a) => sum + a.netAmount, 0);
            const totalAllowances = monthlyAccruals.reduce((sum, a) => sum + a.allowances, 0);
            const totalDeductions = monthlyAccruals.reduce((sum, a) => sum + a.deductions, 0);

            const monthlyPayments = employeeVouchers.filter(v => {
                const vDate = new Date(v.date);
                return v.type === 'payment' && vDate.getFullYear() === currentYear && vDate.getMonth() + 1 === currentMonth;
            });
            const totalPaid = monthlyPayments.reduce((sum, v) => sum + v.amount, 0);

            let reportHtml = `
                <div style="font-family: Arial; direction: rtl; text-align: right;">
                    <div style="text-align: center; margin-bottom: 20px;">
                        <h2>تقرير الرواتب الشهري</h2>
                        <h3>شهر ${currentMonth}/${currentYear}</h3>
                        <p>تاريخ التقرير: ${new Date().toLocaleDateString('ar-YE')}</p>
                    </div>

                    <div style="background: #f8f9fa; padding: 15px; margin-bottom: 20px; border: 1px solid #ddd;">
                        <h4>ملخص الشهر:</h4>
                        <div style="display: flex; justify-content: space-between;">
                            <div>
                                <p><strong>عدد الموظفين النشطين:</strong> ${employees.filter(e => e.active).length}</p>
                                <p><strong>عدد الاستحقاقات:</strong> ${monthlyAccruals.length}</p>
                            </div>
                            <div>
                                <p><strong>إجمالي الاستحقاقات:</strong> ${totalSalaries.toLocaleString()} ر.ي</p>
                                <p><strong>إجمالي المدفوع:</strong> ${totalPaid.toLocaleString()} ر.ي</p>
                                <p><strong>المتبقي:</strong> ${(totalSalaries - totalPaid).toLocaleString()} ر.ي</p>
                            </div>
                        </div>
                    </div>

                    <h4>تفاصيل الموظفين:</h4>
                    <table style="width: 100%; border-collapse: collapse;">
                        <thead>
                            <tr style="background: #f8f9fa;">
                                <th style="border: 1px solid #ddd; padding: 8px;">الموظف</th>
                                <th style="border: 1px solid #ddd; padding: 8px;">المنصب</th>
                                <th style="border: 1px solid #ddd; padding: 8px;">الراتب الأساسي</th>
                                <th style="border: 1px solid #ddd; padding: 8px;">البدلات</th>
                                <th style="border: 1px solid #ddd; padding: 8px;">الخصومات</th>
                                <th style="border: 1px solid #ddd; padding: 8px;">صافي الاستحقاق</th>
                                <th style="border: 1px solid #ddd; padding: 8px;">المدفوع</th>
                                <th style="border: 1px solid #ddd; padding: 8px;">المتبقي</th>
                            </tr>
                        </thead>
                        <tbody>
            `;

            employees.filter(e => e.active).forEach(employee => {
                const accrual = monthlyAccruals.find(a => a.employeeId === employee.id);
                const paid = monthlyPayments.filter(v => v.employeeId === employee.id).reduce((sum, v) => sum + v.amount, 0);

                if (accrual) {
                    const remaining = accrual.netAmount - paid;
                    reportHtml += `
                        <tr>
                            <td style="border: 1px solid #ddd; padding: 8px;">${employee.name}</td>
                            <td style="border: 1px solid #ddd; padding: 8px;">${employee.position}</td>
                            <td style="border: 1px solid #ddd; padding: 8px;">${accrual.basicSalary.toLocaleString()} ر.ي</td>
                            <td style="border: 1px solid #ddd; padding: 8px;">${accrual.allowances.toLocaleString()} ر.ي</td>
                            <td style="border: 1px solid #ddd; padding: 8px;">${accrual.deductions.toLocaleString()} ر.ي</td>
                            <td style="border: 1px solid #ddd; padding: 8px;">${accrual.netAmount.toLocaleString()} ر.ي</td>
                            <td style="border: 1px solid #ddd; padding: 8px;">${paid.toLocaleString()} ر.ي</td>
                            <td style="border: 1px solid #ddd; padding: 8px;">${remaining.toLocaleString()} ر.ي</td>
                        </tr>
                    `;
                } else {
                    reportHtml += `
                        <tr style="background: #fff3cd;">
                            <td style="border: 1px solid #ddd; padding: 8px;">${employee.name}</td>
                            <td style="border: 1px solid #ddd; padding: 8px;">${employee.position}</td>
                            <td colspan="6" style="border: 1px solid #ddd; padding: 8px; text-align: center; color: #856404;">لم يتم قيد استحقاق هذا الشهر</td>
                        </tr>
                    `;
                }
            });

            reportHtml += `
                        </tbody>
                    </table>
                </div>
            `;

            const printWindow = window.open('', '_blank');
            printWindow.document.write(reportHtml);
            printWindow.document.close();
            printWindow.print();
        }

        // حساب إجمالي الاستحقاق
        function calculateAccrualTotal() {
            const basicSalary = parseFloat(document.getElementById('accrualBasicSalary').value) || 0;
            const allowances = parseFloat(document.getElementById('accrualAllowances').value) || 0;
            const deductions = parseFloat(document.getElementById('accrualDeductions').value) || 0;
            const netAmount = basicSalary + allowances - deductions;

            document.getElementById('accrualNetAmount').value = netAmount;
        }

        // حفظ الاستحقاق
        function saveAccrual() {
            const accrualData = {
                employeeId: parseInt(document.getElementById('accrualEmployee').value),
                month: parseInt(document.getElementById('accrualMonthSelect').value),
                year: parseInt(document.getElementById('accrualYearInput').value),
                basicSalary: parseFloat(document.getElementById('accrualBasicSalary').value),
                allowances: parseFloat(document.getElementById('accrualAllowances').value) || 0,
                deductions: parseFloat(document.getElementById('accrualDeductions').value) || 0,
                netAmount: parseFloat(document.getElementById('accrualNetAmount').value),
                notes: document.getElementById('accrualNotes').value,
                createdAt: new Date().toISOString()
            };

            // التحقق من البيانات
            if (!accrualData.employeeId || !accrualData.month || !accrualData.year || !accrualData.basicSalary) {
                showMessage('يرجى ملء جميع الحقول المطلوبة', 'danger');
                return;
            }

            // التحقق من عدم وجود استحقاق مسبق لنفس الفترة
            const existingAccrual = accruals.find(a =>
                a.employeeId === accrualData.employeeId &&
                a.month === accrualData.month &&
                a.year === accrualData.year
            );

            if (existingAccrual) {
                showMessage('يوجد استحقاق مسبق لهذا الموظف في نفس الفترة', 'danger');
                return;
            }

            accrualData.id = nextAccrualId++;
            accruals.push(accrualData);
            saveAccruals();

            // تحديث رصيد الموظف
            const employee = employees.find(e => e.id === accrualData.employeeId);
            if (employee) {
                employee.balance = (employee.balance || 0) + accrualData.netAmount;
                saveEmployees();
            }

            displayAccruals();
            displayBalances();
            displayEmployees();

            bootstrap.Modal.getInstance(document.getElementById('accrualModal')).hide();
            showMessage('تم حفظ الاستحقاق بنجاح', 'success');
        }

        // حفظ السند
        function saveVoucher() {
            const voucherData = {
                employeeId: parseInt(document.getElementById('voucherEmployee').value),
                type: document.getElementById('voucherType').value,
                amount: parseFloat(document.getElementById('voucherAmount').value),
                date: document.getElementById('voucherDate').value,
                description: document.getElementById('voucherDescription').value
            };

            // التحقق من البيانات
            if (!voucherData.employeeId || !voucherData.type || !voucherData.amount || !voucherData.date || !voucherData.description) {
                showMessage('يرجى ملء جميع الحقول المطلوبة', 'danger');
                return;
            }

            // التحقق من الرصيد للسندات الصرف
            if (voucherData.type === 'payment') {
                const currentBalance = calculateEmployeeBalance(voucherData.employeeId);
                if (voucherData.amount > currentBalance) {
                    showMessage('المبلغ أكبر من رصيد الموظف المتاح', 'danger');
                    return;
                }
            }

            // إنشاء رقم السند
            const prefix = voucherData.type === 'payment' ? 'PAY' : 'REC';
            const count = employeeVouchers.filter(v => v.type === voucherData.type).length + 1;
            voucherData.number = `${prefix}${count.toString().padStart(3, '0')}`;
            voucherData.id = nextVoucherId++;

            employeeVouchers.push(voucherData);
            saveEmployeeVouchers();

            displayVouchers();
            displayBalances();
            displayEmployees();

            bootstrap.Modal.getInstance(document.getElementById('voucherModal')).hide();
            showMessage('تم حفظ السند بنجاح', 'success');
        }

        // حذف استحقاق
        function deleteAccrual(accrualId) {
            if (confirm('هل أنت متأكد من حذف هذا الاستحقاق؟')) {
                const accrualIndex = accruals.findIndex(a => a.id === accrualId);
                if (accrualIndex > -1) {
                    accruals.splice(accrualIndex, 1);
                    saveAccruals();
                    displayAccruals();
                    displayBalances();
                    displayEmployees();
                    showMessage('تم حذف الاستحقاق بنجاح', 'success');
                }
            }
        }

        // حذف سند
        function deleteVoucher(voucherId) {
            if (confirm('هل أنت متأكد من حذف هذا السند؟')) {
                const voucherIndex = employeeVouchers.findIndex(v => v.id === voucherId);
                if (voucherIndex > -1) {
                    employeeVouchers.splice(voucherIndex, 1);
                    saveEmployeeVouchers();
                    displayVouchers();
                    displayBalances();
                    displayEmployees();
                    showMessage('تم حذف السند بنجاح', 'success');
                }
            }
        }

        // طباعة استحقاق
        function printAccrual(accrualId) {
            const accrual = accruals.find(a => a.id === accrualId);
            const employee = employees.find(e => e.id === accrual.employeeId);

            if (accrual && employee) {
                const monthNames = ['', 'يناير', 'فبراير', 'مارس', 'أبريل', 'مايو', 'يونيو',
                                  'يوليو', 'أغسطس', 'سبتمبر', 'أكتوبر', 'نوفمبر', 'ديسمبر'];

                const printContent = `
                    <div style="font-family: Arial; direction: rtl; text-align: center;">
                        <h2>قسيمة راتب</h2>
                        <p>شهر ${monthNames[accrual.month]} ${accrual.year}</p>
                        <hr>
                        <div style="text-align: right; margin: 20px;">
                            <p><strong>اسم الموظف:</strong> ${employee.name}</p>
                            <p><strong>المنصب:</strong> ${employee.position}</p>
                            <p><strong>تاريخ التعيين:</strong> ${new Date(employee.hireDate).toLocaleDateString('ar-YE')}</p>
                        </div>
                        <table style="width: 100%; border-collapse: collapse; margin: 20px 0;">
                            <tr>
                                <td style="border: 1px solid #000; padding: 10px; background: #f0f0f0;"><strong>البيان</strong></td>
                                <td style="border: 1px solid #000; padding: 10px; background: #f0f0f0;"><strong>المبلغ</strong></td>
                            </tr>
                            <tr>
                                <td style="border: 1px solid #000; padding: 10px;">الراتب الأساسي</td>
                                <td style="border: 1px solid #000; padding: 10px;">${accrual.basicSalary.toLocaleString()} ر.ي</td>
                            </tr>
                            <tr>
                                <td style="border: 1px solid #000; padding: 10px;">البدلات</td>
                                <td style="border: 1px solid #000; padding: 10px;">${accrual.allowances.toLocaleString()} ر.ي</td>
                            </tr>
                            <tr>
                                <td style="border: 1px solid #000; padding: 10px;">الخصومات</td>
                                <td style="border: 1px solid #000; padding: 10px;">${accrual.deductions.toLocaleString()} ر.ي</td>
                            </tr>
                            <tr style="background: #f0f0f0;">
                                <td style="border: 1px solid #000; padding: 10px;"><strong>صافي الاستحقاق</strong></td>
                                <td style="border: 1px solid #000; padding: 10px;"><strong>${accrual.netAmount.toLocaleString()} ر.ي</strong></td>
                            </tr>
                        </table>
                        ${accrual.notes ? `<p><strong>ملاحظات:</strong> ${accrual.notes}</p>` : ''}
                        <p style="margin-top: 40px;">التاريخ: ${new Date(accrual.createdAt).toLocaleDateString('ar-YE')}</p>
                    </div>
                `;

                const printWindow = window.open('', '_blank');
                printWindow.document.write(printContent);
                printWindow.document.close();
                printWindow.print();
            }
        }

        // طباعة سند
        function printVoucher(voucherId) {
            const voucher = employeeVouchers.find(v => v.id === voucherId);
            const employee = employees.find(e => e.id === voucher.employeeId);

            if (voucher && employee) {
                const typeLabel = voucher.type === 'payment' ? 'سند صرف' : 'سند قبض';

                const printContent = `
                    <div style="font-family: Arial; direction: rtl; text-align: center;">
                        <h2>${typeLabel}</h2>
                        <p>رقم السند: ${voucher.number}</p>
                        <hr>
                        <div style="text-align: right; margin: 20px;">
                            <p><strong>التاريخ:</strong> ${new Date(voucher.date).toLocaleDateString('ar-YE')}</p>
                            <p><strong>اسم الموظف:</strong> ${employee.name}</p>
                            <p><strong>المنصب:</strong> ${employee.position}</p>
                            <p><strong>المبلغ:</strong> ${voucher.amount.toLocaleString()} ر.ي</p>
                            <p><strong>البيان:</strong> ${voucher.description}</p>
                        </div>
                        <div style="margin-top: 60px; display: flex; justify-content: space-between;">
                            <div>
                                <p>توقيع المستلم</p>
                                <p>_______________</p>
                            </div>
                            <div>
                                <p>توقيع المحاسب</p>
                                <p>_______________</p>
                            </div>
                        </div>
                    </div>
                `;

                const printWindow = window.open('', '_blank');
                printWindow.document.write(printContent);
                printWindow.document.close();
                printWindow.print();
            }
        }

        // تحديث الراتب الأساسي عند اختيار الموظف
        document.getElementById('accrualEmployee').addEventListener('change', function() {
            const employeeId = parseInt(this.value);
            if (employeeId) {
                const employee = employees.find(e => e.id === employeeId);
                if (employee) {
                    document.getElementById('accrualBasicSalary').value = employee.salary;
                    calculateAccrualTotal();
                }
            }
        });

        console.log('✅ تم تحميل نظام إدارة الموظفين المحاسبي بنجاح');
    </script>

    <style>
        .sidebar {
            height: 100vh;
            position: fixed;
            width: 25%;
        }
        .nav-link.active {
            background-color: rgba(255,255,255,0.1);
            border-radius: 5px;
        }
        .table th {
            background-color: #343a40;
            color: white;
        }
    </style>
</body>
</html>
