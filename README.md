# 🍞 نظام إدارة مخبوزات ANW
## ANW Bakery Management System

نظام إدارة شامل ومتكامل للمخابز والمطاعم مع دعم كامل لقاعدة البيانات SQLite والريال اليمني ووحدات القياس المتقدمة.

---

## 🚀 **التشغيل السريع**

### **الطريقة المفضلة:**
1. **انقر نقرة مزدوجة** على `test_app.cmd`
2. **انتظر** حتى يكتمل البناء والتشغيل
3. **افتح المتصفح** على: http://localhost:5000
4. **استخدم بيانات الدخول:**
   - اسم المستخدم: `admin`
   - كلمة المرور: `admin123`

### **أو يدوياً:**
```bash
dotnet restore
dotnet build
dotnet run
```

## 🌟 المميزات الرئيسية

### 💰 نظام العملة الموحد
- **العملة الوحيدة**: الريال اليمني (ر.ي) حصرياً
- دعم 3 خانات عشرية للدقة في الحسابات
- تنسيق الأرقام باللغة العربية
- جميع التقارير والحسابات بالريال اليمني

### ⚖️ نظام وحدات القياس المتقدم
- **وحدات هرمية**: كبرى/متوسطة/صغرى
- تحويل تلقائي بين الوحدات
- حساب الأسعار حسب الوحدة المختارة
- تتبع دقيق للمخزون بجميع الوحدات

**مثال عملي:**
```
كيس دقيق 50 كجم:
- الوحدة الكبرى: 1 كيس
- الوحدة المتوسطة: 50 كيلوجرام  
- الوحدة الصغرى: 50,000 جرام
```

### 🧾 نظام الفوترة الموحد
- **مبدأ الفوترة الموحدة**: جميع الفواتير تدعم البيع والشراء
- العميل يمكن أن يكون مورد والعكس صحيح
- فواتير مبيعات ومشتريات للعملاء والموردين
- **البيع السريع**: نقدي فقط لأي طرف
- طباعة حرارية وعادية

### 👥 نظام الأطراف الموحد
- إدارة موحدة للعملاء والموردين
- نفس الشخص يمكن أن يكون عميل ومورد
- إدارة الأرصدة وحدود الائتمان
- كشوف حساب تفصيلية

### 📦 إدارة المخزون المتقدمة
- تتبع المخزون بجميع وحدات القياس
- حركة المخزون التفصيلية
- تحويل تلقائي بين الوحدات
- تنبيهات المخزون المنخفض
- جرد المخزون الدوري

### 👨‍🍳 نظام الوصفات والإنتاج
- وصفات الإنتاج مع حساب التكلفة بدقة
- تحويل وحدات القياس في الوصفات
- تتبع المواد المستخدمة
- حساب تكلفة الإنتاج بالريال اليمني

### 💼 النظام المحاسبي
- الشجرة المحاسبية الكاملة
- القيود اليومية التلقائية
- سندات القبض والصرف
- الميزانية العمومية وقائمة الدخل

## 🛠️ التقنيات المستخدمة

### Backend
- **ASP.NET Core 8.0** - إطار العمل الرئيسي
- **Entity Framework Core** - ORM لقاعدة البيانات
- **SQLite** - قاعدة البيانات المحلية
- **JWT Authentication** - نظام المصادقة
- **BCrypt** - تشفير كلمات المرور

### Frontend
- **HTML5, CSS3, JavaScript** - تقنيات الويب الأساسية
- **Bootstrap 5 RTL** - إطار العمل للتصميم
- **Font Awesome** - الأيقونات

### قاعدة البيانات
- **SQLite** - قاعدة بيانات محلية سهلة الاستخدام
- **Entity Framework Core** - إدارة قاعدة البيانات
- **Code First** - إنشاء قاعدة البيانات من الكود
- **Migrations** - تحديث هيكل قاعدة البيانات
- **Seed Data** - بيانات أولية تلقائية

## 📋 متطلبات النظام

- Windows 10/11 أو أي نظام يدعم .NET
- .NET 8.0 SDK
- 2GB RAM (الحد الأدنى)
- 1GB مساحة تخزين

## 🚀 التثبيت والتشغيل

### 1. تشغيل سريع
```cmd
test_app.cmd
```

### 2. تشغيل يدوي
```bash
dotnet restore
dotnet build
dotnet run
```

### 3. الوصول للنظام
- افتح المتصفح واذهب إلى: `http://localhost:5000`
- بيانات المدير الافتراضية:
  - اسم المستخدم: `admin`
  - كلمة المرور: `admin123`

### 4. قاعدة البيانات
- **تلقائية**: يتم إنشاء ملف `ANWBakery.db` تلقائياً
- **البيانات الأولية**: مستخدم افتراضي + حسابات أساسية
- **لا حاجة لإعداد**: لا حاجة لتثبيت SQL Server

## 👤 أدوار المستخدمين

| الدور | الوصف | الصلاحيات |
|-------|--------|-----------|
| **مدير النظام** | إدارة كاملة للنظام | جميع الصلاحيات |
| **مدير** | إدارة العمليات اليومية | معظم الصلاحيات |
| **محاسب** | إدارة الحسابات والتقارير | الحسابات والتقارير |
| **أمين صندوق** | إدارة المبيعات والمدفوعات | المبيعات والمدفوعات |
| **موظف مبيعات** | تسجيل المبيعات | المبيعات فقط |
| **موظف مخزن** | إدارة المخزون | المخزون فقط |

## 📊 الوحدات الرئيسية

### 1. إدارة المستخدمين والصلاحيات ✅
- إنشاء وإدارة المستخدمين
- تحديد الأدوار والصلاحيات
- نظام مصادقة آمن
- تفعيل/إلغاء تفعيل المستخدمين
- إعادة تعيين كلمات المرور
- إحصائيات المستخدمين

### 2. إدارة وحدات القياس
- إنشاء وحدات القياس الهرمية
- تحويل تلقائي بين الوحدات
- حساب الأسعار حسب الوحدة

### 3. إدارة الأطراف
- إدارة العملاء والموردين
- تتبع الأرصدة والمعاملات
- كشوف الحساب التفصيلية

### 4. إدارة المنتجات
- تصنيف المنتجات والخامات
- ربط المنتجات بوحدات القياس
- إدارة الأسعار والباركود

### 5. نظام الفوترة
- فواتير مبيعات ومشتريات
- البيع السريع النقدي
- مرتجعات المبيعات والمشتريات

### 6. إدارة المخزون
- تتبع المخزون بالوحدات المختلفة
- حركة المخزون التفصيلية
- تحويلات المخزون بين المخازن

### 7. الوصفات والإنتاج
- وصفات الإنتاج مع التكلفة
- أوامر الإنتاج وتتبع التنفيذ
- حساب تكلفة المنتجات

### 8. التقارير والتحليلات
- تقارير المبيعات والمشتريات
- تقارير المخزون والحركة
- التقارير المالية والمحاسبية

## 🔒 الأمان والحماية

- **JWT Authentication** للمصادقة الآمنة
- **تشفير كلمات المرور** باستخدام BCrypt
- **صلاحيات متدرجة** حسب الدور
- **تسجيل العمليات** لجميع الأنشطة
- **حماية من SQL Injection** باستخدام Entity Framework

## 🎯 حالة التطوير الحالية

### ✅ **تم إصلاحه وتحديثه**
- **إصلاح تضارب النماذج**: حذف التعريفات المكررة
- **تحويل إلى SQLite**: قاعدة بيانات محلية سهلة الاستخدام
- **ربط صفحة الحسابات**: اتصال كامل بقاعدة البيانات
- **نظام مصادقة مبسط**: تسجيل دخول وإدارة جلسات
- **البيانات الأولية**: مستخدم افتراضي وحسابات أساسية

### ✅ **الوحدات المكتملة**
- **AccountsController** - إدارة شجرة الحسابات مع قاعدة البيانات
- **UsersController** - إدارة المستخدمين والصلاحيات
- **AuthController** - نظام المصادقة والتسجيل
- **ItemsController** - إدارة المنتجات والخامات
- **PartiesController** - إدارة العملاء والموردين
- **InvoicesController** - نظام الفوترة
- **InventoryController** - إدارة المخزون

### 🔄 **قيد الربط بقاعدة البيانات**
- **صفحة المستخدمين**: ربط الواجهة بقاعدة البيانات
- **صفحة العملاء والموردين**: تفعيل الوظائف الكاملة
- **صفحة المنتجات**: ربط إدارة المخزون
- **صفحة الفواتير**: تفعيل نظام الفوترة الكامل

### 📋 **مخطط للتطوير**
- **ProductionController** - إدارة الإنتاج والوصفات
- **BanksController** - إدارة البنوك والصناديق
- **EmployeesController** - إدارة الموظفين والرواتب
- **ReportsController** - التقارير والتحليلات

### 🚀 **APIs المتاحة حالياً (35+ endpoints)**

#### **إدارة المنتجات والخامات**
```
GET    /api/items              - جلب جميع المنتجات
POST   /api/items              - إنشاء منتج جديد
GET    /api/items/{id}         - جلب منتج بالمعرف
PUT    /api/items/{id}         - تحديث منتج
DELETE /api/items/{id}         - حذف منتج
GET    /api/items/barcode/{barcode} - البحث بالباركود
GET    /api/items/active       - جلب المنتجات النشطة
```

#### **إدارة الأطراف (العملاء والموردين)**
```
GET    /api/parties            - جلب جميع الأطراف
POST   /api/parties            - إنشاء طرف جديد
GET    /api/parties/{id}       - جلب طرف بالمعرف
PUT    /api/parties/{id}       - تحديث طرف
DELETE /api/parties/{id}       - حذف طرف
```

#### **إدارة الفوترة الموحدة**
```
GET    /api/invoices           - جلب جميع الفواتير
POST   /api/invoices           - إنشاء فاتورة جديدة
GET    /api/invoices/{id}      - جلب فاتورة بالمعرف
```

#### **إدارة المخزون**
```
GET    /api/inventory          - جلب أرصدة المخزون
GET    /api/inventory/item/{id} - جلب رصيد صنف معين
GET    /api/inventory/movements - جلب حركة المخزون
POST   /api/inventory/adjustment - إجراء تسوية مخزون
```

#### **إدارة الإنتاج والوصفات**
```
GET    /api/production/recipes - جلب جميع الوصفات
POST   /api/production/recipes - إنشاء وصفة جديدة
GET    /api/production/recipes/{id} - جلب وصفة بالمعرف
POST   /api/production/recipes/{id}/items - إضافة مادة خام للوصفة
POST   /api/production/recipes/{id}/calculate-cost - حساب تكلفة الوصفة
GET    /api/production/orders  - جلب أوامر الإنتاج
```

#### **دليل الحسابات والمحاسبة**
```
GET    /api/accounts           - جلب دليل الحسابات
POST   /api/accounts           - إنشاء حساب جديد
GET    /api/accounts/{id}      - جلب حساب بالمعرف
GET    /api/accounts/{id}/statement - كشف حساب
GET    /api/accounts/journal-entries - جلب القيود المحاسبية
POST   /api/accounts/journal-entries - إنشاء قيد محاسبي
```

#### **إدارة البنوك والصناديق**
```
GET    /api/banks              - جلب جميع البنوك
POST   /api/banks              - إنشاء بنك جديد
GET    /api/banks/accounts     - جلب الحسابات المصرفية
POST   /api/banks/accounts     - إنشاء حساب مصرفي
GET    /api/banks/cash-registers - جلب الصناديق النقدية
POST   /api/banks/cash-registers - إنشاء صندوق نقدي
POST   /api/banks/transfer     - تحويل أموال بين الحسابات
```

#### **إدارة المستخدمين والصلاحيات**
```
GET    /api/users              - جلب جميع المستخدمين
POST   /api/users              - إنشاء مستخدم جديد
GET    /api/users/{id}         - جلب مستخدم بالمعرف
PUT    /api/users/{id}         - تحديث مستخدم
DELETE /api/users/{id}         - حذف مستخدم
POST   /api/users/{id}/toggle-status - تفعيل/إلغاء تفعيل
POST   /api/users/{id}/reset-password - إعادة تعيين كلمة المرور
GET    /api/users/statistics   - إحصائيات المستخدمين
```

#### **الأنظمة الأساسية**
```
GET    /api/units              - إدارة وحدات القياس
POST   /api/auth/login         - تسجيل الدخول
POST   /api/auth/validate      - التحقق من صحة الرمز
```

## 📈 المميزات المستقبلية

- [ ] تطبيق الجوال (Android/iOS)
- [ ] تكامل مع أنظمة الدفع الإلكتروني
- [ ] تقارير ذكية باستخدام AI
- [ ] نظام إشعارات متقدم
- [ ] تكامل مع أنظمة المحاسبة الخارجية

## 🤝 المساهمة

نرحب بالمساهمات! يرجى:
1. عمل Fork للمشروع
2. إنشاء فرع جديد للميزة
3. تطبيق التغييرات
4. إرسال Pull Request

## 📞 الدعم والتواصل

- **البريد الإلكتروني**: <EMAIL>
- **الهاتف**: +967-1-234567
- **العنوان**: اليمن - صنعاء

## 📄 الترخيص

هذا المشروع مرخص تحت رخصة MIT - راجع ملف [LICENSE](LICENSE) للتفاصيل.

---

**تم تطوير هذا النظام خصيصاً لإدارة المخابز والمطاعم في اليمن مع مراعاة البيئة المحلية والعملة الرسمية.**
