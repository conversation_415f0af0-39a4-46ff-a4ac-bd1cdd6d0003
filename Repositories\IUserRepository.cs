using ANWBakerySystem.Models;

namespace ANWBakerySystem.Repositories
{
    /// <summary>
    /// واجهة مستودع المستخدمين
    /// User Repository Interface
    /// </summary>
    public interface IUserRepository
    {
        Task<IEnumerable<User>> GetAllUsersAsync();
        Task<User?> GetUserByIdAsync(int userId);
        Task<User?> GetUserByUsernameAsync(string username);
        Task<User?> GetUserByEmailAsync(string email);
        Task<User> CreateUserAsync(User user);
        Task<User> UpdateUserAsync(User user);
        Task<bool> DeleteUserAsync(int userId);
        Task<bool> UserExistsAsync(int userId);
        Task<bool> UsernameExistsAsync(string username);
        Task<bool> EmailExistsAsync(string email);
        Task<bool> ValidateUserCredentialsAsync(string username, string password);
        Task<User?> AuthenticateUserAsync(string username, string password);
        Task<bool> UpdatePasswordAsync(int userId, string newPasswordHash);
        Task<bool> LockUserAsync(int userId);
        Task<bool> UnlockUserAsync(int userId);
        Task<bool> UpdateLastLoginAsync(int userId);
        Task<IEnumerable<User>> GetUsersByRoleAsync(UserRole role);
        Task<IEnumerable<UserPermission>> GetUserPermissionsAsync(int userId);
        Task<bool> GrantPermissionAsync(int userId, int permissionId, int grantedBy);
        Task<bool> RevokePermissionAsync(int userId, int permissionId);
        Task<bool> HasPermissionAsync(int userId, string permissionName);
    }
}
