using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace ANWBakerySystem.Models
{
    /// <summary>
    /// مكونات الوصفة
    /// Recipe Items
    /// </summary>
    public class RecipeItem
    {
        [Key]
        public int RecipeItemId { get; set; }

        [Required]
        public int RecipeId { get; set; }

        [Required]
        public int ItemId { get; set; }

        [Required]
        [Column(TypeName = "decimal(18,6)")]
        public decimal Quantity { get; set; }

        [Required]
        public int UnitId { get; set; }

        [Column(TypeName = "decimal(18,3)")]
        public decimal UnitCost { get; set; } = 0;

        [Column(TypeName = "decimal(18,3)")]
        public decimal TotalCost { get; set; } = 0;

        [StringLength(500)]
        public string? Notes { get; set; }

        public int SortOrder { get; set; } = 1;

        // Navigation properties
        public virtual Recipe Recipe { get; set; } = null!;
        public virtual Item Item { get; set; } = null!;
        public virtual Unit Unit { get; set; } = null!;
    }
}
