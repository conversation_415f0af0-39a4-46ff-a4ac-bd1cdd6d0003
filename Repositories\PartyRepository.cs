using Microsoft.EntityFrameworkCore;
using ANWBakerySystem.Data;
using ANWBakerySystem.Models;

namespace ANWBakerySystem.Repositories
{
    /// <summary>
    /// تطبيق مستودع الأطراف الموحد (عملاء وموردين)
    /// Unified Party Repository Implementation (Customers and Suppliers)
    /// </summary>
    public class PartyRepository : IPartyRepository
    {
        private readonly ANWBakeryDbContext _context;

        public PartyRepository(ANWBakeryDbContext context)
        {
            _context = context;
        }

        public async Task<IEnumerable<Party>> GetAllPartiesAsync()
        {
            return await _context.Parties
                .Include(p => p.Creator)
                .Include(p => p.Updater)
                .OrderBy(p => p.PartyName)
                .ToListAsync();
        }

        public async Task<IEnumerable<Party>> GetActivePartiesAsync()
        {
            return await _context.Parties
                .Where(p => p.IsActive)
                .OrderBy(p => p.PartyName)
                .ToListAsync();
        }

        public async Task<IEnumerable<Party>> GetCustomersAsync()
        {
            return await _context.Parties
                .Where(p => p.IsCustomer && p.IsActive)
                .OrderBy(p => p.PartyName)
                .ToListAsync();
        }

        public async Task<IEnumerable<Party>> GetSuppliersAsync()
        {
            return await _context.Parties
                .Where(p => p.IsSupplier && p.IsActive)
                .OrderBy(p => p.PartyName)
                .ToListAsync();
        }

        public async Task<IEnumerable<Party>> GetEmployeesAsync()
        {
            return await _context.Parties
                .Where(p => p.IsEmployee && p.IsActive)
                .OrderBy(p => p.PartyName)
                .ToListAsync();
        }

        public async Task<Party?> GetPartyByIdAsync(int partyId)
        {
            return await _context.Parties
                .Include(p => p.Creator)
                .Include(p => p.Updater)
                .Include(p => p.Transactions)
                .FirstOrDefaultAsync(p => p.PartyId == partyId);
        }

        public async Task<Party?> GetPartyByCodeAsync(string partyCode)
        {
            return await _context.Parties
                .FirstOrDefaultAsync(p => p.PartyCode == partyCode);
        }

        public async Task<Party> CreatePartyAsync(Party party)
        {
            _context.Parties.Add(party);
            await _context.SaveChangesAsync();
            return party;
        }

        public async Task<Party> UpdatePartyAsync(Party party)
        {
            party.UpdatedAt = DateTime.Now;
            _context.Parties.Update(party);
            await _context.SaveChangesAsync();
            return party;
        }

        public async Task<bool> DeletePartyAsync(int partyId)
        {
            var party = await _context.Parties.FindAsync(partyId);
            if (party == null) return false;

            party.IsActive = false;
            party.UpdatedAt = DateTime.Now;
            await _context.SaveChangesAsync();
            return true;
        }

        public async Task<bool> PartyExistsAsync(int partyId)
        {
            return await _context.Parties.AnyAsync(p => p.PartyId == partyId);
        }

        public async Task<bool> PartyCodeExistsAsync(string partyCode)
        {
            return await _context.Parties.AnyAsync(p => p.PartyCode == partyCode);
        }

        #region Balance Management

        public async Task<decimal> GetPartyBalanceAsync(int partyId)
        {
            var party = await _context.Parties.FindAsync(partyId);
            return party?.CurrentBalance ?? 0;
        }

        public async Task<bool> UpdatePartyBalanceAsync(int partyId, decimal amount, bool isDebit)
        {
            var party = await _context.Parties.FindAsync(partyId);
            if (party == null) return false;

            if (isDebit)
                party.CurrentBalance += amount;
            else
                party.CurrentBalance -= amount;

            party.UpdatedAt = DateTime.Now;
            await _context.SaveChangesAsync();
            return true;
        }

        public async Task<bool> CheckCreditLimitAsync(int partyId, decimal amount)
        {
            var party = await _context.Parties.FindAsync(partyId);
            if (party == null) return false;

            var newBalance = party.CurrentBalance + amount;
            return newBalance <= party.CreditLimit;
        }

        #endregion

        #region Party Transactions

        public async Task<PartyTransaction> CreateTransactionAsync(PartyTransaction transaction)
        {
            _context.PartyTransactions.Add(transaction);
            await _context.SaveChangesAsync();
            return transaction;
        }

        public async Task<IEnumerable<PartyTransaction>> GetPartyTransactionsAsync(int partyId)
        {
            return await _context.PartyTransactions
                .Include(pt => pt.Invoice)
                .Include(pt => pt.Creator)
                .Where(pt => pt.PartyId == partyId)
                .OrderByDescending(pt => pt.TransactionDate)
                .ToListAsync();
        }

        public async Task<IEnumerable<PartyTransaction>> GetPartyTransactionsAsync(int partyId, DateTime fromDate, DateTime toDate)
        {
            return await _context.PartyTransactions
                .Include(pt => pt.Invoice)
                .Include(pt => pt.Creator)
                .Where(pt => pt.PartyId == partyId && 
                            pt.TransactionDate >= fromDate && 
                            pt.TransactionDate <= toDate)
                .OrderByDescending(pt => pt.TransactionDate)
                .ToListAsync();
        }

        public async Task<decimal> GetPartyBalanceAtDateAsync(int partyId, DateTime date)
        {
            var transactions = await _context.PartyTransactions
                .Where(pt => pt.PartyId == partyId && pt.TransactionDate <= date)
                .OrderBy(pt => pt.TransactionDate)
                .ToListAsync();

            decimal balance = 0;
            foreach (var transaction in transactions)
            {
                balance += transaction.DebitAmount - transaction.CreditAmount;
            }

            return balance;
        }

        #endregion

        #region Party Reports

        public async Task<IEnumerable<Party>> GetPartiesWithBalanceAsync()
        {
            return await _context.Parties
                .Where(p => p.IsActive && p.CurrentBalance != 0)
                .OrderByDescending(p => p.CurrentBalance)
                .ToListAsync();
        }

        public async Task<IEnumerable<Party>> GetPartiesOverCreditLimitAsync()
        {
            return await _context.Parties
                .Where(p => p.IsActive && p.CurrentBalance > p.CreditLimit)
                .OrderByDescending(p => p.CurrentBalance - p.CreditLimit)
                .ToListAsync();
        }

        public async Task<IEnumerable<Party>> GetPartiesWithOverdueInvoicesAsync()
        {
            var today = DateTime.Today;
            return await _context.Parties
                .Where(p => p.IsActive && 
                           p.CustomerInvoices.Any(i => i.DueDate < today && i.RemainingAmount > 0))
                .ToListAsync();
        }

        public async Task<decimal> GetTotalCustomersBalanceAsync()
        {
            return await _context.Parties
                .Where(p => p.IsCustomer && p.IsActive)
                .SumAsync(p => p.CurrentBalance);
        }

        public async Task<decimal> GetTotalSuppliersBalanceAsync()
        {
            return await _context.Parties
                .Where(p => p.IsSupplier && p.IsActive)
                .SumAsync(p => p.CurrentBalance);
        }

        #endregion
    }
}
