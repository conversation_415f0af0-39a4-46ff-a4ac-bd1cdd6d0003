@echo off
setlocal enabledelayedexpansion

echo ========================================
echo    نظام إدارة مخبوزات ANW
echo ========================================

REM التحقق من .NET
dotnet --version >nul 2>&1
if errorlevel 1 (
    echo خطأ: .NET غير مثبت
    pause
    exit /b 1
)

REM الانتقال للمجلد
cd /d "%~dp0"

REM التحقق من المشروع
if not exist "ANWBakerySystem.csproj" (
    echo خطأ: ملف المشروع غير موجود
    pause
    exit /b 1
)

echo جاري بناء المشروع...
dotnet build --configuration Release

if errorlevel 1 (
    echo فشل البناء
    pause
    exit /b 1
)

echo تشغيل النظام...
echo الرابط: http://localhost:5000
echo المستخدم: admin
echo كلمة المرور: admin123
echo.

start http://localhost:5000

dotnet run --configuration Release

pause
