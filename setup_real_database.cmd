@echo off
chcp 65001 > nul
echo ========================================
echo   إنشاء قاعدة البيانات الحقيقية
echo   ANW Bakery Real Database Setup
echo ========================================
echo.

echo 🔍 فحص متطلبات النظام...
echo.

REM فحص وجود .NET
dotnet --version > nul 2>&1
if %ERRORLEVEL% NEQ 0 (
    echo ❌ .NET غير مثبت على النظام
    echo يرجى تثبيت .NET 8.0 أو أحدث
    pause
    exit /b 1
)

echo ✅ .NET مثبت ومتاح

REM فحص وجود SQL Server LocalDB
sqlcmd -S "(localdb)\mssqllocaldb" -Q "SELECT @@VERSION" > nul 2>&1
if %ERRORLEVEL% NEQ 0 (
    echo ⚠️  SQL Server LocalDB غير متاح
    echo 🔧 محاولة استخدام SQL Server Express...
    
    sqlcmd -S ".\SQLEXPRESS" -Q "SELECT @@VERSION" > nul 2>&1
    if %ERRORLEVEL% NEQ 0 (
        echo ❌ SQL Server غير متاح على النظام
        echo.
        echo 📥 يرجى تثبيت أحد الخيارات التالية:
        echo    1. SQL Server Express
        echo    2. SQL Server LocalDB
        echo    3. SQL Server Developer Edition
        echo.
        echo 🌐 روابط التحميل:
        echo    SQL Server Express: https://www.microsoft.com/sql-server/sql-server-downloads
        echo    SQL Server LocalDB: https://docs.microsoft.com/sql/database-engine/configure-windows/sql-server-express-localdb
        echo.
        pause
        exit /b 1
    ) else (
        echo ✅ SQL Server Express متاح
        set "CONNECTION_TYPE=SQLEXPRESS"
    )
) else (
    echo ✅ SQL Server LocalDB متاح
    set "CONNECTION_TYPE=LOCALDB"
)

echo.
echo 🧹 تنظيف المشروع...
dotnet clean > nul 2>&1

echo.
echo 📦 استعادة الحزم...
dotnet restore
if %ERRORLEVEL% NEQ 0 (
    echo ❌ فشل في استعادة الحزم
    pause
    exit /b 1
)

echo.
echo 🔧 بناء المشروع...
dotnet build
if %ERRORLEVEL% NEQ 0 (
    echo ❌ فشل في بناء المشروع
    pause
    exit /b 1
)

echo.
echo 🗄️ فحص وجود قاعدة البيانات...

REM حذف Migrations السابقة إذا كانت موجودة
if exist "Migrations" (
    echo 🧹 حذف Migrations السابقة...
    rmdir /s /q "Migrations"
)

echo.
echo 🔧 إنشاء Migration جديد...
dotnet ef migrations add InitialCreate_RealDB
if %ERRORLEVEL% NEQ 0 (
    echo ❌ فشل في إنشاء Migration
    echo.
    echo 🔧 محاولة تثبيت أدوات Entity Framework...
    dotnet tool install --global dotnet-ef
    echo.
    echo 🔄 إعادة المحاولة...
    dotnet ef migrations add InitialCreate_RealDB
    if %ERRORLEVEL% NEQ 0 (
        echo ❌ فشل في إنشاء Migration مرة أخرى
        pause
        exit /b 1
    )
)

echo.
echo 🗄️ إنشاء قاعدة البيانات...
dotnet ef database update
if %ERRORLEVEL% NEQ 0 (
    echo ❌ فشل في إنشاء قاعدة البيانات
    echo.
    echo 🔧 محاولة حذف قاعدة البيانات الموجودة وإعادة الإنشاء...
    dotnet ef database drop --force
    dotnet ef database update
    if %ERRORLEVEL% NEQ 0 (
        echo ❌ فشل في إنشاء قاعدة البيانات مرة أخرى
        pause
        exit /b 1
    )
)

echo.
echo ✅ تم إنشاء قاعدة البيانات بنجاح!
echo.
echo 📊 معلومات قاعدة البيانات:
echo    📁 الاسم: ANWBakeryDB_Real
if "%CONNECTION_TYPE%"=="LOCALDB" (
    echo    🔗 الخادم: (localdb)\mssqllocaldb
) else (
    echo    🔗 الخادم: .\SQLEXPRESS
)
echo    💾 النوع: SQL Server Database
echo    🔒 الأمان: Windows Authentication
echo.

echo 🚀 تشغيل التطبيق...
echo.
echo 🌐 سيتم فتح المتصفح على: http://localhost:5000
echo 🔐 بيانات الدخول الافتراضية:
echo    👤 اسم المستخدم: admin
echo    🔑 كلمة المرور: admin123
echo.

REM تشغيل التطبيق
start "" "http://localhost:5000"
dotnet run

echo.
echo 🎉 تم إعداد قاعدة البيانات الحقيقية بنجاح!
echo.
pause
