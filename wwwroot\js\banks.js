// إدارة البنوك والصناديق
let cashRegisters = [];
let bankAccounts = [];
let transactions = [];
let linkedAccounts = [];

// تحميل الصفحة
document.addEventListener('DOMContentLoaded', function() {
    console.log('تحميل صفحة البنوك والصناديق...');
    loadLinkedAccounts();
    loadCashRegisters();
    loadBankAccounts();
    loadTransactions();
});

// تحميل الحسابات المرتبطة
function loadLinkedAccounts() {
    // محاكاة تحميل الحسابات من شجرة الحسابات
    linkedAccounts = [
        { accountId: 4, accountName: 'الصندوق الرئيسي', accountCode: '1111' },
        { accountId: 5, accountName: 'صندوق المبيعات', accountCode: '1112' },
        { accountId: 6, accountName: 'البنك الأهلي - حساب جاري', accountCode: '1113' },
        { accountId: 7, accountName: 'بنك سبأ - حساب توفير', accountCode: '1114' }
    ];
    
    populateAccountDropdowns();
}

// ملء قوائم الحسابات
function populateAccountDropdowns() {
    const dropdowns = ['cashAccount', 'bankAccount', 'fromAccount', 'toAccount'];
    
    dropdowns.forEach(dropdownId => {
        const dropdown = document.getElementById(dropdownId);
        if (dropdown) {
            dropdown.innerHTML = '<option value="">-- اختر الحساب --</option>';
            linkedAccounts.forEach(account => {
                const option = document.createElement('option');
                option.value = account.accountId;
                option.textContent = `${account.accountCode} - ${account.accountName}`;
                dropdown.appendChild(option);
            });
        }
    });
}

// تحميل الصناديق النقدية
function loadCashRegisters() {
    console.log('تحميل الصناديق النقدية...');
    
    // بيانات تجريبية
    cashRegisters = [
        {
            cashId: 1,
            cashName: 'الصندوق الرئيسي',
            description: 'الصندوق الرئيسي للمخبز',
            currentBalance: 25000.000,
            linkedAccountId: 4,
            isActive: true,
            createdDate: '2024-01-01'
        },
        {
            cashId: 2,
            cashName: 'صندوق المبيعات',
            description: 'صندوق خاص بالمبيعات اليومية',
            currentBalance: 15000.000,
            linkedAccountId: 5,
            isActive: true,
            createdDate: '2024-01-01'
        }
    ];
    
    displayCashRegisters();
}

// عرض الصناديق النقدية
function displayCashRegisters() {
    const container = document.getElementById('cashRegistersList');
    
    if (cashRegisters.length === 0) {
        container.innerHTML = `
            <div class="text-center text-muted">
                <i class="fas fa-cash-register fa-3x mb-3"></i>
                <p>لا توجد صناديق نقدية</p>
            </div>
        `;
        return;
    }
    
    container.innerHTML = cashRegisters.map(cash => `
        <div class="card mb-3 ${cash.isActive ? '' : 'opacity-50'}">
            <div class="card-body">
                <div class="d-flex justify-content-between align-items-start">
                    <div>
                        <h6 class="card-title">
                            <i class="fas fa-cash-register text-success"></i>
                            ${cash.cashName}
                        </h6>
                        <p class="card-text text-muted small">${cash.description}</p>
                        <div class="d-flex align-items-center">
                            <span class="badge bg-success me-2">
                                ${formatCurrency(cash.currentBalance)}
                            </span>
                            <small class="text-muted">
                                ${cash.isActive ? 'نشط' : 'غير نشط'}
                            </small>
                        </div>
                    </div>
                    <div class="btn-group btn-group-sm">
                        <button class="btn btn-outline-primary" onclick="editCashRegister(${cash.cashId})" title="تعديل">
                            <i class="fas fa-edit"></i>
                        </button>
                        <button class="btn btn-outline-info" onclick="viewCashStatement(${cash.cashId})" title="كشف الحساب">
                            <i class="fas fa-list-alt"></i>
                        </button>
                    </div>
                </div>
            </div>
        </div>
    `).join('');
}

// تحميل الحسابات المصرفية
function loadBankAccounts() {
    console.log('تحميل الحسابات المصرفية...');
    
    // بيانات تجريبية
    bankAccounts = [
        {
            bankId: 1,
            bankName: 'البنك الأهلي اليمني',
            accountNumber: '**********',
            accountType: 'current',
            currentBalance: 85000.000,
            branchName: 'فرع الزبيري',
            iban: 'YE********************',
            linkedAccountId: 6,
            isActive: true,
            createdDate: '2024-01-01'
        },
        {
            bankId: 2,
            bankName: 'بنك سبأ الإسلامي',
            accountNumber: '**********',
            accountType: 'savings',
            currentBalance: 45000.000,
            branchName: 'فرع الحصبة',
            iban: 'YE********************',
            linkedAccountId: 7,
            isActive: true,
            createdDate: '2024-01-01'
        }
    ];
    
    displayBankAccounts();
}

// عرض الحسابات المصرفية
function displayBankAccounts() {
    const container = document.getElementById('bankAccountsList');
    
    if (bankAccounts.length === 0) {
        container.innerHTML = `
            <div class="text-center text-muted">
                <i class="fas fa-university fa-3x mb-3"></i>
                <p>لا توجد حسابات مصرفية</p>
            </div>
        `;
        return;
    }
    
    container.innerHTML = bankAccounts.map(bank => `
        <div class="card mb-3 ${bank.isActive ? '' : 'opacity-50'}">
            <div class="card-body">
                <div class="d-flex justify-content-between align-items-start">
                    <div>
                        <h6 class="card-title">
                            <i class="fas fa-university text-primary"></i>
                            ${bank.bankName}
                        </h6>
                        <p class="card-text">
                            <small class="text-muted">
                                رقم الحساب: ${bank.accountNumber}<br>
                                ${getAccountTypeText(bank.accountType)} - ${bank.branchName}
                            </small>
                        </p>
                        <div class="d-flex align-items-center">
                            <span class="badge bg-primary me-2">
                                ${formatCurrency(bank.currentBalance)}
                            </span>
                            <small class="text-muted">
                                ${bank.isActive ? 'نشط' : 'غير نشط'}
                            </small>
                        </div>
                    </div>
                    <div class="btn-group btn-group-sm">
                        <button class="btn btn-outline-primary" onclick="editBankAccount(${bank.bankId})" title="تعديل">
                            <i class="fas fa-edit"></i>
                        </button>
                        <button class="btn btn-outline-info" onclick="viewBankStatement(${bank.bankId})" title="كشف الحساب">
                            <i class="fas fa-list-alt"></i>
                        </button>
                    </div>
                </div>
            </div>
        </div>
    `).join('');
}

// تحميل المعاملات
function loadTransactions() {
    console.log('تحميل المعاملات المالية...');
    
    // بيانات تجريبية
    transactions = [
        {
            transactionId: 1,
            date: '2024-01-15',
            type: 'transfer',
            fromAccount: 'الصندوق الرئيسي',
            toAccount: 'البنك الأهلي',
            amount: 10000.000,
            description: 'إيداع في البنك',
            createdBy: 'المدير'
        },
        {
            transactionId: 2,
            date: '2024-01-14',
            type: 'deposit',
            fromAccount: '-',
            toAccount: 'صندوق المبيعات',
            amount: 5000.000,
            description: 'إيداع مبيعات اليوم',
            createdBy: 'أمين الصندوق'
        }
    ];
    
    displayTransactions();
}

// عرض المعاملات
function displayTransactions() {
    const tbody = document.getElementById('transactionsTableBody');
    
    if (transactions.length === 0) {
        tbody.innerHTML = `
            <tr>
                <td colspan="7" class="text-center text-muted">
                    <i class="fas fa-exchange-alt fa-2x mb-2"></i>
                    <p>لا توجد معاملات مالية</p>
                </td>
            </tr>
        `;
        return;
    }
    
    tbody.innerHTML = transactions.map(transaction => `
        <tr>
            <td>${transaction.date}</td>
            <td>
                <span class="badge ${getTransactionTypeBadge(transaction.type)}">
                    ${getTransactionTypeText(transaction.type)}
                </span>
            </td>
            <td>${transaction.fromAccount}</td>
            <td>${transaction.toAccount}</td>
            <td><strong>${formatCurrency(transaction.amount)}</strong></td>
            <td>${transaction.description}</td>
            <td>
                <div class="btn-group btn-group-sm">
                    <button class="btn btn-outline-info" onclick="viewTransaction(${transaction.transactionId})" title="عرض">
                        <i class="fas fa-eye"></i>
                    </button>
                    <button class="btn btn-outline-primary" onclick="printTransaction(${transaction.transactionId})" title="طباعة">
                        <i class="fas fa-print"></i>
                    </button>
                </div>
            </td>
        </tr>
    `).join('');
}

// إظهار نافذة إضافة صندوق
function showAddCashModal() {
    document.getElementById('cashForm').reset();
    const modal = new bootstrap.Modal(document.getElementById('cashModal'));
    modal.show();
}

// إظهار نافذة إضافة حساب بنكي
function showAddBankModal() {
    document.getElementById('bankForm').reset();
    const modal = new bootstrap.Modal(document.getElementById('bankModal'));
    modal.show();
}

// إظهار نافذة التحويل
function showTransferModal() {
    document.getElementById('transferForm').reset();
    const modal = new bootstrap.Modal(document.getElementById('transferModal'));
    modal.show();
}

// حفظ صندوق نقدي
function saveCashRegister() {
    const form = document.getElementById('cashForm');
    if (!form.checkValidity()) {
        form.reportValidity();
        return;
    }
    
    const cashData = {
        cashId: Date.now(),
        cashName: document.getElementById('cashName').value,
        description: document.getElementById('cashDescription').value,
        currentBalance: parseFloat(document.getElementById('cashBalance').value) || 0,
        linkedAccountId: parseInt(document.getElementById('cashAccount').value) || null,
        isActive: true,
        createdDate: new Date().toISOString().split('T')[0]
    };
    
    cashRegisters.push(cashData);
    displayCashRegisters();
    
    bootstrap.Modal.getInstance(document.getElementById('cashModal')).hide();
    alert('تم إضافة الصندوق بنجاح!');
}

// حفظ حساب بنكي
function saveBankAccount() {
    const form = document.getElementById('bankForm');
    if (!form.checkValidity()) {
        form.reportValidity();
        return;
    }
    
    const bankData = {
        bankId: Date.now(),
        bankName: document.getElementById('bankName').value,
        accountNumber: document.getElementById('accountNumber').value,
        accountType: document.getElementById('accountType').value,
        currentBalance: parseFloat(document.getElementById('bankBalance').value) || 0,
        branchName: document.getElementById('branchName').value,
        iban: document.getElementById('iban').value,
        linkedAccountId: parseInt(document.getElementById('bankAccount').value) || null,
        notes: document.getElementById('bankNotes').value,
        isActive: true,
        createdDate: new Date().toISOString().split('T')[0]
    };
    
    bankAccounts.push(bankData);
    displayBankAccounts();
    
    bootstrap.Modal.getInstance(document.getElementById('bankModal')).hide();
    alert('تم إضافة الحساب البنكي بنجاح!');
}

// معالجة التحويل
function processTransfer() {
    const form = document.getElementById('transferForm');
    if (!form.checkValidity()) {
        form.reportValidity();
        return;
    }
    
    const fromAccountId = document.getElementById('fromAccount').value;
    const toAccountId = document.getElementById('toAccount').value;
    const amount = parseFloat(document.getElementById('transferAmount').value);
    
    if (fromAccountId === toAccountId) {
        alert('لا يمكن التحويل من وإلى نفس الحساب');
        return;
    }
    
    const transactionData = {
        transactionId: Date.now(),
        date: new Date().toISOString().split('T')[0],
        type: 'transfer',
        fromAccount: getAccountName(fromAccountId),
        toAccount: getAccountName(toAccountId),
        amount: amount,
        description: document.getElementById('transferDescription').value || 'تحويل بين الحسابات',
        createdBy: 'المستخدم الحالي'
    };
    
    transactions.unshift(transactionData);
    displayTransactions();
    
    bootstrap.Modal.getInstance(document.getElementById('transferModal')).hide();
    alert('تم التحويل بنجاح!');
}

// الحصول على اسم الحساب
function getAccountName(accountId) {
    const account = linkedAccounts.find(acc => acc.accountId == accountId);
    return account ? account.accountName : 'غير محدد';
}

// الحصول على نص نوع الحساب البنكي
function getAccountTypeText(type) {
    const types = {
        'current': 'حساب جاري',
        'savings': 'حساب توفير',
        'fixed': 'وديعة ثابتة'
    };
    return types[type] || type;
}

// الحصول على نص نوع المعاملة
function getTransactionTypeText(type) {
    const types = {
        'transfer': 'تحويل',
        'deposit': 'إيداع',
        'withdraw': 'سحب'
    };
    return types[type] || type;
}

// الحصول على لون شارة نوع المعاملة
function getTransactionTypeBadge(type) {
    const badges = {
        'transfer': 'bg-info',
        'deposit': 'bg-success',
        'withdraw': 'bg-danger'
    };
    return badges[type] || 'bg-secondary';
}

// تنسيق العملة
function formatCurrency(amount) {
    return new Intl.NumberFormat('ar-YE', {
        style: 'currency',
        currency: 'YER',
        minimumFractionDigits: 3,
        maximumFractionDigits: 3
    }).format(amount).replace('YER', 'ر.ي');
}

// وظائف إضافية (ستتم إضافتها لاحقاً)
function editCashRegister(cashId) {
    alert('تعديل الصندوق - سيتم تطويره لاحقاً');
}

function editBankAccount(bankId) {
    alert('تعديل الحساب البنكي - سيتم تطويره لاحقاً');
}

function viewCashStatement(cashId) {
    alert('كشف حساب الصندوق - سيتم تطويره لاحقاً');
}

function viewBankStatement(bankId) {
    alert('كشف الحساب البنكي - سيتم تطويره لاحقاً');
}

function viewTransaction(transactionId) {
    alert('عرض تفاصيل المعاملة - سيتم تطويره لاحقاً');
}

function printTransaction(transactionId) {
    alert('طباعة المعاملة - سيتم تطويره لاحقاً');
}

function showDepositModal() {
    alert('نافذة الإيداع - سيتم تطويرها لاحقاً');
}

function showWithdrawModal() {
    alert('نافذة السحب - سيتم تطويرها لاحقاً');
}
