@echo off
echo ========================================
echo   إنشاء قاعدة بيانات ANW Bakery System
echo ========================================

echo.
echo 🔧 تنظيف المشروع...
dotnet clean

echo.
echo 🔧 استعادة الحزم...
dotnet restore

echo.
echo 🔧 بناء المشروع...
dotnet build

if %ERRORLEVEL% NEQ 0 (
    echo ❌ فشل في بناء المشروع!
    pause
    exit /b 1
)

echo.
echo 🔧 حذف Migrations السابقة (إن وجدت)...
if exist "Migrations" rmdir /s /q "Migrations"

echo.
echo 🔧 إنشاء Migration جديد...
dotnet ef migrations add InitialCreate

if %ERRORLEVEL% NEQ 0 (
    echo ❌ فشل في إنشاء Migration!
    pause
    exit /b 1
)

echo.
echo 🔧 إنشاء قاعدة البيانات...
dotnet ef database update

if %ERRORLEVEL% NEQ 0 (
    echo ❌ فشل في إنشاء قاعدة البيانات!
    pause
    exit /b 1
)

echo.
echo ✅ تم إنشاء قاعدة البيانات بنجاح!
echo.
echo 📁 ملف قاعدة البيانات: ANWBakery.db
echo 👤 المستخدم الافتراضي: admin
echo 🔑 كلمة المرور: admin123
echo.
echo 🚀 تشغيل التطبيق...
dotnet run

pause
