using ANWBakerySystem.Models;

namespace ANWBakerySystem.Services
{
    /// <summary>
    /// واجهة خدمة المصادقة والتوثيق
    /// Authentication Service Interface
    /// </summary>
    public interface IAuthService
    {
        Task<AuthResult> LoginAsync(string username, string password);
        Task<AuthResult> RefreshTokenAsync(string token);
        Task<bool> LogoutAsync(string token);
        Task<bool> ChangePasswordAsync(int userId, string currentPassword, string newPassword);
        Task<bool> ResetPasswordAsync(string email);
        Task<string> GenerateJwtTokenAsync(User user);
        Task<bool> ValidateTokenAsync(string token);
        Task<User?> GetUserFromTokenAsync(string token);
        Task<bool> HasPermissionAsync(int userId, string permissionName);
        Task<bool> HasModuleAccessAsync(int userId, string moduleName);
    }

    /// <summary>
    /// نتيجة عملية المصادقة
    /// Authentication Result
    /// </summary>
    public class AuthResult
    {
        public bool Success { get; set; }
        public string? Token { get; set; }
        public User? User { get; set; }
        public string? Message { get; set; }
        public DateTime? ExpiresAt { get; set; }
        public IEnumerable<string>? Permissions { get; set; }
    }
}
