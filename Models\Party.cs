using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace ANWBakerySystem.Models
{
    /// <summary>
    /// نموذج الأطراف الموحد - يدعم العملاء والموردين في نظام واحد
    /// Unified Parties Model - Supports customers and suppliers in one system
    /// </summary>
    public class Party
    {
        [Key]
        public int PartyId { get; set; }

        [Required]
        [StringLength(20)]
        [Display(Name = "كود الطرف")]
        public string PartyCode { get; set; } = string.Empty;

        [Required]
        [StringLength(200)]
        [Display(Name = "اسم الطرف")]
        public string PartyName { get; set; } = string.Empty;

        [StringLength(200)]
        [Display(Name = "الاسم بالإنجليزية")]
        public string? PartyNameEn { get; set; }

        /// <summary>
        /// نوع الطرف - يمكن أن يكون عميل ومورد في نفس الوقت
        /// Party Type - Can be customer and supplier at the same time
        /// </summary>
        [Required]
        [Display(Name = "نوع الطرف")]
        public PartyType PartyType { get; set; }

        [Display(Name = "هل هو عميل")]
        public bool IsCustomer { get; set; } = false;

        [Display(Name = "هل هو مورد")]
        public bool IsSupplier { get; set; } = false;

        [Display(Name = "هل هو موظف")]
        public bool IsEmployee { get; set; } = false;

        [StringLength(100)]
        [Display(Name = "رقم الهاتف")]
        public string? Phone { get; set; }

        [StringLength(100)]
        [Display(Name = "رقم الجوال")]
        public string? Mobile { get; set; }

        [StringLength(200)]
        [Display(Name = "البريد الإلكتروني")]
        public string? Email { get; set; }

        [StringLength(500)]
        [Display(Name = "العنوان")]
        public string? Address { get; set; }

        [StringLength(100)]
        [Display(Name = "المدينة")]
        public string? City { get; set; }

        [StringLength(100)]
        [Display(Name = "المنطقة")]
        public string? Region { get; set; }

        [StringLength(20)]
        [Display(Name = "الرمز البريدي")]
        public string? PostalCode { get; set; }

        [StringLength(100)]
        [Display(Name = "الرقم الضريبي")]
        public string? TaxNumber { get; set; }

        [StringLength(100)]
        [Display(Name = "السجل التجاري")]
        public string? CommercialRegister { get; set; }

        /// <summary>
        /// الرصيد الحالي بالريال اليمني - موجب للمدين، سالب للدائن
        /// Current balance in Yemeni Rial - positive for debit, negative for credit
        /// </summary>
        [Display(Name = "الرصيد الحالي (ر.ي)")]
        [Column(TypeName = "decimal(18,3)")]
        public decimal CurrentBalance { get; set; } = 0;

        /// <summary>
        /// حد الائتمان بالريال اليمني
        /// Credit limit in Yemeni Rial
        /// </summary>
        [Display(Name = "حد الائتمان (ر.ي)")]
        [Column(TypeName = "decimal(18,3)")]
        public decimal CreditLimit { get; set; } = 0;

        [Display(Name = "نسبة الخصم")]
        [Column(TypeName = "decimal(5,2)")]
        public decimal DiscountPercentage { get; set; } = 0;

        [Display(Name = "مدة السداد (أيام)")]
        public int PaymentTermDays { get; set; } = 0;

        [StringLength(500)]
        [Display(Name = "ملاحظات")]
        public string? Notes { get; set; }

        [Display(Name = "نشط")]
        public bool IsActive { get; set; } = true;

        [Display(Name = "تاريخ الإنشاء")]
        public DateTime CreatedAt { get; set; } = DateTime.Now;

        [Display(Name = "تاريخ التحديث")]
        public DateTime? UpdatedAt { get; set; }

        [Display(Name = "منشئ بواسطة")]
        public int CreatedBy { get; set; }

        [Display(Name = "محدث بواسطة")]
        public int? UpdatedBy { get; set; }

        // Navigation properties
        public virtual User Creator { get; set; } = null!;
        public virtual User? Updater { get; set; }
        public virtual ICollection<Invoice> CustomerInvoices { get; set; } = new List<Invoice>();
        public virtual ICollection<Invoice> SupplierInvoices { get; set; } = new List<Invoice>();
        public virtual ICollection<PartyTransaction> Transactions { get; set; } = new List<PartyTransaction>();
    }

    public enum PartyType
    {
        [Display(Name = "عميل")]
        Customer = 1,
        [Display(Name = "مورد")]
        Supplier = 2,
        [Display(Name = "عميل ومورد")]
        CustomerAndSupplier = 3,
        [Display(Name = "موظف")]
        Employee = 4,
        [Display(Name = "أخرى")]
        Other = 5
    }
}
