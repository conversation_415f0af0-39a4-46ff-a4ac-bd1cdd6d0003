<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار API - الحسابات</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
</head>
<body>
    <div class="container mt-5">
        <h2>🧪 اختبار API الحسابات</h2>
        
        <div class="row">
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">
                        <h5>إنشاء حساب جديد</h5>
                    </div>
                    <div class="card-body">
                        <form id="testForm">
                            <div class="mb-3">
                                <label>رقم الحساب:</label>
                                <input type="text" id="accountCode" class="form-control" value="1001" required>
                            </div>
                            <div class="mb-3">
                                <label>اسم الحساب:</label>
                                <input type="text" id="accountName" class="form-control" value="حساب تجريبي" required>
                            </div>
                            <div class="mb-3">
                                <label>نوع الحساب:</label>
                                <select id="accountType" class="form-control" required>
                                    <option value="1">الأصول</option>
                                    <option value="2">الخصوم</option>
                                    <option value="3">حقوق الملكية</option>
                                    <option value="4">الإيرادات</option>
                                    <option value="5">المصروفات</option>
                                </select>
                            </div>
                            <div class="mb-3">
                                <label>طبيعة الحساب:</label>
                                <select id="accountNature" class="form-control" required>
                                    <option value="1">مدين</option>
                                    <option value="2">دائن</option>
                                </select>
                            </div>
                            <div class="mb-3">
                                <label>الرصيد الافتتاحي:</label>
                                <input type="number" id="currentBalance" class="form-control" value="0" step="0.01">
                            </div>
                            <button type="button" onclick="testCreateAccount()" class="btn btn-primary">إنشاء الحساب</button>
                        </form>
                    </div>
                </div>
            </div>
            
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">
                        <h5>النتائج</h5>
                    </div>
                    <div class="card-body">
                        <div id="results" style="background: #f8f9fa; padding: 15px; border-radius: 5px; min-height: 200px;">
                            <p>اضغط على "إنشاء الحساب" لاختبار API</p>
                        </div>
                    </div>
                </div>
                
                <div class="card mt-3">
                    <div class="card-header">
                        <h5>جلب جميع الحسابات</h5>
                    </div>
                    <div class="card-body">
                        <button onclick="testGetAccounts()" class="btn btn-success">جلب الحسابات</button>
                        <div id="accountsList" class="mt-3"></div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        async function testCreateAccount() {
            const results = document.getElementById('results');
            results.innerHTML = '<p>🔄 جاري إنشاء الحساب...</p>';
            
            const accountData = {
                accountCode: document.getElementById('accountCode').value,
                accountName: document.getElementById('accountName').value,
                accountType: parseInt(document.getElementById('accountType').value),
                accountNature: parseInt(document.getElementById('accountNature').value),
                parentAccountId: null,
                level: 0,
                currentBalance: parseFloat(document.getElementById('currentBalance').value) || 0,
                isActive: true,
                allowPosting: true,
                autoLink: null
            };
            
            console.log('📤 البيانات المرسلة:', accountData);
            
            try {
                const response = await fetch('/api/accounts', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify(accountData)
                });
                
                console.log('📥 استجابة الخادم:', response.status, response.statusText);
                
                const responseText = await response.text();
                console.log('📄 نص الاستجابة:', responseText);
                
                if (response.ok) {
                    const result = JSON.parse(responseText);
                    results.innerHTML = `
                        <h6>✅ نجح الإنشاء!</h6>
                        <pre>${JSON.stringify(result, null, 2)}</pre>
                    `;
                } else {
                    results.innerHTML = `
                        <h6>❌ فشل الإنشاء!</h6>
                        <p><strong>كود الخطأ:</strong> ${response.status}</p>
                        <p><strong>رسالة الخطأ:</strong> ${responseText}</p>
                    `;
                }
            } catch (error) {
                console.error('❌ خطأ:', error);
                results.innerHTML = `
                    <h6>❌ خطأ في الشبكة!</h6>
                    <p>${error.message}</p>
                `;
            }
        }
        
        async function testGetAccounts() {
            const accountsList = document.getElementById('accountsList');
            accountsList.innerHTML = '<p>🔄 جاري جلب الحسابات...</p>';
            
            try {
                const response = await fetch('/api/accounts');
                const result = await response.json();
                
                if (result.success) {
                    accountsList.innerHTML = `
                        <h6>✅ تم جلب ${result.data.length} حساب</h6>
                        <pre>${JSON.stringify(result.data, null, 2)}</pre>
                    `;
                } else {
                    accountsList.innerHTML = `
                        <h6>❌ فشل في جلب الحسابات</h6>
                        <p>${result.message}</p>
                    `;
                }
            } catch (error) {
                accountsList.innerHTML = `
                    <h6>❌ خطأ في الشبكة</h6>
                    <p>${error.message}</p>
                `;
            }
        }
    </script>
</body>
</html>
