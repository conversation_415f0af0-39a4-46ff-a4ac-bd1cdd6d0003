@echo off
echo ========================================
echo    تشغيل نظام مخبوزات ANW
echo    ANW Bakery Management System
echo ========================================
echo.

echo إيقاف العمليات السابقة...
echo Stopping previous processes...
taskkill /f /im dotnet.exe >nul 2>&1

echo.
echo بدء تشغيل النظام...
echo Starting the system...

cd /d "C:\ANW_bakery"

echo تحديث المشروع...
echo Updating project...
dotnet restore >nul 2>&1

echo بناء المشروع...
echo Building project...
dotnet build >nul 2>&1

if %errorlevel% neq 0 (
    echo.
    echo ❌ فشل في بناء المشروع
    echo ❌ Project build failed
    pause
    exit /b 1
)

echo.
echo ✅ تم بناء المشروع بنجاح
echo ✅ Project built successfully
echo.

echo تشغيل الخادم...
echo Starting server...
echo.
echo 🌐 سيتم فتح المتصفح تلقائياً على:
echo 🌐 Browser will open automatically at:
echo    http://localhost:5000
echo.
echo 📋 بيانات تسجيل الدخول الافتراضية:
echo 📋 Default login credentials:
echo    المستخدم / Username: admin
echo    كلمة المرور / Password: admin123
echo.
echo ⚠️  لإيقاف الخادم اضغط Ctrl+C
echo ⚠️  To stop the server press Ctrl+C
echo.

timeout /t 3 >nul

start http://localhost:5000

dotnet run

pause
