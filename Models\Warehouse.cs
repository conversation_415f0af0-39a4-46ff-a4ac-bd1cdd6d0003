using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace ANWBakerySystem.Models
{
    /// <summary>
    /// نموذج المخازن
    /// Warehouse model
    /// </summary>
    public class Warehouse
    {
        [Key]
        public int WarehouseId { get; set; }

        [Required]
        [StringLength(50)]
        [Display(Name = "كود المخزن")]
        public string WarehouseCode { get; set; } = string.Empty;

        [Required]
        [StringLength(200)]
        [Display(Name = "اسم المخزن")]
        public string WarehouseName { get; set; } = string.Empty;

        [StringLength(200)]
        [Display(Name = "الاسم بالإنجليزية")]
        public string? WarehouseNameEn { get; set; }

        [StringLength(500)]
        [Display(Name = "العنوان")]
        public string? Address { get; set; }

        [StringLength(100)]
        [Display(Name = "رقم الهاتف")]
        public string? Phone { get; set; }

        [StringLength(200)]
        [Display(Name = "البريد الإلكتروني")]
        public string? Email { get; set; }

        [StringLength(200)]
        [Display(Name = "المسؤول")]
        public string? ResponsiblePerson { get; set; }

        [Display(Name = "نوع المخزن")]
        public WarehouseType WarehouseType { get; set; } = WarehouseType.Main;

        [Display(Name = "المساحة (متر مربع)")]
        [Column(TypeName = "decimal(10,2)")]
        public decimal? Area { get; set; }

        [Display(Name = "السعة القصوى")]
        [Column(TypeName = "decimal(18,3)")]
        public decimal? MaxCapacity { get; set; }

        [Display(Name = "درجة الحرارة المطلوبة")]
        [Column(TypeName = "decimal(5,2)")]
        public decimal? RequiredTemperature { get; set; }

        [Display(Name = "الرطوبة المطلوبة")]
        [Column(TypeName = "decimal(5,2)")]
        public decimal? RequiredHumidity { get; set; }

        [Display(Name = "مخزن رئيسي")]
        public bool IsMainWarehouse { get; set; } = false;

        [Display(Name = "نشط")]
        public bool IsActive { get; set; } = true;

        [StringLength(500)]
        [Display(Name = "ملاحظات")]
        public string? Notes { get; set; }

        [Display(Name = "تاريخ الإنشاء")]
        public DateTime CreatedAt { get; set; } = DateTime.Now;

        [Display(Name = "تاريخ التحديث")]
        public DateTime? UpdatedAt { get; set; }

        [Display(Name = "منشئ بواسطة")]
        public int? CreatedBy { get; set; }

        [Display(Name = "محدث بواسطة")]
        public int? UpdatedBy { get; set; }

        // Navigation properties
        public virtual User? Creator { get; set; }
        public virtual User? Updater { get; set; }
        public virtual ICollection<Inventory> Inventories { get; set; } = new List<Inventory>();
        public virtual ICollection<InventoryMovement> InventoryMovements { get; set; } = new List<InventoryMovement>();
    }

    /// <summary>
    /// أنواع المخازن
    /// Warehouse types
    /// </summary>
    public enum WarehouseType
    {
        [Display(Name = "مخزن رئيسي")]
        Main = 1,

        [Display(Name = "مخزن فرعي")]
        Branch = 2,

        [Display(Name = "مخزن مبرد")]
        Refrigerated = 3,

        [Display(Name = "مخزن مجمد")]
        Frozen = 4,

        [Display(Name = "مخزن جاف")]
        Dry = 5,

        [Display(Name = "مخزن مؤقت")]
        Temporary = 6,

        [Display(Name = "مخزن تالف")]
        Damaged = 7,

        [Display(Name = "مخزن إنتاج")]
        Production = 8
    }
}
