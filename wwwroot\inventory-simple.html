<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إدارة المخزون - نظام ANW</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
</head>
<body>
    <div class="container-fluid">
        <div class="row">
            <!-- Sidebar -->
            <div class="col-md-3 bg-dark text-white p-0">
                <div class="sidebar">
                    <div class="p-3 border-bottom">
                        <h5><i class="fas fa-calculator me-2"></i>نظام ANW</h5>
                        <small>إدارة مخبوزات مبسط</small>
                    </div>
                    <nav class="nav flex-column p-3">
                        <a href="items-simple.html" class="nav-link text-white">
                            <i class="fas fa-boxes me-2"></i>المنتجات والخامات
                        </a>
                        <a href="parties-simple.html" class="nav-link text-white">
                            <i class="fas fa-users me-2"></i>العملاء والموردين
                        </a>
                        <a href="employees-simple.html" class="nav-link text-white">
                            <i class="fas fa-user-tie me-2"></i>الموظفين
                        </a>
                        <a href="invoices-simple.html" class="nav-link text-white">
                            <i class="fas fa-file-invoice me-2"></i>الفواتير
                        </a>
                        <a href="inventory-simple.html" class="nav-link text-white active">
                            <i class="fas fa-warehouse me-2"></i>المخزون
                        </a>
                        <a href="cash-simple.html" class="nav-link text-white">
                            <i class="fas fa-cash-register me-2"></i>الصناديق
                        </a>
                        <a href="banks-simple.html" class="nav-link text-white">
                            <i class="fas fa-university me-2"></i>البنوك
                        </a>
                        <a href="owners-simple.html" class="nav-link text-white">
                            <i class="fas fa-crown me-2"></i>الملاك
                        </a>
                        <a href="users-simple.html" class="nav-link text-white">
                            <i class="fas fa-user-cog me-2"></i>المستخدمين
                        </a>
                        <a href="reports-simple.html" class="nav-link text-white">
                            <i class="fas fa-chart-bar me-2"></i>التقارير
                        </a>
                    </nav>
                </div>
            </div>

            <!-- Main Content -->
            <div class="col-md-9">
                <div class="p-4">
                    <!-- Header -->
                    <div class="d-flex justify-content-between align-items-center mb-4">
                        <h2><i class="fas fa-warehouse text-primary me-2"></i>إدارة المخزون</h2>
                        <div>
                            <button class="btn btn-success" onclick="addProduction()">
                                <i class="fas fa-industry me-1"></i>إنتاج يومي
                            </button>
                            <button class="btn btn-warning" onclick="addDamage()">
                                <i class="fas fa-exclamation-triangle me-1"></i>تالف/فاقد
                            </button>
                            <button class="btn btn-primary" onclick="addStockAdjustment()">
                                <i class="fas fa-edit me-1"></i>تسوية مخزون
                            </button>
                            <button class="btn btn-secondary" onclick="syncWithInvoices()">
                                <i class="fas fa-sync me-1"></i>مزامنة الفواتير
                            </button>
                            <button class="btn btn-info" onclick="exportInventory()">
                                <i class="fas fa-download me-1"></i>تصدير
                            </button>
                        </div>
                    </div>

                    <!-- Inventory Summary -->
                    <div class="row mb-4">
                        <div class="col-md-12">
                            <div class="card">
                                <div class="card-header">
                                    <h5><i class="fas fa-chart-bar me-2"></i>ملخص المخزون</h5>
                                </div>
                                <div class="card-body">
                                    <div id="inventorySummary" class="row">
                                        <!-- سيتم ملؤها بـ JavaScript -->
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Tabs -->
                    <ul class="nav nav-tabs mb-3">
                        <li class="nav-item">
                            <a class="nav-link active" onclick="showTab('movements')">حركات المخزون</a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" onclick="showTab('current')">المخزون الحالي</a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" onclick="showTab('production')">عمليات الإنتاج</a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" onclick="showTab('alerts')">تنبيهات المخزون</a>
                        </li>
                    </ul>

                    <!-- Movements Tab -->
                    <div id="movementsTab" class="tab-content">
                        <!-- Filter -->
                        <div class="row mb-3">
                            <div class="col-md-3">
                                <select id="movementTypeFilter" class="form-control" onchange="filterMovements()">
                                    <option value="">جميع الحركات</option>
                                    <option value="purchase">مشتريات (وارد)</option>
                                    <option value="sale">مبيعات (صادر)</option>
                                    <option value="production_in">إنتاج (وارد منتجات)</option>
                                    <option value="production_out">إنتاج (صادر خامات)</option>
                                    <option value="purchase_return">مرتجع مشتريات (صادر)</option>
                                    <option value="sale_return">مرتجع مبيعات (وارد)</option>
                                    <option value="damage">تالف/فاقد (صادر)</option>
                                    <option value="adjustment">تسوية مخزون</option>
                                </select>
                            </div>
                            <div class="col-md-3">
                                <select id="itemFilter" class="form-control" onchange="filterMovements()">
                                    <option value="">جميع الأصناف</option>
                                </select>
                            </div>
                            <div class="col-md-6">
                                <input type="text" id="movementSearchInput" class="form-control" placeholder="البحث في الحركات..." onkeyup="searchMovements()">
                            </div>
                        </div>

                        <!-- Movements Table -->
                        <div class="card">
                            <div class="card-body">
                                <div class="table-responsive">
                                    <table class="table table-striped table-hover">
                                        <thead class="table-dark">
                                            <tr>
                                                <th>التاريخ</th>
                                                <th>النوع</th>
                                                <th>الصنف</th>
                                                <th>الكمية</th>
                                                <th>الوحدة</th>
                                                <th>السعر</th>
                                                <th>الإجمالي</th>
                                                <th>المصدر</th>
                                                <th>البيان</th>
                                                <th>الإجراءات</th>
                                            </tr>
                                        </thead>
                                        <tbody id="movementsTableBody">
                                            <!-- سيتم ملؤها بـ JavaScript -->
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Current Stock Tab -->
                    <div id="currentTab" class="tab-content" style="display: none;">
                        <div class="card">
                            <div class="card-body">
                                <div class="table-responsive">
                                    <table class="table table-striped table-hover">
                                        <thead class="table-dark">
                                            <tr>
                                                <th>الصنف</th>
                                                <th>النوع</th>
                                                <th>المخزون الحالي</th>
                                                <th>الحد الأدنى</th>
                                                <th>متوسط السعر</th>
                                                <th>قيمة المخزون</th>
                                                <th>الحالة</th>
                                            </tr>
                                        </thead>
                                        <tbody id="currentStockTableBody">
                                            <!-- سيتم ملؤها بـ JavaScript -->
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Production Tab -->
                    <div id="productionTab" class="tab-content" style="display: none;">
                        <div class="card">
                            <div class="card-body">
                                <div class="table-responsive">
                                    <table class="table table-striped table-hover">
                                        <thead class="table-dark">
                                            <tr>
                                                <th>التاريخ</th>
                                                <th>المنتج</th>
                                                <th>الكمية المنتجة</th>
                                                <th>تكلفة الخامات</th>
                                                <th>تكلفة العمالة</th>
                                                <th>التكلفة الإجمالية</th>
                                                <th>الإجراءات</th>
                                            </tr>
                                        </thead>
                                        <tbody id="productionTableBody">
                                            <!-- سيتم ملؤها بـ JavaScript -->
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Alerts Tab -->
                    <div id="alertsTab" class="tab-content" style="display: none;">
                        <div class="card">
                            <div class="card-body">
                                <div id="stockAlerts">
                                    <!-- سيتم ملؤها بـ JavaScript -->
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Stock Movement Modal -->
    <div class="modal fade" id="movementModal" tabindex="-1">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">حركة مخزون</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <form id="movementForm">
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">نوع الحركة *</label>
                                    <select id="movementType" class="form-control" required>
                                        <option value="">اختر النوع</option>
                                        <option value="in">إدخال مخزون</option>
                                        <option value="out">إخراج مخزون</option>
                                        <option value="damage">تالف/فاقد</option>
                                    </select>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">التاريخ *</label>
                                    <input type="date" id="movementDate" class="form-control" required>
                                </div>
                            </div>
                        </div>
                        
                        <!-- Movement Items -->
                        <div class="card mb-3">
                            <div class="card-header d-flex justify-content-between">
                                <h6>أصناف الحركة</h6>
                                <button type="button" class="btn btn-sm btn-success" onclick="addMovementItem()">
                                    <i class="fas fa-plus"></i> إضافة صنف
                                </button>
                            </div>
                            <div class="card-body">
                                <div class="table-responsive">
                                    <table class="table table-sm">
                                        <thead>
                                            <tr>
                                                <th>الصنف</th>
                                                <th>الكمية</th>
                                                <th>السعر</th>
                                                <th>الإجمالي</th>
                                                <th>حذف</th>
                                            </tr>
                                        </thead>
                                        <tbody id="movementItemsTable">
                                            <!-- سيتم ملؤها بـ JavaScript -->
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-8">
                                <div class="mb-3">
                                    <label class="form-label">البيان</label>
                                    <textarea id="movementDescription" class="form-control" rows="2"></textarea>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="card">
                                    <div class="card-body">
                                        <div class="d-flex justify-content-between fw-bold">
                                            <span>الإجمالي:</span>
                                            <span id="movementTotal">0.00 ر.ي</span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                    <button type="button" class="btn btn-primary" onclick="saveMovement()">حفظ</button>
                </div>
            </div>
        </div>
    </div>

    <!-- Production Modal -->
    <div class="modal fade" id="productionModal" tabindex="-1">
        <div class="modal-dialog modal-xl">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">عملية إنتاج جديدة</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <form id="productionForm">
                        <div class="row mb-3">
                            <div class="col-md-4">
                                <label class="form-label">المنتج المراد إنتاجه *</label>
                                <select id="productionProduct" class="form-control" required onchange="loadProductRecipe()">
                                    <option value="">اختر المنتج</option>
                                </select>
                            </div>
                            <div class="col-md-4">
                                <label class="form-label">الكمية المراد إنتاجها *</label>
                                <input type="number" id="productionQuantity" class="form-control" required min="1" step="0.01" onchange="calculateProductionCost()">
                            </div>
                            <div class="col-md-4">
                                <label class="form-label">التاريخ *</label>
                                <input type="date" id="productionDate" class="form-control" required>
                            </div>
                        </div>

                        <!-- Raw Materials Required -->
                        <div class="card mb-3">
                            <div class="card-header">
                                <h6>الخامات المطلوبة</h6>
                            </div>
                            <div class="card-body">
                                <div class="table-responsive">
                                    <table class="table table-sm">
                                        <thead>
                                            <tr>
                                                <th>الخامة</th>
                                                <th>الكمية المطلوبة</th>
                                                <th>المتوفر</th>
                                                <th>السعر</th>
                                                <th>التكلفة</th>
                                                <th>الحالة</th>
                                            </tr>
                                        </thead>
                                        <tbody id="productionMaterialsTable">
                                            <!-- سيتم ملؤها بـ JavaScript -->
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                        </div>

                        <!-- Production Costs -->
                        <div class="row">
                            <div class="col-md-8">
                                <div class="mb-3">
                                    <label class="form-label">ملاحظات الإنتاج</label>
                                    <textarea id="productionNotes" class="form-control" rows="2"></textarea>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="card">
                                    <div class="card-body">
                                        <div class="d-flex justify-content-between">
                                            <span>تكلفة الخامات:</span>
                                            <span id="materialsCost">0.00 ر.ي</span>
                                        </div>
                                        <div class="d-flex justify-content-between">
                                            <span>تكلفة العمالة:</span>
                                            <input type="number" id="laborCost" class="form-control form-control-sm" value="0" step="0.01" onchange="calculateProductionCost()">
                                        </div>
                                        <div class="d-flex justify-content-between">
                                            <span>تكاليف أخرى:</span>
                                            <input type="number" id="otherCosts" class="form-control form-control-sm" value="0" step="0.01" onchange="calculateProductionCost()">
                                        </div>
                                        <hr>
                                        <div class="d-flex justify-content-between fw-bold">
                                            <span>التكلفة الإجمالية:</span>
                                            <span id="totalProductionCost">0.00 ر.ي</span>
                                        </div>
                                        <div class="d-flex justify-content-between text-info">
                                            <span>تكلفة الوحدة:</span>
                                            <span id="unitCost">0.00 ر.ي</span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                    <button type="button" class="btn btn-primary" onclick="saveProduction()">تنفيذ الإنتاج</button>
                </div>
            </div>
        </div>
    </div>

    <!-- Production Modal -->
    <div class="modal fade" id="productionModal" tabindex="-1">
        <div class="modal-dialog modal-xl">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">عملية إنتاج يومي</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <form id="productionForm">
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">تاريخ الإنتاج *</label>
                                    <input type="date" id="productionDate" class="form-control" required>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">المنتج المراد إنتاجه *</label>
                                    <select id="productToMake" class="form-control" required onchange="loadProductRecipe()">
                                        <option value="">اختر المنتج</option>
                                        <!-- سيتم ملؤها بـ JavaScript -->
                                    </select>
                                </div>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">كمية الإنتاج *</label>
                                    <input type="number" id="productionQuantity" class="form-control" required min="1" onchange="calculateProductionCost()">
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">ملاحظات الإنتاج</label>
                                    <textarea id="productionNotes" class="form-control" rows="2"></textarea>
                                </div>
                            </div>
                        </div>

                        <!-- وصفة المنتج -->
                        <div id="productRecipeSection" class="mb-3" style="display: none;">
                            <div class="card">
                                <div class="card-header">
                                    <h6>وصفة المنتج والخامات المطلوبة</h6>
                                </div>
                                <div class="card-body">
                                    <div class="table-responsive">
                                        <table class="table table-sm">
                                            <thead>
                                                <tr>
                                                    <th>الخامة</th>
                                                    <th>الكمية للوحدة</th>
                                                    <th>الوحدة</th>
                                                    <th>إجمالي المطلوب</th>
                                                    <th>المتوفر</th>
                                                    <th>الحالة</th>
                                                </tr>
                                            </thead>
                                            <tbody id="productionRecipeTable">
                                                <!-- سيتم ملؤها بـ JavaScript -->
                                            </tbody>
                                        </table>
                                    </div>
                                    <div class="row mt-3">
                                        <div class="col-md-6">
                                            <div class="card border-info">
                                                <div class="card-body">
                                                    <h6>تكلفة الإنتاج</h6>
                                                    <div class="d-flex justify-content-between">
                                                        <span>تكلفة الخامات:</span>
                                                        <span id="productionMaterialsCost">0.00 ر.ي</span>
                                                    </div>
                                                    <div class="d-flex justify-content-between">
                                                        <span>تكلفة العمالة:</span>
                                                        <span id="productionLaborCost">0.00 ر.ي</span>
                                                    </div>
                                                    <div class="d-flex justify-content-between">
                                                        <span>تكاليف أخرى:</span>
                                                        <span id="productionOtherCosts">0.00 ر.ي</span>
                                                    </div>
                                                    <hr>
                                                    <div class="d-flex justify-content-between fw-bold">
                                                        <span>إجمالي التكلفة:</span>
                                                        <span id="productionTotalCost">0.00 ر.ي</span>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="col-md-6">
                                            <div class="alert alert-info">
                                                <h6><i class="fas fa-info-circle"></i> ملاحظة:</h6>
                                                <p class="mb-0">سيتم خصم الخامات المطلوبة من المخزون وإضافة المنتج النهائي للمخزون تلقائياً.</p>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                    <button type="button" class="btn btn-success" onclick="executeProduction()">
                        <i class="fas fa-cogs"></i> تنفيذ الإنتاج
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Movement Modal -->
    <div class="modal fade" id="movementModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">حركة مخزون</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <form id="movementForm">
                        <input type="hidden" id="movementType">
                        <div class="mb-3">
                            <label class="form-label">التاريخ *</label>
                            <input type="date" id="movementDate" class="form-control" required>
                        </div>
                        <div class="mb-3">
                            <label class="form-label">الصنف *</label>
                            <select id="movementItem" class="form-control" required>
                                <option value="">اختر الصنف</option>
                                <!-- سيتم ملؤها بـ JavaScript -->
                            </select>
                        </div>
                        <div class="mb-3">
                            <label class="form-label">الكمية *</label>
                            <input type="number" id="movementQuantity" class="form-control" required min="0.01" step="0.01">
                        </div>
                        <div class="mb-3">
                            <label class="form-label">السعر</label>
                            <input type="number" id="movementPrice" class="form-control" step="0.01" value="0">
                        </div>
                        <div class="mb-3">
                            <label class="form-label">البيان *</label>
                            <textarea id="movementDescription" class="form-control" rows="3" required></textarea>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                    <button type="button" class="btn btn-primary" onclick="saveMovement()">حفظ</button>
                </div>
            </div>
        </div>
    </div>

    <!-- Production Modal -->
    <div class="modal fade" id="productionModal" tabindex="-1">
        <div class="modal-dialog modal-xl">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">عملية إنتاج يومي</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <form id="productionForm">
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">تاريخ الإنتاج *</label>
                                    <input type="date" id="productionDate" class="form-control" required>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">المنتج المراد إنتاجه *</label>
                                    <select id="productToMake" class="form-control" required>
                                        <option value="">اختر المنتج</option>
                                        <!-- سيتم ملؤها بـ JavaScript -->
                                    </select>
                                </div>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">كمية الإنتاج *</label>
                                    <input type="number" id="productionQuantity" class="form-control" required min="1">
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">ملاحظات الإنتاج</label>
                                    <textarea id="productionNotes" class="form-control" rows="2"></textarea>
                                </div>
                            </div>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                    <button type="button" class="btn btn-success" onclick="executeProduction()">
                        <i class="fas fa-cogs"></i> تنفيذ الإنتاج
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Movement Modal -->
    <div class="modal fade" id="movementModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">حركة مخزون</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <form id="movementForm">
                        <input type="hidden" id="movementType">
                        <div class="mb-3">
                            <label class="form-label">التاريخ *</label>
                            <input type="date" id="movementDate" class="form-control" required>
                        </div>
                        <div class="mb-3">
                            <label class="form-label">الصنف *</label>
                            <select id="movementItem" class="form-control" required>
                                <option value="">اختر الصنف</option>
                                <!-- سيتم ملؤها بـ JavaScript -->
                            </select>
                        </div>
                        <div class="mb-3">
                            <label class="form-label">الكمية *</label>
                            <input type="number" id="movementQuantity" class="form-control" required min="0.01" step="0.01">
                        </div>
                        <div class="mb-3">
                            <label class="form-label">السعر</label>
                            <input type="number" id="movementPrice" class="form-control" step="0.01" value="0">
                        </div>
                        <div class="mb-3">
                            <label class="form-label">البيان *</label>
                            <textarea id="movementDescription" class="form-control" rows="3" required></textarea>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                    <button type="button" class="btn btn-primary" onclick="saveMovement()">حفظ</button>
                </div>
            </div>
        </div>
    </div>

    <!-- Success/Error Messages -->
    <div id="messages"></div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // البيانات المحلية
        let items = [];
        let movements = [];
        let productions = [];
        let invoices = [];
        let availableUnits = [];
        let nextMovementId = 1;
        let nextProductionId = 1;
        let currentMovementItems = [];
        let currentFilter = '';

        // تحميل البيانات عند بدء الصفحة
        document.addEventListener('DOMContentLoaded', function() {
            loadAllData();
            document.getElementById('movementDate').value = new Date().toISOString().split('T')[0];
            document.getElementById('productionDate').value = new Date().toISOString().split('T')[0];
        });

        // تحميل جميع البيانات
        function loadAllData() {
            loadItems();
            loadInvoices();
            loadUnits();
            loadMovements();
            loadProductions();
            syncInventoryWithInvoices();
            displayInventorySummary();
            displayMovements();
            displayCurrentStock();
            displayProductions();
            displayStockAlerts();
            loadItemOptions();
        }

        // تحميل الفواتير
        function loadInvoices() {
            try {
                const savedInvoices = localStorage.getItem('anw_invoices');
                if (savedInvoices && savedInvoices !== 'null') {
                    invoices = JSON.parse(savedInvoices);
                } else {
                    invoices = [];
                }
            } catch (error) {
                console.error('خطأ في تحميل الفواتير:', error);
                invoices = [];
            }
        }

        // تحميل وحدات القياس
        function loadUnits() {
            try {
                const savedUnits = localStorage.getItem('anw_units');
                if (savedUnits && savedUnits !== 'null') {
                    availableUnits = JSON.parse(savedUnits).filter(u => u.active);
                } else {
                    availableUnits = [];
                }
            } catch (error) {
                console.error('خطأ في تحميل وحدات القياس:', error);
                availableUnits = [];
            }
        }

        // مزامنة المخزون مع الفواتير
        function syncInventoryWithInvoices() {
            // إنشاء حركات مخزون من الفواتير
            invoices.forEach(invoice => {
                if (invoice.items && invoice.items.length > 0) {
                    invoice.items.forEach(invoiceItem => {
                        const item = items.find(i => i.id === invoiceItem.itemId);
                        if (item) {
                            let movementType = '';
                            let description = '';

                            // تحديد نوع الحركة حسب نوع الفاتورة
                            if (invoice.type === 'purchase') {
                                movementType = 'purchase';
                                description = `مشتريات من فاتورة رقم ${invoice.number}`;
                            } else if (invoice.type === 'sale') {
                                movementType = 'sale';
                                description = `مبيعات من فاتورة رقم ${invoice.number}`;
                            } else if (invoice.type === 'purchase_return') {
                                movementType = 'purchase_return';
                                description = `مرتجع مشتريات من فاتورة رقم ${invoice.number}`;
                            } else if (invoice.type === 'sale_return') {
                                movementType = 'sale_return';
                                description = `مرتجع مبيعات من فاتورة رقم ${invoice.number}`;
                            }

                            // التحقق من عدم وجود الحركة مسبقاً
                            const existingMovement = movements.find(m =>
                                m.sourceType === 'invoice' &&
                                m.sourceId === invoice.id &&
                                m.itemId === invoiceItem.itemId
                            );

                            if (!existingMovement && movementType) {
                                // إنشاء حركة مخزون جديدة
                                const movement = {
                                    id: nextMovementId++,
                                    date: invoice.date,
                                    type: movementType,
                                    itemId: invoiceItem.itemId,
                                    quantity: invoiceItem.quantity,
                                    unit: invoiceItem.unit || '',
                                    unitName: invoiceItem.unitName || item.unit,
                                    price: invoiceItem.price,
                                    total: invoiceItem.total,
                                    description: description,
                                    sourceType: 'invoice',
                                    sourceId: invoice.id,
                                    sourceNumber: invoice.number
                                };

                                movements.push(movement);

                                // تحديث المخزون
                                updateItemStock(invoiceItem.itemId, invoiceItem.quantity, movementType);
                            }
                        }
                    });
                }
            });

            saveMovements();
            saveItems();
        }

        // تحديث مخزون الصنف
        function updateItemStock(itemId, quantity, movementType) {
            const item = items.find(i => i.id === itemId);
            if (item) {
                // الحركات الواردة (تزيد المخزون)
                if (['purchase', 'sale_return', 'production_in', 'adjustment_in'].includes(movementType)) {
                    item.stock += quantity;
                }
                // الحركات الصادرة (تقلل المخزون)
                else if (['sale', 'purchase_return', 'production_out', 'damage', 'adjustment_out'].includes(movementType)) {
                    item.stock -= quantity;
                    // التأكد من عدم النزول تحت الصفر
                    if (item.stock < 0) {
                        item.stock = 0;
                    }
                }
            }
        }

        // مزامنة يدوية مع الفواتير
        function syncWithInvoices() {
            loadInvoices();
            syncInventoryWithInvoices();
            displayInventorySummary();
            displayMovements();
            displayCurrentStock();
            showMessage('تم مزامنة المخزون مع الفواتير بنجاح', 'success');
        }

        // تحميل الأصناف
        function loadItems() {
            const savedItems = localStorage.getItem('anw_items');
            if (savedItems) {
                items = JSON.parse(savedItems);
            } else {
                // إنشاء أصناف افتراضية
                items = [
                    {id: 1, name: 'دقيق أبيض', type: 'material', stock: 500, minStock: 100, avgPrice: 75, unit: 'kg'},
                    {id: 2, name: 'خبز عربي', type: 'product', stock: 200, minStock: 50, avgPrice: 1750, unit: 'piece'},
                    {id: 3, name: 'سكر', type: 'material', stock: 150, minStock: 30, avgPrice: 120, unit: 'kg'}
                ];
                localStorage.setItem('anw_items', JSON.stringify(items));
            }
        }

        // تحميل حركات المخزون
        function loadMovements() {
            try {
                const savedMovements = localStorage.getItem('anw_movements');
                if (savedMovements && savedMovements !== 'null') {
                    movements = JSON.parse(savedMovements);
                    if (movements.length > 0) {
                        nextMovementId = Math.max(...movements.map(m => m.id), 0) + 1;
                    }
                } else {
                    // إنشاء حركات افتراضية
                    movements = [
                        {id: 1, date: '2024-01-15', type: 'in', itemId: 1, quantity: 100, price: 75, total: 7500, description: 'شراء دقيق من المورد'},
                        {id: 2, date: '2024-01-16', type: 'out', itemId: 2, quantity: 50, price: 1750, total: 87500, description: 'بيع خبز للعميل'},
                        {id: 3, date: '2024-01-17', type: 'damage', itemId: 3, quantity: 5, price: 120, total: 600, description: 'تلف سكر بسبب الرطوبة'}
                    ];
                    nextMovementId = 4;
                    saveMovements();
                }
            } catch (error) {
                console.error('خطأ في تحميل حركات المخزون:', error);
                showMessage('خطأ في تحميل البيانات', 'danger');
            }
        }

        // تحميل عمليات الإنتاج
        function loadProductions() {
            try {
                const savedProductions = localStorage.getItem('anw_productions');
                if (savedProductions && savedProductions !== 'null') {
                    productions = JSON.parse(savedProductions);
                    if (productions.length > 0) {
                        nextProductionId = Math.max(...productions.map(p => p.id), 0) + 1;
                    }
                } else {
                    // إنشاء عمليات إنتاج افتراضية
                    productions = [
                        {
                            id: 1,
                            date: '2024-01-15',
                            productId: 2,
                            quantity: 100,
                            materialsCost: 5000,
                            laborCost: 2000,
                            otherCosts: 500,
                            totalCost: 7500,
                            unitCost: 75,
                            materials: [{itemId: 1, quantity: 50, price: 75, total: 3750}],
                            notes: 'إنتاج خبز عربي'
                        }
                    ];
                    nextProductionId = 2;
                    saveProductions();
                }
            } catch (error) {
                console.error('خطأ في تحميل عمليات الإنتاج:', error);
                showMessage('خطأ في تحميل البيانات', 'danger');
            }
        }

        // حفظ البيانات
        function saveMovements() {
            localStorage.setItem('anw_movements', JSON.stringify(movements));
        }

        function saveProductions() {
            localStorage.setItem('anw_productions', JSON.stringify(productions));
        }

        function saveItems() {
            localStorage.setItem('anw_items', JSON.stringify(items));
        }

        // عرض ملخص المخزون
        function displayInventorySummary() {
            const summaryDiv = document.getElementById('inventorySummary');

            const totalItems = items.length;
            const lowStockItems = items.filter(item => item.stock <= item.minStock).length;
            const totalValue = items.reduce((sum, item) => sum + (item.stock * item.avgPrice), 0);
            const materialsValue = items.filter(item => item.type === 'material').reduce((sum, item) => sum + (item.stock * item.avgPrice), 0);
            const productsValue = items.filter(item => item.type === 'product').reduce((sum, item) => sum + (item.stock * item.avgPrice), 0);

            summaryDiv.innerHTML = `
                <div class="col-md-2">
                    <div class="card border-primary">
                        <div class="card-body text-center">
                            <h5 class="card-title text-primary">${totalItems}</h5>
                            <p class="card-text">إجمالي الأصناف</p>
                        </div>
                    </div>
                </div>
                <div class="col-md-2">
                    <div class="card border-danger">
                        <div class="card-body text-center">
                            <h5 class="card-title text-danger">${lowStockItems}</h5>
                            <p class="card-text">أصناف منخفضة</p>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card border-success">
                        <div class="card-body text-center">
                            <h5 class="card-title text-success">${totalValue.toLocaleString()} ر.ي</h5>
                            <p class="card-text">إجمالي قيمة المخزون</p>
                        </div>
                    </div>
                </div>
                <div class="col-md-2">
                    <div class="card border-info">
                        <div class="card-body text-center">
                            <h5 class="card-title text-info">${materialsValue.toLocaleString()} ر.ي</h5>
                            <p class="card-text">قيمة الخامات</p>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card border-warning">
                        <div class="card-body text-center">
                            <h5 class="card-title text-warning">${productsValue.toLocaleString()} ر.ي</h5>
                            <p class="card-text">قيمة المنتجات</p>
                        </div>
                    </div>
                </div>
            `;
        }

        // عرض حركات المخزون
        function displayMovements() {
            const tbody = document.getElementById('movementsTableBody');
            let filteredMovements = movements;

            // تطبيق الفلتر
            if (currentFilter) {
                filteredMovements = movements.filter(m => m.type === currentFilter);
            }

            if (filteredMovements.length === 0) {
                tbody.innerHTML = `
                    <tr>
                        <td colspan="10" class="text-center text-muted">
                            لا توجد حركات مخزون
                            <br>
                            <button class="btn btn-primary mt-2" onclick="addStockIn()">إضافة حركة جديدة</button>
                        </td>
                    </tr>
                `;
                return;
            }

            let html = '';
            // ترتيب الحركات حسب التاريخ (الأحدث أولاً)
            filteredMovements.sort((a, b) => new Date(b.date) - new Date(a.date)).forEach(movement => {
                const item = items.find(i => i.id === movement.itemId);
                const typeLabel = getMovementTypeLabel(movement.type);
                const typeBadge = getMovementTypeBadge(movement.type);
                const unitDisplay = movement.unitName || (item ? item.unit : 'قطعة');
                const sourceDisplay = getSourceDisplay(movement);

                html += `
                    <tr>
                        <td>${new Date(movement.date).toLocaleDateString('ar-YE')}</td>
                        <td><span class="badge ${typeBadge}">${typeLabel}</span></td>
                        <td>${item ? item.name : 'غير محدد'}</td>
                        <td>${movement.quantity.toLocaleString()}</td>
                        <td>${unitDisplay}</td>
                        <td>${movement.price.toLocaleString()} ر.ي</td>
                        <td class="fw-bold">${movement.total.toLocaleString()} ر.ي</td>
                        <td>${sourceDisplay}</td>
                        <td>${movement.description}</td>
                        <td>
                            <button class="btn btn-sm btn-info" onclick="printMovement(${movement.id})">
                                <i class="fas fa-print"></i>
                            </button>
                            ${movement.sourceType !== 'invoice' ? `
                                <button class="btn btn-sm btn-danger" onclick="deleteMovement(${movement.id})">
                                    <i class="fas fa-trash"></i>
                                </button>
                            ` : ''}
                        </td>
                    </tr>
                `;
            });

            tbody.innerHTML = html;
        }

        // الحصول على عرض المصدر
        function getSourceDisplay(movement) {
            if (movement.sourceType === 'invoice') {
                return `فاتورة رقم ${movement.sourceNumber}`;
            } else if (movement.sourceType === 'production') {
                return `عملية إنتاج رقم ${movement.sourceId}`;
            } else if (movement.sourceType === 'manual') {
                return 'إدخال يدوي';
            } else {
                return 'غير محدد';
            }
        }
        // عرض المخزون الحالي
        function displayCurrentStock() {
            const tbody = document.getElementById('currentStockTableBody');

            if (items.length === 0) {
                tbody.innerHTML = `
                    <tr>
                        <td colspan="7" class="text-center text-muted">لا توجد أصناف في المخزون</td>
                    </tr>
                `;
                return;
            }

            let html = '';
            items.forEach(item => {
                const stockValue = item.stock * item.avgPrice;
                const statusBadge = item.stock <= item.minStock ?
                    '<span class="badge bg-danger">منخفض</span>' :
                    item.stock <= item.minStock * 2 ?
                    '<span class="badge bg-warning">متوسط</span>' :
                    '<span class="badge bg-success">جيد</span>';

                const typeLabel = item.type === 'material' ? 'خامة' : 'منتج';

                html += `
                    <tr>
                        <td>${item.name}</td>
                        <td><span class="badge bg-info">${typeLabel}</span></td>
                        <td class="fw-bold">${item.stock.toLocaleString()}</td>
                        <td>${item.minStock.toLocaleString()}</td>
                        <td>${item.avgPrice.toLocaleString()} ر.ي</td>
                        <td class="fw-bold">${stockValue.toLocaleString()} ر.ي</td>
                        <td>${statusBadge}</td>
                    </tr>
                `;
            });

            tbody.innerHTML = html;
        }

        // عرض عمليات الإنتاج
        function displayProductions() {
            const tbody = document.getElementById('productionTableBody');

            if (productions.length === 0) {
                tbody.innerHTML = `
                    <tr>
                        <td colspan="7" class="text-center text-muted">
                            لا توجد عمليات إنتاج
                            <br>
                            <button class="btn btn-primary mt-2" onclick="addProduction()">إضافة عملية إنتاج</button>
                        </td>
                    </tr>
                `;
                return;
            }

            let html = '';
            productions.forEach(production => {
                const product = items.find(i => i.id === production.productId);

                html += `
                    <tr>
                        <td>${new Date(production.date).toLocaleDateString('ar-YE')}</td>
                        <td>${product ? product.name : 'غير محدد'}</td>
                        <td class="fw-bold">${production.quantity.toLocaleString()}</td>
                        <td>${production.materialsCost.toLocaleString()} ر.ي</td>
                        <td>${production.laborCost.toLocaleString()} ر.ي</td>
                        <td class="fw-bold">${production.totalCost.toLocaleString()} ر.ي</td>
                        <td>
                            <button class="btn btn-sm btn-info" onclick="viewProduction(${production.id})">
                                <i class="fas fa-eye"></i>
                            </button>
                            <button class="btn btn-sm btn-danger" onclick="deleteProduction(${production.id})">
                                <i class="fas fa-trash"></i>
                            </button>
                        </td>
                    </tr>
                `;
            });

            tbody.innerHTML = html;
        }

        // عرض تنبيهات المخزون
        function displayStockAlerts() {
            const alertsDiv = document.getElementById('stockAlerts');
            const lowStockItems = items.filter(item => item.stock <= item.minStock);
            const outOfStockItems = items.filter(item => item.stock <= 0);

            if (lowStockItems.length === 0 && outOfStockItems.length === 0) {
                alertsDiv.innerHTML = `
                    <div class="alert alert-success">
                        <i class="fas fa-check-circle me-2"></i>
                        جميع الأصناف في المخزون ضمن الحدود الآمنة
                    </div>
                `;
                return;
            }

            let html = '';

            if (outOfStockItems.length > 0) {
                html += `
                    <div class="alert alert-danger">
                        <h6><i class="fas fa-exclamation-triangle me-2"></i>أصناف نفدت من المخزون:</h6>
                        <ul class="mb-0">
                            ${outOfStockItems.map(item => `<li>${item.name} - المخزون: ${item.stock}</li>`).join('')}
                        </ul>
                    </div>
                `;
            }

            if (lowStockItems.length > 0) {
                html += `
                    <div class="alert alert-warning">
                        <h6><i class="fas fa-exclamation-circle me-2"></i>أصناف منخفضة في المخزون:</h6>
                        <ul class="mb-0">
                            ${lowStockItems.map(item => `<li>${item.name} - المخزون: ${item.stock} - الحد الأدنى: ${item.minStock}</li>`).join('')}
                        </ul>
                    </div>
                `;
            }

            alertsDiv.innerHTML = html;
        }

        // تحميل خيارات الأصناف
        function loadItemOptions() {
            const filterSelect = document.getElementById('itemFilter');
            const movementSelect = document.getElementById('movementItem');

            let filterHtml = '<option value="">جميع الأصناف</option>';
            let movementHtml = '<option value="">اختر الصنف</option>';

            items.forEach(item => {
                filterHtml += `<option value="${item.id}">${item.name}</option>`;
                movementHtml += `<option value="${item.id}">${item.name} (${item.type === 'material' ? 'خامة' : item.type === 'product' ? 'منتج' : 'خدمة'})</option>`;
            });

            if (filterSelect) filterSelect.innerHTML = filterHtml;
            if (movementSelect) movementSelect.innerHTML = movementHtml;

            // تحميل خيارات المنتجات للإنتاج
            loadProductOptions();
        }

        // الحصول على تسمية نوع الحركة
        function getMovementTypeLabel(type) {
            const types = {
                'purchase': 'مشتريات (وارد)',
                'sale': 'مبيعات (صادر)',
                'production_in': 'إنتاج (وارد منتجات)',
                'production_out': 'إنتاج (صادر خامات)',
                'purchase_return': 'مرتجع مشتريات (صادر)',
                'sale_return': 'مرتجع مبيعات (وارد)',
                'damage': 'تالف/فاقد (صادر)',
                'adjustment': 'تسوية مخزون',
                'adjustment_in': 'تسوية (وارد)',
                'adjustment_out': 'تسوية (صادر)'
            };
            return types[type] || type;
        }

        // الحصول على شارة نوع الحركة
        function getMovementTypeBadge(type) {
            const badges = {
                'purchase': 'bg-success',
                'sale': 'bg-danger',
                'production_in': 'bg-primary',
                'production_out': 'bg-info',
                'purchase_return': 'bg-warning',
                'sale_return': 'bg-success',
                'damage': 'bg-danger',
                'adjustment': 'bg-secondary',
                'adjustment_in': 'bg-success',
                'adjustment_out': 'bg-danger'
            };
            return badges[type] || 'bg-secondary';
        }

        // إضافة عملية إنتاج
        function addProduction() {
            clearProductionForm();
            document.querySelector('#productionModal .modal-title').textContent = 'عملية إنتاج يومي';
            new bootstrap.Modal(document.getElementById('productionModal')).show();
        }

        // إضافة تالف/فاقد
        function addDamage() {
            clearMovementForm();
            document.getElementById('movementType').value = 'damage';
            document.querySelector('#movementModal .modal-title').textContent = 'تسجيل تالف/فاقد';
            new bootstrap.Modal(document.getElementById('movementModal')).show();
        }

        // إضافة تسوية مخزون
        function addStockAdjustment() {
            clearMovementForm();
            document.getElementById('movementType').value = 'adjustment';
            document.querySelector('#movementModal .modal-title').textContent = 'تسوية مخزون';
            new bootstrap.Modal(document.getElementById('movementModal')).show();
        }

        // إضافة حركة تالف
        function addDamage() {
            clearMovementForm();
            document.getElementById('movementType').value = 'damage';
            document.querySelector('#movementModal .modal-title').textContent = 'تسجيل تالف/فاقد';
            new bootstrap.Modal(document.getElementById('movementModal')).show();
        }

        // إضافة عملية إنتاج
        function addProduction() {
            clearProductionForm();
            new bootstrap.Modal(document.getElementById('productionModal')).show();
        }

        // إضافة صنف لحركة المخزون
        function addMovementItem() {
            const tbody = document.getElementById('movementItemsTable');
            const rowIndex = currentMovementItems.length;

            const row = `
                <tr data-index="${rowIndex}">
                    <td>
                        <select class="form-control item-select" onchange="updateMovementItemPrice(${rowIndex})">
                            <option value="">اختر الصنف</option>
                            ${items.map(item => `<option value="${item.id}" data-price="${item.avgPrice}">${item.name}</option>`).join('')}
                        </select>
                    </td>
                    <td>
                        <input type="number" class="form-control quantity-input" value="1" min="0.01" step="0.01" onchange="calculateMovementItemTotal(${rowIndex})">
                    </td>
                    <td>
                        <input type="number" class="form-control price-input" value="0" step="0.01" onchange="calculateMovementItemTotal(${rowIndex})">
                    </td>
                    <td>
                        <span class="item-total">0.00 ر.ي</span>
                    </td>
                    <td>
                        <button type="button" class="btn btn-sm btn-danger" onclick="removeMovementItem(${rowIndex})">
                            <i class="fas fa-trash"></i>
                        </button>
                    </td>
                </tr>
            `;

            tbody.insertAdjacentHTML('beforeend', row);
            currentMovementItems.push({itemId: null, quantity: 1, price: 0, total: 0});
        }
        // حفظ حركة المخزون
        function saveMovement() {
            const movementData = {
                type: document.getElementById('movementType').value,
                date: document.getElementById('movementDate').value,
                description: document.getElementById('movementDescription').value,
                items: currentMovementItems.filter(item => item.itemId)
            };

            if (!movementData.type || movementData.items.length === 0) {
                showMessage('يرجى ملء جميع الحقول المطلوبة وإضافة أصناف', 'danger');
                return;
            }

            // معالجة كل صنف في الحركة
            movementData.items.forEach(movementItem => {
                const item = items.find(i => i.id === movementItem.itemId);
                if (item) {
                    // تحديث المخزون
                    if (movementData.type === 'in') {
                        item.stock += movementItem.quantity;
                        // تحديث متوسط السعر
                        const totalValue = (item.stock - movementItem.quantity) * item.avgPrice + movementItem.total;
                        item.avgPrice = totalValue / item.stock;
                    } else if (movementData.type === 'out' || movementData.type === 'damage') {
                        item.stock -= movementItem.quantity;
                    }

                    // إنشاء حركة منفصلة لكل صنف
                    const movement = {
                        id: nextMovementId++,
                        date: movementData.date,
                        type: movementData.type,
                        itemId: movementItem.itemId,
                        quantity: movementItem.quantity,
                        price: movementItem.price,
                        total: movementItem.total,
                        description: movementData.description
                    };

                    movements.push(movement);
                }
            });

            saveMovements();
            saveItems();
            loadAllData();
            bootstrap.Modal.getInstance(document.getElementById('movementModal')).hide();
            showMessage('تم حفظ حركة المخزون بنجاح', 'success');
        }

        // حفظ عملية الإنتاج
        function saveProduction() {
            const productionData = {
                productId: parseInt(document.getElementById('productionProduct').value),
                quantity: parseFloat(document.getElementById('productionQuantity').value),
                date: document.getElementById('productionDate').value,
                laborCost: parseFloat(document.getElementById('laborCost').value) || 0,
                otherCosts: parseFloat(document.getElementById('otherCosts').value) || 0,
                notes: document.getElementById('productionNotes').value
            };

            if (!productionData.productId || !productionData.quantity) {
                showMessage('يرجى ملء جميع الحقول المطلوبة', 'danger');
                return;
            }

            const product = items.find(i => i.id === productionData.productId);
            if (!product || !product.recipe) {
                showMessage('المنتج المحدد لا يحتوي على وصفة إنتاج', 'danger');
                return;
            }

            // التحقق من توفر الخامات
            let materialsCost = 0;
            const requiredMaterials = [];

            for (let recipeItem of product.recipe) {
                const material = items.find(i => i.id === recipeItem.materialId);
                const requiredQuantity = recipeItem.quantity * productionData.quantity;

                if (!material || material.stock < requiredQuantity) {
                    showMessage(`الخامة ${material ? material.name : 'غير محددة'} غير متوفرة بالكمية المطلوبة`, 'danger');
                    return;
                }

                const cost = requiredQuantity * material.avgPrice;
                materialsCost += cost;

                requiredMaterials.push({
                    itemId: material.id,
                    quantity: requiredQuantity,
                    price: material.avgPrice,
                    total: cost
                });
            }

            // تنفيذ الإنتاج
            productionData.id = nextProductionId++;
            productionData.materialsCost = materialsCost;
            productionData.totalCost = materialsCost + productionData.laborCost + productionData.otherCosts;
            productionData.unitCost = productionData.totalCost / productionData.quantity;
            productionData.materials = requiredMaterials;

            // تحديث مخزون الخامات (إخراج)
            requiredMaterials.forEach(material => {
                const item = items.find(i => i.id === material.itemId);
                if (item) {
                    item.stock -= material.quantity;

                    // إنشاء حركة إخراج للخامة
                    const movement = {
                        id: nextMovementId++,
                        date: productionData.date,
                        type: 'production',
                        itemId: material.itemId,
                        quantity: material.quantity,
                        price: material.price,
                        total: material.total,
                        description: `استخدام في إنتاج ${product.name}`
                    };
                    movements.push(movement);
                }
            });

            // تحديث مخزون المنتج (إدخال)
            product.stock += productionData.quantity;
            // تحديث متوسط سعر المنتج
            const totalValue = (product.stock - productionData.quantity) * product.avgPrice + productionData.totalCost;
            product.avgPrice = totalValue / product.stock;

            // إنشاء حركة إدخال للمنتج
            const productMovement = {
                id: nextMovementId++,
                date: productionData.date,
                type: 'production',
                itemId: productionData.productId,
                quantity: productionData.quantity,
                price: productionData.unitCost,
                total: productionData.totalCost,
                description: `إنتاج ${product.name}`
            };
            movements.push(productMovement);

            productions.push(productionData);
            saveProductions();
            saveMovements();
            saveItems();
            loadAllData();
            bootstrap.Modal.getInstance(document.getElementById('productionModal')).hide();
            showMessage('تم تنفيذ عملية الإنتاج بنجاح', 'success');
        }

        // تبديل التبويبات
        function showTab(tabName) {
            // إخفاء جميع التبويبات
            document.getElementById('movementsTab').style.display = 'none';
            document.getElementById('currentTab').style.display = 'none';
            document.getElementById('productionTab').style.display = 'none';
            document.getElementById('alertsTab').style.display = 'none';

            // إزالة active من جميع الروابط
            document.querySelectorAll('.nav-link').forEach(link => {
                link.classList.remove('active');
            });

            // إظهار التبويب المحدد
            if (tabName === 'movements') {
                document.getElementById('movementsTab').style.display = 'block';
            } else if (tabName === 'current') {
                document.getElementById('currentTab').style.display = 'block';
            } else if (tabName === 'production') {
                document.getElementById('productionTab').style.display = 'block';
            } else if (tabName === 'alerts') {
                document.getElementById('alertsTab').style.display = 'block';
            }

            // إضافة active للرابط المحدد
            event.target.classList.add('active');
        }

        // تنظيف النماذج
        function clearMovementForm() {
            document.getElementById('movementForm').reset();
            document.getElementById('movementDate').value = new Date().toISOString().split('T')[0];
            loadItemOptions();
        }

        function clearProductionForm() {
            document.getElementById('productionForm').reset();
            document.getElementById('productionDate').value = new Date().toISOString().split('T')[0];
            loadProductOptions();
        }

        // عرض رسالة
        function showMessage(message, type) {
            const messagesDiv = document.getElementById('messages');
            const alertHtml = `
                <div class="alert alert-${type} alert-dismissible fade show position-fixed"
                     style="top: 20px; left: 20px; z-index: 9999; min-width: 300px;">
                    ${message}
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
            `;
            messagesDiv.innerHTML = alertHtml;

            setTimeout(() => {
                const alert = messagesDiv.querySelector('.alert');
                if (alert) {
                    bootstrap.Alert.getOrCreateInstance(alert).close();
                }
            }, 3000);
        }

        // تحميل خيارات المنتجات للإنتاج
        function loadProductOptions() {
            const select = document.getElementById('productToMake');
            if (!select) return;

            const products = items.filter(item => item.type === 'product' && item.recipe && item.recipe.length > 0);

            let html = '<option value="">اختر المنتج</option>';
            products.forEach(product => {
                html += `<option value="${product.id}">${product.name}</option>`;
            });

            select.innerHTML = html;
        }

        // تنفيذ الإنتاج
        function executeProduction() {
            const productId = parseInt(document.getElementById('productToMake').value);
            const quantity = parseFloat(document.getElementById('productionQuantity').value);
            const date = document.getElementById('productionDate').value;
            const notes = document.getElementById('productionNotes').value;

            if (!productId || !quantity || !date) {
                showMessage('يرجى ملء جميع الحقول المطلوبة', 'danger');
                return;
            }

            const product = items.find(i => i.id === productId);
            if (!product || !product.recipe) {
                showMessage('المنتج المحدد لا يحتوي على وصفة', 'danger');
                return;
            }

            // التحقق من توفر الخامات
            let canProduce = true;
            const shortages = [];

            product.recipe.forEach(recipeItem => {
                const material = items.find(i => i.id === recipeItem.materialId);
                const totalNeeded = recipeItem.quantity * quantity;

                if (!material || material.stock < totalNeeded) {
                    canProduce = false;
                    shortages.push(`${material ? material.name : 'غير محدد'}: مطلوب ${totalNeeded.toLocaleString()}, متوفر ${material ? material.stock.toLocaleString() : 0}`);
                }
            });

            if (!canProduce) {
                showMessage('لا يمكن تنفيذ الإنتاج بسبب نقص في الخامات:\n' + shortages.join('\n'), 'danger');
                return;
            }

            // تنفيذ الإنتاج
            const productionId = nextProductionId++;

            // خصم الخامات من المخزون
            product.recipe.forEach(recipeItem => {
                const material = items.find(i => i.id === recipeItem.materialId);
                const totalNeeded = recipeItem.quantity * quantity;

                // تحديث مخزون الخامة
                material.stock -= totalNeeded;

                // إنشاء حركة صادر للخامة
                const movement = {
                    id: nextMovementId++,
                    date: date,
                    type: 'production_out',
                    itemId: recipeItem.materialId,
                    quantity: totalNeeded,
                    unit: recipeItem.unit,
                    unitName: recipeItem.unitName,
                    price: recipeItem.unitPrice,
                    total: recipeItem.unitPrice * totalNeeded,
                    description: `استهلاك في إنتاج ${product.name} - عملية رقم ${productionId}`,
                    sourceType: 'production',
                    sourceId: productionId,
                    sourceNumber: productionId
                };

                movements.push(movement);
            });

            // إضافة المنتج للمخزون
            product.stock += quantity;

            // إنشاء حركة وارد للمنتج
            const productMovement = {
                id: nextMovementId++,
                date: date,
                type: 'production_in',
                itemId: productId,
                quantity: quantity,
                unit: '',
                unitName: product.unit,
                price: product.totalCost || 0,
                total: (product.totalCost || 0) * quantity,
                description: `إنتاج ${product.name} - عملية رقم ${productionId}`,
                sourceType: 'production',
                sourceId: productionId,
                sourceNumber: productionId
            };

            movements.push(productMovement);

            // حفظ البيانات
            saveItems();
            saveMovements();

            // تحديث العرض
            loadAllData();

            bootstrap.Modal.getInstance(document.getElementById('productionModal')).hide();
            showMessage(`تم تنفيذ عملية الإنتاج بنجاح - عملية رقم ${productionId}`, 'success');
        }

        // حفظ حركة مخزون
        function saveMovement() {
            const movementData = {
                type: document.getElementById('movementType').value,
                date: document.getElementById('movementDate').value,
                itemId: parseInt(document.getElementById('movementItem').value),
                quantity: parseFloat(document.getElementById('movementQuantity').value),
                price: parseFloat(document.getElementById('movementPrice').value) || 0,
                description: document.getElementById('movementDescription').value
            };

            if (!movementData.type || !movementData.date || !movementData.itemId || !movementData.quantity || !movementData.description) {
                showMessage('يرجى ملء جميع الحقول المطلوبة', 'danger');
                return;
            }

            const item = items.find(i => i.id === movementData.itemId);
            if (!item) {
                showMessage('الصنف المحدد غير موجود', 'danger');
                return;
            }

            // إنشاء الحركة
            const movement = {
                id: nextMovementId++,
                date: movementData.date,
                type: movementData.type,
                itemId: movementData.itemId,
                quantity: movementData.quantity,
                unit: '',
                unitName: item.unit,
                price: movementData.price,
                total: movementData.quantity * movementData.price,
                description: movementData.description,
                sourceType: 'manual',
                sourceId: null,
                sourceNumber: null
            };

            movements.push(movement);

            // تحديث المخزون
            updateItemStock(movementData.itemId, movementData.quantity, movementData.type);

            saveItems();
            saveMovements();
            loadAllData();

            bootstrap.Modal.getInstance(document.getElementById('movementModal')).hide();
            showMessage('تم حفظ الحركة بنجاح', 'success');
        }

        // مزامنة يدوية مع الفواتير
        function syncWithInvoices() {
            loadInvoices();
            syncInventoryWithInvoices();
            displayInventorySummary();
            displayMovements();
            displayCurrentStock();
            showMessage('تم مزامنة المخزون مع الفواتير بنجاح', 'success');
        }

        console.log('✅ تم تحميل نظام إدارة المخزون المحاسبي المتكامل بنجاح');
    </script>

    <style>
        .sidebar {
            height: 100vh;
            position: fixed;
            width: 25%;
        }
        .nav-link.active {
            background-color: rgba(255,255,255,0.1);
            border-radius: 5px;
        }
        .table th {
            background-color: #343a40;
            color: white;
        }
        .tab-content {
            animation: fadeIn 0.3s;
        }
        @keyframes fadeIn {
            from { opacity: 0; }
            to { opacity: 1; }
        }
    </style>
</body>
</html>
