using System.ComponentModel.DataAnnotations;

namespace ANWBakerySystem.Models
{
    /// <summary>
    /// DTO لعرض بيانات الوصفة
    /// Recipe Data Transfer Object for display
    /// </summary>
    public class RecipeDto
    {
        public int RecipeId { get; set; }
        public string RecipeCode { get; set; } = string.Empty;
        public string RecipeName { get; set; } = string.Empty;
        public string? RecipeNameEn { get; set; }
        public int ProductItemId { get; set; }
        public string ProductItemName { get; set; } = string.Empty;
        public string? ProductItemCode { get; set; }
        public int ProductionUnitId { get; set; }
        public string ProductionUnitName { get; set; } = string.Empty;
        public UnitType ProductionUnitType { get; set; }
        public decimal ProductionQuantity { get; set; }
        public decimal BaseProductionQuantity { get; set; }
        public decimal RawMaterialsCost { get; set; }
        public decimal LaborCost { get; set; }
        public decimal AdditionalCosts { get; set; }
        public decimal TotalCost { get; set; }
        public decimal CostPerUnit { get; set; }
        public int PreparationTimeMinutes { get; set; }
        public int CookingTimeMinutes { get; set; }
        public int TotalTimeMinutes { get; set; }
        public string? Temperature { get; set; }
        public string? PreparationInstructions { get; set; }
        public string? CookingInstructions { get; set; }
        public string? Notes { get; set; }
        public bool IsActive { get; set; }
        public DateTime CreatedAt { get; set; }
        public DateTime? UpdatedAt { get; set; }
        public List<RecipeItemDto> Items { get; set; } = new List<RecipeItemDto>();
    }

    /// <summary>
    /// DTO لعرض بيانات مادة الوصفة
    /// Recipe Item Data Transfer Object for display
    /// </summary>
    public class RecipeItemDto
    {
        public int RecipeItemId { get; set; }
        public int ItemId { get; set; }
        public string ItemName { get; set; } = string.Empty;
        public string? ItemCode { get; set; }
        public int UnitId { get; set; }
        public string UnitName { get; set; } = string.Empty;
        public UnitType UnitType { get; set; }
        public decimal RequiredQuantity { get; set; }
        public decimal BaseQuantity { get; set; }
        public decimal ConversionFactor { get; set; }
        public decimal UnitCost { get; set; }
        public decimal TotalCost { get; set; }
        public string? Notes { get; set; }
        public int LineOrder { get; set; }
    }

    /// <summary>
    /// طلب إنشاء وصفة جديدة
    /// Create Recipe Request
    /// </summary>
    public class CreateRecipeRequest
    {
        [Required(ErrorMessage = "كود الوصفة مطلوب")]
        [StringLength(50, ErrorMessage = "كود الوصفة يجب أن يكون أقل من 50 حرف")]
        public string RecipeCode { get; set; } = string.Empty;

        [Required(ErrorMessage = "اسم الوصفة مطلوب")]
        [StringLength(200, ErrorMessage = "اسم الوصفة يجب أن يكون أقل من 200 حرف")]
        public string RecipeName { get; set; } = string.Empty;

        [StringLength(200, ErrorMessage = "الاسم بالإنجليزية يجب أن يكون أقل من 200 حرف")]
        public string? RecipeNameEn { get; set; }

        [Required(ErrorMessage = "المنتج النهائي مطلوب")]
        public int ProductItemId { get; set; }

        [Required(ErrorMessage = "وحدة الإنتاج مطلوبة")]
        public int ProductionUnitId { get; set; }

        [Required(ErrorMessage = "نوع وحدة الإنتاج مطلوب")]
        public UnitType ProductionUnitType { get; set; }

        [Required(ErrorMessage = "كمية الإنتاج مطلوبة")]
        [Range(0.001, double.MaxValue, ErrorMessage = "كمية الإنتاج يجب أن تكون أكبر من صفر")]
        public decimal ProductionQuantity { get; set; }

        [Required(ErrorMessage = "كمية الإنتاج الأساسية مطلوبة")]
        [Range(0.001, double.MaxValue, ErrorMessage = "كمية الإنتاج الأساسية يجب أن تكون أكبر من صفر")]
        public decimal BaseProductionQuantity { get; set; }

        [Range(0, double.MaxValue, ErrorMessage = "تكلفة العمالة يجب أن تكون أكبر من أو تساوي صفر")]
        public decimal? LaborCost { get; set; }

        [Range(0, double.MaxValue, ErrorMessage = "التكاليف الإضافية يجب أن تكون أكبر من أو تساوي صفر")]
        public decimal? AdditionalCosts { get; set; }

        [Range(0, int.MaxValue, ErrorMessage = "وقت التحضير يجب أن يكون أكبر من أو يساوي صفر")]
        public int? PreparationTimeMinutes { get; set; }

        [Range(0, int.MaxValue, ErrorMessage = "وقت الطبخ يجب أن يكون أكبر من أو يساوي صفر")]
        public int? CookingTimeMinutes { get; set; }

        [StringLength(100, ErrorMessage = "درجة الحرارة يجب أن تكون أقل من 100 حرف")]
        public string? Temperature { get; set; }

        [StringLength(1000, ErrorMessage = "تعليمات التحضير يجب أن تكون أقل من 1000 حرف")]
        public string? PreparationInstructions { get; set; }

        [StringLength(1000, ErrorMessage = "تعليمات الطبخ يجب أن تكون أقل من 1000 حرف")]
        public string? CookingInstructions { get; set; }

        [StringLength(500, ErrorMessage = "الملاحظات يجب أن تكون أقل من 500 حرف")]
        public string? Notes { get; set; }

        public bool? IsActive { get; set; }
    }

    /// <summary>
    /// طلب إضافة مادة خام للوصفة
    /// Create Recipe Item Request
    /// </summary>
    public class CreateRecipeItemRequest
    {
        [Required(ErrorMessage = "معرف المادة الخام مطلوب")]
        public int ItemId { get; set; }

        [Required(ErrorMessage = "معرف وحدة القياس مطلوب")]
        public int UnitId { get; set; }

        [Required(ErrorMessage = "نوع الوحدة مطلوب")]
        public UnitType UnitType { get; set; }

        [Required(ErrorMessage = "الكمية المطلوبة مطلوبة")]
        [Range(0.001, double.MaxValue, ErrorMessage = "الكمية المطلوبة يجب أن تكون أكبر من صفر")]
        public decimal RequiredQuantity { get; set; }

        [Required(ErrorMessage = "معامل التحويل مطلوب")]
        [Range(0.001, double.MaxValue, ErrorMessage = "معامل التحويل يجب أن يكون أكبر من صفر")]
        public decimal ConversionFactor { get; set; }

        [Required(ErrorMessage = "تكلفة الوحدة مطلوبة")]
        [Range(0, double.MaxValue, ErrorMessage = "تكلفة الوحدة يجب أن تكون أكبر من أو تساوي صفر")]
        public decimal UnitCost { get; set; }

        [StringLength(500, ErrorMessage = "الملاحظات يجب أن تكون أقل من 500 حرف")]
        public string? Notes { get; set; }

        [Range(0, int.MaxValue, ErrorMessage = "ترتيب السطر يجب أن يكون أكبر من أو يساوي صفر")]
        public int? LineOrder { get; set; }
    }

    /// <summary>
    /// DTO لعرض بيانات أمر الإنتاج
    /// Production Order Data Transfer Object for display
    /// </summary>
    public class ProductionOrderDto
    {
        public int ProductionOrderId { get; set; }
        public string OrderNumber { get; set; } = string.Empty;
        public DateTime OrderDate { get; set; }
        public DateTime? PlannedProductionDate { get; set; }
        public DateTime? ActualProductionDate { get; set; }
        public int RecipeId { get; set; }
        public string RecipeName { get; set; } = string.Empty;
        public int ProductItemId { get; set; }
        public string ProductItemName { get; set; } = string.Empty;
        public string? ProductItemCode { get; set; }
        public int WarehouseId { get; set; }
        public string WarehouseName { get; set; } = string.Empty;
        public decimal RequiredQuantity { get; set; }
        public decimal ProducedQuantity { get; set; }
        public decimal PlannedCost { get; set; }
        public decimal ActualCost { get; set; }
        public ProductionOrderStatus Status { get; set; }
        public ProductionPriority Priority { get; set; }
        public string? Notes { get; set; }
        public DateTime? StartedAt { get; set; }
        public DateTime? CompletedAt { get; set; }
        public DateTime CreatedAt { get; set; }
        public DateTime? UpdatedAt { get; set; }
        public List<ProductionOrderItemDto> Items { get; set; } = new List<ProductionOrderItemDto>();
    }

    /// <summary>
    /// DTO لعرض بيانات مادة أمر الإنتاج
    /// Production Order Item Data Transfer Object for display
    /// </summary>
    public class ProductionOrderItemDto
    {
        public int ProductionOrderItemId { get; set; }
        public int ItemId { get; set; }
        public string ItemName { get; set; } = string.Empty;
        public string? ItemCode { get; set; }
        public decimal RequiredQuantity { get; set; }
        public decimal UsedQuantity { get; set; }
        public decimal PlannedCost { get; set; }
        public decimal ActualCost { get; set; }
        public ProductionItemStatus Status { get; set; }
    }

    /// <summary>
    /// طلب إنشاء أمر إنتاج جديد
    /// Create Production Order Request
    /// </summary>
    public class CreateProductionOrderRequest
    {
        [StringLength(50, ErrorMessage = "رقم أمر الإنتاج يجب أن يكون أقل من 50 حرف")]
        public string? OrderNumber { get; set; }

        public DateTime? OrderDate { get; set; }

        public DateTime? PlannedProductionDate { get; set; }

        [Required(ErrorMessage = "الوصفة مطلوبة")]
        public int RecipeId { get; set; }

        [Required(ErrorMessage = "المخزن مطلوب")]
        public int WarehouseId { get; set; }

        [Required(ErrorMessage = "الكمية المطلوبة مطلوبة")]
        [Range(0.001, double.MaxValue, ErrorMessage = "الكمية المطلوبة يجب أن تكون أكبر من صفر")]
        public decimal RequiredQuantity { get; set; }

        public ProductionPriority? Priority { get; set; }

        [StringLength(500, ErrorMessage = "الملاحظات يجب أن تكون أقل من 500 حرف")]
        public string? Notes { get; set; }
    }

    /// <summary>
    /// نتيجة عملية الوصفة الواحدة
    /// Single Recipe Result
    /// </summary>
    public class RecipeResult
    {
        public bool Success { get; set; }
        public string Message { get; set; } = string.Empty;
        public RecipeDto? Recipe { get; set; }
        public List<string>? Errors { get; set; }
    }

    /// <summary>
    /// نتيجة عملية الوصفات المتعددة
    /// Multiple Recipes Result
    /// </summary>
    public class RecipesResult
    {
        public bool Success { get; set; }
        public string Message { get; set; } = string.Empty;
        public List<RecipeDto>? Recipes { get; set; }
        public int TotalCount { get; set; }
        public int Page { get; set; }
        public int PageSize { get; set; }
        public int TotalPages { get; set; }
        public List<string>? Errors { get; set; }
    }

    /// <summary>
    /// نتيجة عملية مادة الوصفة
    /// Recipe Item Result
    /// </summary>
    public class RecipeItemResult
    {
        public bool Success { get; set; }
        public string Message { get; set; } = string.Empty;
        public RecipeItemDto? RecipeItem { get; set; }
        public List<string>? Errors { get; set; }
    }

    /// <summary>
    /// نتيجة عملية أمر الإنتاج الواحد
    /// Single Production Order Result
    /// </summary>
    public class ProductionOrderResult
    {
        public bool Success { get; set; }
        public string Message { get; set; } = string.Empty;
        public ProductionOrderDto? Order { get; set; }
        public List<string>? Errors { get; set; }
    }

    /// <summary>
    /// نتيجة عملية أوامر الإنتاج المتعددة
    /// Multiple Production Orders Result
    /// </summary>
    public class ProductionOrdersResult
    {
        public bool Success { get; set; }
        public string Message { get; set; } = string.Empty;
        public List<ProductionOrderDto>? Orders { get; set; }
        public int TotalCount { get; set; }
        public int Page { get; set; }
        public int PageSize { get; set; }
        public int TotalPages { get; set; }
        public List<string>? Errors { get; set; }
    }
}
