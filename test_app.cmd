@echo off
echo ========================================
echo    نظام إدارة مخبوزات ANW - SQLite
echo    ANW Bakery Management System - SQLite
echo ========================================
echo.

echo 🔄 تنظيف المشروع...
dotnet clean

echo.
echo 🔄 استعادة الحزم...
dotnet restore

echo.
echo 🔧 تجميع المشروع...
dotnet build

if %ERRORLEVEL% NEQ 0 (
    echo.
    echo ❌ فشل في تجميع المشروع
    echo ❌ Build failed
    echo.
    echo 💡 تحقق من الأخطاء أعلاه
    pause
    exit /b 1
)

echo.
echo ✅ تم تجميع المشروع بنجاح
echo ✅ Build successful

echo.
echo 🗄️ قاعدة البيانات: SQLite (محلية)
echo 📁 ملف قاعدة البيانات: ANWBakery.db
echo.
echo 🚀 تشغيل التطبيق...
echo 🌐 الرابط: http://localhost:5000
echo.
echo 📝 بيانات تسجيل الدخول:
echo    👤 اسم المستخدم: admin
echo    🔑 كلمة المرور: admin123
echo.
echo 🔧 الوظائف المتاحة:
echo    ✅ صفحة الحسابات - مربوطة بقاعدة البيانات
echo    ✅ جميع الأخطاء محلولة - 0 Errors
echo    ✅ جميع التضاربات محلولة - لا توجد تعريفات مكررة
echo    🔄 باقي الصفحات - تحتاج ربط
echo.
echo ⚠️  اضغط Ctrl+C لإيقاف التطبيق
echo ========================================

dotnet run
