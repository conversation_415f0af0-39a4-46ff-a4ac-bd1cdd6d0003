<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>المنتجات والخامات - نظام ANW</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
</head>
<body>
    <div class="container-fluid">
        <div class="row">
            <!-- Sidebar -->
            <div class="col-md-3 bg-dark text-white p-0">
                <div class="sidebar">
                    <div class="p-3 border-bottom">
                        <h5><i class="fas fa-calculator me-2"></i>نظام ANW</h5>
                        <small>إدارة مخبوزات مبسط</small>
                    </div>
                    <nav class="nav flex-column p-3">
                        <a href="items-simple.html" class="nav-link text-white active">
                            <i class="fas fa-boxes me-2"></i>المنتجات والخامات
                        </a>
                        <a href="parties-simple.html" class="nav-link text-white">
                            <i class="fas fa-users me-2"></i>العملاء والموردين
                        </a>
                        <a href="employees-simple.html" class="nav-link text-white">
                            <i class="fas fa-user-tie me-2"></i>الموظفين
                        </a>
                        <a href="invoices-simple.html" class="nav-link text-white">
                            <i class="fas fa-file-invoice me-2"></i>الفواتير
                        </a>
                        <a href="inventory-simple.html" class="nav-link text-white">
                            <i class="fas fa-warehouse me-2"></i>المخزون
                        </a>
                        <a href="cash-simple.html" class="nav-link text-white">
                            <i class="fas fa-cash-register me-2"></i>الصناديق
                        </a>
                        <a href="banks-simple.html" class="nav-link text-white">
                            <i class="fas fa-university me-2"></i>البنوك
                        </a>
                        <a href="owners-simple.html" class="nav-link text-white">
                            <i class="fas fa-crown me-2"></i>الملاك
                        </a>
                        <a href="users-simple.html" class="nav-link text-white">
                            <i class="fas fa-user-cog me-2"></i>المستخدمين
                        </a>
                        <a href="reports-simple.html" class="nav-link text-white">
                            <i class="fas fa-chart-bar me-2"></i>التقارير
                        </a>
                    </nav>
                </div>
            </div>

            <!-- Main Content -->
            <div class="col-md-9">
                <div class="p-4">
                    <!-- Header -->
                    <div class="d-flex justify-content-between align-items-center mb-4">
                        <h2><i class="fas fa-boxes text-primary me-2"></i>المنتجات والخامات</h2>
                        <div>
                            <button class="btn btn-success" onclick="addItem()">
                                <i class="fas fa-plus me-1"></i>إضافة منتج
                            </button>
                            <button class="btn btn-info" onclick="exportItems()">
                                <i class="fas fa-download me-1"></i>تصدير
                            </button>
                        </div>
                    </div>

                    <!-- Filter Tabs -->
                    <ul class="nav nav-tabs mb-3">
                        <li class="nav-item">
                            <a class="nav-link active" onclick="filterItems('all')">الكل</a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" onclick="filterItems('products')">المنتجات</a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" onclick="filterItems('materials')">الخامات</a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" onclick="filterItems('services')">الخدمات</a>
                        </li>
                    </ul>

                    <!-- Search -->
                    <div class="row mb-3">
                        <div class="col-md-6">
                            <input type="text" id="searchInput" class="form-control" placeholder="البحث بالاسم أو الكود أو الباركود..." onkeyup="searchItems()">
                        </div>
                    </div>

                    <!-- Items Table -->
                    <div class="card">
                        <div class="card-body">
                            <div class="table-responsive">
                                <table class="table table-striped table-hover">
                                    <thead class="table-dark">
                                        <tr>
                                            <th>الكود</th>
                                            <th>الاسم</th>
                                            <th>النوع</th>
                                            <th>الوحدة</th>
                                            <th>سعر الشراء</th>
                                            <th>سعر البيع</th>
                                            <th>المخزون</th>
                                            <th>الحالة</th>
                                            <th>الإجراءات</th>
                                        </tr>
                                    </thead>
                                    <tbody id="itemsTableBody">
                                        <tr>
                                            <td colspan="9" class="text-center">
                                                <div class="spinner-border text-primary" role="status">
                                                    <span class="visually-hidden">جاري التحميل...</span>
                                                </div>
                                                <p class="mt-2">جاري تحميل المنتجات...</p>
                                            </td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Add/Edit Item Modal -->
    <div class="modal fade" id="itemModal" tabindex="-1">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">إضافة منتج جديد</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <form id="itemForm">
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">كود المنتج *</label>
                                    <input type="text" id="itemCode" class="form-control" required>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">اسم المنتج *</label>
                                    <input type="text" id="itemName" class="form-control" required>
                                </div>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">نوع المنتج *</label>
                                    <select id="itemType" class="form-control" required onchange="updateFormByType()">
                                        <option value="">اختر النوع</option>
                                        <option value="product">منتج</option>
                                        <option value="material">خامة</option>
                                        <option value="service">خدمة</option>
                                    </select>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <!-- اختيار وحدة القياس للخامات -->
                                <div id="materialUnitsSection" class="mb-3" style="display: none;">
                                    <label class="form-label">وحدة القياس *</label>
                                    <select id="materialUnitGroup" class="form-control" onchange="loadSelectedUnit()">
                                        <option value="">اختر وحدة القياس</option>
                                        <!-- سيتم ملؤها بـ JavaScript -->
                                    </select>
                                    <small class="text-muted">
                                        <a href="units-simple.html" target="_blank">إدارة وحدات القياس</a>
                                    </small>

                                    <!-- عرض تفاصيل الوحدة المختارة -->
                                    <div id="selectedUnitDetails" class="mt-3" style="display: none;">
                                        <div class="card border-info">
                                            <div class="card-body">
                                                <h6 class="card-title">تفاصيل الوحدة المختارة:</h6>
                                                <div class="alert alert-info">
                                                    <small><i class="fas fa-info-circle"></i> سعر الوحدة الكبرى يُحسب تلقائياً من سعر الشراء المدخل أعلاه</small>
                                                </div>
                                                <div class="row">
                                                    <div class="col-md-4">
                                                        <label class="form-label">سعر الوحدة الكبرى</label>
                                                        <input type="number" id="majorUnitPrice" class="form-control" step="0.01" readonly style="background-color: #e9ecef;">
                                                        <small id="majorUnitLabel" class="text-success">محسوب من سعر الشراء</small>
                                                    </div>
                                                    <div class="col-md-4">
                                                        <label class="form-label">سعر الوحدة المتوسطة</label>
                                                        <input type="number" id="mediumUnitPrice" class="form-control" step="0.01" readonly>
                                                        <small id="mediumUnitLabel" class="text-muted">محسوب تلقائياً</small>
                                                    </div>
                                                    <div class="col-md-4">
                                                        <label class="form-label">سعر الوحدة الصغرى</label>
                                                        <input type="number" id="minorUnitPrice" class="form-control" step="0.01" readonly>
                                                        <small id="minorUnitLabel" class="text-muted">محسوب تلقائياً</small>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <!-- وحدة المنتجات (قطعة فقط) -->
                                <div id="productUnitSection" class="mb-3" style="display: none;">
                                    <label class="form-label">وحدة القياس</label>
                                    <input type="text" class="form-control" value="قطعة" readonly>
                                    <small class="text-muted">المنتجات تُقاس بالقطع فقط</small>
                                </div>

                                <!-- وحدة الخدمات -->
                                <div id="serviceUnitSection" class="mb-3" style="display: none;">
                                    <label class="form-label">وحدة القياس</label>
                                    <select id="serviceUnit" class="form-control">
                                        <option value="hour">ساعة</option>
                                        <option value="day">يوم</option>
                                        <option value="service">خدمة</option>
                                    </select>
                                </div>

                                <!-- الوحدة القديمة (مخفية) -->
                                <input type="hidden" id="itemUnit" value="piece">
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-md-4">
                                <div class="mb-3">
                                    <label class="form-label">سعر الشراء</label>
                                    <input type="number" id="purchasePrice" class="form-control" value="0" step="0.01" onchange="updatePricesFromPurchasePrice()">
                                    <small class="text-muted">سعر الوحدة المتوسطة</small>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="mb-3">
                                    <label class="form-label">سعر البيع</label>
                                    <input type="number" id="salePrice" class="form-control" value="0" step="0.01">
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="mb-3">
                                    <label class="form-label">المخزون الحالي</label>
                                    <input type="number" id="currentStock" class="form-control" value="0" step="0.01">
                                </div>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">الباركود</label>
                                    <input type="text" id="barcode" class="form-control">
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">الحد الأدنى للمخزون</label>
                                    <input type="number" id="minStock" class="form-control" value="0" step="0.01">
                                </div>
                            </div>
                        </div>

                        <!-- وصفة المنتج -->
                        <div id="productRecipeSection" class="mb-3" style="display: none;">
                            <div class="card">
                                <div class="card-header d-flex justify-content-between">
                                    <h6>وصفة المنتج (الخامات المطلوبة)</h6>
                                    <button type="button" class="btn btn-sm btn-success" onclick="addRecipeItem()">
                                        <i class="fas fa-plus"></i> إضافة خامة
                                    </button>
                                </div>
                                <div class="card-body">
                                    <div class="table-responsive">
                                        <table class="table table-sm">
                                            <thead>
                                                <tr>
                                                    <th>الخامة</th>
                                                    <th>الكمية</th>
                                                    <th>الوحدة</th>
                                                    <th>السعر</th>
                                                    <th>التكلفة</th>
                                                    <th>حذف</th>
                                                </tr>
                                            </thead>
                                            <tbody id="recipeItemsTable">
                                                <!-- سيتم ملؤها بـ JavaScript -->
                                            </tbody>
                                        </table>
                                    </div>
                                    <div class="row mt-3">
                                        <div class="col-md-8">
                                            <div class="row">
                                                <div class="col-md-6">
                                                    <label class="form-label">تكلفة العمالة (للوحدة)</label>
                                                    <input type="number" id="laborCostPerUnit" class="form-control" step="0.01" value="0" onchange="calculateProductCost()">
                                                </div>
                                                <div class="col-md-6">
                                                    <label class="form-label">تكاليف أخرى (للوحدة)</label>
                                                    <input type="number" id="otherCostsPerUnit" class="form-control" step="0.01" value="0" onchange="calculateProductCost()">
                                                </div>
                                            </div>
                                        </div>
                                        <div class="col-md-4">
                                            <div class="card border-info">
                                                <div class="card-body">
                                                    <div class="d-flex justify-content-between">
                                                        <span>تكلفة الخامات:</span>
                                                        <span id="materialsCostDisplay">0.00 ر.ي</span>
                                                    </div>
                                                    <div class="d-flex justify-content-between">
                                                        <span>تكلفة العمالة:</span>
                                                        <span id="laborCostDisplay">0.00 ر.ي</span>
                                                    </div>
                                                    <div class="d-flex justify-content-between">
                                                        <span>تكاليف أخرى:</span>
                                                        <span id="otherCostsDisplay">0.00 ر.ي</span>
                                                    </div>
                                                    <hr>
                                                    <div class="d-flex justify-content-between fw-bold">
                                                        <span>إجمالي التكلفة:</span>
                                                        <span id="totalCostDisplay">0.00 ر.ي</span>
                                                    </div>
                                                    <div class="d-flex justify-content-between">
                                                        <span>هامش الربح:</span>
                                                        <input type="number" id="profitMargin" class="form-control form-control-sm" step="0.01" value="0" onchange="calculateProductCost()">
                                                    </div>
                                                    <div class="d-flex justify-content-between fw-bold text-success">
                                                        <span>سعر البيع المقترح:</span>
                                                        <span id="suggestedSalePrice">0.00 ر.ي</span>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="mb-3">
                            <label class="form-label">ملاحظات</label>
                            <textarea id="description" class="form-control" rows="3"></textarea>
                        </div>
                        <div class="form-check mb-3">
                            <input type="checkbox" id="isActive" class="form-check-input" checked>
                            <label class="form-check-label">منتج نشط</label>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                    <button type="button" class="btn btn-primary" onclick="saveItem()">حفظ</button>
                </div>
            </div>
        </div>
    </div>

    <!-- Success/Error Messages -->
    <div id="messages"></div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // البيانات المحلية
        let items = [];
        let nextId = 1;
        let currentFilter = 'all';
        let currentRecipeItems = [];
        let availableUnits = [];

        // تحميل البيانات عند بدء الصفحة
        document.addEventListener('DOMContentLoaded', function() {
            loadUnits();
            loadItems();
        });

        // تحميل وحدات القياس
        function loadUnits() {
            try {
                const savedUnits = localStorage.getItem('anw_units');
                if (savedUnits && savedUnits !== 'null') {
                    availableUnits = JSON.parse(savedUnits).filter(u => u.active);
                } else {
                    availableUnits = [];
                }
                loadUnitOptions();
            } catch (error) {
                console.error('خطأ في تحميل وحدات القياس:', error);
                availableUnits = [];
            }
        }

        // تحميل خيارات وحدات القياس
        function loadUnitOptions() {
            const select = document.getElementById('materialUnitGroup');

            let html = '<option value="">اختر وحدة القياس</option>';

            availableUnits.forEach(unit => {
                html += `<option value="${unit.id}">${unit.groupName} (${unit.majorUnit} → ${unit.mediumUnit} → ${unit.minorUnit})</option>`;
            });

            select.innerHTML = html;
        }

        // تحميل تفاصيل الوحدة المختارة
        function loadSelectedUnit() {
            const unitId = parseInt(document.getElementById('materialUnitGroup').value);
            const detailsDiv = document.getElementById('selectedUnitDetails');

            if (!unitId) {
                detailsDiv.style.display = 'none';
                return;
            }

            const unit = availableUnits.find(u => u.id === unitId);
            if (unit) {
                document.getElementById('majorUnitLabel').textContent = unit.majorUnit + ' (محسوب من سعر الشراء)';
                document.getElementById('mediumUnitLabel').textContent = unit.mediumUnit + ' (محسوب تلقائياً)';
                document.getElementById('minorUnitLabel').textContent = unit.minorUnit + ' (محسوب تلقائياً)';

                // تنظيف الأسعار
                document.getElementById('majorUnitPrice').value = '';
                document.getElementById('mediumUnitPrice').value = '';
                document.getElementById('minorUnitPrice').value = '';

                detailsDiv.style.display = 'block';

                // إعادة حساب الأسعار إذا كان هناك سعر شراء
                updatePricesFromPurchasePrice();
            }
        }

        // تحميل المنتجات من localStorage
        function loadItems() {
            try {
                const savedItems = localStorage.getItem('anw_items');
                if (savedItems && savedItems !== 'null') {
                    items = JSON.parse(savedItems);
                    if (items.length > 0) {
                        nextId = Math.max(...items.map(i => i.id), 0) + 1;
                    }
                } else {
                    // إنشاء منتجات افتراضية مع نظام الوحدات الجديد
                    items = [
                        {
                            id: 1,
                            code: 'M001',
                            name: 'دقيق أبيض',
                            type: 'material',
                            unit: 'kg',
                            purchasePrice: 75, // سعر الكيلوجرام (الوحدة المتوسطة)
                            salePrice: 85,
                            stock: 500,
                            minStock: 100,
                            barcode: '2345678901234',
                            description: 'دقيق أبيض فاخر',
                            active: true,
                            unitGroupId: 1, // وحدات الوزن الثقيل (كيس 50 كجم)
                            unitPrices: {
                                majorUnitPrice: 3750, // 75 × 50 = 3750 (سعر الكيس)
                                mediumUnitPrice: 75,   // سعر الكيلوجرام
                                minorUnitPrice: 0.075  // 75 ÷ 1000 = 0.075 (سعر الجرام)
                            }
                        },
                        {
                            id: 2,
                            code: 'M002',
                            name: 'سكر أبيض',
                            type: 'material',
                            unit: 'kg',
                            purchasePrice: 120, // سعر الكيلوجرام (الوحدة المتوسطة)
                            salePrice: 140,
                            stock: 200,
                            minStock: 50,
                            barcode: '3456789012345',
                            description: 'سكر أبيض ناعم',
                            active: true,
                            unitGroupId: 2, // وحدات الوزن الخفيف (كيس 25 كجم)
                            unitPrices: {
                                majorUnitPrice: 3000, // 120 × 25 = 3000 (سعر الكيس)
                                mediumUnitPrice: 120,  // سعر الكيلوجرام
                                minorUnitPrice: 0.12   // 120 ÷ 1000 = 0.12 (سعر الجرام)
                            }
                        },
                        {
                            id: 3,
                            code: 'P001',
                            name: 'خبز عربي',
                            type: 'product',
                            unit: 'piece',
                            purchasePrice: 0,
                            salePrice: 1.75,
                            stock: 100,
                            minStock: 20,
                            barcode: '1234567890123',
                            description: 'خبز عربي طازج',
                            active: true,
                            recipe: [
                                {materialId: 1, quantity: 0.5, unit: 'medium', unitPrice: 75, cost: 37.5, unitName: 'كيلوجرام'},
                                {materialId: 2, quantity: 50, unit: 'minor', unitPrice: 0.12, cost: 6, unitName: 'جرام'}
                            ],
                            laborCostPerUnit: 0.25,
                            otherCostsPerUnit: 0.1,
                            profitMargin: 1.1,
                            totalCost: 43.85
                        }
                    ];
                    nextId = 4;
                    saveItems();
                }
                displayItems();
            } catch (error) {
                console.error('خطأ في تحميل المنتجات:', error);
                showMessage('خطأ في تحميل البيانات', 'danger');
            }
        }

        // حفظ المنتجات في localStorage
        function saveItems() {
            localStorage.setItem('anw_items', JSON.stringify(items));
        }

        // عرض المنتجات
        function displayItems() {
            const tbody = document.getElementById('itemsTableBody');
            let filteredItems = items;

            // تطبيق الفلتر
            if (currentFilter !== 'all') {
                filteredItems = items.filter(item => {
                    if (currentFilter === 'products') return item.type === 'product';
                    if (currentFilter === 'materials') return item.type === 'material';
                    if (currentFilter === 'services') return item.type === 'service';
                    return true;
                });
            }

            if (filteredItems.length === 0) {
                tbody.innerHTML = `
                    <tr>
                        <td colspan="9" class="text-center text-muted">
                            لا توجد منتجات
                            <br>
                            <button class="btn btn-primary mt-2" onclick="addItem()">إضافة منتج جديد</button>
                        </td>
                    </tr>
                `;
                return;
            }

            let html = '';
            filteredItems.forEach(item => {
                const typeLabel = getTypeLabel(item.type);
                const statusBadge = item.active ? 
                    '<span class="badge bg-success">نشط</span>' : 
                    '<span class="badge bg-secondary">غير نشط</span>';
                
                const stockClass = item.stock <= item.minStock ? 'text-danger fw-bold' : '';

                html += `
                    <tr>
                        <td>${item.code}</td>
                        <td>${item.name}</td>
                        <td><span class="badge bg-info">${typeLabel}</span></td>
                        <td>${getUnitLabel(item.unit)}</td>
                        <td>${item.purchasePrice.toLocaleString()} ر.ي</td>
                        <td>${item.salePrice.toLocaleString()} ر.ي</td>
                        <td class="${stockClass}">${item.stock.toLocaleString()}</td>
                        <td>${statusBadge}</td>
                        <td>
                            <button class="btn btn-sm btn-warning" onclick="editItem(${item.id})">
                                <i class="fas fa-edit"></i>
                            </button>
                            <button class="btn btn-sm btn-danger" onclick="deleteItem(${item.id})">
                                <i class="fas fa-trash"></i>
                            </button>
                        </td>
                    </tr>
                `;
            });

            tbody.innerHTML = html;
        }

        // الحصول على تسمية النوع
        function getTypeLabel(type) {
            const types = {
                'product': 'منتج',
                'material': 'خامة',
                'service': 'خدمة'
            };
            return types[type] || type;
        }

        // الحصول على تسمية الوحدة
        function getUnitLabel(unit) {
            const units = {
                'piece': 'قطعة',
                'kg': 'كيلوجرام',
                'bag': 'كيس',
                'box': 'صندوق',
                'liter': 'لتر'
            };
            return units[unit] || unit;
        }

        // فلترة المنتجات
        function filterItems(filter) {
            currentFilter = filter;
            
            // تحديث التبويبات
            document.querySelectorAll('.nav-link').forEach(link => {
                link.classList.remove('active');
            });
            event.target.classList.add('active');
            
            displayItems();
        }

        // البحث في المنتجات
        function searchItems() {
            const searchTerm = document.getElementById('searchInput').value.toLowerCase();
            const tbody = document.getElementById('itemsTableBody');
            
            let filteredItems = items.filter(item => {
                return item.name.toLowerCase().includes(searchTerm) ||
                       item.code.toLowerCase().includes(searchTerm) ||
                       (item.barcode && item.barcode.toLowerCase().includes(searchTerm));
            });

            // تطبيق فلتر النوع أيضاً
            if (currentFilter !== 'all') {
                filteredItems = filteredItems.filter(item => {
                    if (currentFilter === 'products') return item.type === 'product';
                    if (currentFilter === 'materials') return item.type === 'material';
                    if (currentFilter === 'services') return item.type === 'service';
                    return true;
                });
            }

            if (filteredItems.length === 0) {
                tbody.innerHTML = `
                    <tr>
                        <td colspan="9" class="text-center text-muted">لا توجد نتائج للبحث</td>
                    </tr>
                `;
                return;
            }

            let html = '';
            filteredItems.forEach(item => {
                const typeLabel = getTypeLabel(item.type);
                const statusBadge = item.active ? 
                    '<span class="badge bg-success">نشط</span>' : 
                    '<span class="badge bg-secondary">غير نشط</span>';
                
                const stockClass = item.stock <= item.minStock ? 'text-danger fw-bold' : '';

                html += `
                    <tr>
                        <td>${item.code}</td>
                        <td>${item.name}</td>
                        <td><span class="badge bg-info">${typeLabel}</span></td>
                        <td>${getUnitLabel(item.unit)}</td>
                        <td>${item.purchasePrice.toLocaleString()} ر.ي</td>
                        <td>${item.salePrice.toLocaleString()} ر.ي</td>
                        <td class="${stockClass}">${item.stock.toLocaleString()}</td>
                        <td>${statusBadge}</td>
                        <td>
                            <button class="btn btn-sm btn-warning" onclick="editItem(${item.id})">
                                <i class="fas fa-edit"></i>
                            </button>
                            <button class="btn btn-sm btn-danger" onclick="deleteItem(${item.id})">
                                <i class="fas fa-trash"></i>
                            </button>
                        </td>
                    </tr>
                `;
            });

            tbody.innerHTML = html;
        }

        // إضافة منتج جديد
        function addItem() {
            clearForm();
            document.querySelector('.modal-title').textContent = 'إضافة منتج جديد';
            new bootstrap.Modal(document.getElementById('itemModal')).show();
        }

        // تحرير منتج
        function editItem(itemId) {
            const item = items.find(i => i.id === itemId);
            if (item) {
                document.getElementById('itemCode').value = item.code;
                document.getElementById('itemName').value = item.name;
                document.getElementById('itemType').value = item.type;
                document.getElementById('itemUnit').value = item.unit;
                document.getElementById('purchasePrice').value = item.purchasePrice;
                document.getElementById('salePrice').value = item.salePrice;
                document.getElementById('currentStock').value = item.stock;
                document.getElementById('minStock').value = item.minStock;
                document.getElementById('barcode').value = item.barcode || '';
                document.getElementById('description').value = item.description || '';
                document.getElementById('isActive').checked = item.active;

                // تحديث النموذج حسب النوع
                updateFormByType();

                // تحميل بيانات الوحدة للخامات
                if (item.type === 'material' && item.unitGroupId) {
                    document.getElementById('materialUnitGroup').value = item.unitGroupId;
                    loadSelectedUnit();

                    // تحديث الأسعار
                    if (item.unitPrices) {
                        document.getElementById('majorUnitPrice').value = item.unitPrices.majorUnitPrice;
                        document.getElementById('mediumUnitPrice').value = item.unitPrices.mediumUnitPrice;
                        document.getElementById('minorUnitPrice').value = item.unitPrices.minorUnitPrice;
                    }
                }

                // تحميل الوصفة للمنتجات
                if (item.type === 'product' && item.recipe) {
                    currentRecipeItems = [...item.recipe];
                    displayRecipeItems();

                    if (item.laborCostPerUnit) document.getElementById('laborCostPerUnit').value = item.laborCostPerUnit;
                    if (item.otherCostsPerUnit) document.getElementById('otherCostsPerUnit').value = item.otherCostsPerUnit;
                    if (item.profitMargin) document.getElementById('profitMargin').value = item.profitMargin;

                    calculateProductCost();
                }

                document.getElementById('itemForm').dataset.editId = itemId;
                document.querySelector('.modal-title').textContent = 'تحرير المنتج';

                new bootstrap.Modal(document.getElementById('itemModal')).show();
            }
        }

        // عرض عناصر الوصفة
        function displayRecipeItems() {
            const tbody = document.getElementById('recipeItemsTable');
            let html = '';

            currentRecipeItems.forEach((recipeItem, index) => {
                const material = items.find(i => i.id === recipeItem.materialId);

                html += `
                    <tr data-index="${index}">
                        <td>${material ? material.name : 'غير محدد'}</td>
                        <td>${recipeItem.quantity}</td>
                        <td>${recipeItem.unitName}</td>
                        <td>${recipeItem.unitPrice.toLocaleString()} ر.ي</td>
                        <td>${recipeItem.cost.toLocaleString()} ر.ي</td>
                        <td>
                            <button type="button" class="btn btn-sm btn-danger" onclick="removeRecipeItem(${index})">
                                <i class="fas fa-trash"></i>
                            </button>
                        </td>
                    </tr>
                `;
            });

            tbody.innerHTML = html;
        }

        // حذف منتج
        function deleteItem(itemId) {
            if (confirm('هل أنت متأكد من حذف هذا المنتج؟')) {
                items = items.filter(i => i.id !== itemId);
                saveItems();
                displayItems();
                showMessage('تم حذف المنتج بنجاح', 'success');
            }
        }

        // حفظ المنتج
        function saveItem() {
            const form = document.getElementById('itemForm');
            const editId = form.dataset.editId;

            const itemData = {
                code: document.getElementById('itemCode').value,
                name: document.getElementById('itemName').value,
                type: document.getElementById('itemType').value,
                unit: document.getElementById('itemUnit').value,
                purchasePrice: parseFloat(document.getElementById('purchasePrice').value) || 0,
                salePrice: parseFloat(document.getElementById('salePrice').value) || 0,
                stock: parseFloat(document.getElementById('currentStock').value) || 0,
                minStock: parseFloat(document.getElementById('minStock').value) || 0,
                barcode: document.getElementById('barcode').value,
                description: document.getElementById('description').value,
                active: document.getElementById('isActive').checked
            };

            // إضافة بيانات الوحدات للخامات
            if (itemData.type === 'material') {
                const unitGroupId = parseInt(document.getElementById('materialUnitGroup').value);
                const purchasePrice = parseFloat(document.getElementById('purchasePrice').value) || 0;

                if (!unitGroupId) {
                    showMessage('يرجى اختيار وحدة القياس للخامة', 'danger');
                    return;
                }

                if (!purchasePrice) {
                    showMessage('يرجى إدخال سعر الشراء للخامة', 'danger');
                    return;
                }

                const unit = availableUnits.find(u => u.id === unitGroupId);
                if (unit) {
                    // سعر الشراء هو سعر الوحدة المتوسطة
                    const majorUnitPrice = purchasePrice * unit.majorToMedium;
                    const minorUnitPrice = purchasePrice / unit.mediumToMinor;

                    itemData.unitGroupId = unitGroupId;
                    itemData.unitPrices = {
                        majorUnitPrice: majorUnitPrice,
                        mediumUnitPrice: purchasePrice,
                        minorUnitPrice: minorUnitPrice
                    };
                }
            }

            // إضافة وصفة المنتج
            if (itemData.type === 'product' && currentRecipeItems.length > 0) {
                itemData.recipe = currentRecipeItems.filter(item => item.materialId);
                itemData.laborCostPerUnit = parseFloat(document.getElementById('laborCostPerUnit').value) || 0;
                itemData.otherCostsPerUnit = parseFloat(document.getElementById('otherCostsPerUnit').value) || 0;
                itemData.profitMargin = parseFloat(document.getElementById('profitMargin').value) || 0;

                // حساب التكلفة الإجمالية
                const materialsCost = currentRecipeItems.reduce((sum, item) => sum + item.cost, 0);
                itemData.totalCost = materialsCost + itemData.laborCostPerUnit + itemData.otherCostsPerUnit;
            }

            // إضافة وحدة الخدمة
            if (itemData.type === 'service') {
                itemData.unit = document.getElementById('serviceUnit').value;
            }

            // التحقق من البيانات
            if (!itemData.code || !itemData.name || !itemData.type) {
                showMessage('يرجى ملء جميع الحقول المطلوبة', 'danger');
                return;
            }

            // التحقق من عدم تكرار الكود
            const existingItem = items.find(i => i.code === itemData.code && i.id != editId);
            if (existingItem) {
                showMessage('كود المنتج موجود بالفعل', 'danger');
                return;
            }

            if (editId) {
                // تحديث منتج موجود
                const itemIndex = items.findIndex(i => i.id == editId);
                items[itemIndex] = { ...items[itemIndex], ...itemData };
                showMessage('تم تحديث المنتج بنجاح', 'success');
            } else {
                // إضافة منتج جديد
                itemData.id = nextId++;
                items.push(itemData);
                showMessage('تم إضافة المنتج بنجاح', 'success');
            }

            saveItems();
            displayItems();
            bootstrap.Modal.getInstance(document.getElementById('itemModal')).hide();
        }

        // تحديث النموذج حسب نوع الصنف
        function updateFormByType() {
            const itemType = document.getElementById('itemType').value;

            // إخفاء جميع الأقسام
            document.getElementById('materialUnitsSection').style.display = 'none';
            document.getElementById('productUnitSection').style.display = 'none';
            document.getElementById('serviceUnitSection').style.display = 'none';
            document.getElementById('productRecipeSection').style.display = 'none';
            document.getElementById('selectedUnitDetails').style.display = 'none';

            // إظهار القسم المناسب
            if (itemType === 'material') {
                document.getElementById('materialUnitsSection').style.display = 'block';
                document.getElementById('itemUnit').value = 'kg'; // افتراضي للخامات
                loadUnitOptions(); // تحديث خيارات الوحدات

                // تحديث تسمية سعر الشراء للخامات
                const purchasePriceLabel = document.querySelector('label[for="purchasePrice"]');
                if (purchasePriceLabel) {
                    purchasePriceLabel.innerHTML = 'سعر الشراء *';
                }
                const purchasePriceSmall = document.querySelector('#purchasePrice + small');
                if (purchasePriceSmall) {
                    purchasePriceSmall.textContent = 'سعر الوحدة المتوسطة';
                }
            } else if (itemType === 'product') {
                document.getElementById('productUnitSection').style.display = 'block';
                document.getElementById('productRecipeSection').style.display = 'block';
                document.getElementById('itemUnit').value = 'piece'; // المنتجات بالقطع فقط

                // تحديث تسمية سعر الشراء للمنتجات
                const purchasePriceLabel = document.querySelector('label[for="purchasePrice"]');
                if (purchasePriceLabel) {
                    purchasePriceLabel.innerHTML = 'تكلفة الإنتاج';
                }
                const purchasePriceSmall = document.querySelector('#purchasePrice + small');
                if (purchasePriceSmall) {
                    purchasePriceSmall.textContent = 'محسوبة من الوصفة';
                }
            } else if (itemType === 'service') {
                document.getElementById('serviceUnitSection').style.display = 'block';
                document.getElementById('itemUnit').value = 'service'; // افتراضي للخدمات

                // تحديث تسمية سعر الشراء للخدمات
                const purchasePriceLabel = document.querySelector('label[for="purchasePrice"]');
                if (purchasePriceLabel) {
                    purchasePriceLabel.innerHTML = 'تكلفة الخدمة';
                }
                const purchasePriceSmall = document.querySelector('#purchasePrice + small');
                if (purchasePriceSmall) {
                    purchasePriceSmall.textContent = 'تكلفة تقديم الخدمة';
                }
            }
        }

        // تحديث الأسعار من سعر الشراء
        function updatePricesFromPurchasePrice() {
            const unitId = parseInt(document.getElementById('materialUnitGroup').value);
            const purchasePrice = parseFloat(document.getElementById('purchasePrice').value) || 0;

            if (!unitId || !purchasePrice) {
                // تنظيف الأسعار إذا لم يكن هناك سعر شراء أو وحدة
                document.getElementById('majorUnitPrice').value = '';
                document.getElementById('mediumUnitPrice').value = '';
                document.getElementById('minorUnitPrice').value = '';
                return;
            }

            const unit = availableUnits.find(u => u.id === unitId);
            if (unit) {
                // سعر الشراء هو سعر الوحدة المتوسطة
                // نحسب سعر الوحدة الكبرى من سعر الوحدة المتوسطة
                const majorPrice = purchasePrice * unit.majorToMedium;
                const minorPrice = purchasePrice / unit.mediumToMinor;

                document.getElementById('majorUnitPrice').value = majorPrice.toFixed(2);
                document.getElementById('mediumUnitPrice').value = purchasePrice.toFixed(2);
                document.getElementById('minorUnitPrice').value = minorPrice.toFixed(6);
            }
        }

        // حساب أسعار الوحدات (للاستخدام الداخلي)
        function calculateUnitPrices() {
            updatePricesFromPurchasePrice();
        }

        // إضافة خامة للوصفة
        function addRecipeItem() {
            const tbody = document.getElementById('recipeItemsTable');
            const rowIndex = currentRecipeItems.length;

            // فلترة الخامات فقط
            const materials = items.filter(item => item.type === 'material');

            const row = `
                <tr data-index="${rowIndex}">
                    <td>
                        <select class="form-control material-select" onchange="updateRecipeItemPrice(${rowIndex})">
                            <option value="">اختر الخامة</option>
                            ${materials.map(material => `<option value="${material.id}">${material.name}</option>`).join('')}
                        </select>
                    </td>
                    <td>
                        <input type="number" class="form-control quantity-input" value="1" min="0.01" step="0.01" onchange="calculateRecipeItemCost(${rowIndex})">
                    </td>
                    <td>
                        <select class="form-control unit-select" onchange="calculateRecipeItemCost(${rowIndex})">
                            <option value="">اختر الوحدة</option>
                        </select>
                    </td>
                    <td>
                        <span class="unit-price">0.00 ر.ي</span>
                    </td>
                    <td>
                        <span class="item-cost">0.00 ر.ي</span>
                    </td>
                    <td>
                        <button type="button" class="btn btn-sm btn-danger" onclick="removeRecipeItem(${rowIndex})">
                            <i class="fas fa-trash"></i>
                        </button>
                    </td>
                </tr>
            `;

            tbody.insertAdjacentHTML('beforeend', row);
            currentRecipeItems.push({materialId: null, quantity: 1, unit: '', unitPrice: 0, cost: 0});
        }

        // تحديث سعر خامة الوصفة
        function updateRecipeItemPrice(index) {
            const row = document.querySelector(`tr[data-index="${index}"]`);
            const materialSelect = row.querySelector('.material-select');
            const unitSelect = row.querySelector('.unit-select');

            if (materialSelect.value) {
                const material = items.find(i => i.id == materialSelect.value);
                if (material && material.unitGroupId) {
                    const unit = availableUnits.find(u => u.id === material.unitGroupId);
                    if (unit && material.unitPrices) {
                        // تحديث خيارات الوحدات مع الأسعار
                        let unitsHtml = '<option value="">اختر الوحدة</option>';

                        // الوحدة الكبرى
                        unitsHtml += `<option value="major" data-price="${material.unitPrices.majorUnitPrice}">
                            ${unit.majorUnit} - ${material.unitPrices.majorUnitPrice.toLocaleString()} ر.ي
                        </option>`;

                        // الوحدة المتوسطة
                        unitsHtml += `<option value="medium" data-price="${material.unitPrices.mediumUnitPrice}">
                            ${unit.mediumUnit} - ${material.unitPrices.mediumUnitPrice.toLocaleString()} ر.ي
                        </option>`;

                        // الوحدة الصغرى
                        unitsHtml += `<option value="minor" data-price="${material.unitPrices.minorUnitPrice}">
                            ${unit.minorUnit} - ${material.unitPrices.minorUnitPrice.toLocaleString()} ر.ي
                        </option>`;

                        unitSelect.innerHTML = unitsHtml;
                        currentRecipeItems[index].materialId = parseInt(materialSelect.value);
                    }
                }
            }
        }

        // حساب تكلفة خامة الوصفة
        function calculateRecipeItemCost(index) {
            const row = document.querySelector(`tr[data-index="${index}"]`);
            const materialSelect = row.querySelector('.material-select');
            const unitSelect = row.querySelector('.unit-select');
            const quantityInput = row.querySelector('.quantity-input');
            const unitPriceSpan = row.querySelector('.unit-price');
            const itemCostSpan = row.querySelector('.item-cost');

            if (materialSelect.value && unitSelect.value) {
                const quantity = parseFloat(quantityInput.value) || 0;

                // الحصول على السعر من الخيار المحدد
                const selectedOption = unitSelect.options[unitSelect.selectedIndex];
                const unitPrice = parseFloat(selectedOption.getAttribute('data-price')) || 0;

                const cost = quantity * unitPrice;

                unitPriceSpan.textContent = unitPrice.toLocaleString() + ' ر.ي';
                itemCostSpan.textContent = cost.toLocaleString() + ' ر.ي';

                // تحديث بيانات الوصفة
                currentRecipeItems[index].materialId = parseInt(materialSelect.value);
                currentRecipeItems[index].quantity = quantity;
                currentRecipeItems[index].unit = unitSelect.value;
                currentRecipeItems[index].unitPrice = unitPrice;
                currentRecipeItems[index].cost = cost;

                // الحصول على اسم الوحدة للعرض
                const material = items.find(i => i.id == materialSelect.value);
                if (material && material.unitGroupId) {
                    const unit = availableUnits.find(u => u.id === material.unitGroupId);
                    if (unit) {
                        switch(unitSelect.value) {
                            case 'major':
                                currentRecipeItems[index].unitName = unit.majorUnit;
                                break;
                            case 'medium':
                                currentRecipeItems[index].unitName = unit.mediumUnit;
                                break;
                            case 'minor':
                                currentRecipeItems[index].unitName = unit.minorUnit;
                                break;
                        }
                    }
                }

                calculateProductCost();
            }
        }

        // حذف خامة من الوصفة
        function removeRecipeItem(index) {
            const row = document.querySelector(`tr[data-index="${index}"]`);
            row.remove();
            currentRecipeItems.splice(index, 1);
            calculateProductCost();
        }

        // حساب تكلفة المنتج
        function calculateProductCost() {
            const materialsCost = currentRecipeItems.reduce((sum, item) => sum + item.cost, 0);
            const laborCost = parseFloat(document.getElementById('laborCostPerUnit').value) || 0;
            const otherCosts = parseFloat(document.getElementById('otherCostsPerUnit').value) || 0;
            const profitMargin = parseFloat(document.getElementById('profitMargin').value) || 0;

            const totalCost = materialsCost + laborCost + otherCosts;
            const suggestedPrice = totalCost + profitMargin;

            document.getElementById('materialsCostDisplay').textContent = materialsCost.toLocaleString() + ' ر.ي';
            document.getElementById('laborCostDisplay').textContent = laborCost.toLocaleString() + ' ر.ي';
            document.getElementById('otherCostsDisplay').textContent = otherCosts.toLocaleString() + ' ر.ي';
            document.getElementById('totalCostDisplay').textContent = totalCost.toLocaleString() + ' ر.ي';
            document.getElementById('suggestedSalePrice').textContent = suggestedPrice.toLocaleString() + ' ر.ي';

            // تحديث سعر البيع تلقائياً
            document.getElementById('salePrice').value = suggestedPrice.toFixed(2);
        }

        // تنظيف النموذج
        function clearForm() {
            document.getElementById('itemForm').reset();
            document.getElementById('itemForm').removeAttribute('data-edit-id');
            document.getElementById('purchasePrice').value = '0';
            document.getElementById('salePrice').value = '0';
            document.getElementById('currentStock').value = '0';
            document.getElementById('minStock').value = '0';
            document.getElementById('isActive').checked = true;

            // إخفاء جميع الأقسام
            document.getElementById('materialUnitsSection').style.display = 'none';
            document.getElementById('productUnitSection').style.display = 'none';
            document.getElementById('serviceUnitSection').style.display = 'none';
            document.getElementById('productRecipeSection').style.display = 'none';
            document.getElementById('selectedUnitDetails').style.display = 'none';

            // تنظيف الوصفة
            document.getElementById('recipeItemsTable').innerHTML = '';
            currentRecipeItems = [];
            calculateProductCost();

            // إعادة تحميل خيارات الوحدات
            loadUnitOptions();
        }

        // تصدير المنتجات
        function exportItems() {
            const csvContent = "data:text/csv;charset=utf-8," 
                + "الكود,الاسم,النوع,الوحدة,سعر الشراء,سعر البيع,المخزون,الحد الأدنى,الباركود,الحالة\n"
                + items.map(item => 
                    `${item.code},${item.name},${getTypeLabel(item.type)},${getUnitLabel(item.unit)},${item.purchasePrice},${item.salePrice},${item.stock},${item.minStock},${item.barcode || ''},${item.active ? 'نشط' : 'غير نشط'}`
                ).join("\n");

            const encodedUri = encodeURI(csvContent);
            const link = document.createElement("a");
            link.setAttribute("href", encodedUri);
            link.setAttribute("download", "products.csv");
            document.body.appendChild(link);
            link.click();
            document.body.removeChild(link);
            
            showMessage('تم تصدير المنتجات بنجاح', 'success');
        }

        // عرض رسالة
        function showMessage(message, type) {
            const messagesDiv = document.getElementById('messages');
            const alertHtml = `
                <div class="alert alert-${type} alert-dismissible fade show position-fixed" 
                     style="top: 20px; left: 20px; z-index: 9999; min-width: 300px;">
                    ${message}
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
            `;
            messagesDiv.innerHTML = alertHtml;
            
            setTimeout(() => {
                const alert = messagesDiv.querySelector('.alert');
                if (alert) {
                    bootstrap.Alert.getOrCreateInstance(alert).close();
                }
            }, 3000);
        }

        console.log('✅ تم تحميل نظام المنتجات المبسط بنجاح');
    </script>

    <style>
        .sidebar {
            height: 100vh;
            position: fixed;
            width: 25%;
        }
        .nav-link.active {
            background-color: rgba(255,255,255,0.1);
            border-radius: 5px;
        }
        .table th {
            background-color: #343a40;
            color: white;
        }
    </style>
</body>
</html>
