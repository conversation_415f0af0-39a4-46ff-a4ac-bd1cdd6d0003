using System.ComponentModel.DataAnnotations;

namespace ANWBakerySystem.Models
{
    /// <summary>
    /// DTO لعرض بيانات الطرف
    /// Party Data Transfer Object for display
    /// </summary>
    public class PartyDto
    {
        public int PartyId { get; set; }
        public string PartyCode { get; set; } = string.Empty;
        public string PartyName { get; set; } = string.Empty;
        public string? PartyNameEn { get; set; }
        public PartyType PartyType { get; set; }
        public string? ContactPerson { get; set; }
        public string? Phone { get; set; }
        public string? Mobile { get; set; }
        public string? Email { get; set; }
        public string? Address { get; set; }
        public string? City { get; set; }
        public string? Country { get; set; }
        public string? TaxNumber { get; set; }
        public string? CommercialRegister { get; set; }
        public decimal CurrentBalance { get; set; }
        public decimal CreditLimit { get; set; }
        public decimal DiscountPercentage { get; set; }
        public int PaymentTermDays { get; set; }
        public string? Notes { get; set; }
        public bool IsActive { get; set; }
        public DateTime CreatedAt { get; set; }
        public DateTime? UpdatedAt { get; set; }
    }

    /// <summary>
    /// طلب إنشاء طرف جديد
    /// Create Party Request
    /// </summary>
    public class CreatePartyRequest
    {
        [Required(ErrorMessage = "كود الطرف مطلوب")]
        [StringLength(20, ErrorMessage = "كود الطرف يجب أن يكون أقل من 20 حرف")]
        public string PartyCode { get; set; } = string.Empty;

        [Required(ErrorMessage = "اسم الطرف مطلوب")]
        [StringLength(200, ErrorMessage = "اسم الطرف يجب أن يكون أقل من 200 حرف")]
        public string PartyName { get; set; } = string.Empty;

        [StringLength(200, ErrorMessage = "الاسم بالإنجليزية يجب أن يكون أقل من 200 حرف")]
        public string? PartyNameEn { get; set; }

        [Required(ErrorMessage = "نوع الطرف مطلوب")]
        public PartyType PartyType { get; set; }

        [StringLength(100, ErrorMessage = "اسم الشخص المسؤول يجب أن يكون أقل من 100 حرف")]
        public string? ContactPerson { get; set; }

        [StringLength(20, ErrorMessage = "رقم الهاتف يجب أن يكون أقل من 20 حرف")]
        [Phone(ErrorMessage = "رقم الهاتف غير صالح")]
        public string? Phone { get; set; }

        [StringLength(20, ErrorMessage = "رقم الجوال يجب أن يكون أقل من 20 حرف")]
        [Phone(ErrorMessage = "رقم الجوال غير صالح")]
        public string? Mobile { get; set; }

        [StringLength(100, ErrorMessage = "البريد الإلكتروني يجب أن يكون أقل من 100 حرف")]
        [EmailAddress(ErrorMessage = "البريد الإلكتروني غير صالح")]
        public string? Email { get; set; }

        [StringLength(500, ErrorMessage = "العنوان يجب أن يكون أقل من 500 حرف")]
        public string? Address { get; set; }

        [StringLength(100, ErrorMessage = "المدينة يجب أن تكون أقل من 100 حرف")]
        public string? City { get; set; }

        [StringLength(100, ErrorMessage = "البلد يجب أن يكون أقل من 100 حرف")]
        public string? Country { get; set; }

        [StringLength(50, ErrorMessage = "الرقم الضريبي يجب أن يكون أقل من 50 حرف")]
        public string? TaxNumber { get; set; }

        [StringLength(50, ErrorMessage = "السجل التجاري يجب أن يكون أقل من 50 حرف")]
        public string? CommercialRegister { get; set; }

        [Range(0, double.MaxValue, ErrorMessage = "حد الائتمان يجب أن يكون أكبر من أو يساوي صفر")]
        public decimal? CreditLimit { get; set; }

        [Range(0, 100, ErrorMessage = "نسبة الخصم يجب أن تكون بين 0 و 100")]
        public decimal? DiscountPercentage { get; set; }

        [Range(0, int.MaxValue, ErrorMessage = "مدة السداد يجب أن تكون أكبر من أو تساوي صفر")]
        public int? PaymentTermDays { get; set; }

        [StringLength(500, ErrorMessage = "الملاحظات يجب أن تكون أقل من 500 حرف")]
        public string? Notes { get; set; }

        public bool? IsActive { get; set; }
    }

    /// <summary>
    /// طلب تحديث طرف موجود
    /// Update Party Request
    /// </summary>
    public class UpdatePartyRequest
    {
        [Required(ErrorMessage = "كود الطرف مطلوب")]
        [StringLength(20, ErrorMessage = "كود الطرف يجب أن يكون أقل من 20 حرف")]
        public string PartyCode { get; set; } = string.Empty;

        [Required(ErrorMessage = "اسم الطرف مطلوب")]
        [StringLength(200, ErrorMessage = "اسم الطرف يجب أن يكون أقل من 200 حرف")]
        public string PartyName { get; set; } = string.Empty;

        [StringLength(200, ErrorMessage = "الاسم بالإنجليزية يجب أن يكون أقل من 200 حرف")]
        public string? PartyNameEn { get; set; }

        [Required(ErrorMessage = "نوع الطرف مطلوب")]
        public PartyType PartyType { get; set; }

        [StringLength(100, ErrorMessage = "اسم الشخص المسؤول يجب أن يكون أقل من 100 حرف")]
        public string? ContactPerson { get; set; }

        [StringLength(20, ErrorMessage = "رقم الهاتف يجب أن يكون أقل من 20 حرف")]
        [Phone(ErrorMessage = "رقم الهاتف غير صالح")]
        public string? Phone { get; set; }

        [StringLength(20, ErrorMessage = "رقم الجوال يجب أن يكون أقل من 20 حرف")]
        [Phone(ErrorMessage = "رقم الجوال غير صالح")]
        public string? Mobile { get; set; }

        [StringLength(100, ErrorMessage = "البريد الإلكتروني يجب أن يكون أقل من 100 حرف")]
        [EmailAddress(ErrorMessage = "البريد الإلكتروني غير صالح")]
        public string? Email { get; set; }

        [StringLength(500, ErrorMessage = "العنوان يجب أن يكون أقل من 500 حرف")]
        public string? Address { get; set; }

        [StringLength(100, ErrorMessage = "المدينة يجب أن تكون أقل من 100 حرف")]
        public string? City { get; set; }

        [StringLength(100, ErrorMessage = "البلد يجب أن يكون أقل من 100 حرف")]
        public string? Country { get; set; }

        [StringLength(50, ErrorMessage = "الرقم الضريبي يجب أن يكون أقل من 50 حرف")]
        public string? TaxNumber { get; set; }

        [StringLength(50, ErrorMessage = "السجل التجاري يجب أن يكون أقل من 50 حرف")]
        public string? CommercialRegister { get; set; }

        [Range(0, double.MaxValue, ErrorMessage = "حد الائتمان يجب أن يكون أكبر من أو يساوي صفر")]
        public decimal? CreditLimit { get; set; }

        [Range(0, 100, ErrorMessage = "نسبة الخصم يجب أن تكون بين 0 و 100")]
        public decimal? DiscountPercentage { get; set; }

        [Range(0, int.MaxValue, ErrorMessage = "مدة السداد يجب أن تكون أكبر من أو تساوي صفر")]
        public int? PaymentTermDays { get; set; }

        [StringLength(500, ErrorMessage = "الملاحظات يجب أن تكون أقل من 500 حرف")]
        public string? Notes { get; set; }

        public bool? IsActive { get; set; }
    }

    /// <summary>
    /// نتيجة عملية الطرف الواحد
    /// Single Party Result
    /// </summary>
    public class PartyResult
    {
        public bool Success { get; set; }
        public string Message { get; set; } = string.Empty;
        public PartyDto? Party { get; set; }
        public List<string>? Errors { get; set; }
    }

    /// <summary>
    /// نتيجة عملية الأطراف المتعددة
    /// Multiple Parties Result
    /// </summary>
    public class PartiesResult
    {
        public bool Success { get; set; }
        public string Message { get; set; } = string.Empty;
        public List<PartyDto>? Parties { get; set; }
        public int TotalCount { get; set; }
        public int Page { get; set; }
        public int PageSize { get; set; }
        public int TotalPages { get; set; }
        public List<string>? Errors { get; set; }
    }

    /// <summary>
    /// DTO لكشف حساب الطرف
    /// Party Statement DTO
    /// </summary>
    public class PartyStatementDto
    {
        public int PartyId { get; set; }
        public string PartyName { get; set; } = string.Empty;
        public decimal OpeningBalance { get; set; }
        public decimal TotalDebits { get; set; }
        public decimal TotalCredits { get; set; }
        public decimal ClosingBalance { get; set; }
        public List<PartyTransactionDto> Transactions { get; set; } = new List<PartyTransactionDto>();
    }

    /// <summary>
    /// DTO لمعاملة الطرف
    /// Party Transaction DTO
    /// </summary>
    public class PartyTransactionDto
    {
        public int TransactionId { get; set; }
        public DateTime TransactionDate { get; set; }
        public string TransactionType { get; set; } = string.Empty;
        public string? ReferenceNumber { get; set; }
        public string Description { get; set; } = string.Empty;
        public decimal DebitAmount { get; set; }
        public decimal CreditAmount { get; set; }
        public decimal Balance { get; set; }
    }
}
