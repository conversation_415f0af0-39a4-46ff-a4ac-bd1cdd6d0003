using Microsoft.AspNetCore.Mvc;
using ANWBakerySystem.Data;
using ANWBakerySystem.Models;
using Microsoft.EntityFrameworkCore;

namespace ANWBakerySystem.Controllers
{
    [ApiController]
    [Route("api/[controller]")]
    public class InvoicesController : ControllerBase
    {
        private readonly ANWBakeryDbContext _context;

        public InvoicesController(ANWBakeryDbContext context)
        {
            _context = context;
        }

        // GET: api/invoices
        [HttpGet]
        public async Task<ActionResult> GetInvoices([FromQuery] string? type = null, [FromQuery] DateTime? fromDate = null, [FromQuery] DateTime? toDate = null)
        {
            try
            {
                var query = _context.Invoices
                    .Include(i => i.Party)
                    .AsQueryable();

                // تصفية حسب النوع
                if (!string.IsNullOrEmpty(type))
                {
                    if (Enum.TryParse<InvoiceType>(type, true, out var invoiceType))
                    {
                        query = query.Where(i => i.InvoiceType == invoiceType);
                    }
                }

                // تصفية حسب التاريخ
                if (fromDate.HasValue)
                {
                    query = query.Where(i => i.InvoiceDate >= fromDate.Value);
                }

                if (toDate.HasValue)
                {
                    query = query.Where(i => i.InvoiceDate <= toDate.Value);
                }

                var invoices = await query
                    .OrderByDescending(i => i.InvoiceDate)
                    .ThenByDescending(i => i.InvoiceId)
                    .Select(i => new
                    {
                        i.InvoiceId,
                        i.InvoiceNumber,
                        i.InvoiceType,
                        i.InvoiceDate,
                        PartyName = i.Party != null ? i.Party.PartyName : "عميل نقدي",
                        i.TotalAmount,
                        i.DiscountAmount,
                        i.TaxAmount,
                        NetAmount = i.TotalAmount,
                        i.PaidAmount,
                        i.RemainingAmount,
                        PaymentStatus = i.RemainingAmount <= 0 ? "مدفوعة" : i.PaidAmount > 0 ? "مدفوعة جزئياً" : "غير مدفوعة",
                        InvoiceStatus = i.Status.ToString(),
                        i.CreatedAt
                    })
                    .Take(100) // حد أقصى 100 فاتورة
                    .ToListAsync();

                return Ok(new
                {
                    success = true,
                    data = invoices,
                    message = "تم جلب الفواتير بنجاح"
                });
            }
            catch (Exception ex)
            {
                return StatusCode(500, new
                {
                    success = false,
                    message = "خطأ في جلب الفواتير",
                    error = ex.Message
                });
            }
        }

        // GET: api/invoices/{id}
        [HttpGet("{id}")]
        public async Task<ActionResult> GetInvoice(int id)
        {
            try
            {
                var invoice = await _context.Invoices
                    .Include(i => i.Party)
                    .Include(i => i.InvoiceItems)
                        .ThenInclude(ii => ii.Item)
                            .ThenInclude(item => item.Unit)
                    .Where(i => i.InvoiceId == id)
                    .Select(i => new
                    {
                        i.InvoiceId,
                        i.InvoiceNumber,
                        i.InvoiceType,
                        i.InvoiceDate,
                        i.PartyId,
                        PartyName = i.Party != null ? i.Party.PartyName : "عميل نقدي",
                        i.TotalAmount,
                        i.DiscountAmount,
                        i.TaxAmount,
                        NetAmount = i.TotalAmount,
                        i.PaidAmount,
                        i.RemainingAmount,
                        PaymentStatus = i.RemainingAmount <= 0 ? "مدفوعة" : i.PaidAmount > 0 ? "مدفوعة جزئياً" : "غير مدفوعة",
                        InvoiceStatus = i.Status.ToString(),
                        i.Notes,
                        i.CreatedAt,
                        Items = i.InvoiceItems.Select(ii => new
                        {
                            ii.InvoiceItemId,
                            ii.ItemId,
                            ItemName = ii.Item.ItemName,
                            ItemCode = ii.Item.ItemCode,
                            UnitName = ii.Item.Unit.UnitName,
                            ii.Quantity,
                            ii.UnitPrice,
                            ii.DiscountAmount,
                            TotalPrice = ii.TotalAmount,
                            ii.LineOrder
                        }).OrderBy(ii => ii.LineOrder)
                    })
                    .FirstOrDefaultAsync();

                if (invoice == null)
                {
                    return NotFound(new
                    {
                        success = false,
                        message = "الفاتورة غير موجودة"
                    });
                }

                return Ok(new
                {
                    success = true,
                    data = invoice,
                    message = "تم جلب الفاتورة بنجاح"
                });
            }
            catch (Exception ex)
            {
                return StatusCode(500, new
                {
                    success = false,
                    message = "خطأ في جلب الفاتورة",
                    error = ex.Message
                });
            }
        }

        // POST: api/invoices
        [HttpPost]
        public async Task<ActionResult> CreateInvoice([FromBody] CreateInvoiceRequest request)
        {
            try
            {
                using var transaction = await _context.Database.BeginTransactionAsync();

                try
                {
                    // إنشاء رقم فاتورة تلقائي
                    var invoiceNumber = await GenerateInvoiceNumber(request.InvoiceType);

                    var invoice = new Invoice
                    {
                        InvoiceNumber = invoiceNumber,
                        InvoiceType = request.InvoiceType,
                        InvoiceDate = request.InvoiceDate,
                        PartyId = request.PartyId,
                        TotalAmount = request.TotalAmount,
                        DiscountAmount = request.DiscountAmount,
                        TaxAmount = request.TaxAmount,
                        PaidAmount = request.PaidAmount,
                        RemainingAmount = request.TotalAmount - request.PaidAmount,
                        Status = InvoiceStatus.Approved,
                        Notes = request.Notes,
                        CreatedAt = DateTime.Now
                    };

                    _context.Invoices.Add(invoice);
                    await _context.SaveChangesAsync();

                    // إضافة أصناف الفاتورة
                    foreach (var itemRequest in request.Items)
                    {
                        var invoiceItem = new InvoiceItem
                        {
                            InvoiceId = invoice.InvoiceId,
                            ItemId = itemRequest.ItemId,
                            Quantity = itemRequest.Quantity,
                            UnitPrice = itemRequest.UnitPrice,
                            DiscountAmount = itemRequest.DiscountAmount,
                            TotalAmount = itemRequest.TotalPrice,
                            LineOrder = itemRequest.LineOrder
                        };

                        _context.InvoiceItems.Add(invoiceItem);
                    }

                    await _context.SaveChangesAsync();
                    await transaction.CommitAsync();

                    return Ok(new
                    {
                        success = true,
                        data = new { 
                            invoiceId = invoice.InvoiceId,
                            invoiceNumber = invoice.InvoiceNumber
                        },
                        message = "تم إنشاء الفاتورة بنجاح"
                    });
                }
                catch
                {
                    await transaction.RollbackAsync();
                    throw;
                }
            }
            catch (Exception ex)
            {
                return StatusCode(500, new
                {
                    success = false,
                    message = "خطأ في إنشاء الفاتورة",
                    error = ex.Message
                });
            }
        }

        // PUT: api/invoices/{id}/status
        [HttpPut("{id}/status")]
        public async Task<ActionResult> UpdateInvoiceStatus(int id, [FromBody] UpdateInvoiceStatusRequest request)
        {
            try
            {
                var invoice = await _context.Invoices.FindAsync(id);
                if (invoice == null)
                {
                    return NotFound(new
                    {
                        success = false,
                        message = "الفاتورة غير موجودة"
                    });
                }

                invoice.Status = request.Status;
                await _context.SaveChangesAsync();

                return Ok(new
                {
                    success = true,
                    message = "تم تحديث حالة الفاتورة بنجاح"
                });
            }
            catch (Exception ex)
            {
                return StatusCode(500, new
                {
                    success = false,
                    message = "خطأ في تحديث حالة الفاتورة",
                    error = ex.Message
                });
            }
        }

        // DELETE: api/invoices/{id}
        [HttpDelete("{id}")]
        public async Task<ActionResult> DeleteInvoice(int id)
        {
            try
            {
                var invoice = await _context.Invoices
                    .Include(i => i.InvoiceItems)
                    .FirstOrDefaultAsync(i => i.InvoiceId == id);

                if (invoice == null)
                {
                    return NotFound(new
                    {
                        success = false,
                        message = "الفاتورة غير موجودة"
                    });
                }

                // حذف أصناف الفاتورة أولاً
                _context.InvoiceItems.RemoveRange(invoice.InvoiceItems);
                
                // حذف الفاتورة
                _context.Invoices.Remove(invoice);
                
                await _context.SaveChangesAsync();

                return Ok(new
                {
                    success = true,
                    message = "تم حذف الفاتورة بنجاح"
                });
            }
            catch (Exception ex)
            {
                return StatusCode(500, new
                {
                    success = false,
                    message = "خطأ في حذف الفاتورة",
                    error = ex.Message
                });
            }
        }

        // GET: api/invoices/sales
        [HttpGet("sales")]
        public async Task<ActionResult> GetSalesInvoices()
        {
            return await GetInvoices("Sales");
        }

        // GET: api/invoices/purchases
        [HttpGet("purchases")]
        public async Task<ActionResult> GetPurchaseInvoices()
        {
            return await GetInvoices("Purchase");
        }

        private async Task<string> GenerateInvoiceNumber(InvoiceType invoiceType)
        {
            var prefix = invoiceType switch
            {
                InvoiceType.Sales => "S",
                InvoiceType.Purchase => "P",
                InvoiceType.SalesReturn => "SR",
                InvoiceType.PurchaseReturn => "PR",
                _ => "INV"
            };

            var year = DateTime.Now.Year;
            var lastInvoice = await _context.Invoices
                .Where(i => i.InvoiceType == invoiceType && i.InvoiceDate.Year == year)
                .OrderByDescending(i => i.InvoiceId)
                .FirstOrDefaultAsync();

            var nextNumber = 1;
            if (lastInvoice != null)
            {
                // استخراج الرقم من آخر فاتورة
                var lastNumber = lastInvoice.InvoiceNumber.Split('-').LastOrDefault();
                if (int.TryParse(lastNumber, out var num))
                {
                    nextNumber = num + 1;
                }
            }

            return $"{prefix}-{year}-{nextNumber:D6}";
        }
    }

    public class CreateInvoiceRequest
    {
        public InvoiceType InvoiceType { get; set; }
        public DateTime InvoiceDate { get; set; } = DateTime.Now;
        public int? PartyId { get; set; }
        public decimal TotalAmount { get; set; }
        public decimal DiscountAmount { get; set; } = 0;
        public decimal TaxAmount { get; set; } = 0;
        public decimal PaidAmount { get; set; } = 0;
        public string? Notes { get; set; }
        public List<CreateInvoiceItemRequest> Items { get; set; } = new List<CreateInvoiceItemRequest>();
    }

    public class CreateInvoiceItemRequest
    {
        public int ItemId { get; set; }
        public decimal Quantity { get; set; }
        public decimal UnitPrice { get; set; }
        public decimal DiscountAmount { get; set; } = 0;
        public decimal TotalPrice { get; set; }
        public int LineOrder { get; set; }
    }

    public class UpdateInvoiceStatusRequest
    {
        public InvoiceStatus Status { get; set; }
    }
}
