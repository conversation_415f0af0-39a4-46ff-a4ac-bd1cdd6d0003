using Microsoft.AspNetCore.Mvc;
using ANWBakerySystem.Data;
using ANWBakerySystem.Models;
using Microsoft.EntityFrameworkCore;

namespace ANWBakerySystem.Controllers
{
    [ApiController]
    [Route("api/[controller]")]
    public class DashboardController : ControllerBase
    {
        private readonly ANWBakeryDbContext _context;

        public DashboardController(ANWBakeryDbContext context)
        {
            _context = context;
        }

        // GET: api/dashboard/statistics
        [HttpGet("statistics")]
        public async Task<ActionResult> GetStatistics()
        {
            try
            {
                var today = DateTime.Today;
                var startOfMonth = new DateTime(today.Year, today.Month, 1);

                // حساب مبيعات اليوم
                var todaySales = await _context.Invoices
                    .Where(i => i.InvoiceDate.Date == today && i.InvoiceType == InvoiceType.Sales)
                    .SumAsync(i => i.TotalAmount);

                // حساب مبيعات الشهر
                var monthSales = await _context.Invoices
                    .Where(i => i.InvoiceDate >= startOfMonth && i.InvoiceType == InvoiceType.Sales)
                    .SumAsync(i => i.TotalAmount);

                // عدد فواتير اليوم
                var todayInvoices = await _context.Invoices
                    .CountAsync(i => i.InvoiceDate.Date == today);

                // إجمالي العملاء
                var totalCustomers = await _context.Parties
                    .CountAsync(p => p.PartyType == PartyType.Customer);

                // إجمالي الموردين
                var totalSuppliers = await _context.Parties
                    .CountAsync(p => p.PartyType == PartyType.Supplier);

                // إجمالي الأصناف
                var totalItems = await _context.Items.CountAsync();

                // إجمالي المستخدمين
                var totalUsers = await _context.Users.CountAsync();

                return Ok(new
                {
                    todaySales = todaySales,
                    monthSales = monthSales,
                    todayInvoices = todayInvoices,
                    totalCustomers = totalCustomers,
                    totalSuppliers = totalSuppliers,
                    totalItems = totalItems,
                    totalUsers = totalUsers,
                    currency = "YER",
                    currencySymbol = "ر.ي"
                });
            }
            catch (Exception ex)
            {
                return StatusCode(500, new { message = "خطأ في جلب الإحصائيات", error = ex.Message });
            }
        }

        // GET: api/dashboard/recent-invoices
        [HttpGet("recent-invoices")]
        public async Task<ActionResult> GetRecentInvoices()
        {
            try
            {
                var recentInvoices = await _context.Invoices
                    .Include(i => i.Party)
                    .OrderByDescending(i => i.InvoiceDate)
                    .Take(10)
                    .Select(i => new
                    {
                        i.InvoiceId,
                        i.InvoiceNumber,
                        i.InvoiceDate,
                        i.InvoiceType,
                        i.TotalAmount,
                        PartyName = i.Party != null ? i.Party.PartyName : "غير محدد"
                    })
                    .ToListAsync();

                return Ok(recentInvoices);
            }
            catch (Exception ex)
            {
                return StatusCode(500, new { message = "خطأ في جلب الفواتير الأخيرة", error = ex.Message });
            }
        }

        // GET: api/dashboard/low-stock
        [HttpGet("low-stock")]
        public async Task<ActionResult> GetLowStockItems()
        {
            try
            {
                var lowStockItems = await _context.Inventories
                    .Include(inv => inv.Item)
                    .Where(inv => inv.AvailableQuantity <= 10) // استخدام حد أدنى افتراضي
                    .Select(inv => new
                    {
                        inv.Item.ItemId,
                        inv.Item.ItemName,
                        inv.Item.ItemCode,
                        CurrentStock = inv.AvailableQuantity,
                        MinimumStock = 10 // حد أدنى افتراضي
                    })
                    .ToListAsync();

                return Ok(lowStockItems);
            }
            catch (Exception ex)
            {
                return StatusCode(500, new { message = "خطأ في جلب الأصناف منخفضة المخزون", error = ex.Message });
            }
        }
    }
}
