// إدارة المستخدمين
const API_BASE_URL = '/api';
let users = [];
let currentUser = null;

// تحميل البيانات عند تحميل الصفحة
document.addEventListener('DOMContentLoaded', function() {
    checkAuthentication();
    loadUsers();
    setupEventListeners();
});

// التحقق من المصادقة
function checkAuthentication() {
    const token = localStorage.getItem('authToken');
    const user = localStorage.getItem('currentUser');
    
    if (!token || !user) {
        window.location.href = 'index.html';
        return;
    }
    
    // عرض اسم المستخدم
    const userData = JSON.parse(user);
    document.getElementById('currentUser').textContent = userData.fullName || userData.username;
}

// إعداد مستمعي الأحداث
function setupEventListeners() {
    // البحث والتصفية
    document.getElementById('searchInput').addEventListener('input', filterUsers);
    document.getElementById('roleFilter').addEventListener('change', filterUsers);
    document.getElementById('statusFilter').addEventListener('change', filterUsers);
}

// تحميل المستخدمين
async function loadUsers() {
    try {
        // جلب البيانات من قاعدة البيانات
        const response = await fetch('/api/users');
        if (response.ok) {
            const data = await response.json();
            users = data.map(user => ({
                ...user,
                lastLogin: user.lastLogin || new Date().toISOString(),
                createdAt: user.createdAt || new Date().toISOString()
            }));
        } else {
            // لا توجد بيانات - قاعدة بيانات فارغة
            users = [];
        }

        displayUsers(users);
        updateStatistics();

    } catch (error) {
        console.error('خطأ في تحميل المستخدمين:', error);
        // قاعدة بيانات فارغة في حالة فشل الاتصال
        users = [];
        displayUsers(users);
        updateStatistics();
    }
}

// عرض المستخدمين
function displayUsers(usersToShow) {
    const tbody = document.getElementById('usersTableBody');
    
    if (usersToShow.length === 0) {
        tbody.innerHTML = `
            <tr>
                <td colspan="9" class="text-center">لا توجد مستخدمين</td>
            </tr>
        `;
        return;
    }
    
    tbody.innerHTML = usersToShow.map(user => `
        <tr>
            <td>
                <div class="user-avatar">
                    <i class="fas fa-user-circle fa-2x text-secondary"></i>
                </div>
            </td>
            <td>
                <strong>${user.username}</strong>
            </td>
            <td>${user.fullName}</td>
            <td>${user.email || '-'}</td>
            <td>${user.phone || '-'}</td>
            <td>
                <span class="badge ${getRoleBadgeClass(user.role)}">
                    ${getRoleDisplayName(user.role)}
                </span>
            </td>
            <td>
                <span class="badge ${user.isActive ? 'bg-success' : 'bg-danger'}">
                    ${user.isActive ? 'نشط' : 'غير نشط'}
                </span>
            </td>
            <td>
                <small class="text-muted">
                    ${user.lastLogin ? formatDateTime(user.lastLogin) : 'لم يسجل دخول'}
                </small>
            </td>
            <td>
                <div class="btn-group btn-group-sm">
                    <button class="btn btn-outline-primary" onclick="editUser(${user.userId})" title="تعديل">
                        <i class="fas fa-edit"></i>
                    </button>
                    <button class="btn btn-outline-warning" onclick="resetPassword(${user.userId})" title="إعادة تعيين كلمة المرور">
                        <i class="fas fa-key"></i>
                    </button>
                    <button class="btn btn-outline-${user.isActive ? 'danger' : 'success'}" 
                            onclick="toggleUserStatus(${user.userId})" 
                            title="${user.isActive ? 'إلغاء التفعيل' : 'تفعيل'}">
                        <i class="fas fa-${user.isActive ? 'user-slash' : 'user-check'}"></i>
                    </button>
                    ${user.userId !== 1 ? `
                        <button class="btn btn-outline-danger" onclick="deleteUser(${user.userId})" title="حذف">
                            <i class="fas fa-trash"></i>
                        </button>
                    ` : ''}
                </div>
            </td>
        </tr>
    `).join('');
}

// تصفية المستخدمين
function filterUsers() {
    const searchTerm = document.getElementById('searchInput').value.toLowerCase();
    const roleFilter = document.getElementById('roleFilter').value;
    const statusFilter = document.getElementById('statusFilter').value;
    
    let filteredUsers = users.filter(user => {
        const matchesSearch = !searchTerm || 
            user.username.toLowerCase().includes(searchTerm) ||
            user.fullName.toLowerCase().includes(searchTerm) ||
            (user.email && user.email.toLowerCase().includes(searchTerm));
            
        const matchesRole = !roleFilter || user.role === roleFilter;
        const matchesStatus = !statusFilter || user.isActive.toString() === statusFilter;
        
        return matchesSearch && matchesRole && matchesStatus;
    });
    
    displayUsers(filteredUsers);
}

// عرض نموذج إضافة مستخدم
function showAddUserModal() {
    currentUser = null;
    document.getElementById('userModalTitle').textContent = 'إضافة مستخدم جديد';
    document.getElementById('userForm').reset();
    document.getElementById('userId').value = '';
    document.getElementById('isActive').checked = true;
    
    const modal = new bootstrap.Modal(document.getElementById('userModal'));
    modal.show();
}

// تعديل مستخدم
function editUser(userId) {
    const user = users.find(u => u.userId === userId);
    if (!user) return;
    
    currentUser = user;
    document.getElementById('userModalTitle').textContent = 'تعديل المستخدم';
    
    // ملء النموذج
    document.getElementById('userId').value = user.userId;
    document.getElementById('username').value = user.username;
    document.getElementById('fullName').value = user.fullName;
    document.getElementById('email').value = user.email || '';
    document.getElementById('phone').value = user.phone || '';
    document.getElementById('role').value = user.role;
    document.getElementById('isActive').checked = user.isActive;
    
    // إخفاء حقول كلمة المرور في التعديل
    document.getElementById('password').required = false;
    document.getElementById('confirmPassword').required = false;
    
    const modal = new bootstrap.Modal(document.getElementById('userModal'));
    modal.show();
}

// حفظ المستخدم
async function saveUser() {
    try {
        const formData = {
            userId: document.getElementById('userId').value,
            username: document.getElementById('username').value,
            fullName: document.getElementById('fullName').value,
            email: document.getElementById('email').value,
            phone: document.getElementById('phone').value,
            role: document.getElementById('role').value,
            isActive: document.getElementById('isActive').checked,
            mustChangePassword: document.getElementById('mustChangePassword').checked
        };
        
        // التحقق من صحة البيانات
        if (!formData.username || !formData.fullName || !formData.role) {
            showError('يرجى ملء جميع الحقول المطلوبة');
            return;
        }
        
        // التحقق من كلمة المرور للمستخدمين الجدد
        if (!formData.userId) {
            const password = document.getElementById('password').value;
            const confirmPassword = document.getElementById('confirmPassword').value;
            
            if (!password) {
                showError('يرجى إدخال كلمة المرور');
                return;
            }
            
            if (password !== confirmPassword) {
                showError('كلمة المرور وتأكيدها غير متطابقتين');
                return;
            }
            
            formData.password = password;
        }
        
        // حفظ البيانات في قاعدة البيانات
        let response;
        if (formData.userId) {
            // تحديث مستخدم موجود
            response = await fetch(`/api/users/${formData.userId}`, {
                method: 'PUT',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify(formData)
            });
        } else {
            // إضافة مستخدم جديد
            response = await fetch('/api/users', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify(formData)
            });
        }

        if (!response.ok) {
            const error = await response.json();
            throw new Error(error.message || 'فشل في حفظ البيانات');
        }
        
        // إغلاق النموذج وإعادة تحميل البيانات
        const modal = bootstrap.Modal.getInstance(document.getElementById('userModal'));
        modal.hide();

        // إعادة تحميل المستخدمين من قاعدة البيانات
        await loadUsers();
        showSuccess('تم حفظ بيانات المستخدم بنجاح');
        
    } catch (error) {
        console.error('خطأ في حفظ المستخدم:', error);
        showError('فشل في حفظ بيانات المستخدم');
    }
}

// تبديل حالة المستخدم
async function toggleUserStatus(userId) {
    try {
        const user = users.find(u => u.userId === userId);
        if (!user) return;

        const action = user.isActive ? 'إلغاء تفعيل' : 'تفعيل';

        if (confirm(`هل أنت متأكد من ${action} المستخدم "${user.fullName}"؟`)) {
            // تحديث في قاعدة البيانات
            const response = await fetch(`/api/users/${userId}`, {
                method: 'PUT',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    ...user,
                    isActive: !user.isActive
                })
            });

            if (response.ok) {
                // إعادة تحميل البيانات من قاعدة البيانات
                await loadUsers();
                showSuccess(`تم ${action} المستخدم بنجاح`);
            } else {
                throw new Error('فشل في تحديث حالة المستخدم');
            }
        }

    } catch (error) {
        console.error('خطأ في تغيير حالة المستخدم:', error);
        showError('فشل في تغيير حالة المستخدم');
    }
}

// إعادة تعيين كلمة المرور
async function resetPassword(userId) {
    try {
        const user = users.find(u => u.userId === userId);
        if (!user) return;
        
        const newPassword = prompt(`إدخال كلمة المرور الجديدة للمستخدم "${user.fullName}":`);
        
        if (newPassword && newPassword.length >= 6) {
            // محاكاة إعادة تعيين كلمة المرور
            showSuccess('تم إعادة تعيين كلمة المرور بنجاح');
        } else if (newPassword !== null) {
            showError('كلمة المرور يجب أن تكون 6 أحرف على الأقل');
        }
        
    } catch (error) {
        console.error('خطأ في إعادة تعيين كلمة المرور:', error);
        showError('فشل في إعادة تعيين كلمة المرور');
    }
}

// حذف مستخدم
async function deleteUser(userId) {
    try {
        const user = users.find(u => u.userId === userId);
        if (!user) return;

        if (confirm(`هل أنت متأكد من حذف المستخدم "${user.fullName}"؟\n\nهذا الإجراء لا يمكن التراجع عنه.`)) {
            // حذف من قاعدة البيانات
            const response = await fetch(`/api/users/${userId}`, {
                method: 'DELETE'
            });

            if (response.ok) {
                // إعادة تحميل البيانات من قاعدة البيانات
                await loadUsers();
                showSuccess('تم حذف المستخدم بنجاح');
            } else {
                throw new Error('فشل في حذف المستخدم');
            }
        }

    } catch (error) {
        console.error('خطأ في حذف المستخدم:', error);
        showError('فشل في حذف المستخدم');
    }
}

// تحديث الإحصائيات
function updateStatistics() {
    const totalUsers = users.length;
    const activeUsers = users.filter(u => u.isActive).length;
    const onlineUsers = users.filter(u => u.lastLogin && isRecentLogin(u.lastLogin)).length;
    const adminUsers = users.filter(u => u.role === 'Admin').length;
    
    document.getElementById('totalUsers').textContent = totalUsers;
    document.getElementById('activeUsers').textContent = activeUsers;
    document.getElementById('onlineUsers').textContent = onlineUsers;
    document.getElementById('adminUsers').textContent = adminUsers;
}

// عرض نموذج إدارة الصلاحيات
function showPermissionsModal() {
    alert('سيتم تطوير نموذج إدارة الصلاحيات قريباً');
}

// وظائف مساعدة
function getRoleBadgeClass(role) {
    const classes = {
        'Admin': 'bg-danger',
        'Manager': 'bg-warning',
        'Cashier': 'bg-info',
        'Employee': 'bg-secondary'
    };
    return classes[role] || 'bg-secondary';
}

function getRoleDisplayName(role) {
    const names = {
        'Admin': 'مدير النظام',
        'Manager': 'مدير',
        'Cashier': 'أمين صندوق',
        'Employee': 'موظف'
    };
    return names[role] || role;
}

function formatDateTime(dateString) {
    const date = new Date(dateString);
    return date.toLocaleString('ar-YE', {
        year: 'numeric',
        month: 'short',
        day: 'numeric',
        hour: '2-digit',
        minute: '2-digit'
    });
}

function isRecentLogin(lastLogin) {
    const loginDate = new Date(lastLogin);
    const now = new Date();
    const diffMinutes = (now - loginDate) / (1000 * 60);
    return diffMinutes <= 30; // آخر 30 دقيقة
}

function showError(message) {
    const alertDiv = document.createElement('div');
    alertDiv.className = 'alert alert-danger alert-dismissible fade show position-fixed';
    alertDiv.style.cssText = 'top: 20px; right: 20px; z-index: 9999; min-width: 300px;';
    alertDiv.innerHTML = `
        <i class="fas fa-exclamation-triangle me-2"></i>
        ${message}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    `;
    document.body.appendChild(alertDiv);

    setTimeout(() => {
        if (alertDiv.parentNode) {
            alertDiv.remove();
        }
    }, 5000);
}

function showSuccess(message) {
    const alertDiv = document.createElement('div');
    alertDiv.className = 'alert alert-success alert-dismissible fade show position-fixed';
    alertDiv.style.cssText = 'top: 20px; right: 20px; z-index: 9999; min-width: 300px;';
    alertDiv.innerHTML = `
        <i class="fas fa-check-circle me-2"></i>
        ${message}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    `;
    document.body.appendChild(alertDiv);

    setTimeout(() => {
        if (alertDiv.parentNode) {
            alertDiv.remove();
        }
    }, 3000);
}

function logout() {
    if (confirm('هل أنت متأكد من تسجيل الخروج؟')) {
        localStorage.removeItem('authToken');
        localStorage.removeItem('currentUser');
        window.location.href = 'index.html';
    }
}
