using System.ComponentModel.DataAnnotations;

namespace ANWBakerySystem.Models
{
    /// <summary>
    /// DTO لعرض بيانات البنك
    /// Bank Data Transfer Object for display
    /// </summary>
    public class BankDto
    {
        public int BankId { get; set; }
        public string BankCode { get; set; } = string.Empty;
        public string BankName { get; set; } = string.Empty;
        public string? BankNameEn { get; set; }
        public string? Address { get; set; }
        public string? Phone { get; set; }
        public string? Email { get; set; }
        public string? Website { get; set; }
        public string? SwiftCode { get; set; }
        public string? Notes { get; set; }
        public bool IsActive { get; set; }
        public DateTime CreatedAt { get; set; }
        public DateTime? UpdatedAt { get; set; }
        public int AccountsCount { get; set; }
    }

    /// <summary>
    /// طلب إنشاء بنك جديد
    /// Create Bank Request
    /// </summary>
    public class CreateBankRequest
    {
        [Required(ErrorMessage = "كود البنك مطلوب")]
        [StringLength(20, ErrorMessage = "كود البنك يجب أن يكون أقل من 20 حرف")]
        public string BankCode { get; set; } = string.Empty;

        [Required(ErrorMessage = "اسم البنك مطلوب")]
        [StringLength(200, ErrorMessage = "اسم البنك يجب أن يكون أقل من 200 حرف")]
        public string BankName { get; set; } = string.Empty;

        [StringLength(200, ErrorMessage = "الاسم بالإنجليزية يجب أن يكون أقل من 200 حرف")]
        public string? BankNameEn { get; set; }

        [StringLength(500, ErrorMessage = "العنوان يجب أن يكون أقل من 500 حرف")]
        public string? Address { get; set; }

        [StringLength(20, ErrorMessage = "رقم الهاتف يجب أن يكون أقل من 20 حرف")]
        [Phone(ErrorMessage = "رقم الهاتف غير صالح")]
        public string? Phone { get; set; }

        [StringLength(100, ErrorMessage = "البريد الإلكتروني يجب أن يكون أقل من 100 حرف")]
        [EmailAddress(ErrorMessage = "البريد الإلكتروني غير صالح")]
        public string? Email { get; set; }

        [StringLength(100, ErrorMessage = "الموقع الإلكتروني يجب أن يكون أقل من 100 حرف")]
        [Url(ErrorMessage = "الموقع الإلكتروني غير صالح")]
        public string? Website { get; set; }

        [StringLength(50, ErrorMessage = "رمز SWIFT يجب أن يكون أقل من 50 حرف")]
        public string? SwiftCode { get; set; }

        [StringLength(500, ErrorMessage = "الملاحظات يجب أن تكون أقل من 500 حرف")]
        public string? Notes { get; set; }

        public bool? IsActive { get; set; }
    }

    /// <summary>
    /// DTO لعرض بيانات الحساب المصرفي
    /// Bank Account Data Transfer Object for display
    /// </summary>
    public class BankAccountDto
    {
        public int BankAccountId { get; set; }
        public int BankId { get; set; }
        public string BankName { get; set; } = string.Empty;
        public string AccountNumber { get; set; } = string.Empty;
        public string AccountName { get; set; } = string.Empty;
        public BankAccountType AccountType { get; set; }
        public string Currency { get; set; } = string.Empty;
        public decimal CurrentBalance { get; set; }
        public decimal OpeningBalance { get; set; }
        public string? IbanNumber { get; set; }
        public int? LinkedAccountId { get; set; }
        public string? LinkedAccountName { get; set; }
        public string? Notes { get; set; }
        public bool IsActive { get; set; }
        public DateTime CreatedAt { get; set; }
        public DateTime? UpdatedAt { get; set; }
    }

    /// <summary>
    /// طلب إنشاء حساب مصرفي جديد
    /// Create Bank Account Request
    /// </summary>
    public class CreateBankAccountRequest
    {
        [Required(ErrorMessage = "البنك مطلوب")]
        public int BankId { get; set; }

        [Required(ErrorMessage = "رقم الحساب مطلوب")]
        [StringLength(50, ErrorMessage = "رقم الحساب يجب أن يكون أقل من 50 حرف")]
        public string AccountNumber { get; set; } = string.Empty;

        [Required(ErrorMessage = "اسم الحساب مطلوب")]
        [StringLength(200, ErrorMessage = "اسم الحساب يجب أن يكون أقل من 200 حرف")]
        public string AccountName { get; set; } = string.Empty;

        [Required(ErrorMessage = "نوع الحساب مطلوب")]
        public BankAccountType AccountType { get; set; }

        [StringLength(3, ErrorMessage = "عملة الحساب يجب أن تكون 3 أحرف")]
        public string? Currency { get; set; }

        [Range(0, double.MaxValue, ErrorMessage = "الرصيد الافتتاحي يجب أن يكون أكبر من أو يساوي صفر")]
        public decimal? OpeningBalance { get; set; }

        [StringLength(100, ErrorMessage = "رقم IBAN يجب أن يكون أقل من 100 حرف")]
        public string? IbanNumber { get; set; }

        public int? LinkedAccountId { get; set; }

        [StringLength(500, ErrorMessage = "الملاحظات يجب أن تكون أقل من 500 حرف")]
        public string? Notes { get; set; }

        public bool? IsActive { get; set; }
    }

    /// <summary>
    /// DTO لعرض بيانات الصندوق النقدي
    /// Cash Register Data Transfer Object for display
    /// </summary>
    public class CashRegisterDto
    {
        public int CashRegisterId { get; set; }
        public string CashCode { get; set; } = string.Empty;
        public string CashName { get; set; } = string.Empty;
        public string? Description { get; set; }
        public decimal CurrentBalance { get; set; }
        public decimal OpeningBalance { get; set; }
        public int? LinkedAccountId { get; set; }
        public string? LinkedAccountName { get; set; }
        public string? Location { get; set; }
        public int? ResponsibleUserId { get; set; }
        public string? ResponsibleUserName { get; set; }
        public string? Notes { get; set; }
        public bool IsActive { get; set; }
        public DateTime CreatedAt { get; set; }
        public DateTime? UpdatedAt { get; set; }
    }

    /// <summary>
    /// طلب إنشاء صندوق نقدي جديد
    /// Create Cash Register Request
    /// </summary>
    public class CreateCashRegisterRequest
    {
        [Required(ErrorMessage = "كود الصندوق مطلوب")]
        [StringLength(20, ErrorMessage = "كود الصندوق يجب أن يكون أقل من 20 حرف")]
        public string CashCode { get; set; } = string.Empty;

        [Required(ErrorMessage = "اسم الصندوق مطلوب")]
        [StringLength(200, ErrorMessage = "اسم الصندوق يجب أن يكون أقل من 200 حرف")]
        public string CashName { get; set; } = string.Empty;

        [StringLength(500, ErrorMessage = "وصف الصندوق يجب أن يكون أقل من 500 حرف")]
        public string? Description { get; set; }

        [Range(0, double.MaxValue, ErrorMessage = "الرصيد الافتتاحي يجب أن يكون أكبر من أو يساوي صفر")]
        public decimal? OpeningBalance { get; set; }

        public int? LinkedAccountId { get; set; }

        [StringLength(100, ErrorMessage = "الموقع يجب أن يكون أقل من 100 حرف")]
        public string? Location { get; set; }

        public int? ResponsibleUserId { get; set; }

        [StringLength(500, ErrorMessage = "الملاحظات يجب أن تكون أقل من 500 حرف")]
        public string? Notes { get; set; }

        public bool? IsActive { get; set; }
    }

    /// <summary>
    /// طلب تحويل الأموال
    /// Transfer Money Request
    /// </summary>
    public class CreateTransferRequest
    {
        [Required(ErrorMessage = "نوع الحساب المصدر مطلوب")]
        public AccountSourceType FromAccountType { get; set; }

        [Required(ErrorMessage = "معرف الحساب المصدر مطلوب")]
        public int FromAccountId { get; set; }

        [Required(ErrorMessage = "نوع الحساب المستهدف مطلوب")]
        public AccountSourceType ToAccountType { get; set; }

        [Required(ErrorMessage = "معرف الحساب المستهدف مطلوب")]
        public int ToAccountId { get; set; }

        [Required(ErrorMessage = "مبلغ التحويل مطلوب")]
        [Range(0.001, double.MaxValue, ErrorMessage = "مبلغ التحويل يجب أن يكون أكبر من صفر")]
        public decimal Amount { get; set; }

        [StringLength(500, ErrorMessage = "وصف التحويل يجب أن يكون أقل من 500 حرف")]
        public string? Description { get; set; }
    }

    /// <summary>
    /// أنواع مصادر الحسابات
    /// Account Source Types
    /// </summary>
    public enum AccountSourceType
    {
        [Display(Name = "حساب مصرفي")]
        BankAccount = 1,
        [Display(Name = "صندوق نقدي")]
        CashRegister = 2
    }

    /// <summary>
    /// نتيجة عملية البنك الواحد
    /// Single Bank Result
    /// </summary>
    public class BankResult
    {
        public bool Success { get; set; }
        public string Message { get; set; } = string.Empty;
        public BankDto? Bank { get; set; }
        public List<string>? Errors { get; set; }
    }

    /// <summary>
    /// نتيجة عملية البنوك المتعددة
    /// Multiple Banks Result
    /// </summary>
    public class BanksResult
    {
        public bool Success { get; set; }
        public string Message { get; set; } = string.Empty;
        public List<BankDto>? Banks { get; set; }
        public int TotalCount { get; set; }
        public List<string>? Errors { get; set; }
    }

    /// <summary>
    /// نتيجة عملية الحساب المصرفي الواحد
    /// Single Bank Account Result
    /// </summary>
    public class BankAccountResult
    {
        public bool Success { get; set; }
        public string Message { get; set; } = string.Empty;
        public BankAccountDto? Account { get; set; }
        public List<string>? Errors { get; set; }
    }

    /// <summary>
    /// نتيجة عملية الحسابات المصرفية المتعددة
    /// Multiple Bank Accounts Result
    /// </summary>
    public class BankAccountsResult
    {
        public bool Success { get; set; }
        public string Message { get; set; } = string.Empty;
        public List<BankAccountDto>? Accounts { get; set; }
        public int TotalCount { get; set; }
        public List<string>? Errors { get; set; }
    }

    /// <summary>
    /// نتيجة عملية الصندوق النقدي الواحد
    /// Single Cash Register Result
    /// </summary>
    public class CashRegisterResult
    {
        public bool Success { get; set; }
        public string Message { get; set; } = string.Empty;
        public CashRegisterDto? CashRegister { get; set; }
        public List<string>? Errors { get; set; }
    }

    /// <summary>
    /// نتيجة عملية الصناديق النقدية المتعددة
    /// Multiple Cash Registers Result
    /// </summary>
    public class CashRegistersResult
    {
        public bool Success { get; set; }
        public string Message { get; set; } = string.Empty;
        public List<CashRegisterDto>? CashRegisters { get; set; }
        public int TotalCount { get; set; }
        public List<string>? Errors { get; set; }
    }

    /// <summary>
    /// نتيجة عملية التحويل
    /// Transfer Result
    /// </summary>
    public class TransferResult
    {
        public bool Success { get; set; }
        public string Message { get; set; } = string.Empty;
        public decimal? TransferAmount { get; set; }
        public decimal? FromAccountBalance { get; set; }
        public decimal? ToAccountBalance { get; set; }
        public List<string>? Errors { get; set; }
    }
}
