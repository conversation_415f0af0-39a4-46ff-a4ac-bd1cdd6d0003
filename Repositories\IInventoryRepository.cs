using ANWBakerySystem.Models;

namespace ANWBakerySystem.Repositories
{
    /// <summary>
    /// واجهة مستودع المخزون
    /// Inventory Repository Interface
    /// </summary>
    public interface IInventoryRepository
    {
        // إدارة المخزون
        // Inventory Management
        Task<IEnumerable<Inventory>> GetAllInventoryAsync();
        Task<IEnumerable<Inventory>> GetInventoryByWarehouseAsync(int warehouseId);
        Task<Inventory?> GetInventoryAsync(int itemId, int warehouseId);
        Task<Inventory> CreateInventoryAsync(Inventory inventory);
        Task<Inventory> UpdateInventoryAsync(Inventory inventory);
        Task<bool> DeleteInventoryAsync(int inventoryId);
        
        // حركة المخزون
        // Inventory Movement
        Task<InventoryMovement> CreateMovementAsync(InventoryMovement movement);
        Task<IEnumerable<InventoryMovement>> GetMovementsByItemAsync(int itemId);
        Task<IEnumerable<InventoryMovement>> GetMovementsByWarehouseAsync(int warehouseId);
        Task<IEnumerable<InventoryMovement>> GetMovementsByDateRangeAsync(DateTime fromDate, DateTime toDate);
        Task<IEnumerable<InventoryMovement>> GetMovementsByTypeAsync(MovementType movementType);
        
        // عمليات المخزون
        // Inventory Operations
        Task<bool> AddStockAsync(int itemId, int warehouseId, decimal quantity, decimal unitCost, 
            MovementType movementType, string? referenceNumber, int userId);
        Task<bool> RemoveStockAsync(int itemId, int warehouseId, decimal quantity, 
            MovementType movementType, string? referenceNumber, int userId);
        Task<bool> TransferStockAsync(int itemId, int fromWarehouseId, int toWarehouseId, 
            decimal quantity, string? referenceNumber, int userId);
        Task<bool> AdjustStockAsync(int itemId, int warehouseId, decimal newQuantity, 
            string reason, int userId);
        
        // استعلامات المخزون
        // Inventory Queries
        Task<decimal> GetAvailableQuantityAsync(int itemId, int warehouseId);
        Task<decimal> GetTotalQuantityAsync(int itemId);
        Task<decimal> GetReservedQuantityAsync(int itemId, int warehouseId);
        Task<bool> IsQuantityAvailableAsync(int itemId, int warehouseId, decimal requiredQuantity);
        Task<decimal> GetAverageCostAsync(int itemId, int warehouseId);
        Task<decimal> GetInventoryValueAsync(int itemId, int warehouseId);
        Task<decimal> GetTotalInventoryValueAsync(int warehouseId);
        
        // تقارير المخزون
        // Inventory Reports
        Task<IEnumerable<Inventory>> GetLowStockItemsAsync();
        Task<IEnumerable<Inventory>> GetZeroStockItemsAsync();
        Task<IEnumerable<Inventory>> GetOverStockItemsAsync();
        Task<IEnumerable<InventoryMovement>> GetSlowMovingItemsAsync(int days);
        Task<IEnumerable<InventoryMovement>> GetFastMovingItemsAsync(int days);
        
        // إدارة المخازن
        // Warehouse Management
        Task<IEnumerable<Warehouse>> GetAllWarehousesAsync();
        Task<IEnumerable<Warehouse>> GetActiveWarehousesAsync();
        Task<Warehouse?> GetWarehouseByIdAsync(int warehouseId);
        Task<Warehouse?> GetWarehouseByCodeAsync(string warehouseCode);
        Task<Warehouse> CreateWarehouseAsync(Warehouse warehouse);
        Task<Warehouse> UpdateWarehouseAsync(Warehouse warehouse);
        Task<bool> DeleteWarehouseAsync(int warehouseId);
        Task<bool> WarehouseExistsAsync(int warehouseId);
        Task<bool> WarehouseCodeExistsAsync(string warehouseCode);
        
        // جرد المخزون
        // Stock Count
        Task<bool> StartStockCountAsync(int warehouseId, int userId);
        Task<bool> UpdateStockCountAsync(int itemId, int warehouseId, decimal countedQuantity, int userId);
        Task<bool> CompleteStockCountAsync(int warehouseId, int userId);
        Task<IEnumerable<Inventory>> GetStockCountVariancesAsync(int warehouseId);
    }
}
