<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار الخادم - نظام مخبوزات ANW</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .test-result {
            padding: 10px;
            margin: 10px 0;
            border-radius: 5px;
            border: 1px solid #ddd;
        }
        .success {
            background-color: #d4edda;
            border-color: #c3e6cb;
            color: #155724;
        }
        .error {
            background-color: #f8d7da;
            border-color: #f5c6cb;
            color: #721c24;
        }
        .loading {
            background-color: #fff3cd;
            border-color: #ffeaa7;
            color: #856404;
        }
        button {
            background-color: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background-color: #0056b3;
        }
        pre {
            background-color: #f8f9fa;
            padding: 10px;
            border-radius: 5px;
            overflow-x: auto;
            white-space: pre-wrap;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🧪 اختبار خادم نظام مخبوزات ANW</h1>
        
        <div class="test-result loading" id="status">
            ⏳ جاري فحص حالة الخادم...
        </div>
        
        <button onclick="testServer()">🔄 إعادة اختبار الخادم</button>
        <button onclick="testLogin()">🔐 اختبار تسجيل الدخول</button>
        <button onclick="testDemoData()">📊 اختبار البيانات التجريبية</button>
        
        <div id="results"></div>
        
        <h3>📋 سجل الاختبارات:</h3>
        <pre id="log"></pre>
    </div>

    <script>
        let logContent = '';
        
        function log(message) {
            const timestamp = new Date().toLocaleTimeString('ar-YE');
            logContent += `[${timestamp}] ${message}\n`;
            document.getElementById('log').textContent = logContent;
        }
        
        function showResult(id, message, type) {
            const element = document.getElementById(id);
            element.textContent = message;
            element.className = `test-result ${type}`;
        }
        
        async function testServer() {
            log('بدء اختبار حالة الخادم...');
            showResult('status', '⏳ جاري فحص حالة الخادم...', 'loading');
            
            try {
                const response = await fetch('/api/frontend/status');
                if (response.ok) {
                    const data = await response.json();
                    showResult('status', `✅ الخادم يعمل بنجاح! ${data.message}`, 'success');
                    log(`نجح الاتصال بالخادم: ${data.system}`);
                    
                    // عرض تفاصيل الاستجابة
                    const resultsDiv = document.getElementById('results');
                    resultsDiv.innerHTML = `
                        <h4>📄 تفاصيل استجابة الخادم:</h4>
                        <pre>${JSON.stringify(data, null, 2)}</pre>
                    `;
                } else {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }
            } catch (error) {
                showResult('status', `❌ خطأ في الاتصال بالخادم: ${error.message}`, 'error');
                log(`فشل الاتصال بالخادم: ${error.message}`);
            }
        }
        
        async function testLogin() {
            log('بدء اختبار تسجيل الدخول...');
            
            try {
                const response = await fetch('/api/frontend/login', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        username: 'admin',
                        password: 'admin123'
                    })
                });
                
                if (response.ok) {
                    const data = await response.json();
                    log(`نجح اختبار تسجيل الدخول: ${data.message}`);
                    
                    const resultsDiv = document.getElementById('results');
                    resultsDiv.innerHTML = `
                        <h4>🔐 نتيجة اختبار تسجيل الدخول:</h4>
                        <pre>${JSON.stringify(data, null, 2)}</pre>
                    `;
                } else {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }
            } catch (error) {
                log(`فشل اختبار تسجيل الدخول: ${error.message}`);
            }
        }
        
        async function testDemoData() {
            log('بدء اختبار البيانات التجريبية...');
            
            try {
                const response = await fetch('/api/frontend/demo-data');
                if (response.ok) {
                    const data = await response.json();
                    log('نجح تحميل البيانات التجريبية');
                    
                    const resultsDiv = document.getElementById('results');
                    resultsDiv.innerHTML = `
                        <h4>📊 البيانات التجريبية:</h4>
                        <p><strong>المستخدمين:</strong> ${data.users.length}</p>
                        <p><strong>الحسابات:</strong> ${data.accounts.length}</p>
                        <p><strong>الأصناف:</strong> ${data.items.length}</p>
                        <p><strong>إجمالي المبيعات:</strong> ${data.statistics.totalSales.toLocaleString()} ر.ي</p>
                        <pre>${JSON.stringify(data, null, 2)}</pre>
                    `;
                } else {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }
            } catch (error) {
                log(`فشل تحميل البيانات التجريبية: ${error.message}`);
            }
        }
        
        // اختبار تلقائي عند تحميل الصفحة
        document.addEventListener('DOMContentLoaded', function() {
            log('تم تحميل صفحة الاختبار');
            testServer();
        });
    </script>
</body>
</html>
