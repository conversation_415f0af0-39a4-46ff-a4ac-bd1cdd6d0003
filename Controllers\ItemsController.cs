using Microsoft.AspNetCore.Mvc;
using ANWBakerySystem.Data;
using ANWBakerySystem.Models;
using Microsoft.EntityFrameworkCore;

namespace ANWBakerySystem.Controllers
{
    [ApiController]
    [Route("api/[controller]")]
    public class ItemsController : ControllerBase
    {
        private readonly ANWBakeryDbContext _context;

        public ItemsController(ANWBakeryDbContext context)
        {
            _context = context;
        }

        // GET: api/items
        [HttpGet]
        public async Task<ActionResult> GetItems([FromQuery] string? type = null, [FromQuery] int? categoryId = null)
        {
            try
            {
                var query = _context.Items
                    .Include(i => i.Unit)
                    .Include(i => i.Category)
                    .AsQueryable();

                // تصفية حسب النوع
                if (!string.IsNullOrEmpty(type))
                {
                    if (Enum.TryParse<ItemType>(type, true, out var itemType))
                    {
                        query = query.Where(i => i.ItemType == itemType);
                    }
                }

                // تصفية حسب الفئة
                if (categoryId.HasValue)
                {
                    query = query.Where(i => i.CategoryId == categoryId.Value);
                }

                var items = await query
                    .Where(i => i.IsActive)
                    .OrderBy(i => i.ItemName)
                    .Select(i => new
                    {
                        i.ItemId,
                        i.ItemCode,
                        i.ItemName,
                        i.ItemType,
                        i.Barcode,
                        UnitName = i.Unit.UnitName,
                        CategoryName = i.Category != null ? i.Category.CategoryName : "غير محدد",
                        i.PurchasePrice,
                        i.SalePrice,
                        CurrentStock = 0, // سيتم حسابه من InventoryMovements
                        i.MinimumStock,
                        i.IsActive,
                        i.CreatedAt
                    })
                    .ToListAsync();

                return Ok(new
                {
                    success = true,
                    data = items,
                    message = "تم جلب الأصناف بنجاح"
                });
            }
            catch (Exception ex)
            {
                return StatusCode(500, new
                {
                    success = false,
                    message = "خطأ في جلب الأصناف",
                    error = ex.Message
                });
            }
        }

        // GET: api/items/{id}
        [HttpGet("{id}")]
        public async Task<ActionResult> GetItem(int id)
        {
            try
            {
                var item = await _context.Items
                    .Include(i => i.Unit)
                    .Include(i => i.Category)
                    .Where(i => i.ItemId == id)
                    .Select(i => new
                    {
                        i.ItemId,
                        i.ItemCode,
                        i.ItemName,
                        i.ItemType,
                        i.Barcode,
                        i.UnitId,
                        UnitName = i.Unit.UnitName,
                        i.CategoryId,
                        CategoryName = i.Category != null ? i.Category.CategoryName : null,
                        i.PurchasePrice,
                        i.SalePrice,
                        CurrentStock = 0, // سيتم حسابه من InventoryMovements
                        i.MinimumStock,
                        i.MaximumStock,
                        ReorderLevel = i.ReorderPoint,
                        i.Description,
                        i.IsActive,
                        i.CreatedAt
                    })
                    .FirstOrDefaultAsync();

                if (item == null)
                {
                    return NotFound(new
                    {
                        success = false,
                        message = "الصنف غير موجود"
                    });
                }

                return Ok(new
                {
                    success = true,
                    data = item,
                    message = "تم جلب الصنف بنجاح"
                });
            }
            catch (Exception ex)
            {
                return StatusCode(500, new
                {
                    success = false,
                    message = "خطأ في جلب الصنف",
                    error = ex.Message
                });
            }
        }

        // POST: api/items
        [HttpPost]
        public async Task<ActionResult> CreateItem([FromBody] CreateItemRequest request)
        {
            try
            {
                // التحقق من عدم وجود كود مكرر
                var existingItem = await _context.Items
                    .FirstOrDefaultAsync(i => i.ItemCode == request.ItemCode);

                if (existingItem != null)
                {
                    return BadRequest(new
                    {
                        success = false,
                        message = "كود الصنف موجود بالفعل"
                    });
                }

                // التحقق من عدم وجود باركود مكرر
                if (!string.IsNullOrEmpty(request.Barcode))
                {
                    var existingBarcode = await _context.Items
                        .FirstOrDefaultAsync(i => i.Barcode == request.Barcode);

                    if (existingBarcode != null)
                    {
                        return BadRequest(new
                        {
                            success = false,
                            message = "الباركود موجود بالفعل"
                        });
                    }
                }

                var item = new Item
                {
                    ItemCode = request.ItemCode,
                    ItemName = request.ItemName,
                    ItemType = request.ItemType,
                    Barcode = request.Barcode,
                    UnitId = request.UnitId,
                    CategoryId = request.CategoryId,
                    PurchasePrice = request.PurchasePrice,
                    SalePrice = request.SalePrice,
                    // CurrentStock سيتم إدارته من خلال InventoryMovements
                    MinimumStock = request.MinimumStock,
                    MaximumStock = request.MaximumStock,
                    ReorderPoint = (decimal)(request.ReorderLevel ?? 0),
                    Description = request.Description,
                    IsActive = request.IsActive,
                    CreatedAt = DateTime.Now
                };

                _context.Items.Add(item);
                await _context.SaveChangesAsync();

                return Ok(new
                {
                    success = true,
                    data = new { itemId = item.ItemId },
                    message = "تم إنشاء الصنف بنجاح"
                });
            }
            catch (Exception ex)
            {
                return StatusCode(500, new
                {
                    success = false,
                    message = "خطأ في إنشاء الصنف",
                    error = ex.Message
                });
            }
        }

        // PUT: api/items/{id}
        [HttpPut("{id}")]
        public async Task<ActionResult> UpdateItem(int id, [FromBody] UpdateItemRequest request)
        {
            try
            {
                var item = await _context.Items.FindAsync(id);
                if (item == null)
                {
                    return NotFound(new
                    {
                        success = false,
                        message = "الصنف غير موجود"
                    });
                }

                // التحقق من عدم وجود كود مكرر
                if (request.ItemCode != item.ItemCode)
                {
                    var existingItem = await _context.Items
                        .FirstOrDefaultAsync(i => i.ItemCode == request.ItemCode && i.ItemId != id);

                    if (existingItem != null)
                    {
                        return BadRequest(new
                        {
                            success = false,
                            message = "كود الصنف موجود بالفعل"
                        });
                    }
                }

                // التحقق من عدم وجود باركود مكرر
                if (!string.IsNullOrEmpty(request.Barcode) && request.Barcode != item.Barcode)
                {
                    var existingBarcode = await _context.Items
                        .FirstOrDefaultAsync(i => i.Barcode == request.Barcode && i.ItemId != id);

                    if (existingBarcode != null)
                    {
                        return BadRequest(new
                        {
                            success = false,
                            message = "الباركود موجود بالفعل"
                        });
                    }
                }

                item.ItemCode = request.ItemCode;
                item.ItemName = request.ItemName;
                item.ItemType = request.ItemType;
                item.Barcode = request.Barcode;
                item.UnitId = request.UnitId;
                item.CategoryId = request.CategoryId;
                item.PurchasePrice = request.PurchasePrice;
                item.SalePrice = request.SalePrice;
                // CurrentStock سيتم إدارته من خلال InventoryMovements
                item.MinimumStock = request.MinimumStock;
                item.MaximumStock = request.MaximumStock;
                item.ReorderPoint = (decimal)(request.ReorderLevel ?? 0);
                item.Description = request.Description;
                item.IsActive = request.IsActive;

                await _context.SaveChangesAsync();

                return Ok(new
                {
                    success = true,
                    message = "تم تحديث الصنف بنجاح"
                });
            }
            catch (Exception ex)
            {
                return StatusCode(500, new
                {
                    success = false,
                    message = "خطأ في تحديث الصنف",
                    error = ex.Message
                });
            }
        }

        // DELETE: api/items/{id}
        [HttpDelete("{id}")]
        public async Task<ActionResult> DeleteItem(int id)
        {
            try
            {
                var item = await _context.Items.FindAsync(id);
                if (item == null)
                {
                    return NotFound(new
                    {
                        success = false,
                        message = "الصنف غير موجود"
                    });
                }

                // التحقق من عدم استخدام الصنف في فواتير
                var isUsed = await _context.InvoiceItems.AnyAsync(ii => ii.ItemId == id);
                if (isUsed)
                {
                    return BadRequest(new
                    {
                        success = false,
                        message = "لا يمكن حذف الصنف لأنه مستخدم في فواتير"
                    });
                }

                _context.Items.Remove(item);
                await _context.SaveChangesAsync();

                return Ok(new
                {
                    success = true,
                    message = "تم حذف الصنف بنجاح"
                });
            }
            catch (Exception ex)
            {
                return StatusCode(500, new
                {
                    success = false,
                    message = "خطأ في حذف الصنف",
                    error = ex.Message
                });
            }
        }

        // GET: api/items/materials
        [HttpGet("materials")]
        public async Task<ActionResult> GetMaterials()
        {
            return await GetItems("RawMaterial");
        }

        // GET: api/items/products
        [HttpGet("products")]
        public async Task<ActionResult> GetProducts()
        {
            return await GetItems("Product");
        }

        // GET: api/items/services
        [HttpGet("services")]
        public async Task<ActionResult> GetServices()
        {
            return await GetItems("Service");
        }
    }

    public class CreateItemRequest
    {
        public string ItemCode { get; set; } = string.Empty;
        public string ItemName { get; set; } = string.Empty;
        public ItemType ItemType { get; set; }
        public string? Barcode { get; set; }
        public int UnitId { get; set; }
        public int? CategoryId { get; set; }
        public decimal PurchasePrice { get; set; } = 0;
        public decimal SalePrice { get; set; } = 0;
        public decimal CurrentStock { get; set; } = 0;
        public decimal MinimumStock { get; set; } = 0;
        public decimal? MaximumStock { get; set; }
        public decimal? ReorderLevel { get; set; }
        public string? Description { get; set; }
        public bool IsActive { get; set; } = true;
    }

    public class UpdateItemRequest
    {
        public string ItemCode { get; set; } = string.Empty;
        public string ItemName { get; set; } = string.Empty;
        public ItemType ItemType { get; set; }
        public string? Barcode { get; set; }
        public int UnitId { get; set; }
        public int? CategoryId { get; set; }
        public decimal PurchasePrice { get; set; }
        public decimal SalePrice { get; set; }
        public decimal CurrentStock { get; set; }
        public decimal MinimumStock { get; set; }
        public decimal? MaximumStock { get; set; }
        public decimal? ReorderLevel { get; set; }
        public string? Description { get; set; }
        public bool IsActive { get; set; }
    }
}
