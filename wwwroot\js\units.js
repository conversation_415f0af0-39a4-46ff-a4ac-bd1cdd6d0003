// إدارة وحدات القياس المرجعية - متصل بـ APIs
const API_BASE_URL = '/api';
let units = [];
let filteredUnits = [];

// تحميل الصفحة
document.addEventListener('DOMContentLoaded', function() {
    console.log('🔄 تحميل صفحة وحدات القياس المرجعية...');
    initializePage();
});

// تهيئة الصفحة
async function initializePage() {
    try {
        await loadUnits();
        console.log('✅ تم تحميل صفحة وحدات القياس بنجاح');
    } catch (error) {
        console.error('❌ خطأ في تحميل صفحة وحدات القياس:', error);
        showError('خطأ في تحميل البيانات. يرجى إعادة تحميل الصفحة.');
    }
}

// تحميل وحدات القياس
async function loadUnits() {
    console.log('🔄 تحميل وحدات القياس المرجعية...');
    showLoading();
    
    try {
        const response = await fetch(`${API_BASE_URL}/units`);
        if (response.ok) {
            const result = await response.json();
            if (result.success) {
                units = result.data || [];
                filteredUnits = [...units];
                displayUnits();
                console.log('✅ تم تحميل وحدات القياس:', units.length);
                return;
            }
        }
        
        // في حالة فشل API، عرض قائمة فارغة
        console.error('❌ فشل تحميل وحدات القياس من قاعدة البيانات');
        units = [];
        filteredUnits = [];
        displayUnits();
        showError('لا توجد وحدات قياس في قاعدة البيانات. ابدأ بإضافة وحدة جديدة.');

    } catch (error) {
        console.error('❌ خطأ في تحميل وحدات القياس:', error);
        units = [];
        filteredUnits = [];
        displayUnits();
        showError('خطأ في الاتصال بقاعدة البيانات.');
    } finally {
        hideLoading();
    }
}

// إخفاء التحميل
function hideLoading() {
    // يتم استبدالها بالمحتوى الفعلي
}


// عرض وحدات القياس
function displayUnits() {
    const tbody = document.getElementById('unitsTableBody');
    
    if (!filteredUnits || filteredUnits.length === 0) {
        tbody.innerHTML = `
            <tr>
                <td colspan="9" class="text-center text-muted">
                    <i class="fas fa-inbox fa-3x mb-3"></i>
                    <p>لا توجد وحدات قياس مضافة بعد</p>
                    <button class="btn btn-primary" onclick="showAddModal()">
                        <i class="fas fa-plus"></i> إضافة وحدة جديدة
                    </button>
                </td>
            </tr>
        `;
        return;
    }

    let html = '';
    filteredUnits.forEach((unit, index) => {
        const statusBadge = unit.isActive ?
            '<span class="badge bg-success">نشط</span>' :
            '<span class="badge bg-secondary">غير نشط</span>';

        html += `
            <tr>
                <td><strong>${unit.unitId}</strong></td>
                <td>
                    <strong>${unit.unitName}</strong>
                    <br><small class="text-muted">${unit.unitNameEn || '-'}</small>
                </td>
                <td><span class="badge bg-primary">${unit.unitSymbol}</span></td>
                <td>
                    <div class="text-center">
                        <span class="badge bg-primary">${unit.majorUnit.name}</span>
                        <br><small class="text-muted">${unit.majorUnit.factor}</small>
                    </div>
                </td>
                <td>
                    <div class="text-center">
                        <span class="badge bg-info">${unit.mediumUnit.name}</span>
                        <br><small class="text-muted">${unit.mediumUnit.factor}</small>
                    </div>
                </td>
                <td>
                    <div class="text-center">
                        <span class="badge bg-secondary">${unit.minorUnit.name}</span>
                        <br><small class="text-muted">${unit.minorUnit.factor}</small>
                    </div>
                </td>
                <td><span class="badge bg-warning">${unit.usageCount || 0}</span></td>
                <td>${statusBadge}</td>
                <td>
                    <div class="btn-group btn-group-sm">
                        <button class="btn btn-outline-primary" onclick="editUnit(${unit.unitId})" title="تعديل">
                            <i class="fas fa-edit"></i>
                        </button>
                        <button class="btn btn-outline-info" onclick="viewUnitDetails(${unit.unitId})" title="التفاصيل">
                            <i class="fas fa-eye"></i>
                        </button>
                        <button class="btn btn-outline-danger" onclick="deleteUnit(${unit.unitId})" title="حذف" ${unit.usageCount > 0 ? 'disabled' : ''}>
                            <i class="fas fa-trash"></i>
                        </button>
                    </div>
                </td>
            </tr>
        `;
    });
    
    tbody.innerHTML = html;
}

// الحصول على شارة النوع
function getTypeBadge(type) {
    const types = {
        'weight': '<span class="badge bg-success">وزن</span>',
        'volume': '<span class="badge bg-info">حجم</span>',
        'length': '<span class="badge bg-warning">طول</span>',
        'piece': '<span class="badge bg-primary">قطعة</span>',
        'custom': '<span class="badge bg-secondary">مخصص</span>'
    };
    return types[type] || '<span class="badge bg-light text-dark">غير محدد</span>';
}

// إظهار التحميل
function showLoading() {
    const tbody = document.getElementById('unitsTableBody');
    tbody.innerHTML = `
        <tr>
            <td colspan="9" class="text-center">
                <div class="spinner-border text-primary" role="status">
                    <span class="visually-hidden">جاري التحميل...</span>
                </div>
                <p class="mt-2">جاري تحميل وحدات القياس...</p>
            </td>
        </tr>
    `;
}

// عرض رسالة خطأ
function showError(message) {
    const alertDiv = document.createElement('div');
    alertDiv.className = 'alert alert-danger alert-dismissible fade show position-fixed';
    alertDiv.style.cssText = 'top: 20px; right: 20px; z-index: 9999; min-width: 300px;';
    alertDiv.innerHTML = `
        <i class="fas fa-exclamation-triangle me-2"></i>
        ${message}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    `;
    document.body.appendChild(alertDiv);

    setTimeout(() => {
        if (alertDiv.parentNode) {
            alertDiv.remove();
        }
    }, 5000);
}

// عرض رسالة نجاح
function showSuccess(message) {
    const alertDiv = document.createElement('div');
    alertDiv.className = 'alert alert-success alert-dismissible fade show position-fixed';
    alertDiv.style.cssText = 'top: 20px; right: 20px; z-index: 9999; min-width: 300px;';
    alertDiv.innerHTML = `
        <i class="fas fa-check-circle me-2"></i>
        ${message}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    `;
    document.body.appendChild(alertDiv);

    setTimeout(() => {
        if (alertDiv.parentNode) {
            alertDiv.remove();
        }
    }, 3000);
}

// عرض تحميل الحفظ
function showSaveLoading() {
    const saveBtn = document.querySelector('#unitModal .btn-primary');
    if (saveBtn) {
        saveBtn.disabled = true;
        saveBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>جاري الحفظ...';
    }
}

// إخفاء تحميل الحفظ
function hideSaveLoading() {
    const saveBtn = document.querySelector('#unitModal .btn-primary');
    if (saveBtn) {
        saveBtn.disabled = false;
        saveBtn.innerHTML = '<i class="fas fa-save me-2"></i>حفظ';
    }
}

// إضافة وحدة جديدة
function showAddModal() {
    // إعادة تعيين النموذج
    document.getElementById('unitForm').reset();
    document.getElementById('unitId').value = '';
    document.getElementById('modalTitle').textContent = 'إضافة وحدة قياس هرمية جديدة';

    // تعيين قيم افتراضية
    document.getElementById('majorUnitFactor').value = 1;
    document.getElementById('sortOrder').value = units.length + 1;
    document.getElementById('isActive').checked = true;

    // إظهار النافذة
    const modal = new bootstrap.Modal(document.getElementById('unitModal'));
    modal.show();
}

// تحديث المثال
function updateExample() {
    const majorName = document.getElementById('majorUnitName').value || 'الوحدة الكبرى';
    const mediumName = document.getElementById('mediumUnitName').value || 'الوحدة المتوسطة';
    const minorName = document.getElementById('minorUnitName').value || 'الوحدة الصغرى';
    const mediumFactor = document.getElementById('mediumUnitFactor').value || '0';
    const minorFactor = document.getElementById('minorUnitFactor').value || '0';

    const exampleText = `
        <strong>1 ${majorName}</strong> =
        <strong>${mediumFactor} ${mediumName}</strong> =
        <strong>${minorFactor} ${minorName}</strong>
    `;

    document.getElementById('exampleText').innerHTML = exampleText;
}

// حفظ الوحدة
async function saveUnit() {
    const form = document.getElementById('unitForm');
    if (!form.checkValidity()) {
        form.reportValidity();
        return;
    }

    try {
        showSaveLoading();

        const unitData = getUnitFormData();
        const isEdit = document.getElementById('unitId').value;

        let response;
        if (isEdit) {
            // تحديث وحدة موجودة
            response = await fetch(`${API_BASE_URL}/units/${isEdit}`, {
                method: 'PUT',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify(unitData)
            });
        } else {
            // إضافة وحدة جديدة
            response = await fetch(`${API_BASE_URL}/units`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify(unitData)
            });
        }

        if (response.ok) {
            const result = await response.json();
            if (result.success) {
                showSuccess(result.message || (isEdit ? 'تم تحديث الوحدة بنجاح' : 'تم إضافة الوحدة بنجاح'));

                // إخفاء النافذة وإعادة تحميل البيانات
                bootstrap.Modal.getInstance(document.getElementById('unitModal')).hide();
                await loadUnits();
            } else {
                throw new Error(result.message || 'فشل في حفظ الوحدة');
            }
        } else {
            throw new Error(`HTTP ${response.status}`);
        }

    } catch (error) {
        console.error('❌ خطأ في حفظ الوحدة:', error);
        showError('حدث خطأ أثناء حفظ الوحدة: ' + error.message);
    } finally {
        hideSaveLoading();
    }
}

// جمع بيانات النموذج
function getUnitFormData() {
    return {
        unitName: document.getElementById('unitName').value.trim(),
        unitSymbol: document.getElementById('unitSymbol').value.trim(),
        unitNameEn: document.getElementById('unitNameEn').value.trim(),

        // النظام الهرمي
        majorUnit: {
            name: document.getElementById('majorUnitName').value.trim(),
            factor: 1
        },
        mediumUnit: {
            name: document.getElementById('mediumUnitName').value.trim(),
            factor: parseInt(document.getElementById('mediumUnitFactor').value) || 1
        },
        minorUnit: {
            name: document.getElementById('minorUnitName').value.trim(),
            factor: parseInt(document.getElementById('minorUnitFactor').value) || 1
        },

        description: document.getElementById('description').value.trim(),
        sortOrder: parseInt(document.getElementById('sortOrder').value) || 1,
        isActive: document.getElementById('isActive').checked
    };
}

// تعديل وحدة
function editUnit(unitId) {
    const unit = units.find(u => u.unitId === unitId);
    if (!unit) {
        showError('لم يتم العثور على الوحدة');
        return;
    }

    // ملء النموذج ببيانات الوحدة
    document.getElementById('unitId').value = unit.unitId;
    document.getElementById('unitName').value = unit.unitName || '';
    document.getElementById('unitSymbol').value = unit.unitSymbol || '';
    document.getElementById('unitNameEn').value = unit.unitNameEn || '';

    // النظام الهرمي
    document.getElementById('majorUnitName').value = unit.majorUnit?.name || '';
    document.getElementById('majorUnitFactor').value = unit.majorUnit?.factor || 1;
    document.getElementById('mediumUnitName').value = unit.mediumUnit?.name || '';
    document.getElementById('mediumUnitFactor').value = unit.mediumUnit?.factor || 1;
    document.getElementById('minorUnitName').value = unit.minorUnit?.name || '';
    document.getElementById('minorUnitFactor').value = unit.minorUnit?.factor || 1;

    document.getElementById('description').value = unit.description || '';
    document.getElementById('sortOrder').value = unit.sortOrder || 1;
    document.getElementById('isActive').checked = unit.isActive !== false;

    // تحديث المثال
    updateExample();

    // تحديث العنوان
    document.getElementById('modalTitle').textContent = 'تعديل وحدة القياس';

    // إظهار النافذة
    const modal = new bootstrap.Modal(document.getElementById('unitModal'));
    modal.show();
}

// حذف وحدة
async function deleteUnit(unitId) {
    const unit = units.find(u => u.unitId === unitId);
    if (!unit) {
        showError('لم يتم العثور على الوحدة');
        return;
    }

    if (confirm(`هل أنت متأكد من حذف وحدة "${unit.unitName}"؟\n\nهذا الإجراء لا يمكن التراجع عنه.`)) {
        try {
            const response = await fetch(`${API_BASE_URL}/units/${unitId}`, {
                method: 'DELETE'
            });

            if (response.ok) {
                const result = await response.json();
                if (result.success) {
                    showSuccess(result.message || 'تم حذف الوحدة بنجاح');
                    await loadUnits();
                } else {
                    throw new Error(result.message || 'فشل في حذف الوحدة');
                }
            } else {
                throw new Error(`HTTP ${response.status}`);
            }
        } catch (error) {
            console.error('❌ خطأ في حذف الوحدة:', error);
            showError('حدث خطأ أثناء حذف الوحدة: ' + error.message);
        }
    }
}

// عرض تفاصيل الوحدة
function viewUnitDetails(unitId) {
    const unit = units.find(u => u.unitId === unitId);
    if (!unit) {
        showError('لم يتم العثور على الوحدة');
        return;
    }

    alert(`تفاصيل الوحدة:\n\nالاسم: ${unit.unitName}\nالرمز: ${unit.unitSymbol}\nالنوع: ${unit.unitType}\nمعامل التحويل: ${unit.conversionFactor}\nعدد الاستخدامات: ${unit.usageCount}`);
}

// البحث والفلترة
function searchUnits() {
    const searchTerm = document.getElementById('searchInput').value.toLowerCase();
    const typeFilter = document.getElementById('typeFilter').value;
    const statusFilter = document.getElementById('statusFilter').value;

    filteredUnits = units.filter(unit => {
        const matchesSearch = !searchTerm ||
            unit.unitName.toLowerCase().includes(searchTerm) ||
            unit.unitSymbol.toLowerCase().includes(searchTerm) ||
            unit.unitNameEn?.toLowerCase().includes(searchTerm);

        const matchesType = !typeFilter || unit.unitType === typeFilter;
        const matchesStatus = !statusFilter || unit.isActive.toString() === statusFilter;

        return matchesSearch && matchesType && matchesStatus;
    });

    displayUnits();
}

function filterUnits() {
    searchUnits();
}

// طباعة تقرير الوحدات
function printUnitsReport() {
    alert('طباعة تقرير وحدات القياس - سيتم تطويرها');
}

// وظائف المساعدة
function showSuccess(message) {
    const alertDiv = document.createElement('div');
    alertDiv.className = 'alert alert-success alert-dismissible fade show position-fixed';
    alertDiv.style.cssText = 'top: 20px; right: 20px; z-index: 9999; min-width: 300px;';
    alertDiv.innerHTML = `
        <i class="fas fa-check-circle me-2"></i>
        ${message}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    `;
    document.body.appendChild(alertDiv);

    setTimeout(() => {
        if (alertDiv.parentNode) {
            alertDiv.remove();
        }
    }, 3000);
}

function showSaveLoading() {
    const saveBtn = document.querySelector('button[onclick="saveUnit()"]');
    if (saveBtn) {
        saveBtn.disabled = true;
        saveBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>جاري الحفظ...';
    }
}

function hideSaveLoading() {
    const saveBtn = document.querySelector('button[onclick="saveUnit()"]');
    if (saveBtn) {
        saveBtn.disabled = false;
        saveBtn.innerHTML = '<i class="fas fa-save me-2"></i>حفظ الوحدة';
    }
}

// تسجيل الخروج
function logout() {
    if (confirm('هل أنت متأكد من تسجيل الخروج؟')) {
        localStorage.removeItem('isLoggedIn');
        window.location.href = 'login.html';
    }
}
