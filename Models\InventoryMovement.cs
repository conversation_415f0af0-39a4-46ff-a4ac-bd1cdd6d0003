using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace ANWBakerySystem.Models
{
    /// <summary>
    /// حركة المخزون مع دعم وحدات القياس المتقدمة
    /// Inventory Movement with advanced unit measurement support
    /// </summary>
    public class InventoryMovement
    {
        [Key]
        public int MovementId { get; set; }

        [Required]
        [StringLength(50)]
        [Display(Name = "رقم الحركة")]
        public string MovementNumber { get; set; } = string.Empty;

        [Required]
        [Display(Name = "تاريخ الحركة")]
        public DateTime MovementDate { get; set; } = DateTime.Now;

        [Required]
        [Display(Name = "نوع الحركة")]
        public MovementType MovementType { get; set; }

        [Required]
        [Display(Name = "الصنف")]
        public int ItemId { get; set; }

        [Required]
        [Display(Name = "المخزن")]
        public int WarehouseId { get; set; }

        [Display(Name = "المخزن المصدر")]
        public int? FromWarehouseId { get; set; }

        [Display(Name = "المخزن الهدف")]
        public int? ToWarehouseId { get; set; }

        [Required]
        [Display(Name = "وحدة القياس")]
        public int UnitId { get; set; }

        [Required]
        [Display(Name = "نوع الوحدة")]
        public UnitType UnitType { get; set; }

        /// <summary>
        /// الكمية بالوحدة المختارة
        /// Quantity in selected unit
        /// </summary>
        [Required]
        [Display(Name = "الكمية")]
        [Column(TypeName = "decimal(18,6)")]
        public decimal Quantity { get; set; }

        /// <summary>
        /// الكمية بالوحدة الأساسية (للمخزون)
        /// Quantity in base unit (for inventory)
        /// </summary>
        [Required]
        [Display(Name = "الكمية الأساسية")]
        [Column(TypeName = "decimal(18,6)")]
        public decimal BaseQuantity { get; set; }

        /// <summary>
        /// معامل التحويل من الوحدة المختارة إلى الأساسية
        /// Conversion factor from selected unit to base unit
        /// </summary>
        [Required]
        [Display(Name = "معامل التحويل")]
        [Column(TypeName = "decimal(18,6)")]
        public decimal ConversionFactor { get; set; } = 1;

        /// <summary>
        /// تكلفة الوحدة بالريال اليمني
        /// Unit cost in Yemeni Rial
        /// </summary>
        [Display(Name = "تكلفة الوحدة (ر.ي)")]
        [Column(TypeName = "decimal(18,3)")]
        public decimal UnitCost { get; set; } = 0;

        /// <summary>
        /// إجمالي التكلفة بالريال اليمني
        /// Total cost in Yemeni Rial
        /// </summary>
        [Display(Name = "إجمالي التكلفة (ر.ي)")]
        [Column(TypeName = "decimal(18,3)")]
        public decimal TotalCost { get; set; } = 0;

        /// <summary>
        /// الرصيد قبل الحركة بالوحدة الأساسية
        /// Balance before movement in base unit
        /// </summary>
        [Display(Name = "الرصيد قبل الحركة")]
        [Column(TypeName = "decimal(18,6)")]
        public decimal BalanceBefore { get; set; } = 0;

        /// <summary>
        /// الرصيد بعد الحركة بالوحدة الأساسية
        /// Balance after movement in base unit
        /// </summary>
        [Display(Name = "الرصيد بعد الحركة")]
        [Column(TypeName = "decimal(18,6)")]
        public decimal BalanceAfter { get; set; } = 0;

        [StringLength(100)]
        [Display(Name = "رقم المرجع")]
        public string? ReferenceNumber { get; set; }

        [Display(Name = "رقم الفاتورة")]
        public int? InvoiceId { get; set; }

        [StringLength(500)]
        [Display(Name = "الوصف")]
        public string? Description { get; set; }

        [StringLength(500)]
        [Display(Name = "ملاحظات")]
        public string? Notes { get; set; }

        [StringLength(100)]
        [Display(Name = "رقم الدفعة")]
        public string? BatchNumber { get; set; }

        [Display(Name = "تاريخ الانتهاء")]
        public DateTime? ExpiryDate { get; set; }

        [StringLength(100)]
        [Display(Name = "الرقم التسلسلي")]
        public string? SerialNumber { get; set; }

        [Display(Name = "منشئ بواسطة")]
        public int CreatedBy { get; set; }

        [Display(Name = "تاريخ الإنشاء")]
        public DateTime CreatedAt { get; set; } = DateTime.Now;

        // Navigation properties
        public virtual Item Item { get; set; } = null!;
        public virtual Unit Unit { get; set; } = null!;
        public virtual Warehouse Warehouse { get; set; } = null!;
        public virtual Warehouse? FromWarehouse { get; set; }
        public virtual Warehouse? ToWarehouse { get; set; }
        public virtual Invoice? Invoice { get; set; }
        public virtual User Creator { get; set; } = null!;

        /// <summary>
        /// حساب الكمية الأساسية والتكلفة الإجمالية
        /// Calculate base quantity and total cost
        /// </summary>
        public void CalculateValues()
        {
            BaseQuantity = Quantity * ConversionFactor;
            TotalCost = BaseQuantity * UnitCost;
        }

        /// <summary>
        /// تحديد معامل التحويل حسب نوع الوحدة
        /// Set conversion factor based on unit type
        /// </summary>
        public void SetConversionFactor(Unit unit)
        {
            ConversionFactor = UnitType switch
            {
                UnitType.Large => unit.GetLargeToSmallConversionFactor(),
                UnitType.Medium => unit.GetMediumToSmallConversionFactor(),
                UnitType.Small => 1,
                _ => 1
            };
        }
    }

    public enum MovementType
    {
        [Display(Name = "وارد شراء")]
        PurchaseIn = 1,
        [Display(Name = "صادر بيع")]
        SaleOut = 2,
        [Display(Name = "وارد مرتجع مبيعات")]
        SalesReturnIn = 3,
        [Display(Name = "صادر مرتجع مشتريات")]
        PurchaseReturnOut = 4,
        [Display(Name = "وارد تحويل")]
        TransferIn = 5,
        [Display(Name = "صادر تحويل")]
        TransferOut = 6,
        [Display(Name = "وارد إنتاج")]
        ProductionIn = 7,
        [Display(Name = "صادر إنتاج")]
        ProductionOut = 8,
        [Display(Name = "تسوية موجب")]
        AdjustmentIn = 9,
        [Display(Name = "تسوية سالب")]
        AdjustmentOut = 10,
        [Display(Name = "رصيد افتتاحي")]
        OpeningBalance = 11,
        [Display(Name = "جرد")]
        StockCount = 12,
        [Display(Name = "تالف")]
        Damaged = 13,
        [Display(Name = "منتهي الصلاحية")]
        Expired = 14
    }
}
