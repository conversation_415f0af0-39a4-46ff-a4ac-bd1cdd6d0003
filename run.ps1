# نظام إدارة مخبوزات ANW
Write-Host "========================================" -ForegroundColor Green
Write-Host "   نظام إدارة مخبوزات ANW" -ForegroundColor Yellow
Write-Host "========================================" -ForegroundColor Green
Write-Host ""

# التحقق من .NET
try {
    $dotnetVersion = dotnet --version
    Write-Host "✅ .NET متوفر: $dotnetVersion" -ForegroundColor Green
} catch {
    Write-Host "❌ .NET غير مثبت" -ForegroundColor Red
    Read-Host "اضغط Enter للخروج"
    exit 1
}

# الانتقال لمجلد المشروع
Set-Location "C:\ANW_bakery"

# التحقق من ملف المشروع
if (-not (Test-Path "ANWBakerySystem.csproj")) {
    Write-Host "❌ ملف المشروع غير موجود" -ForegroundColor Red
    Read-Host "اضغط Enter للخروج"
    exit 1
}

# إيقاف العمليات السابقة
Write-Host "🔄 إيقاف العمليات السابقة..." -ForegroundColor Yellow
Get-Process -Name "dotnet" -ErrorAction SilentlyContinue | Stop-Process -Force

# بناء المشروع
Write-Host "🔨 بناء المشروع..." -ForegroundColor Yellow
$buildResult = dotnet build --configuration Release
if ($LASTEXITCODE -ne 0) {
    Write-Host "❌ فشل في بناء المشروع" -ForegroundColor Red
    Read-Host "اضغط Enter للخروج"
    exit 1
}

Write-Host "✅ تم بناء المشروع بنجاح" -ForegroundColor Green
Write-Host ""

# معلومات النظام
Write-Host "📋 معلومات النظام:" -ForegroundColor Cyan
Write-Host "   🌐 الرابط: http://localhost:5000" -ForegroundColor White
Write-Host "   💾 قاعدة البيانات: SQL Server" -ForegroundColor White
Write-Host "   👤 المستخدم: admin" -ForegroundColor White
Write-Host "   🔑 كلمة المرور: admin123" -ForegroundColor White
Write-Host ""

# فتح المتصفح
Write-Host "🌐 فتح المتصفح..." -ForegroundColor Yellow
Start-Sleep -Seconds 2
Start-Process "http://localhost:5000"

# تشغيل التطبيق
Write-Host "🚀 تشغيل النظام..." -ForegroundColor Green
Write-Host "⚠️  لإيقاف النظام اضغط Ctrl+C" -ForegroundColor Red
Write-Host ""

dotnet run --configuration Release --urls "http://localhost:5000"

Read-Host "اضغط Enter للخروج"
