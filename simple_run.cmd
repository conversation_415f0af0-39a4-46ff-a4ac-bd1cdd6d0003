@echo off
echo ========================================
echo    تشغيل مبسط - ANW Bakery System
echo    Simple Run - ANW Bakery System
echo ========================================
echo.

echo 🗄️ حذف قاعدة البيانات القديمة...
if exist ANWBakery.db del ANWBakery.db
if exist "bin\Debug\net8.0\ANWBakery.db" del "bin\Debug\net8.0\ANWBakery.db"

echo.
echo 🔧 تجميع المشروع...
dotnet build --no-restore

if %ERRORLEVEL% NEQ 0 (
    echo ❌ فشل في التجميع
    pause
    exit /b 1
)

echo.
echo ✅ تم التجميع بنجاح
echo.
echo 🚀 تشغيل التطبيق...
echo 🌐 سيكون متاح على: http://localhost:5000
echo.
echo 📝 بيانات تسجيل الدخول:
echo    👤 اسم المستخدم: admin
echo    🔑 كلمة المرور: admin123
echo.
echo ⚠️  ملاحظة: قد تظهر أخطاء في إنشاء البيانات الأولية
echo    لكن التطبيق سيعمل ويمكن إضافة البيانات يدوياً
echo.
echo ⏳ انتظار تشغيل التطبيق...
echo ========================================

dotnet run --no-build
