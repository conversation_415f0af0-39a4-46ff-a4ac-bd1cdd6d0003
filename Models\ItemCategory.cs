using System.ComponentModel.DataAnnotations;

namespace ANWBakerySystem.Models
{
    /// <summary>
    /// فئات المنتجات والأصناف
    /// Item Categories
    /// </summary>
    public class ItemCategory
    {
        [Key]
        public int CategoryId { get; set; }

        [Required]
        [StringLength(50)]
        [Display(Name = "كود الفئة")]
        public string CategoryCode { get; set; } = string.Empty;

        [Required]
        [StringLength(200)]
        [Display(Name = "اسم الفئة")]
        public string CategoryName { get; set; } = string.Empty;

        [StringLength(200)]
        [Display(Name = "الاسم بالإنجليزية")]
        public string? CategoryNameEn { get; set; }

        [StringLength(500)]
        [Display(Name = "الوصف")]
        public string? Description { get; set; }

        [Display(Name = "الفئة الأب")]
        public int? ParentCategoryId { get; set; }

        [Display(Name = "ترتيب العرض")]
        public int DisplayOrder { get; set; } = 0;

        [Display(Name = "نشط")]
        public bool IsActive { get; set; } = true;

        [Display(Name = "تاريخ الإنشاء")]
        public DateTime CreatedAt { get; set; } = DateTime.Now;

        [Display(Name = "تاريخ التحديث")]
        public DateTime? UpdatedAt { get; set; }

        [Display(Name = "منشئ بواسطة")]
        public int CreatedBy { get; set; }

        [Display(Name = "محدث بواسطة")]
        public int? UpdatedBy { get; set; }

        // Navigation properties
        public virtual ItemCategory? ParentCategory { get; set; }
        public virtual ICollection<ItemCategory> SubCategories { get; set; } = new List<ItemCategory>();
        public virtual ICollection<Item> Items { get; set; } = new List<Item>();
        public virtual User Creator { get; set; } = null!;
        public virtual User? Updater { get; set; }
    }
}
