<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>وحدات القياس - نظام ANW</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
</head>
<body>
    <div class="container-fluid">
        <div class="row">
            <!-- Sidebar -->
            <div class="col-md-3 bg-dark text-white p-0">
                <div class="sidebar">
                    <div class="p-3 border-bottom">
                        <h5><i class="fas fa-calculator me-2"></i>نظام ANW</h5>
                        <small>إدارة مخبوزات مبسط</small>
                    </div>
                    <nav class="nav flex-column p-3">
                        <a href="units-simple.html" class="nav-link text-white active">
                            <i class="fas fa-ruler me-2"></i>وحدات القياس
                        </a>
                        <a href="items-simple.html" class="nav-link text-white">
                            <i class="fas fa-boxes me-2"></i>المنتجات والخامات
                        </a>
                        <a href="parties-simple.html" class="nav-link text-white">
                            <i class="fas fa-users me-2"></i>العملاء والموردين
                        </a>
                        <a href="employees-simple.html" class="nav-link text-white">
                            <i class="fas fa-user-tie me-2"></i>الموظفين
                        </a>
                        <a href="invoices-simple.html" class="nav-link text-white">
                            <i class="fas fa-file-invoice me-2"></i>الفواتير
                        </a>
                        <a href="inventory-simple.html" class="nav-link text-white">
                            <i class="fas fa-warehouse me-2"></i>المخزون
                        </a>
                        <a href="cash-simple.html" class="nav-link text-white">
                            <i class="fas fa-cash-register me-2"></i>الصناديق
                        </a>
                        <a href="banks-simple.html" class="nav-link text-white">
                            <i class="fas fa-university me-2"></i>البنوك
                        </a>
                        <a href="owners-simple.html" class="nav-link text-white">
                            <i class="fas fa-crown me-2"></i>الملاك
                        </a>
                        <a href="users-simple.html" class="nav-link text-white">
                            <i class="fas fa-user-cog me-2"></i>المستخدمين
                        </a>
                        <a href="reports-simple.html" class="nav-link text-white">
                            <i class="fas fa-chart-bar me-2"></i>التقارير
                        </a>
                    </nav>
                </div>
            </div>

            <!-- Main Content -->
            <div class="col-md-9">
                <div class="p-4">
                    <!-- Header -->
                    <div class="d-flex justify-content-between align-items-center mb-4">
                        <h2><i class="fas fa-ruler text-primary me-2"></i>إدارة وحدات القياس</h2>
                        <button class="btn btn-success" onclick="addUnit()">
                            <i class="fas fa-plus me-1"></i>إضافة وحدة قياس
                        </button>
                    </div>

                    <!-- Units Summary -->
                    <div class="row mb-4">
                        <div class="col-md-12">
                            <div class="card">
                                <div class="card-header">
                                    <h5><i class="fas fa-info-circle me-2"></i>نظام الوحدات الهرمي</h5>
                                </div>
                                <div class="card-body">
                                    <div class="alert alert-info">
                                        <h6><i class="fas fa-lightbulb me-2"></i>كيف يعمل النظام:</h6>
                                        <ul class="mb-0">
                                            <li><strong>الوحدة الكبرى:</strong> مثل "كيس 50 كجم" - الوحدة الأساسية للشراء</li>
                                            <li><strong>الوحدة المتوسطة:</strong> مثل "كيلوجرام" - وحدة فرعية من الكبرى</li>
                                            <li><strong>الوحدة الصغرى:</strong> مثل "جرام" - أصغر وحدة للقياس الدقيق</li>
                                            <li><strong>التحويل التلقائي:</strong> يتم حساب الأسعار تلقائياً لجميع الوحدات</li>
                                        </ul>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Units Examples -->
                    <div class="row mb-4">
                        <div class="col-md-12">
                            <div class="card">
                                <div class="card-header">
                                    <h5><i class="fas fa-examples me-2"></i>أمثلة على وحدات القياس</h5>
                                </div>
                                <div class="card-body">
                                    <div id="unitsExamples" class="row">
                                        <!-- سيتم ملؤها بـ JavaScript -->
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Units Table -->
                    <div class="card">
                        <div class="card-header">
                            <h5><i class="fas fa-list me-2"></i>وحدات القياس المتاحة</h5>
                        </div>
                        <div class="card-body">
                            <div class="table-responsive">
                                <table class="table table-striped table-hover">
                                    <thead class="table-dark">
                                        <tr>
                                            <th>اسم المجموعة</th>
                                            <th>الوحدة الكبرى</th>
                                            <th>الوحدة المتوسطة</th>
                                            <th>الوحدة الصغرى</th>
                                            <th>معامل التحويل</th>
                                            <th>النوع</th>
                                            <th>الإجراءات</th>
                                        </tr>
                                    </thead>
                                    <tbody id="unitsTableBody">
                                        <!-- سيتم ملؤها بـ JavaScript -->
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Unit Modal -->
    <div class="modal fade" id="unitModal" tabindex="-1">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">إضافة وحدة قياس جديدة</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <form id="unitForm">
                        <div class="mb-3">
                            <label class="form-label">اسم مجموعة الوحدات *</label>
                            <input type="text" id="unitGroupName" class="form-control" required placeholder="مثال: وحدات الوزن">
                            <small class="text-muted">اسم يجمع الوحدات الثلاث معاً</small>
                        </div>

                        <div class="mb-3">
                            <label class="form-label">نوع القياس *</label>
                            <select id="unitType" class="form-control" required>
                                <option value="">اختر النوع</option>
                                <option value="weight">وزن</option>
                                <option value="volume">حجم</option>
                                <option value="length">طول</option>
                                <option value="area">مساحة</option>
                                <option value="count">عدد</option>
                                <option value="other">أخرى</option>
                            </select>
                        </div>

                        <div class="row">
                            <div class="col-md-4">
                                <div class="mb-3">
                                    <label class="form-label">الوحدة الكبرى *</label>
                                    <input type="text" id="majorUnit" class="form-control" required placeholder="مثال: كيس 50 كجم">
                                    <small class="text-muted">الوحدة الأساسية للشراء</small>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="mb-3">
                                    <label class="form-label">الوحدة المتوسطة *</label>
                                    <input type="text" id="mediumUnit" class="form-control" required placeholder="مثال: كيلوجرام">
                                    <small class="text-muted">وحدة فرعية</small>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="mb-3">
                                    <label class="form-label">الوحدة الصغرى *</label>
                                    <input type="text" id="minorUnit" class="form-control" required placeholder="مثال: جرام">
                                    <small class="text-muted">أصغر وحدة</small>
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">عدد الوحدات المتوسطة في الكبرى *</label>
                                    <input type="number" id="majorToMedium" class="form-control" required placeholder="50" onchange="calculateConversions()">
                                    <small class="text-muted">كم كيلوجرام في الكيس</small>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">عدد الوحدات الصغرى في المتوسطة *</label>
                                    <input type="number" id="mediumToMinor" class="form-control" required placeholder="1000" onchange="calculateConversions()">
                                    <small class="text-muted">كم جرام في الكيلوجرام</small>
                                </div>
                            </div>
                        </div>

                        <div class="mb-3">
                            <label class="form-label">إجمالي الوحدات الصغرى في الكبرى</label>
                            <input type="number" id="majorToMinor" class="form-control" readonly>
                            <small class="text-muted">محسوب تلقائياً (50 × 1000 = 50000)</small>
                        </div>

                        <div class="mb-3">
                            <label class="form-label">ملاحظات</label>
                            <textarea id="unitNotes" class="form-control" rows="2" placeholder="ملاحظات إضافية عن هذه الوحدة"></textarea>
                        </div>

                        <div class="form-check mb-3">
                            <input type="checkbox" id="isActive" class="form-check-input" checked>
                            <label class="form-check-label">وحدة نشطة</label>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                    <button type="button" class="btn btn-primary" onclick="saveUnit()">حفظ</button>
                </div>
            </div>
        </div>
    </div>

    <!-- Success/Error Messages -->
    <div id="messages"></div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // البيانات المحلية
        let units = [];
        let nextUnitId = 1;

        // تحميل البيانات عند بدء الصفحة
        document.addEventListener('DOMContentLoaded', function() {
            loadUnits();
            displayUnitsExamples();
            displayUnits();
        });

        // تحميل وحدات القياس
        function loadUnits() {
            try {
                const savedUnits = localStorage.getItem('anw_units');
                if (savedUnits && savedUnits !== 'null') {
                    units = JSON.parse(savedUnits);
                    if (units.length > 0) {
                        nextUnitId = Math.max(...units.map(u => u.id), 0) + 1;
                    }
                } else {
                    // إنشاء وحدات افتراضية
                    units = [
                        {
                            id: 1,
                            groupName: 'وحدات الوزن الثقيل',
                            type: 'weight',
                            majorUnit: 'كيس 50 كجم',
                            mediumUnit: 'كيلوجرام',
                            minorUnit: 'جرام',
                            majorToMedium: 50,
                            mediumToMinor: 1000,
                            majorToMinor: 50000,
                            notes: 'للمواد الثقيلة مثل الدقيق والسكر',
                            active: true
                        },
                        {
                            id: 2,
                            groupName: 'وحدات الوزن الخفيف',
                            type: 'weight',
                            majorUnit: 'كيس 25 كجم',
                            mediumUnit: 'كيلوجرام',
                            minorUnit: 'جرام',
                            majorToMedium: 25,
                            mediumToMinor: 1000,
                            majorToMinor: 25000,
                            notes: 'للمواد الخفيفة مثل البيكنج باودر',
                            active: true
                        },
                        {
                            id: 3,
                            groupName: 'وحدات السوائل',
                            type: 'volume',
                            majorUnit: 'جالون 20 لتر',
                            mediumUnit: 'لتر',
                            minorUnit: 'مليلتر',
                            majorToMedium: 20,
                            mediumToMinor: 1000,
                            majorToMinor: 20000,
                            notes: 'للسوائل مثل الزيت والماء',
                            active: true
                        },
                        {
                            id: 4,
                            groupName: 'وحدات العدد',
                            type: 'count',
                            majorUnit: 'كرتون 24 قطعة',
                            mediumUnit: 'قطعة',
                            minorUnit: 'قطعة',
                            majorToMedium: 24,
                            mediumToMinor: 1,
                            majorToMinor: 24,
                            notes: 'للمواد المعدودة مثل البيض',
                            active: true
                        }
                    ];
                    nextUnitId = 5;
                    saveUnits();
                }
            } catch (error) {
                console.error('خطأ في تحميل وحدات القياس:', error);
                showMessage('خطأ في تحميل البيانات', 'danger');
            }
        }

        // حفظ وحدات القياس
        function saveUnits() {
            localStorage.setItem('anw_units', JSON.stringify(units));
        }

        // عرض أمثلة الوحدات
        function displayUnitsExamples() {
            const examplesDiv = document.getElementById('unitsExamples');

            const examples = [
                {
                    title: 'مثال: دقيق أبيض',
                    major: 'كيس 50 كجم = 3750 ر.ي',
                    medium: 'كيلوجرام = 75 ر.ي',
                    minor: 'جرام = 0.075 ر.ي',
                    color: 'primary'
                },
                {
                    title: 'مثال: زيت طبخ',
                    major: 'جالون 20 لتر = 4000 ر.ي',
                    medium: 'لتر = 200 ر.ي',
                    minor: 'مليلتر = 0.2 ر.ي',
                    color: 'success'
                },
                {
                    title: 'مثال: بيض',
                    major: 'كرتون 30 بيضة = 900 ر.ي',
                    medium: 'بيضة = 30 ر.ي',
                    minor: 'بيضة = 30 ر.ي',
                    color: 'warning'
                }
            ];

            let html = '';
            examples.forEach(example => {
                html += `
                    <div class="col-md-4 mb-3">
                        <div class="card border-${example.color}">
                            <div class="card-body">
                                <h6 class="card-title text-${example.color}">${example.title}</h6>
                                <ul class="list-unstyled mb-0">
                                    <li><i class="fas fa-arrow-up text-${example.color}"></i> ${example.major}</li>
                                    <li><i class="fas fa-arrow-right text-${example.color}"></i> ${example.medium}</li>
                                    <li><i class="fas fa-arrow-down text-${example.color}"></i> ${example.minor}</li>
                                </ul>
                            </div>
                        </div>
                    </div>
                `;
            });

            examplesDiv.innerHTML = html;
        }

        // عرض وحدات القياس
        function displayUnits() {
            const tbody = document.getElementById('unitsTableBody');

            if (units.length === 0) {
                tbody.innerHTML = `
                    <tr>
                        <td colspan="7" class="text-center text-muted">
                            لا توجد وحدات قياس
                            <br>
                            <button class="btn btn-primary mt-2" onclick="addUnit()">إضافة وحدة قياس جديدة</button>
                        </td>
                    </tr>
                `;
                return;
            }

            let html = '';
            units.forEach(unit => {
                const typeLabel = getTypeLabel(unit.type);
                const statusBadge = unit.active ?
                    '<span class="badge bg-success">نشطة</span>' :
                    '<span class="badge bg-secondary">غير نشطة</span>';

                html += `
                    <tr>
                        <td><strong>${unit.groupName}</strong></td>
                        <td>
                            <span class="badge bg-primary">${unit.majorUnit}</span>
                            <br><small class="text-muted">الأساسية</small>
                        </td>
                        <td>
                            <span class="badge bg-info">${unit.mediumUnit}</span>
                            <br><small class="text-muted">1 ${unit.majorUnit} = ${unit.majorToMedium} ${unit.mediumUnit}</small>
                        </td>
                        <td>
                            <span class="badge bg-secondary">${unit.minorUnit}</span>
                            <br><small class="text-muted">1 ${unit.mediumUnit} = ${unit.mediumToMinor} ${unit.minorUnit}</small>
                        </td>
                        <td>
                            <strong>1 : ${unit.majorToMedium} : ${unit.majorToMinor}</strong>
                            <br><small class="text-muted">كبرى : متوسطة : صغرى</small>
                        </td>
                        <td><span class="badge bg-warning">${typeLabel}</span></td>
                        <td>
                            <button class="btn btn-sm btn-warning" onclick="editUnit(${unit.id})">
                                <i class="fas fa-edit"></i>
                            </button>
                            <button class="btn btn-sm btn-info" onclick="testUnit(${unit.id})">
                                <i class="fas fa-calculator"></i>
                            </button>
                            <button class="btn btn-sm btn-danger" onclick="deleteUnit(${unit.id})">
                                <i class="fas fa-trash"></i>
                            </button>
                        </td>
                    </tr>
                `;
            });

            tbody.innerHTML = html;
        }

        // الحصول على تسمية النوع
        function getTypeLabel(type) {
            const types = {
                'weight': 'وزن',
                'volume': 'حجم',
                'length': 'طول',
                'area': 'مساحة',
                'count': 'عدد',
                'other': 'أخرى'
            };
            return types[type] || type;
        }

        // حساب معاملات التحويل
        function calculateConversions() {
            const majorToMedium = parseFloat(document.getElementById('majorToMedium').value) || 0;
            const mediumToMinor = parseFloat(document.getElementById('mediumToMinor').value) || 0;
            const majorToMinor = majorToMedium * mediumToMinor;

            document.getElementById('majorToMinor').value = majorToMinor;
        }

        // إضافة وحدة قياس جديدة
        function addUnit() {
            clearForm();
            document.querySelector('.modal-title').textContent = 'إضافة وحدة قياس جديدة';
            new bootstrap.Modal(document.getElementById('unitModal')).show();
        }

        // تحرير وحدة قياس
        function editUnit(unitId) {
            const unit = units.find(u => u.id === unitId);
            if (unit) {
                document.getElementById('unitGroupName').value = unit.groupName;
                document.getElementById('unitType').value = unit.type;
                document.getElementById('majorUnit').value = unit.majorUnit;
                document.getElementById('mediumUnit').value = unit.mediumUnit;
                document.getElementById('minorUnit').value = unit.minorUnit;
                document.getElementById('majorToMedium').value = unit.majorToMedium;
                document.getElementById('mediumToMinor').value = unit.mediumToMinor;
                document.getElementById('majorToMinor').value = unit.majorToMinor;
                document.getElementById('unitNotes').value = unit.notes || '';
                document.getElementById('isActive').checked = unit.active;

                document.getElementById('unitForm').dataset.editId = unitId;
                document.querySelector('.modal-title').textContent = 'تحرير وحدة القياس';

                new bootstrap.Modal(document.getElementById('unitModal')).show();
            }
        }

        // حذف وحدة قياس
        function deleteUnit(unitId) {
            if (confirm('هل أنت متأكد من حذف هذه الوحدة؟\nسيؤثر هذا على جميع الخامات المرتبطة بها.')) {
                units = units.filter(u => u.id !== unitId);
                saveUnits();
                displayUnits();
                showMessage('تم حذف وحدة القياس بنجاح', 'success');
            }
        }

        // اختبار وحدة القياس
        function testUnit(unitId) {
            const unit = units.find(u => u.id === unitId);
            if (unit) {
                const testPrice = 1000; // سعر افتراضي للاختبار
                const mediumPrice = testPrice / unit.majorToMedium;
                const minorPrice = mediumPrice / unit.mediumToMinor;

                const message = `
                    <strong>اختبار وحدة: ${unit.groupName}</strong><br>
                    إذا كان سعر ${unit.majorUnit} = ${testPrice} ر.ي<br>
                    فإن سعر ${unit.mediumUnit} = ${mediumPrice.toFixed(4)} ر.ي<br>
                    وسعر ${unit.minorUnit} = ${minorPrice.toFixed(6)} ر.ي
                `;

                showMessage(message, 'info');
            }
        }
        // حفظ وحدة القياس
        function saveUnit() {
            const form = document.getElementById('unitForm');
            const editId = form.dataset.editId;

            const unitData = {
                groupName: document.getElementById('unitGroupName').value,
                type: document.getElementById('unitType').value,
                majorUnit: document.getElementById('majorUnit').value,
                mediumUnit: document.getElementById('mediumUnit').value,
                minorUnit: document.getElementById('minorUnit').value,
                majorToMedium: parseFloat(document.getElementById('majorToMedium').value),
                mediumToMinor: parseFloat(document.getElementById('mediumToMinor').value),
                majorToMinor: parseFloat(document.getElementById('majorToMinor').value),
                notes: document.getElementById('unitNotes').value,
                active: document.getElementById('isActive').checked
            };

            // التحقق من البيانات
            if (!unitData.groupName || !unitData.type || !unitData.majorUnit ||
                !unitData.mediumUnit || !unitData.minorUnit ||
                !unitData.majorToMedium || !unitData.mediumToMinor) {
                showMessage('يرجى ملء جميع الحقول المطلوبة', 'danger');
                return;
            }

            // التحقق من صحة معاملات التحويل
            if (unitData.majorToMedium <= 0 || unitData.mediumToMinor <= 0) {
                showMessage('معاملات التحويل يجب أن تكون أكبر من صفر', 'danger');
                return;
            }

            // التحقق من عدم تكرار اسم المجموعة
            const existingUnit = units.find(u => u.groupName === unitData.groupName && u.id != editId);
            if (existingUnit) {
                showMessage('اسم مجموعة الوحدات موجود بالفعل', 'danger');
                return;
            }

            if (editId) {
                // تحديث وحدة موجودة
                const unitIndex = units.findIndex(u => u.id == editId);
                units[unitIndex] = { ...units[unitIndex], ...unitData };
                showMessage('تم تحديث وحدة القياس بنجاح', 'success');
            } else {
                // إضافة وحدة جديدة
                unitData.id = nextUnitId++;
                units.push(unitData);
                showMessage('تم إضافة وحدة القياس بنجاح', 'success');
            }

            saveUnits();
            displayUnits();
            bootstrap.Modal.getInstance(document.getElementById('unitModal')).hide();
        }

        // تنظيف النموذج
        function clearForm() {
            document.getElementById('unitForm').reset();
            document.getElementById('unitForm').removeAttribute('data-edit-id');
            document.getElementById('isActive').checked = true;
            document.getElementById('majorToMinor').value = '';
        }

        // عرض رسالة
        function showMessage(message, type) {
            const messagesDiv = document.getElementById('messages');
            const alertHtml = `
                <div class="alert alert-${type} alert-dismissible fade show position-fixed"
                     style="top: 20px; left: 20px; z-index: 9999; min-width: 300px;">
                    ${message}
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
            `;
            messagesDiv.innerHTML = alertHtml;

            setTimeout(() => {
                const alert = messagesDiv.querySelector('.alert');
                if (alert) {
                    bootstrap.Alert.getOrCreateInstance(alert).close();
                }
            }, 5000);
        }

        // دالة للحصول على وحدات القياس (للاستخدام في صفحات أخرى)
        function getUnitsForSelect() {
            return units.filter(u => u.active);
        }

        // دالة للحصول على وحدة قياس محددة
        function getUnitById(unitId) {
            return units.find(u => u.id === unitId);
        }

        // دالة لحساب السعر حسب الوحدة
        function calculateUnitPrice(unitId, basePrice, unitLevel) {
            const unit = getUnitById(unitId);
            if (!unit) return 0;

            switch(unitLevel) {
                case 'major':
                    return basePrice;
                case 'medium':
                    return basePrice / unit.majorToMedium;
                case 'minor':
                    return (basePrice / unit.majorToMedium) / unit.mediumToMinor;
                default:
                    return 0;
            }
        }

        // تصدير الوظائف للاستخدام العام
        window.unitsManager = {
            getUnitsForSelect: getUnitsForSelect,
            getUnitById: getUnitById,
            calculateUnitPrice: calculateUnitPrice
        };

        console.log('✅ تم تحميل نظام وحدات القياس بنجاح');
    </script>

    <style>
        .sidebar {
            height: 100vh;
            position: fixed;
            width: 25%;
        }
        .nav-link.active {
            background-color: rgba(255,255,255,0.1);
            border-radius: 5px;
        }
        .table th {
            background-color: #343a40;
            color: white;
        }
        .card {
            animation: fadeIn 0.3s;
        }
        @keyframes fadeIn {
            from { opacity: 0; }
            to { opacity: 1; }
        }
        .badge {
            font-size: 0.8em;
        }
    </style>
</body>
</html>
