using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace ANWBakerySystem.Models
{
    /// <summary>
    /// أصناف الفواتير مع دعم وحدات القياس المتقدمة
    /// Invoice Items with advanced unit measurement support
    /// </summary>
    public class InvoiceItem
    {
        [Key]
        public int InvoiceItemId { get; set; }

        [Required]
        [Display(Name = "الفاتورة")]
        public int InvoiceId { get; set; }

        [Required]
        [Display(Name = "الصنف")]
        public int ItemId { get; set; }

        [Required]
        [StringLength(200)]
        [Display(Name = "اسم الصنف")]
        public string ItemName { get; set; } = string.Empty;

        [StringLength(50)]
        [Display(Name = "كود الصنف")]
        public string? ItemCode { get; set; }

        [StringLength(100)]
        [Display(Name = "الباركود")]
        public string? Barcode { get; set; }

        [Required]
        [Display(Name = "وحدة القياس")]
        public int UnitId { get; set; }

        [Required]
        [Display(Name = "نوع الوحدة")]
        public UnitType UnitType { get; set; }

        [Required]
        [StringLength(50)]
        [Display(Name = "اسم الوحدة")]
        public string UnitName { get; set; } = string.Empty;

        /// <summary>
        /// الكمية بالوحدة المختارة
        /// Quantity in selected unit
        /// </summary>
        [Required]
        [Display(Name = "الكمية")]
        [Column(TypeName = "decimal(18,6)")]
        public decimal Quantity { get; set; }

        /// <summary>
        /// الكمية بالوحدة الأساسية (للمخزون)
        /// Quantity in base unit (for inventory)
        /// </summary>
        [Required]
        [Display(Name = "الكمية الأساسية")]
        [Column(TypeName = "decimal(18,6)")]
        public decimal BaseQuantity { get; set; }

        /// <summary>
        /// معامل التحويل من الوحدة المختارة إلى الأساسية
        /// Conversion factor from selected unit to base unit
        /// </summary>
        [Required]
        [Display(Name = "معامل التحويل")]
        [Column(TypeName = "decimal(18,6)")]
        public decimal ConversionFactor { get; set; } = 1;

        /// <summary>
        /// سعر الوحدة بالريال اليمني
        /// Unit price in Yemeni Rial
        /// </summary>
        [Required]
        [Display(Name = "سعر الوحدة (ر.ي)")]
        [Column(TypeName = "decimal(18,3)")]
        public decimal UnitPrice { get; set; }

        /// <summary>
        /// إجمالي السطر قبل الخصم بالريال اليمني
        /// Line total before discount in Yemeni Rial
        /// </summary>
        [Display(Name = "الإجمالي الفرعي (ر.ي)")]
        [Column(TypeName = "decimal(18,3)")]
        public decimal SubTotal { get; set; }

        /// <summary>
        /// مبلغ الخصم بالريال اليمني
        /// Discount amount in Yemeni Rial
        /// </summary>
        [Display(Name = "الخصم (ر.ي)")]
        [Column(TypeName = "decimal(18,3)")]
        public decimal DiscountAmount { get; set; } = 0;

        [Display(Name = "نسبة الخصم")]
        [Column(TypeName = "decimal(5,2)")]
        public decimal DiscountPercentage { get; set; } = 0;

        /// <summary>
        /// مبلغ الضريبة بالريال اليمني
        /// Tax amount in Yemeni Rial
        /// </summary>
        [Display(Name = "الضريبة (ر.ي)")]
        [Column(TypeName = "decimal(18,3)")]
        public decimal TaxAmount { get; set; } = 0;

        [Display(Name = "نسبة الضريبة")]
        [Column(TypeName = "decimal(5,2)")]
        public decimal TaxPercentage { get; set; } = 0;

        /// <summary>
        /// الإجمالي النهائي للسطر بالريال اليمني
        /// Final line total in Yemeni Rial
        /// </summary>
        [Display(Name = "الإجمالي النهائي (ر.ي)")]
        [Column(TypeName = "decimal(18,3)")]
        public decimal TotalAmount { get; set; }

        [StringLength(500)]
        [Display(Name = "ملاحظات")]
        public string? Notes { get; set; }

        [Display(Name = "رقم الدفعة")]
        [StringLength(100)]
        public string? BatchNumber { get; set; }

        [Display(Name = "تاريخ الانتهاء")]
        public DateTime? ExpiryDate { get; set; }

        [Display(Name = "الرقم التسلسلي")]
        [StringLength(100)]
        public string? SerialNumber { get; set; }

        [Display(Name = "ترتيب السطر")]
        public int LineOrder { get; set; } = 0;

        // Navigation properties
        public virtual Invoice Invoice { get; set; } = null!;
        public virtual Item Item { get; set; } = null!;
        public virtual Unit Unit { get; set; } = null!;

        /// <summary>
        /// حساب الإجماليات تلقائياً
        /// Calculate totals automatically
        /// </summary>
        public void CalculateTotals()
        {
            SubTotal = Quantity * UnitPrice;
            
            if (DiscountPercentage > 0)
                DiscountAmount = SubTotal * (DiscountPercentage / 100);
            
            var afterDiscount = SubTotal - DiscountAmount;
            
            if (TaxPercentage > 0)
                TaxAmount = afterDiscount * (TaxPercentage / 100);
            
            TotalAmount = afterDiscount + TaxAmount;
            
            // حساب الكمية الأساسية للمخزون
            BaseQuantity = Quantity * ConversionFactor;
        }

        /// <summary>
        /// تحديد معامل التحويل حسب نوع الوحدة
        /// Set conversion factor based on unit type
        /// </summary>
        public void SetConversionFactor(Unit unit)
        {
            ConversionFactor = UnitType switch
            {
                UnitType.Large => unit.GetLargeToSmallConversionFactor(),
                UnitType.Medium => unit.GetMediumToSmallConversionFactor(),
                UnitType.Small => 1,
                _ => 1
            };
        }
    }
}
