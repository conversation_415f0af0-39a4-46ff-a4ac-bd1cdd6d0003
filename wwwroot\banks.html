<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>البنوك والصناديق - نظام إدارة مخبوزات ANW</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="css/style.css" rel="stylesheet">
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary">
        <div class="container-fluid">
            <a class="navbar-brand" href="dashboard.html">
                <i class="fas fa-bread-slice me-2"></i>
                نظام إدارة مخبوزات ANW
            </a>
            <div class="navbar-nav ms-auto">
                <a class="nav-link" href="dashboard.html">
                    <i class="fas fa-home"></i> الرئيسية
                </a>
                <a class="nav-link" href="#" onclick="logout()">
                    <i class="fas fa-sign-out-alt"></i> تسجيل الخروج
                </a>
            </div>
        </div>
    </nav>

    <div class="container-fluid mt-4">
        <div class="row">
            <!-- Sidebar -->
            <div class="col-md-3">
                <div class="sidebar-container">
                    <div class="card">
                        <div class="card-header">
                            <h5><i class="fas fa-list"></i> القوائم</h5>
                        </div>
                        <div class="list-group list-group-flush">
                            <a href="dashboard.html" class="list-group-item list-group-item-action">
                                <i class="fas fa-tachometer-alt"></i> لوحة التحكم
                            </a>
                            <a href="accounts.html" class="list-group-item list-group-item-action">
                                <i class="fas fa-sitemap"></i> شجرة الحسابات
                            </a>
                            <a href="banks.html" class="list-group-item list-group-item-action active">
                                <i class="fas fa-university"></i> البنوك والصناديق
                            </a>
                            <a href="employees.html" class="list-group-item list-group-item-action">
                                <i class="fas fa-users"></i> الموظفين
                            </a>
                            <a href="journal.html" class="list-group-item list-group-item-action">
                                <i class="fas fa-book"></i> القيود المحاسبية
                            </a>
                            <a href="reports.html" class="list-group-item list-group-item-action">
                                <i class="fas fa-chart-bar"></i> التقارير المالية
                            </a>
                            <a href="units.html" class="list-group-item list-group-item-action">
                                <i class="fas fa-balance-scale"></i> وحدات القياس
                            </a>
                            <a href="parties.html" class="list-group-item list-group-item-action">
                                <i class="fas fa-handshake"></i> العملاء والموردين
                            </a>
                            <a href="items.html" class="list-group-item list-group-item-action">
                                <i class="fas fa-box"></i> المنتجات والخامات
                            </a>
                            <a href="production.html" class="list-group-item list-group-item-action">
                                <i class="fas fa-industry"></i> الإنتاج
                            </a>
                            <a href="invoices.html" class="list-group-item list-group-item-action">
                                <i class="fas fa-file-invoice"></i> الفواتير
                            </a>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Main Content -->
            <div class="col-md-9">
                <!-- Banks and Cash Registers -->
                <div class="row mb-4">
                    <!-- Cash Registers -->
                    <div class="col-md-6">
                        <div class="card">
                            <div class="card-header d-flex justify-content-between align-items-center">
                                <h5><i class="fas fa-cash-register"></i> الصناديق النقدية</h5>
                                <button class="btn btn-success btn-sm" onclick="showAddCashModal()">
                                    <i class="fas fa-plus"></i> إضافة صندوق
                                </button>
                            </div>
                            <div class="card-body">
                                <div id="cashRegistersList">
                                    <!-- Cash registers will be loaded here -->
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Bank Accounts -->
                    <div class="col-md-6">
                        <div class="card">
                            <div class="card-header d-flex justify-content-between align-items-center">
                                <h5><i class="fas fa-university"></i> الحسابات المصرفية</h5>
                                <button class="btn btn-primary btn-sm" onclick="showAddBankModal()">
                                    <i class="fas fa-plus"></i> إضافة حساب بنكي
                                </button>
                            </div>
                            <div class="card-body">
                                <div id="bankAccountsList">
                                    <!-- Bank accounts will be loaded here -->
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Transactions -->
                <div class="card">
                    <div class="card-header d-flex justify-content-between align-items-center">
                        <h5><i class="fas fa-exchange-alt"></i> المعاملات المالية</h5>
                        <div>
                            <button class="btn btn-info btn-sm" onclick="showTransferModal()">
                                <i class="fas fa-exchange-alt"></i> تحويل بين الحسابات
                            </button>
                            <button class="btn btn-warning btn-sm" onclick="showDepositModal()">
                                <i class="fas fa-plus-circle"></i> إيداع
                            </button>
                            <button class="btn btn-danger btn-sm" onclick="showWithdrawModal()">
                                <i class="fas fa-minus-circle"></i> سحب
                            </button>
                        </div>
                    </div>
                    <div class="card-body">
                        <div class="table-responsive">
                            <table class="table table-hover">
                                <thead class="table-dark">
                                    <tr>
                                        <th>التاريخ</th>
                                        <th>نوع المعاملة</th>
                                        <th>من</th>
                                        <th>إلى</th>
                                        <th>المبلغ (ر.ي)</th>
                                        <th>البيان</th>
                                        <th>إجراءات</th>
                                    </tr>
                                </thead>
                                <tbody id="transactionsTableBody">
                                    <!-- Transactions will be loaded here -->
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Add Cash Register Modal -->
    <div class="modal fade" id="cashModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">إضافة صندوق نقدي</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <form id="cashForm">
                        <div class="mb-3">
                            <label for="cashName" class="form-label">اسم الصندوق *</label>
                            <input type="text" class="form-control" id="cashName" required>
                        </div>
                        <div class="mb-3">
                            <label for="cashDescription" class="form-label">الوصف</label>
                            <textarea class="form-control" id="cashDescription" rows="2"></textarea>
                        </div>
                        <div class="mb-3">
                            <label for="cashBalance" class="form-label">الرصيد الافتتاحي (ر.ي)</label>
                            <input type="number" class="form-control" id="cashBalance" step="0.001" value="0">
                        </div>
                        <div class="mb-3">
                            <label for="cashAccount" class="form-label">الحساب المرتبط</label>
                            <select class="form-select" id="cashAccount">
                                <option value="">-- اختر الحساب --</option>
                            </select>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                    <button type="button" class="btn btn-success" onclick="saveCashRegister()">
                        <i class="fas fa-save"></i> حفظ
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Add Bank Account Modal -->
    <div class="modal fade" id="bankModal" tabindex="-1">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">إضافة حساب بنكي</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <form id="bankForm">
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="bankName" class="form-label">اسم البنك *</label>
                                    <input type="text" class="form-control" id="bankName" required>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="accountNumber" class="form-label">رقم الحساب *</label>
                                    <input type="text" class="form-control" id="accountNumber" required>
                                </div>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="accountType" class="form-label">نوع الحساب</label>
                                    <select class="form-select" id="accountType">
                                        <option value="current">حساب جاري</option>
                                        <option value="savings">حساب توفير</option>
                                        <option value="fixed">وديعة ثابتة</option>
                                    </select>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="bankBalance" class="form-label">الرصيد الحالي (ر.ي)</label>
                                    <input type="number" class="form-control" id="bankBalance" step="0.001" value="0">
                                </div>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="branchName" class="form-label">اسم الفرع</label>
                                    <input type="text" class="form-control" id="branchName">
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="iban" class="form-label">رقم الآيبان</label>
                                    <input type="text" class="form-control" id="iban">
                                </div>
                            </div>
                        </div>
                        <div class="mb-3">
                            <label for="bankAccount" class="form-label">الحساب المرتبط</label>
                            <select class="form-select" id="bankAccount">
                                <option value="">-- اختر الحساب --</option>
                            </select>
                        </div>
                        <div class="mb-3">
                            <label for="bankNotes" class="form-label">ملاحظات</label>
                            <textarea class="form-control" id="bankNotes" rows="2"></textarea>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                    <button type="button" class="btn btn-primary" onclick="saveBankAccount()">
                        <i class="fas fa-save"></i> حفظ
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Transfer Modal -->
    <div class="modal fade" id="transferModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">تحويل بين الحسابات</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <form id="transferForm">
                        <div class="mb-3">
                            <label for="fromAccount" class="form-label">من الحساب *</label>
                            <select class="form-select" id="fromAccount" required>
                                <option value="">-- اختر الحساب --</option>
                            </select>
                        </div>
                        <div class="mb-3">
                            <label for="toAccount" class="form-label">إلى الحساب *</label>
                            <select class="form-select" id="toAccount" required>
                                <option value="">-- اختر الحساب --</option>
                            </select>
                        </div>
                        <div class="mb-3">
                            <label for="transferAmount" class="form-label">المبلغ (ر.ي) *</label>
                            <input type="number" class="form-control" id="transferAmount" step="0.001" required>
                        </div>
                        <div class="mb-3">
                            <label for="transferDescription" class="form-label">البيان</label>
                            <textarea class="form-control" id="transferDescription" rows="2"></textarea>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                    <button type="button" class="btn btn-info" onclick="processTransfer()">
                        <i class="fas fa-exchange-alt"></i> تحويل
                    </button>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="js/auth-simple.js"></script>
    <script src="js/banks.js"></script>
</body>
</html>
