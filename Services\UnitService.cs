using ANWBakerySystem.Models;
using ANWBakerySystem.Repositories;

namespace ANWBakerySystem.Services
{
    /// <summary>
    /// تطبيق خدمة وحدات القياس المتقدمة
    /// Advanced Unit Service Implementation
    /// </summary>
    public class UnitService : IUnitService
    {
        private readonly IUnitRepository _unitRepository;

        public UnitService(IUnitRepository unitRepository)
        {
            _unitRepository = unitRepository;
        }

        public async Task<IEnumerable<Unit>> GetAllUnitsAsync()
        {
            return await _unitRepository.GetAllUnitsAsync();
        }

        public async Task<IEnumerable<Unit>> GetActiveUnitsAsync()
        {
            return await _unitRepository.GetActiveUnitsAsync();
        }

        public async Task<Unit?> GetUnitByIdAsync(int unitId)
        {
            return await _unitRepository.GetUnitByIdAsync(unitId);
        }

        public async Task<UnitResult> CreateUnitAsync(CreateUnitRequest request)
        {
            try
            {
                // التحقق من صحة البيانات
                var validationErrors = ValidateCreateUnitRequest(request);
                if (validationErrors.Any())
                {
                    return new UnitResult
                    {
                        Success = false,
                        Message = "بيانات غير صالحة",
                        Errors = validationErrors
                    };
                }

                // التحقق من عدم تكرار اسم الوحدة
                if (await _unitRepository.UnitNameExistsAsync(request.UnitName))
                {
                    return new UnitResult
                    {
                        Success = false,
                        Message = "اسم الوحدة موجود مسبقاً"
                    };
                }

                // إنشاء الوحدة الجديدة
                var unit = new Unit
                {
                    UnitName = request.UnitName,
                    UnitSymbol = request.UnitSymbol,
                    UnitNameEn = request.UnitNameEn,
                    LargeUnitCount = request.LargeUnitCount,
                    LargeUnitName = request.LargeUnitName,
                    MediumUnitCount = request.MediumUnitCount,
                    MediumUnitName = request.MediumUnitName,
                    SmallUnitCount = request.SmallUnitCount,
                    SmallUnitName = request.SmallUnitName,
                    BaseUnitName = request.BaseUnitName,
                    ConversionNotes = request.ConversionNotes,
                    CreatedBy = request.CreatedBy,
                    CreatedAt = DateTime.Now,
                    IsActive = true
                };

                var createdUnit = await _unitRepository.CreateUnitAsync(unit);

                return new UnitResult
                {
                    Success = true,
                    Unit = createdUnit,
                    Message = "تم إنشاء الوحدة بنجاح"
                };
            }
            catch (Exception ex)
            {
                return new UnitResult
                {
                    Success = false,
                    Message = $"خطأ في إنشاء الوحدة: {ex.Message}"
                };
            }
        }

        public async Task<UnitResult> UpdateUnitAsync(int unitId, UpdateUnitRequest request)
        {
            try
            {
                var unit = await _unitRepository.GetUnitByIdAsync(unitId);
                if (unit == null)
                {
                    return new UnitResult
                    {
                        Success = false,
                        Message = "الوحدة غير موجودة"
                    };
                }

                // التحقق من صحة البيانات
                var validationErrors = ValidateUpdateUnitRequest(request);
                if (validationErrors.Any())
                {
                    return new UnitResult
                    {
                        Success = false,
                        Message = "بيانات غير صالحة",
                        Errors = validationErrors
                    };
                }

                // التحقق من عدم تكرار اسم الوحدة (إذا تم تغييره)
                if (unit.UnitName != request.UnitName && 
                    await _unitRepository.UnitNameExistsAsync(request.UnitName))
                {
                    return new UnitResult
                    {
                        Success = false,
                        Message = "اسم الوحدة موجود مسبقاً"
                    };
                }

                // تحديث بيانات الوحدة
                unit.UnitName = request.UnitName;
                unit.UnitSymbol = request.UnitSymbol;
                unit.UnitNameEn = request.UnitNameEn;
                unit.LargeUnitCount = request.LargeUnitCount;
                unit.LargeUnitName = request.LargeUnitName;
                unit.MediumUnitCount = request.MediumUnitCount;
                unit.MediumUnitName = request.MediumUnitName;
                unit.SmallUnitCount = request.SmallUnitCount;
                unit.SmallUnitName = request.SmallUnitName;
                unit.BaseUnitName = request.BaseUnitName;
                unit.ConversionNotes = request.ConversionNotes;
                unit.IsActive = request.IsActive;
                unit.UpdatedBy = request.UpdatedBy;
                unit.UpdatedAt = DateTime.Now;

                var updatedUnit = await _unitRepository.UpdateUnitAsync(unit);

                return new UnitResult
                {
                    Success = true,
                    Unit = updatedUnit,
                    Message = "تم تحديث الوحدة بنجاح"
                };
            }
            catch (Exception ex)
            {
                return new UnitResult
                {
                    Success = false,
                    Message = $"خطأ في تحديث الوحدة: {ex.Message}"
                };
            }
        }

        public async Task<bool> DeleteUnitAsync(int unitId)
        {
            return await _unitRepository.DeleteUnitAsync(unitId);
        }

        #region Advanced Conversion Calculations

        public async Task<ConversionResult> ConvertQuantityAsync(ConvertQuantityRequest request)
        {
            try
            {
                // التحقق من صحة الطلب
                if (request.Quantity <= 0)
                {
                    return new ConversionResult
                    {
                        Success = false,
                        Message = "الكمية يجب أن تكون أكبر من الصفر"
                    };
                }

                // الحصول على معامل التحويل
                var conversionFactor = await _unitRepository.GetConversionFactorAsync(
                    request.FromUnitId, request.ToUnitId, request.FromUnitType, request.ToUnitType);

                // حساب الكمية المحولة
                var convertedQuantity = request.Quantity * conversionFactor;

                // الحصول على معلومات التحويل
                var conversionInfo = await GetConversionInfoAsync(
                    request.FromUnitId, request.ToUnitId, request.FromUnitType, request.ToUnitType);

                // حفظ سجل التحويل إذا كان مطلوباً
                if (request.UserId.HasValue)
                {
                    await SaveConversionHistoryAsync(request, convertedQuantity, conversionFactor);
                }

                return new ConversionResult
                {
                    Success = true,
                    ConvertedQuantity = convertedQuantity,
                    ConversionFactor = conversionFactor,
                    Message = "تم التحويل بنجاح",
                    ConversionInfo = conversionInfo
                };
            }
            catch (Exception ex)
            {
                return new ConversionResult
                {
                    Success = false,
                    Message = $"خطأ في التحويل: {ex.Message}"
                };
            }
        }

        public async Task<decimal> ConvertToBaseUnitAsync(decimal quantity, int unitId, UnitType unitType)
        {
            return await _unitRepository.ConvertToBaseUnitAsync(quantity, unitId, unitType);
        }

        public async Task<decimal> ConvertFromBaseUnitAsync(decimal baseQuantity, int unitId, UnitType unitType)
        {
            return await _unitRepository.ConvertFromBaseUnitAsync(baseQuantity, unitId, unitType);
        }

        public async Task<decimal> CalculatePriceByUnitAsync(decimal basePrice, int unitId, UnitType unitType)
        {
            return await _unitRepository.CalculatePriceByUnitTypeAsync(basePrice, unitId, unitType);
        }

        #endregion

        #region Validation

        public async Task<bool> ValidateUnitConversionAsync(int fromUnitId, int toUnitId, UnitType fromType, UnitType toType)
        {
            try
            {
                var fromUnit = await _unitRepository.GetUnitByIdAsync(fromUnitId);
                var toUnit = await _unitRepository.GetUnitByIdAsync(toUnitId);

                if (fromUnit == null || toUnit == null)
                    return false;

                // التحقق من أن الوحدات نشطة
                if (!fromUnit.IsActive || !toUnit.IsActive)
                    return false;

                // التحقق من صحة أنواع الوحدات
                if (!IsValidUnitType(fromType) || !IsValidUnitType(toType))
                    return false;

                return true;
            }
            catch
            {
                return false;
            }
        }

        public async Task<UnitConversionInfo> GetConversionInfoAsync(int fromUnitId, int toUnitId, UnitType fromType, UnitType toType)
        {
            var fromUnit = await _unitRepository.GetUnitByIdAsync(fromUnitId);
            var toUnit = await _unitRepository.GetUnitByIdAsync(toUnitId);
            var conversionFactor = await _unitRepository.GetConversionFactorAsync(fromUnitId, toUnitId, fromType, toType);

            var fromUnitName = GetUnitTypeName(fromUnit!, fromType);
            var toUnitName = GetUnitTypeName(toUnit!, toType);

            return new UnitConversionInfo
            {
                FromUnit = fromUnit!,
                ToUnit = toUnit!,
                FromUnitType = fromType,
                ToUnitType = toType,
                ConversionFactor = conversionFactor,
                ConversionDescription = $"1 {fromUnitName} = {conversionFactor} {toUnitName}"
            };
        }

        #endregion

        #region Reports

        public async Task<IEnumerable<UnitUsageReport>> GetUnitUsageReportAsync()
        {
            var units = await _unitRepository.GetAllUnitsAsync();
            var reports = new List<UnitUsageReport>();

            foreach (var unit in units)
            {
                var report = new UnitUsageReport
                {
                    Unit = unit,
                    ItemsCount = 0, // يحتاج ربط مع جدول المنتجات
                    ConversionsCount = 0, // يحتاج ربط مع جدول التحويلات
                    LastUsed = unit.UpdatedAt ?? unit.CreatedAt,
                    IsActive = unit.IsActive
                };
                reports.Add(report);
            }

            return reports.OrderByDescending(r => r.ItemsCount);
        }

        public async Task<IEnumerable<UnitConversion>> GetConversionHistoryAsync(int unitId)
        {
            return await _unitRepository.GetConversionsAsync(unitId, unitId);
        }

        #endregion

        #region Private Helper Methods

        private IEnumerable<string> ValidateCreateUnitRequest(CreateUnitRequest request)
        {
            var errors = new List<string>();

            if (string.IsNullOrWhiteSpace(request.UnitName))
                errors.Add("اسم الوحدة مطلوب");

            if (string.IsNullOrWhiteSpace(request.UnitSymbol))
                errors.Add("رمز الوحدة مطلوب");

            if (request.LargeUnitCount <= 0)
                errors.Add("عدد الوحدة الكبرى يجب أن يكون أكبر من الصفر");

            if (request.MediumUnitCount <= 0)
                errors.Add("عدد الوحدة المتوسطة يجب أن يكون أكبر من الصفر");

            if (request.SmallUnitCount <= 0)
                errors.Add("عدد الوحدة الصغرى يجب أن يكون أكبر من الصفر");

            if (string.IsNullOrWhiteSpace(request.BaseUnitName))
                errors.Add("اسم الوحدة الأساسية مطلوب");

            return errors;
        }

        private IEnumerable<string> ValidateUpdateUnitRequest(UpdateUnitRequest request)
        {
            var errors = new List<string>();

            if (string.IsNullOrWhiteSpace(request.UnitName))
                errors.Add("اسم الوحدة مطلوب");

            if (string.IsNullOrWhiteSpace(request.UnitSymbol))
                errors.Add("رمز الوحدة مطلوب");

            if (request.LargeUnitCount <= 0)
                errors.Add("عدد الوحدة الكبرى يجب أن يكون أكبر من الصفر");

            if (request.MediumUnitCount <= 0)
                errors.Add("عدد الوحدة المتوسطة يجب أن يكون أكبر من الصفر");

            if (request.SmallUnitCount <= 0)
                errors.Add("عدد الوحدة الصغرى يجب أن يكون أكبر من الصفر");

            if (string.IsNullOrWhiteSpace(request.BaseUnitName))
                errors.Add("اسم الوحدة الأساسية مطلوب");

            return errors;
        }

        private bool IsValidUnitType(UnitType unitType)
        {
            return Enum.IsDefined(typeof(UnitType), unitType);
        }

        private string GetUnitTypeName(Unit unit, UnitType unitType)
        {
            return unitType switch
            {
                UnitType.Large => unit.LargeUnitName,
                UnitType.Medium => unit.MediumUnitName,
                UnitType.Small => unit.SmallUnitName,
                UnitType.Base => unit.BaseUnitName,
                _ => unit.UnitName
            };
        }

        private async Task SaveConversionHistoryAsync(ConvertQuantityRequest request, decimal convertedQuantity, decimal conversionFactor)
        {
            var conversion = new UnitConversion
            {
                FromUnitId = request.FromUnitId,
                ToUnitId = request.ToUnitId,
                FromQuantity = request.Quantity,
                ToQuantity = convertedQuantity,
                ConversionFactor = conversionFactor,
                ConversionType = request.FromUnitType.ToString(),
                ReferenceNumber = request.ReferenceNumber,
                OperationType = request.OperationType,
                ConvertedBy = request.UserId ?? 0,
                ConversionDate = DateTime.Now
            };

            await _unitRepository.CreateConversionAsync(conversion);
        }

        #endregion
    }
}
