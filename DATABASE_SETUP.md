# 🗄️ إعداد قاعدة البيانات - ANW Bakery System

## 📋 **خطوات إنشاء قاعدة البيانات:**

### **🚀 الطريقة السريعة:**
```cmd
quick_start.cmd
```
- ✅ **يتحقق من وجود قاعدة البيانات**
- ✅ **ينشئها تلقائياً إذا لم تكن موجودة**
- ✅ **يشغل التطبيق مباشرة**

### **🔧 الطريقة اليدوية:**

#### **1. إنشاء قاعدة البيانات:**
```cmd
create_database.cmd
```

#### **2. أو خطوة بخطوة:**
```cmd
cd C:\ANW_bakery
dotnet clean
dotnet restore
dotnet build
dotnet ef migrations add InitialCreate
dotnet ef database update
dotnet run
```

## 📁 **ملفات قاعدة البيانات:**

### **📄 ملف قاعدة البيانات:**
- **الاسم:** `ANWBakery.db`
- **النوع:** SQLite Database
- **الموقع:** `C:\ANW_bakery\ANWBakery.db`

### **📂 مجلد Migrations:**
- **الموقع:** `C:\ANW_bakery\Migrations\`
- **يحتوي على:** ملفات C# لإنشاء الجداول

## 🗃️ **جداول قاعدة البيانات:**

### **👥 إدارة المستخدمين:**
- `Users` - المستخدمين
- `Permissions` - الصلاحيات
- `UserPermissions` - صلاحيات المستخدمين

### **📊 شجرة الحسابات:**
- `Accounts` - الحسابات
- `JournalEntries` - القيود اليومية
- `JournalEntryLines` - سطور القيود

### **📦 إدارة المخزون:**
- `Units` - وحدات القياس
- `Items` - الأصناف والمنتجات
- `ItemCategories` - فئات الأصناف
- `Inventories` - المخزون
- `InventoryMovements` - حركات المخزون
- `Warehouses` - المخازن

### **🧾 الفواتير:**
- `Invoices` - الفواتير
- `InvoiceItems` - أصناف الفواتير

### **👥 العملاء والموردين:**
- `Parties` - العملاء والموردين
- `PartyTransactions` - معاملات الأطراف

### **🏦 البنوك والصناديق:**
- `Banks` - البنوك
- `BankAccounts` - الحسابات البنكية
- `BankTransactions` - الحركات البنكية
- `CashRegisters` - الصناديق
- `CashTransactions` - الحركات النقدية

### **🍞 الإنتاج والوصفات:**
- `Recipes` - الوصفات
- `RecipeItems` - مكونات الوصفات
- `ProductionOrders` - أوامر الإنتاج

### **🔗 جداول الربط:**
- `UnitConversions` - تحويلات الوحدات

## 📊 **البيانات الأساسية:**

### **👤 المستخدم الافتراضي:**
- **اسم المستخدم:** `admin`
- **كلمة المرور:** `admin123`
- **الدور:** مدير النظام
- **الصلاحيات:** جميع الصلاحيات

### **🏦 الحسابات الأساسية:**
1. **الأصول** (كود: 1)
2. **الخصوم** (كود: 2)
3. **حقوق الملكية** (كود: 3)
4. **الإيرادات** (كود: 4)
5. **المصروفات** (كود: 5)

### **💰 الصندوق الافتراضي:**
- **الاسم:** الصندوق الرئيسي
- **الموقع:** المكتب الرئيسي
- **الرصيد الافتتاحي:** 0

### **🏦 البنك الافتراضي:**
- **الاسم:** البنك الأهلي اليمني
- **الكود:** NBY
- **Swift Code:** NBYEYESA

## ✅ **التحقق من نجاح الإعداد:**

### **🔍 ملفات يجب أن تكون موجودة:**
- ✅ `ANWBakery.db` - ملف قاعدة البيانات
- ✅ `Migrations/` - مجلد الهجرة
- ✅ `Migrations/[timestamp]_InitialCreate.cs` - ملف الهجرة

### **🌐 اختبار التطبيق:**
1. **تشغيل:** `dotnet run`
2. **فتح المتصفح:** `http://localhost:5000`
3. **تسجيل الدخول:** admin / admin123
4. **التحقق من الصفحات:** جميع الصفحات تعمل بدون أخطاء

## 🚨 **حل المشاكل الشائعة:**

### **❌ خطأ: "No migrations found"**
```cmd
dotnet ef migrations add InitialCreate
dotnet ef database update
```

### **❌ خطأ: "Database already exists"**
```cmd
del ANWBakery.db
dotnet ef database update
```

### **❌ خطأ: "EF Tools not found"**
```cmd
dotnet tool install --global dotnet-ef
```

### **❌ خطأ: "Build failed"**
```cmd
dotnet clean
dotnet restore
dotnet build
```

## 🎯 **النتيجة النهائية:**

✅ **قاعدة بيانات SQLite حقيقية**
✅ **جميع الجداول والعلاقات**
✅ **بيانات أساسية جاهزة**
✅ **مستخدم admin جاهز**
✅ **حسابات أساسية جاهزة**
✅ **نظام نظيف 100% بدون بيانات وهمية**
