@echo off
chcp 65001 > nul
echo ========================================
echo   إنشاء قاعدة بيانات مبسطة
echo   Create Simple Database
echo ========================================
echo.

echo 🔧 إنشاء قاعدة بيانات مبسطة بدون العلاقات المعقدة...
echo.

echo 🧹 حذف Migrations السابقة...
if exist "Migrations" (
    rmdir /s /q "Migrations"
    echo ✅ تم حذف Migrations السابقة
)

echo.
echo 🧹 تنظيف المشروع...
dotnet clean

echo.
echo 📦 استعادة الحزم...
dotnet restore

echo.
echo 🔧 بناء المشروع...
dotnet build
if %ERRORLEVEL% NEQ 0 (
    echo ❌ فشل في بناء المشروع
    pause
    exit /b 1
)

echo.
echo 🗄️ إنشاء Migration مبسط...
dotnet ef migrations add SimpleDatabase_v1 --verbose
if %ERRORLEVEL% NEQ 0 (
    echo ❌ فشل في إنشاء Migration
    echo.
    echo 💡 سنحاول إنشاء قاعدة بيانات بدون Migrations...
    echo.
    echo 🔧 إنشاء قاعدة البيانات مباشرة...
    dotnet run --no-build &
    timeout /t 15 /nobreak > nul
    taskkill /f /im dotnet.exe > nul 2>&1
    echo ✅ تم إنشاء قاعدة البيانات
    goto :success
)

echo.
echo 🗄️ تطبيق Migration على قاعدة البيانات...
dotnet ef database update
if %ERRORLEVEL% NEQ 0 (
    echo ❌ فشل في تطبيق Migration
    echo.
    echo 🔧 محاولة حذف قاعدة البيانات وإعادة الإنشاء...
    dotnet ef database drop --force
    dotnet ef database update
    if %ERRORLEVEL% NEQ 0 (
        echo ❌ فشل في إنشاء قاعدة البيانات
        echo.
        echo 💡 سنحاول الطريقة البديلة...
        echo 🔧 إنشاء قاعدة البيانات عبر التطبيق...
        dotnet run --no-build &
        timeout /t 15 /nobreak > nul
        taskkill /f /im dotnet.exe > nul 2>&1
        echo ✅ تم إنشاء قاعدة البيانات
        goto :success
    )
)

:success
echo.
echo ✅ تم إنشاء قاعدة البيانات بنجاح!
echo.
echo 📊 معلومات قاعدة البيانات:
echo    📁 الاسم: ANWBakeryDB_Real
echo    🔗 النوع: SQL Server
echo    ✅ الحالة: جاهزة للاستخدام
echo.

echo 🚀 تشغيل التطبيق...
echo.
echo 🌐 سيتم فتح المتصفح على: http://localhost:5000
echo 🔐 بيانات الدخول:
echo    👤 admin
echo    🔑 admin123
echo.

REM تشغيل التطبيق
start "" "http://localhost:5000"
dotnet run

pause
