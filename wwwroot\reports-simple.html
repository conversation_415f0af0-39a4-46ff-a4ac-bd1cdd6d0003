<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>التقارير - نظام ANW</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
</head>
<body>
    <div class="container-fluid">
        <div class="row">
            <!-- Sidebar -->
            <div class="col-md-3 bg-dark text-white p-0">
                <div class="sidebar">
                    <div class="p-3 border-bottom">
                        <h5><i class="fas fa-calculator me-2"></i>نظام ANW</h5>
                        <small>إدارة مخبوزات مبسط</small>
                    </div>
                    <nav class="nav flex-column p-3">
                        <a href="units-simple.html" class="nav-link text-white">
                            <i class="fas fa-ruler me-2"></i>وحدات القياس
                        </a>
                        <a href="items-simple.html" class="nav-link text-white">
                            <i class="fas fa-boxes me-2"></i>المنتجات والخامات
                        </a>
                        <a href="parties-simple.html" class="nav-link text-white">
                            <i class="fas fa-users me-2"></i>العملاء والموردين
                        </a>
                        <a href="employees-simple.html" class="nav-link text-white">
                            <i class="fas fa-user-tie me-2"></i>الموظفين
                        </a>
                        <a href="invoices-simple.html" class="nav-link text-white">
                            <i class="fas fa-file-invoice me-2"></i>الفواتير
                        </a>
                        <a href="inventory-simple.html" class="nav-link text-white">
                            <i class="fas fa-warehouse me-2"></i>المخزون
                        </a>
                        <a href="cash-simple.html" class="nav-link text-white">
                            <i class="fas fa-cash-register me-2"></i>الصناديق
                        </a>
                        <a href="banks-simple.html" class="nav-link text-white">
                            <i class="fas fa-university me-2"></i>البنوك
                        </a>
                        <a href="owners-simple.html" class="nav-link text-white">
                            <i class="fas fa-crown me-2"></i>الملاك
                        </a>
                        <a href="users-simple.html" class="nav-link text-white">
                            <i class="fas fa-user-cog me-2"></i>المستخدمين
                        </a>
                        <a href="reports-simple.html" class="nav-link text-white active">
                            <i class="fas fa-chart-bar me-2"></i>التقارير
                        </a>
                    </nav>
                </div>
            </div>

            <!-- Main Content -->
            <div class="col-md-9">
                <div class="p-4">
                    <!-- Header -->
                    <div class="d-flex justify-content-between align-items-center mb-4">
                        <h2><i class="fas fa-chart-bar text-primary me-2"></i>التقارير والإحصائيات</h2>
                        <div>
                            <button class="btn btn-success" onclick="exportAllReports()">
                                <i class="fas fa-download me-1"></i>تصدير جميع التقارير
                            </button>
                            <button class="btn btn-info" onclick="printDashboard()">
                                <i class="fas fa-print me-1"></i>طباعة لوحة التحكم
                            </button>
                        </div>
                    </div>

                    <!-- Dashboard Summary -->
                    <div class="row mb-4">
                        <div class="col-md-12">
                            <div class="card">
                                <div class="card-header">
                                    <h5><i class="fas fa-tachometer-alt me-2"></i>لوحة التحكم الرئيسية</h5>
                                </div>
                                <div class="card-body">
                                    <div id="dashboardSummary" class="row">
                                        <!-- سيتم ملؤها بـ JavaScript -->
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Reports Tabs -->
                    <ul class="nav nav-tabs mb-3">
                        <li class="nav-item">
                            <a class="nav-link active" onclick="showTab('financial')">التقارير المالية</a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" onclick="showTab('inventory')">تقارير المخزون</a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" onclick="showTab('sales')">تقارير المبيعات</a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" onclick="showTab('accounts')">كشوف الحسابات</a>
                        </li>
                    </ul>

                    <!-- Financial Reports Tab -->
                    <div id="financialTab" class="tab-content">
                        <div class="row">
                            <div class="col-md-6">
                                <div class="card">
                                    <div class="card-header">
                                        <h6><i class="fas fa-cash-register me-2"></i>تقرير الصناديق</h6>
                                    </div>
                                    <div class="card-body">
                                        <div id="cashBoxesReport">
                                            <!-- سيتم ملؤها بـ JavaScript -->
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="card">
                                    <div class="card-header">
                                        <h6><i class="fas fa-university me-2"></i>تقرير البنوك</h6>
                                    </div>
                                    <div class="card-body">
                                        <div id="banksReport">
                                            <!-- سيتم ملؤها بـ JavaScript -->
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="row mt-3">
                            <div class="col-md-12">
                                <div class="card">
                                    <div class="card-header">
                                        <h6><i class="fas fa-crown me-2"></i>تقرير رأس المال والملاك</h6>
                                    </div>
                                    <div class="card-body">
                                        <div id="ownersReport">
                                            <!-- سيتم ملؤها بـ JavaScript -->
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Inventory Reports Tab -->
                    <div id="inventoryTab" class="tab-content" style="display: none;">
                        <div class="row">
                            <div class="col-md-6">
                                <div class="card">
                                    <div class="card-header">
                                        <h6><i class="fas fa-boxes me-2"></i>تقرير المخزون الحالي</h6>
                                    </div>
                                    <div class="card-body">
                                        <div id="currentInventoryReport">
                                            <!-- سيتم ملؤها بـ JavaScript -->
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="card">
                                    <div class="card-header">
                                        <h6><i class="fas fa-exclamation-triangle me-2"></i>تقرير المخزون المنخفض</h6>
                                    </div>
                                    <div class="card-body">
                                        <div id="lowStockReport">
                                            <!-- سيتم ملؤها بـ JavaScript -->
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="row mt-3">
                            <div class="col-md-12">
                                <div class="card">
                                    <div class="card-header">
                                        <h6><i class="fas fa-exchange-alt me-2"></i>تقرير حركة المخزون</h6>
                                    </div>
                                    <div class="card-body">
                                        <div id="inventoryMovementReport">
                                            <!-- سيتم ملؤها بـ JavaScript -->
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Sales Reports Tab -->
                    <div id="salesTab" class="tab-content" style="display: none;">
                        <div class="row">
                            <div class="col-md-6">
                                <div class="card">
                                    <div class="card-header">
                                        <h6><i class="fas fa-chart-line me-2"></i>تقرير المبيعات اليومية</h6>
                                    </div>
                                    <div class="card-body">
                                        <div id="dailySalesReport">
                                            <!-- سيتم ملؤها بـ JavaScript -->
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="card">
                                    <div class="card-header">
                                        <h6><i class="fas fa-star me-2"></i>أفضل المنتجات مبيعاً</h6>
                                    </div>
                                    <div class="card-body">
                                        <div id="topProductsReport">
                                            <!-- سيتم ملؤها بـ JavaScript -->
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="row mt-3">
                            <div class="col-md-12">
                                <div class="card">
                                    <div class="card-header">
                                        <h6><i class="fas fa-file-invoice me-2"></i>تقرير الفواتير</h6>
                                    </div>
                                    <div class="card-body">
                                        <div id="invoicesReport">
                                            <!-- سيتم ملؤها بـ JavaScript -->
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Accounts Reports Tab -->
                    <div id="accountsTab" class="tab-content" style="display: none;">
                        <div class="row">
                            <div class="col-md-6">
                                <div class="card">
                                    <div class="card-header">
                                        <h6><i class="fas fa-users me-2"></i>كشف حساب العملاء</h6>
                                    </div>
                                    <div class="card-body">
                                        <div id="customersAccountsReport">
                                            <!-- سيتم ملؤها بـ JavaScript -->
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="card">
                                    <div class="card-header">
                                        <h6><i class="fas fa-truck me-2"></i>كشف حساب الموردين</h6>
                                    </div>
                                    <div class="card-body">
                                        <div id="suppliersAccountsReport">
                                            <!-- سيتم ملؤها بـ JavaScript -->
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="row mt-3">
                            <div class="col-md-12">
                                <div class="card">
                                    <div class="card-header">
                                        <h6><i class="fas fa-user-tie me-2"></i>كشف حساب الموظفين</h6>
                                    </div>
                                    <div class="card-body">
                                        <div id="employeesAccountsReport">
                                            <!-- سيتم ملؤها بـ JavaScript -->
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Success/Error Messages -->
    <div id="messages"></div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // البيانات المحلية
        let items = [];
        let parties = [];
        let employees = [];
        let invoices = [];
        let cashBoxes = [];
        let banks = [];
        let owners = [];
        let inventoryMovements = [];

        // تحميل البيانات عند بدء الصفحة
        document.addEventListener('DOMContentLoaded', function() {
            loadAllData();
            generateAllReports();
        });

        // تحميل جميع البيانات
        function loadAllData() {
            try {
                items = JSON.parse(localStorage.getItem('anw_items') || '[]');
                parties = JSON.parse(localStorage.getItem('anw_parties') || '[]');
                employees = JSON.parse(localStorage.getItem('anw_employees') || '[]');
                invoices = JSON.parse(localStorage.getItem('anw_invoices') || '[]');
                cashBoxes = JSON.parse(localStorage.getItem('anw_cashboxes') || '[]');
                banks = JSON.parse(localStorage.getItem('anw_banks') || '[]');
                owners = JSON.parse(localStorage.getItem('anw_owners') || '[]');
                inventoryMovements = JSON.parse(localStorage.getItem('anw_inventory_movements') || '[]');
            } catch (error) {
                console.error('خطأ في تحميل البيانات:', error);
                showMessage('خطأ في تحميل البيانات', 'danger');
            }
        }

        // إنشاء جميع التقارير
        function generateAllReports() {
            generateDashboardSummary();
            generateCashBoxesReport();
            generateBanksReport();
            generateOwnersReport();
            generateCurrentInventoryReport();
            generateLowStockReport();
            generateInventoryMovementReport();
            generateDailySalesReport();
            generateTopProductsReport();
            generateInvoicesReport();
            generateCustomersAccountsReport();
            generateSuppliersAccountsReport();
            generateEmployeesAccountsReport();
        }

        // إنشاء ملخص لوحة التحكم
        function generateDashboardSummary() {
            const summaryDiv = document.getElementById('dashboardSummary');

            // حساب الإحصائيات
            const totalItems = items.length;
            const totalParties = parties.length;
            const totalEmployees = employees.length;
            const totalInvoices = invoices.length;

            const totalCashBalance = cashBoxes.reduce((sum, box) => sum + (box.balance || 0), 0);
            const totalBankBalance = banks.reduce((sum, bank) => sum + (bank.balance || 0), 0);
            const totalCapital = owners.reduce((sum, owner) => sum + (owner.capitalInvested || 0) - (owner.withdrawals || 0), 0);

            const todayInvoices = invoices.filter(inv => {
                const invDate = new Date(inv.date).toDateString();
                const today = new Date().toDateString();
                return invDate === today;
            });
            const todaySales = todayInvoices.reduce((sum, inv) => sum + (inv.total || 0), 0);

            const lowStockItems = items.filter(item => item.stock <= item.minStock).length;

            summaryDiv.innerHTML = `
                <div class="col-md-3 mb-3">
                    <div class="card border-primary">
                        <div class="card-body text-center">
                            <h4 class="card-title text-primary">${totalItems}</h4>
                            <p class="card-text">إجمالي الأصناف</p>
                        </div>
                    </div>
                </div>
                <div class="col-md-3 mb-3">
                    <div class="card border-success">
                        <div class="card-body text-center">
                            <h4 class="card-title text-success">${todaySales.toLocaleString()} ر.ي</h4>
                            <p class="card-text">مبيعات اليوم</p>
                        </div>
                    </div>
                </div>
                <div class="col-md-3 mb-3">
                    <div class="card border-info">
                        <div class="card-body text-center">
                            <h4 class="card-title text-info">${(totalCashBalance + totalBankBalance).toLocaleString()} ر.ي</h4>
                            <p class="card-text">إجمالي السيولة</p>
                        </div>
                    </div>
                </div>
                <div class="col-md-3 mb-3">
                    <div class="card border-warning">
                        <div class="card-body text-center">
                            <h4 class="card-title text-warning">${lowStockItems}</h4>
                            <p class="card-text">أصناف منخفضة المخزون</p>
                        </div>
                    </div>
                </div>
                <div class="col-md-3 mb-3">
                    <div class="card border-secondary">
                        <div class="card-body text-center">
                            <h4 class="card-title text-secondary">${totalParties}</h4>
                            <p class="card-text">العملاء والموردين</p>
                        </div>
                    </div>
                </div>
                <div class="col-md-3 mb-3">
                    <div class="card border-dark">
                        <div class="card-body text-center">
                            <h4 class="card-title text-dark">${totalEmployees}</h4>
                            <p class="card-text">الموظفين</p>
                        </div>
                    </div>
                </div>
                <div class="col-md-3 mb-3">
                    <div class="card border-danger">
                        <div class="card-body text-center">
                            <h4 class="card-title text-danger">${totalCapital.toLocaleString()} ر.ي</h4>
                            <p class="card-text">صافي رأس المال</p>
                        </div>
                    </div>
                </div>
                <div class="col-md-3 mb-3">
                    <div class="card border-primary">
                        <div class="card-body text-center">
                            <h4 class="card-title text-primary">${totalInvoices}</h4>
                            <p class="card-text">إجمالي الفواتير</p>
                        </div>
                    </div>
                </div>
            `;
        }

        // تقرير الصناديق
        function generateCashBoxesReport() {
            const reportDiv = document.getElementById('cashBoxesReport');

            if (cashBoxes.length === 0) {
                reportDiv.innerHTML = '<p class="text-muted">لا توجد صناديق</p>';
                return;
            }

            let html = '<div class="table-responsive"><table class="table table-sm">';
            html += '<thead><tr><th>اسم الصندوق</th><th>الرصيد</th><th>الحالة</th></tr></thead><tbody>';

            cashBoxes.forEach(box => {
                const statusBadge = box.active ? '<span class="badge bg-success">نشط</span>' : '<span class="badge bg-secondary">غير نشط</span>';
                const balanceClass = box.balance >= 0 ? 'text-success' : 'text-danger';

                html += `
                    <tr>
                        <td>${box.name}</td>
                        <td class="${balanceClass} fw-bold">${box.balance.toLocaleString()} ر.ي</td>
                        <td>${statusBadge}</td>
                    </tr>
                `;
            });

            html += '</tbody></table></div>';

            const totalBalance = cashBoxes.reduce((sum, box) => sum + box.balance, 0);
            html += `<div class="mt-2"><strong>إجمالي أرصدة الصناديق: ${totalBalance.toLocaleString()} ر.ي</strong></div>`;

            reportDiv.innerHTML = html;
        }

        // تقرير البنوك
        function generateBanksReport() {
            const reportDiv = document.getElementById('banksReport');

            if (banks.length === 0) {
                reportDiv.innerHTML = '<p class="text-muted">لا توجد بنوك</p>';
                return;
            }

            let html = '<div class="table-responsive"><table class="table table-sm">';
            html += '<thead><tr><th>اسم البنك</th><th>رقم الحساب</th><th>الرصيد</th></tr></thead><tbody>';

            banks.forEach(bank => {
                const balanceClass = bank.balance >= 0 ? 'text-success' : 'text-danger';

                html += `
                    <tr>
                        <td>${bank.name}</td>
                        <td>${bank.accountNumber}</td>
                        <td class="${balanceClass} fw-bold">${bank.balance.toLocaleString()} ر.ي</td>
                    </tr>
                `;
            });

            html += '</tbody></table></div>';

            const totalBalance = banks.reduce((sum, bank) => sum + bank.balance, 0);
            html += `<div class="mt-2"><strong>إجمالي أرصدة البنوك: ${totalBalance.toLocaleString()} ر.ي</strong></div>`;

            reportDiv.innerHTML = html;
        }

        // تقرير الملاك
        function generateOwnersReport() {
            const reportDiv = document.getElementById('ownersReport');

            if (owners.length === 0) {
                reportDiv.innerHTML = '<p class="text-muted">لا توجد ملاك</p>';
                return;
            }

            let html = '<div class="table-responsive"><table class="table table-sm">';
            html += '<thead><tr><th>اسم المالك</th><th>نسبة الملكية</th><th>رأس المال المستثمر</th><th>السحوبات</th><th>صافي رأس المال</th></tr></thead><tbody>';

            owners.forEach(owner => {
                const netCapital = owner.capitalInvested - owner.withdrawals;
                const netCapitalClass = netCapital >= 0 ? 'text-success' : 'text-danger';

                html += `
                    <tr>
                        <td>${owner.name}</td>
                        <td><span class="badge bg-primary">${owner.ownershipPercentage}%</span></td>
                        <td class="text-success">${owner.capitalInvested.toLocaleString()} ر.ي</td>
                        <td class="text-danger">${owner.withdrawals.toLocaleString()} ر.ي</td>
                        <td class="${netCapitalClass} fw-bold">${netCapital.toLocaleString()} ر.ي</td>
                    </tr>
                `;
            });

            html += '</tbody></table></div>';

            const totalCapital = owners.reduce((sum, owner) => sum + owner.capitalInvested - owner.withdrawals, 0);
            html += `<div class="mt-2"><strong>إجمالي صافي رأس المال: ${totalCapital.toLocaleString()} ر.ي</strong></div>`;

            reportDiv.innerHTML = html;
        }
        // تقرير المخزون الحالي
        function generateCurrentInventoryReport() {
            const reportDiv = document.getElementById('currentInventoryReport');

            if (items.length === 0) {
                reportDiv.innerHTML = '<p class="text-muted">لا توجد أصناف</p>';
                return;
            }

            let html = '<div class="table-responsive"><table class="table table-sm">';
            html += '<thead><tr><th>الصنف</th><th>النوع</th><th>المخزون</th><th>قيمة المخزون</th></tr></thead><tbody>';

            let totalValue = 0;
            items.forEach(item => {
                const typeLabel = item.type === 'product' ? 'منتج' : item.type === 'material' ? 'خامة' : 'خدمة';
                const stockValue = item.stock * item.purchasePrice;
                totalValue += stockValue;

                html += `
                    <tr>
                        <td>${item.name}</td>
                        <td><span class="badge bg-info">${typeLabel}</span></td>
                        <td>${item.stock.toLocaleString()}</td>
                        <td>${stockValue.toLocaleString()} ر.ي</td>
                    </tr>
                `;
            });

            html += '</tbody></table></div>';
            html += `<div class="mt-2"><strong>إجمالي قيمة المخزون: ${totalValue.toLocaleString()} ر.ي</strong></div>`;

            reportDiv.innerHTML = html;
        }

        // تقرير المخزون المنخفض
        function generateLowStockReport() {
            const reportDiv = document.getElementById('lowStockReport');

            const lowStockItems = items.filter(item => item.stock <= item.minStock);

            if (lowStockItems.length === 0) {
                reportDiv.innerHTML = '<div class="alert alert-success">جميع الأصناف في المستوى الطبيعي</div>';
                return;
            }

            let html = '<div class="table-responsive"><table class="table table-sm">';
            html += '<thead><tr><th>الصنف</th><th>المخزون الحالي</th><th>الحد الأدنى</th><th>المطلوب</th></tr></thead><tbody>';

            lowStockItems.forEach(item => {
                const needed = item.minStock - item.stock + item.minStock; // كمية إضافية مقترحة

                html += `
                    <tr class="table-warning">
                        <td>${item.name}</td>
                        <td class="text-danger fw-bold">${item.stock.toLocaleString()}</td>
                        <td>${item.minStock.toLocaleString()}</td>
                        <td class="text-success">${needed.toLocaleString()}</td>
                    </tr>
                `;
            });

            html += '</tbody></table></div>';

            reportDiv.innerHTML = html;
        }

        // تقرير حركة المخزون
        function generateInventoryMovementReport() {
            const reportDiv = document.getElementById('inventoryMovementReport');

            if (inventoryMovements.length === 0) {
                reportDiv.innerHTML = '<p class="text-muted">لا توجد حركات مخزون</p>';
                return;
            }

            let html = '<div class="table-responsive"><table class="table table-sm">';
            html += '<thead><tr><th>التاريخ</th><th>الصنف</th><th>النوع</th><th>الكمية</th><th>البيان</th></tr></thead><tbody>';

            // أحدث 10 حركات
            const recentMovements = inventoryMovements.slice(-10).reverse();

            recentMovements.forEach(movement => {
                const item = items.find(i => i.id === movement.itemId);
                const typeLabel = movement.type === 'in' ? 'دخول' : 'خروج';
                const typeBadge = movement.type === 'in' ? 'bg-success' : 'bg-danger';
                const quantityClass = movement.type === 'in' ? 'text-success' : 'text-danger';

                html += `
                    <tr>
                        <td>${new Date(movement.date).toLocaleDateString('ar-YE')}</td>
                        <td>${item ? item.name : 'غير محدد'}</td>
                        <td><span class="badge ${typeBadge}">${typeLabel}</span></td>
                        <td class="${quantityClass} fw-bold">${movement.quantity.toLocaleString()}</td>
                        <td>${movement.description}</td>
                    </tr>
                `;
            });

            html += '</tbody></table></div>';

            reportDiv.innerHTML = html;
        }

        // تقرير المبيعات اليومية
        function generateDailySalesReport() {
            const reportDiv = document.getElementById('dailySalesReport');

            const today = new Date().toDateString();
            const todayInvoices = invoices.filter(inv => {
                const invDate = new Date(inv.date).toDateString();
                return invDate === today && inv.type === 'sale';
            });

            if (todayInvoices.length === 0) {
                reportDiv.innerHTML = '<p class="text-muted">لا توجد مبيعات اليوم</p>';
                return;
            }

            let html = '<div class="table-responsive"><table class="table table-sm">';
            html += '<thead><tr><th>رقم الفاتورة</th><th>العميل</th><th>المبلغ</th><th>الوقت</th></tr></thead><tbody>';

            let totalSales = 0;
            todayInvoices.forEach(invoice => {
                const party = parties.find(p => p.id === invoice.partyId);
                totalSales += invoice.total;

                html += `
                    <tr>
                        <td>${invoice.number}</td>
                        <td>${party ? party.name : 'عميل نقدي'}</td>
                        <td class="text-success fw-bold">${invoice.total.toLocaleString()} ر.ي</td>
                        <td>${new Date(invoice.date).toLocaleTimeString('ar-YE', {hour: '2-digit', minute: '2-digit'})}</td>
                    </tr>
                `;
            });

            html += '</tbody></table></div>';
            html += `<div class="mt-2"><strong>إجمالي مبيعات اليوم: ${totalSales.toLocaleString()} ر.ي</strong></div>`;

            reportDiv.innerHTML = html;
        }

        // تقرير أفضل المنتجات مبيعاً
        function generateTopProductsReport() {
            const reportDiv = document.getElementById('topProductsReport');

            // حساب كمية المبيعات لكل منتج
            const productSales = {};

            invoices.filter(inv => inv.type === 'sale').forEach(invoice => {
                if (invoice.items) {
                    invoice.items.forEach(item => {
                        if (!productSales[item.itemId]) {
                            productSales[item.itemId] = {
                                quantity: 0,
                                revenue: 0,
                                name: ''
                            };
                        }
                        productSales[item.itemId].quantity += item.quantity;
                        productSales[item.itemId].revenue += item.total;

                        const product = items.find(i => i.id === item.itemId);
                        if (product) {
                            productSales[item.itemId].name = product.name;
                        }
                    });
                }
            });

            // ترتيب حسب الإيرادات
            const sortedProducts = Object.entries(productSales)
                .sort((a, b) => b[1].revenue - a[1].revenue)
                .slice(0, 5);

            if (sortedProducts.length === 0) {
                reportDiv.innerHTML = '<p class="text-muted">لا توجد مبيعات</p>';
                return;
            }

            let html = '<div class="table-responsive"><table class="table table-sm">';
            html += '<thead><tr><th>المنتج</th><th>الكمية المباعة</th><th>الإيرادات</th></tr></thead><tbody>';

            sortedProducts.forEach(([productId, data], index) => {
                const rankBadge = index === 0 ? 'bg-warning' : index === 1 ? 'bg-secondary' : 'bg-info';

                html += `
                    <tr>
                        <td>
                            <span class="badge ${rankBadge}">${index + 1}</span>
                            ${data.name}
                        </td>
                        <td>${data.quantity.toLocaleString()}</td>
                        <td class="text-success fw-bold">${data.revenue.toLocaleString()} ر.ي</td>
                    </tr>
                `;
            });

            html += '</tbody></table></div>';

            reportDiv.innerHTML = html;
        }

        // تقرير الفواتير
        function generateInvoicesReport() {
            const reportDiv = document.getElementById('invoicesReport');

            if (invoices.length === 0) {
                reportDiv.innerHTML = '<p class="text-muted">لا توجد فواتير</p>';
                return;
            }

            const salesInvoices = invoices.filter(inv => inv.type === 'sale');
            const purchaseInvoices = invoices.filter(inv => inv.type === 'purchase');

            const totalSales = salesInvoices.reduce((sum, inv) => sum + inv.total, 0);
            const totalPurchases = purchaseInvoices.reduce((sum, inv) => sum + inv.total, 0);

            let html = `
                <div class="row">
                    <div class="col-md-4">
                        <div class="card border-success">
                            <div class="card-body text-center">
                                <h5 class="text-success">${salesInvoices.length}</h5>
                                <p>فواتير المبيعات</p>
                                <small>${totalSales.toLocaleString()} ر.ي</small>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="card border-danger">
                            <div class="card-body text-center">
                                <h5 class="text-danger">${purchaseInvoices.length}</h5>
                                <p>فواتير المشتريات</p>
                                <small>${totalPurchases.toLocaleString()} ر.ي</small>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="card border-info">
                            <div class="card-body text-center">
                                <h5 class="text-info">${(totalSales - totalPurchases).toLocaleString()}</h5>
                                <p>صافي الربح</p>
                                <small>ر.ي</small>
                            </div>
                        </div>
                    </div>
                </div>
            `;

            reportDiv.innerHTML = html;
        }

        // كشف حساب العملاء
        function generateCustomersAccountsReport() {
            const reportDiv = document.getElementById('customersAccountsReport');

            const customers = parties.filter(p => p.type === 'customer');

            if (customers.length === 0) {
                reportDiv.innerHTML = '<p class="text-muted">لا توجد عملاء</p>';
                return;
            }

            let html = '<div class="table-responsive"><table class="table table-sm">';
            html += '<thead><tr><th>اسم العميل</th><th>الرصيد</th><th>آخر معاملة</th></tr></thead><tbody>';

            customers.forEach(customer => {
                const balanceClass = customer.balance >= 0 ? 'text-success' : 'text-danger';
                const lastInvoice = invoices.filter(inv => inv.partyId === customer.id).pop();
                const lastTransaction = lastInvoice ? new Date(lastInvoice.date).toLocaleDateString('ar-YE') : 'لا توجد';

                html += `
                    <tr>
                        <td>${customer.name}</td>
                        <td class="${balanceClass} fw-bold">${customer.balance.toLocaleString()} ر.ي</td>
                        <td>${lastTransaction}</td>
                    </tr>
                `;
            });

            html += '</tbody></table></div>';

            const totalBalance = customers.reduce((sum, customer) => sum + customer.balance, 0);
            html += `<div class="mt-2"><strong>إجمالي أرصدة العملاء: ${totalBalance.toLocaleString()} ر.ي</strong></div>`;

            reportDiv.innerHTML = html;
        }

        // كشف حساب الموردين
        function generateSuppliersAccountsReport() {
            const reportDiv = document.getElementById('suppliersAccountsReport');

            const suppliers = parties.filter(p => p.type === 'supplier');

            if (suppliers.length === 0) {
                reportDiv.innerHTML = '<p class="text-muted">لا توجد موردين</p>';
                return;
            }

            let html = '<div class="table-responsive"><table class="table table-sm">';
            html += '<thead><tr><th>اسم المورد</th><th>الرصيد</th><th>آخر معاملة</th></tr></thead><tbody>';

            suppliers.forEach(supplier => {
                const balanceClass = supplier.balance >= 0 ? 'text-danger' : 'text-success'; // عكس العملاء
                const lastInvoice = invoices.filter(inv => inv.partyId === supplier.id).pop();
                const lastTransaction = lastInvoice ? new Date(lastInvoice.date).toLocaleDateString('ar-YE') : 'لا توجد';

                html += `
                    <tr>
                        <td>${supplier.name}</td>
                        <td class="${balanceClass} fw-bold">${Math.abs(supplier.balance).toLocaleString()} ر.ي</td>
                        <td>${lastTransaction}</td>
                    </tr>
                `;
            });

            html += '</tbody></table></div>';

            const totalBalance = suppliers.reduce((sum, supplier) => sum + Math.abs(supplier.balance), 0);
            html += `<div class="mt-2"><strong>إجمالي مستحقات الموردين: ${totalBalance.toLocaleString()} ر.ي</strong></div>`;

            reportDiv.innerHTML = html;
        }

        // كشف حساب الموظفين
        function generateEmployeesAccountsReport() {
            const reportDiv = document.getElementById('employeesAccountsReport');

            if (employees.length === 0) {
                reportDiv.innerHTML = '<p class="text-muted">لا توجد موظفين</p>';
                return;
            }

            let html = '<div class="table-responsive"><table class="table table-sm">';
            html += '<thead><tr><th>اسم الموظف</th><th>الراتب الشهري</th><th>الرصيد</th><th>الحالة</th></tr></thead><tbody>';

            employees.forEach(employee => {
                const balanceClass = employee.balance >= 0 ? 'text-danger' : 'text-success';
                const statusBadge = employee.active ? '<span class="badge bg-success">نشط</span>' : '<span class="badge bg-secondary">غير نشط</span>';

                html += `
                    <tr>
                        <td>${employee.name}</td>
                        <td>${employee.salary.toLocaleString()} ر.ي</td>
                        <td class="${balanceClass} fw-bold">${Math.abs(employee.balance).toLocaleString()} ر.ي</td>
                        <td>${statusBadge}</td>
                    </tr>
                `;
            });

            html += '</tbody></table></div>';

            const totalSalaries = employees.filter(e => e.active).reduce((sum, emp) => sum + emp.salary, 0);
            html += `<div class="mt-2"><strong>إجمالي الرواتب الشهرية: ${totalSalaries.toLocaleString()} ر.ي</strong></div>`;

            reportDiv.innerHTML = html;
        }

        // تبديل التبويبات
        function showTab(tabName) {
            document.getElementById('financialTab').style.display = 'none';
            document.getElementById('inventoryTab').style.display = 'none';
            document.getElementById('salesTab').style.display = 'none';
            document.getElementById('accountsTab').style.display = 'none';

            document.querySelectorAll('.nav-link').forEach(link => {
                link.classList.remove('active');
            });

            if (tabName === 'financial') {
                document.getElementById('financialTab').style.display = 'block';
            } else if (tabName === 'inventory') {
                document.getElementById('inventoryTab').style.display = 'block';
            } else if (tabName === 'sales') {
                document.getElementById('salesTab').style.display = 'block';
            } else if (tabName === 'accounts') {
                document.getElementById('accountsTab').style.display = 'block';
            }

            event.target.classList.add('active');
        }

        // تصدير جميع التقارير
        function exportAllReports() {
            const reportData = {
                date: new Date().toLocaleDateString('ar-YE'),
                dashboard: document.getElementById('dashboardSummary').innerText,
                cashBoxes: document.getElementById('cashBoxesReport').innerText,
                banks: document.getElementById('banksReport').innerText,
                owners: document.getElementById('ownersReport').innerText,
                inventory: document.getElementById('currentInventoryReport').innerText,
                lowStock: document.getElementById('lowStockReport').innerText,
                sales: document.getElementById('dailySalesReport').innerText,
                topProducts: document.getElementById('topProductsReport').innerText,
                customers: document.getElementById('customersAccountsReport').innerText,
                suppliers: document.getElementById('suppliersAccountsReport').innerText,
                employees: document.getElementById('employeesAccountsReport').innerText
            };

            const dataStr = JSON.stringify(reportData, null, 2);
            const dataBlob = new Blob([dataStr], {type: 'application/json'});

            const link = document.createElement('a');
            link.href = URL.createObjectURL(dataBlob);
            link.download = `ANW_Reports_${new Date().toISOString().split('T')[0]}.json`;
            link.click();

            showMessage('تم تصدير التقارير بنجاح', 'success');
        }

        // طباعة لوحة التحكم
        function printDashboard() {
            window.print();
        }

        // عرض رسالة
        function showMessage(message, type) {
            const messagesDiv = document.getElementById('messages');
            const alertHtml = `
                <div class="alert alert-${type} alert-dismissible fade show position-fixed"
                     style="top: 20px; left: 20px; z-index: 9999; min-width: 300px;">
                    ${message}
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
            `;
            messagesDiv.innerHTML = alertHtml;

            setTimeout(() => {
                const alert = messagesDiv.querySelector('.alert');
                if (alert) {
                    bootstrap.Alert.getOrCreateInstance(alert).close();
                }
            }, 3000);
        }

        console.log('✅ تم تحميل نظام التقارير بنجاح');
    </script>

    <style>
        .sidebar {
            height: 100vh;
            position: fixed;
            width: 25%;
        }
        .nav-link.active {
            background-color: rgba(255,255,255,0.1);
            border-radius: 5px;
        }
        .table th {
            background-color: #343a40;
            color: white;
        }
        .tab-content {
            animation: fadeIn 0.3s;
        }
        @keyframes fadeIn {
            from { opacity: 0; }
            to { opacity: 1; }
        }
        @media print {
            .sidebar, .btn, .nav-tabs {
                display: none !important;
            }
            .col-md-9 {
                width: 100% !important;
            }
        }
    </style>
</body>
</html>
