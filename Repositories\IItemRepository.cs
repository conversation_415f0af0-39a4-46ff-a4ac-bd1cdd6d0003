using ANWBakerySystem.Models;

namespace ANWBakerySystem.Repositories
{
    /// <summary>
    /// واجهة مستودع المنتجات والأصناف
    /// Item Repository Interface
    /// </summary>
    public interface IItemRepository
    {
        Task<IEnumerable<Item>> GetAllItemsAsync();
        Task<IEnumerable<Item>> GetActiveItemsAsync();
        Task<IEnumerable<Item>> GetItemsByCategoryAsync(int categoryId);
        Task<IEnumerable<Item>> GetItemsByTypeAsync(ItemType itemType);
        Task<Item?> GetItemByIdAsync(int itemId);
        Task<Item?> GetItemByCodeAsync(string itemCode);
        Task<Item?> GetItemByBarcodeAsync(string barcode);
        Task<Item> CreateItemAsync(Item item);
        Task<Item> UpdateItemAsync(Item item);
        Task<bool> DeleteItemAsync(int itemId);
        Task<bool> ItemExistsAsync(int itemId);
        Task<bool> ItemCodeExistsAsync(string itemCode);
        Task<bool> BarcodeExistsAsync(string barcode);
        
        // إدارة الأسعار
        // Price Management
        Task<decimal> GetItemPriceAsync(int itemId, UnitType unitType);
        Task<bool> UpdateItemPriceAsync(int itemId, UnitType unitType, decimal price);
        Task<bool> UpdateAllItemPricesAsync(int itemId, decimal purchasePrice, decimal salePrice, 
            decimal salePriceLarge, decimal salePriceMedium, decimal salePriceSmall);
        
        // فئات المنتجات
        // Item Categories
        Task<IEnumerable<ItemCategory>> GetAllCategoriesAsync();
        Task<IEnumerable<ItemCategory>> GetActiveCategoriesAsync();
        Task<IEnumerable<ItemCategory>> GetMainCategoriesAsync();
        Task<IEnumerable<ItemCategory>> GetSubCategoriesAsync(int parentCategoryId);
        Task<ItemCategory?> GetCategoryByIdAsync(int categoryId);
        Task<ItemCategory> CreateCategoryAsync(ItemCategory category);
        Task<ItemCategory> UpdateCategoryAsync(ItemCategory category);
        Task<bool> DeleteCategoryAsync(int categoryId);
        
        // البحث والتصفية
        // Search and Filter
        Task<IEnumerable<Item>> SearchItemsAsync(string searchTerm);
        Task<IEnumerable<Item>> GetLowStockItemsAsync();
        Task<IEnumerable<Item>> GetExpiredItemsAsync();
        Task<IEnumerable<Item>> GetItemsNearExpiryAsync(int days);
    }
}
