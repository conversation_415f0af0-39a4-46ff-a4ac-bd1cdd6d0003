<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إعدادات النظام - نظام إدارة مخبوزات ANW</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="css/style.css" rel="stylesheet">
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary">
        <div class="container-fluid">
            <a class="navbar-brand" href="dashboard.html">
                <i class="fas fa-bread-slice me-2"></i>
                نظام إدارة مخبوزات ANW
            </a>
            <div class="navbar-nav ms-auto">
                <a class="nav-link" href="dashboard.html">
                    <i class="fas fa-home"></i> الرئيسية
                </a>
                <a class="nav-link" href="#" onclick="logout()">
                    <i class="fas fa-sign-out-alt"></i> تسجيل الخروج
                </a>
            </div>
        </div>
    </nav>

    <div class="container-fluid mt-4">
        <div class="row">
            <!-- Sidebar -->
            <div class="col-md-3">
                <div class="sidebar-container">
                    <div class="card">
                        <div class="card-header">
                            <h5><i class="fas fa-list"></i> القوائم</h5>
                        </div>
                        <div class="list-group list-group-flush">
                            <a href="dashboard.html" class="list-group-item list-group-item-action">
                                <i class="fas fa-tachometer-alt"></i> لوحة التحكم
                            </a>
                            <a href="accounts.html" class="list-group-item list-group-item-action">
                                <i class="fas fa-sitemap"></i> شجرة الحسابات
                            </a>
                            <a href="banks.html" class="list-group-item list-group-item-action">
                                <i class="fas fa-university"></i> البنوك والصناديق
                            </a>
                            <a href="employees.html" class="list-group-item list-group-item-action">
                                <i class="fas fa-users"></i> الموظفين
                            </a>
                            <a href="journal.html" class="list-group-item list-group-item-action">
                                <i class="fas fa-book"></i> القيود المحاسبية
                            </a>
                            <a href="reports.html" class="list-group-item list-group-item-action">
                                <i class="fas fa-chart-bar"></i> التقارير المالية
                            </a>
                            <a href="units.html" class="list-group-item list-group-item-action">
                                <i class="fas fa-balance-scale"></i> وحدات القياس
                            </a>
                            <a href="parties.html" class="list-group-item list-group-item-action">
                                <i class="fas fa-handshake"></i> العملاء والموردين
                            </a>
                            <a href="items.html" class="list-group-item list-group-item-action">
                                <i class="fas fa-box"></i> المنتجات والخامات
                            </a>
                            <a href="production.html" class="list-group-item list-group-item-action">
                                <i class="fas fa-industry"></i> الإنتاج
                            </a>
                            <a href="invoices.html" class="list-group-item list-group-item-action">
                                <i class="fas fa-file-invoice"></i> الفواتير
                            </a>
                            <a href="inventory.html" class="list-group-item list-group-item-action">
                                <i class="fas fa-warehouse"></i> المخزون
                            </a>
                            <a href="recipes.html" class="list-group-item list-group-item-action">
                                <i class="fas fa-utensils"></i> الوصفات
                            </a>
                            <a href="settings.html" class="list-group-item list-group-item-action active">
                                <i class="fas fa-cog"></i> الإعدادات
                            </a>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Main Content -->
            <div class="col-md-9">
                <div class="row">
                    <!-- General Settings -->
                    <div class="col-md-6 mb-4">
                        <div class="card">
                            <div class="card-header">
                                <h5><i class="fas fa-cog"></i> الإعدادات العامة</h5>
                            </div>
                            <div class="card-body">
                                <form id="generalSettingsForm">
                                    <div class="mb-3">
                                        <label for="companyName" class="form-label">اسم الشركة</label>
                                        <input type="text" class="form-control" id="companyName" value="مخبوزات ANW">
                                    </div>
                                    <div class="mb-3">
                                        <label for="companyAddress" class="form-label">عنوان الشركة</label>
                                        <textarea class="form-control" id="companyAddress" rows="3">صنعاء - اليمن</textarea>
                                    </div>
                                    <div class="mb-3">
                                        <label for="companyPhone" class="form-label">رقم الهاتف</label>
                                        <input type="text" class="form-control" id="companyPhone" value="+967-1-234567">
                                    </div>
                                    <div class="mb-3">
                                        <label for="companyEmail" class="form-label">البريد الإلكتروني</label>
                                        <input type="email" class="form-control" id="companyEmail" value="<EMAIL>">
                                    </div>
                                    <button type="button" class="btn btn-primary" onclick="saveGeneralSettings()">
                                        <i class="fas fa-save"></i> حفظ الإعدادات
                                    </button>
                                </form>
                            </div>
                        </div>
                    </div>

                    <!-- Currency Settings -->
                    <div class="col-md-6 mb-4">
                        <div class="card">
                            <div class="card-header">
                                <h5><i class="fas fa-money-bill"></i> إعدادات العملة</h5>
                            </div>
                            <div class="card-body">
                                <form id="currencySettingsForm">
                                    <div class="mb-3">
                                        <label for="baseCurrency" class="form-label">العملة الأساسية</label>
                                        <select class="form-select" id="baseCurrency" disabled>
                                            <option value="YER" selected>الريال اليمني (ر.ي)</option>
                                        </select>
                                        <small class="text-muted">العملة الأساسية للنظام هي الريال اليمني فقط</small>
                                    </div>
                                    <div class="mb-3">
                                        <label for="decimalPlaces" class="form-label">عدد الخانات العشرية</label>
                                        <select class="form-select" id="decimalPlaces">
                                            <option value="2">2</option>
                                            <option value="3" selected>3</option>
                                            <option value="4">4</option>
                                        </select>
                                    </div>
                                    <div class="mb-3">
                                        <label for="currencySymbol" class="form-label">رمز العملة</label>
                                        <input type="text" class="form-control" id="currencySymbol" value="ر.ي" readonly>
                                    </div>
                                    <button type="button" class="btn btn-primary" onclick="saveCurrencySettings()">
                                        <i class="fas fa-save"></i> حفظ الإعدادات
                                    </button>
                                </form>
                            </div>
                        </div>
                    </div>

                    <!-- System Settings -->
                    <div class="col-md-6 mb-4">
                        <div class="card">
                            <div class="card-header">
                                <h5><i class="fas fa-desktop"></i> إعدادات النظام</h5>
                            </div>
                            <div class="card-body">
                                <form id="systemSettingsForm">
                                    <div class="mb-3">
                                        <label for="language" class="form-label">لغة النظام</label>
                                        <select class="form-select" id="language">
                                            <option value="ar" selected>العربية</option>
                                            <option value="en">English</option>
                                        </select>
                                    </div>
                                    <div class="mb-3">
                                        <label for="dateFormat" class="form-label">تنسيق التاريخ</label>
                                        <select class="form-select" id="dateFormat">
                                            <option value="dd/mm/yyyy" selected>يوم/شهر/سنة</option>
                                            <option value="mm/dd/yyyy">شهر/يوم/سنة</option>
                                            <option value="yyyy-mm-dd">سنة-شهر-يوم</option>
                                        </select>
                                    </div>
                                    <div class="mb-3">
                                        <div class="form-check">
                                            <input class="form-check-input" type="checkbox" id="enableNotifications" checked>
                                            <label class="form-check-label" for="enableNotifications">
                                                تفعيل الإشعارات
                                            </label>
                                        </div>
                                    </div>
                                    <div class="mb-3">
                                        <div class="form-check">
                                            <input class="form-check-input" type="checkbox" id="enableBackup" checked>
                                            <label class="form-check-label" for="enableBackup">
                                                تفعيل النسخ الاحتياطي التلقائي
                                            </label>
                                        </div>
                                    </div>
                                    <button type="button" class="btn btn-primary" onclick="saveSystemSettings()">
                                        <i class="fas fa-save"></i> حفظ الإعدادات
                                    </button>
                                </form>
                            </div>
                        </div>
                    </div>

                    <!-- Database Settings -->
                    <div class="col-md-6 mb-4">
                        <div class="card">
                            <div class="card-header">
                                <h5><i class="fas fa-database"></i> إعدادات قاعدة البيانات</h5>
                            </div>
                            <div class="card-body">
                                <div class="mb-3">
                                    <label class="form-label">حجم قاعدة البيانات</label>
                                    <div class="progress mb-2">
                                        <div class="progress-bar" role="progressbar" style="width: 25%" id="dbSizeProgress">
                                            25%
                                        </div>
                                    </div>
                                    <small class="text-muted">المساحة المستخدمة: 2.5 MB من 10 MB</small>
                                </div>
                                
                                <div class="d-grid gap-2">
                                    <button type="button" class="btn btn-success" onclick="createBackup()">
                                        <i class="fas fa-download"></i> إنشاء نسخة احتياطية
                                    </button>
                                    <button type="button" class="btn btn-warning" onclick="optimizeDatabase()">
                                        <i class="fas fa-compress"></i> تحسين قاعدة البيانات
                                    </button>
                                    <button type="button" class="btn btn-info" onclick="minimizeDatabase()">
                                        <i class="fas fa-compress-arrows-alt"></i> تصغير حجم قاعدة البيانات
                                    </button>
                                    <button type="button" class="btn btn-danger" onclick="resetDatabase()">
                                        <i class="fas fa-trash-restore"></i> إعادة تعيين قاعدة البيانات
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Printing Settings -->
                    <div class="col-md-6 mb-4">
                        <div class="card">
                            <div class="card-header">
                                <h5><i class="fas fa-print"></i> إعدادات الطباعة</h5>
                            </div>
                            <div class="card-body">
                                <form id="printingSettingsForm">
                                    <div class="mb-3">
                                        <label for="defaultPrinter" class="form-label">الطابعة الافتراضية</label>
                                        <select class="form-select" id="defaultPrinter">
                                            <option value="system">طابعة النظام الافتراضية</option>
                                            <option value="thermal">طابعة حرارية</option>
                                            <option value="laser">طابعة ليزر</option>
                                        </select>
                                    </div>
                                    <div class="mb-3">
                                        <label for="paperSize" class="form-label">حجم الورق</label>
                                        <select class="form-select" id="paperSize">
                                            <option value="A4" selected>A4</option>
                                            <option value="A5">A5</option>
                                            <option value="thermal">حراري (80mm)</option>
                                        </select>
                                    </div>
                                    <div class="mb-3">
                                        <div class="form-check">
                                            <input class="form-check-input" type="checkbox" id="printLogo" checked>
                                            <label class="form-check-label" for="printLogo">
                                                طباعة شعار الشركة
                                            </label>
                                        </div>
                                    </div>
                                    <div class="mb-3">
                                        <div class="form-check">
                                            <input class="form-check-input" type="checkbox" id="printBarcode" checked>
                                            <label class="form-check-label" for="printBarcode">
                                                طباعة الباركود
                                            </label>
                                        </div>
                                    </div>
                                    <button type="button" class="btn btn-primary" onclick="savePrintingSettings()">
                                        <i class="fas fa-save"></i> حفظ الإعدادات
                                    </button>
                                </form>
                            </div>
                        </div>
                    </div>

                    <!-- Security Settings -->
                    <div class="col-md-6 mb-4">
                        <div class="card">
                            <div class="card-header">
                                <h5><i class="fas fa-shield-alt"></i> إعدادات الأمان</h5>
                            </div>
                            <div class="card-body">
                                <form id="securitySettingsForm">
                                    <div class="mb-3">
                                        <label for="sessionTimeout" class="form-label">انتهاء الجلسة (دقيقة)</label>
                                        <select class="form-select" id="sessionTimeout">
                                            <option value="30" selected>30 دقيقة</option>
                                            <option value="60">60 دقيقة</option>
                                            <option value="120">120 دقيقة</option>
                                            <option value="0">بدون انتهاء</option>
                                        </select>
                                    </div>
                                    <div class="mb-3">
                                        <div class="form-check">
                                            <input class="form-check-input" type="checkbox" id="enableAuditLog" checked>
                                            <label class="form-check-label" for="enableAuditLog">
                                                تفعيل سجل العمليات
                                            </label>
                                        </div>
                                    </div>
                                    <div class="mb-3">
                                        <div class="form-check">
                                            <input class="form-check-input" type="checkbox" id="requirePasswordChange">
                                            <label class="form-check-label" for="requirePasswordChange">
                                                طلب تغيير كلمة المرور دورياً
                                            </label>
                                        </div>
                                    </div>
                                    <button type="button" class="btn btn-primary" onclick="saveSecuritySettings()">
                                        <i class="fas fa-save"></i> حفظ الإعدادات
                                    </button>
                                </form>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="js/auth-simple.js"></script>
    <script src="js/settings.js"></script>
</body>
</html>
