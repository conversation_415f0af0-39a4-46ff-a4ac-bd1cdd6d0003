using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace ANWBakerySystem.Models
{
    /// <summary>
    /// نموذج المخزون مع دعم وحدات القياس المتقدمة
    /// Inventory model with advanced unit measurement support
    /// </summary>
    public class Inventory
    {
        [Key]
        public int InventoryId { get; set; }

        [Required]
        [Display(Name = "الصنف")]
        public int ItemId { get; set; }

        [Required]
        [Display(Name = "المخزن")]
        public int WarehouseId { get; set; }

        /// <summary>
        /// الكمية المتاحة بالوحدة الأساسية
        /// Available quantity in base unit
        /// </summary>
        [Display(Name = "الكمية المتاحة")]
        [Column(TypeName = "decimal(18,6)")]
        public decimal AvailableQuantity { get; set; } = 0;

        /// <summary>
        /// الكمية المحجوزة بالوحدة الأساسية
        /// Reserved quantity in base unit
        /// </summary>
        [Display(Name = "الكمية المحجوزة")]
        [Column(TypeName = "decimal(18,6)")]
        public decimal ReservedQuantity { get; set; } = 0;

        /// <summary>
        /// إجمالي الكمية بالوحدة الأساسية
        /// Total quantity in base unit
        /// </summary>
        [Display(Name = "إجمالي الكمية")]
        [Column(TypeName = "decimal(18,6)")]
        public decimal TotalQuantity { get; set; } = 0;

        /// <summary>
        /// متوسط تكلفة الوحدة بالريال اليمني
        /// Average unit cost in Yemeni Rial
        /// </summary>
        [Display(Name = "متوسط التكلفة (ر.ي)")]
        [Column(TypeName = "decimal(18,3)")]
        public decimal AverageCost { get; set; } = 0;

        /// <summary>
        /// إجمالي قيمة المخزون بالريال اليمني
        /// Total inventory value in Yemeni Rial
        /// </summary>
        [Display(Name = "إجمالي القيمة (ر.ي)")]
        [Column(TypeName = "decimal(18,3)")]
        public decimal TotalValue { get; set; } = 0;

        [Display(Name = "آخر حركة")]
        public DateTime? LastMovementDate { get; set; }

        [Display(Name = "تاريخ آخر جرد")]
        public DateTime? LastCountDate { get; set; }

        [Display(Name = "تاريخ التحديث")]
        public DateTime UpdatedAt { get; set; } = DateTime.Now;

        [Display(Name = "محدث بواسطة")]
        public int UpdatedBy { get; set; }

        // Navigation properties
        public virtual Item Item { get; set; } = null!;
        public virtual Warehouse Warehouse { get; set; } = null!;
        public virtual User Updater { get; set; } = null!;
        public virtual ICollection<InventoryMovement> Movements { get; set; } = [];

        /// <summary>
        /// حساب الكمية المتاحة للبيع
        /// Calculate available quantity for sale
        /// </summary>
        public decimal GetAvailableForSale()
        {
            return AvailableQuantity - ReservedQuantity;
        }

        /// <summary>
        /// تحديث إجمالي الكمية
        /// Update total quantity
        /// </summary>
        public void UpdateTotalQuantity()
        {
            TotalQuantity = AvailableQuantity + ReservedQuantity;
        }

        /// <summary>
        /// تحديث إجمالي القيمة
        /// Update total value
        /// </summary>
        public void UpdateTotalValue()
        {
            TotalValue = TotalQuantity * AverageCost;
        }

        /// <summary>
        /// التحقق من توفر الكمية المطلوبة
        /// Check if required quantity is available
        /// </summary>
        public bool IsQuantityAvailable(decimal requiredQuantity)
        {
            return GetAvailableForSale() >= requiredQuantity;
        }
    }


}
