using System.ComponentModel.DataAnnotations;

namespace ANWBakerySystem.Models
{
    /// <summary>
    /// DTO لعرض بيانات المخزون
    /// Inventory Data Transfer Object for display
    /// </summary>
    public class InventoryDto
    {
        public int InventoryId { get; set; }
        public int ItemId { get; set; }
        public string ItemName { get; set; } = string.Empty;
        public string? ItemCode { get; set; }
        public string? Barcode { get; set; }
        public int UnitId { get; set; }
        public string UnitName { get; set; } = string.Empty;
        public string UnitSymbol { get; set; } = string.Empty;
        public int WarehouseId { get; set; }
        public string WarehouseName { get; set; } = string.Empty;
        public decimal CurrentStock { get; set; }
        public decimal ReservedStock { get; set; }
        public decimal AvailableStock { get; set; }
        public decimal MinStock { get; set; }
        public decimal MaxStock { get; set; }
        public decimal ReorderPoint { get; set; }
        public decimal AverageCost { get; set; }
        public decimal LastCost { get; set; }
        public decimal TotalValue { get; set; }
        public DateTime? LastMovementDate { get; set; }
        public string? Location { get; set; }
    }

    /// <summary>
    /// DTO لعرض بيانات حركة المخزون
    /// Inventory Movement Data Transfer Object for display
    /// </summary>
    public class InventoryMovementDto
    {
        public int MovementId { get; set; }
        public int ItemId { get; set; }
        public string ItemName { get; set; } = string.Empty;
        public string? ItemCode { get; set; }
        public int WarehouseId { get; set; }
        public string WarehouseName { get; set; } = string.Empty;
        public MovementType MovementType { get; set; }
        public DateTime MovementDate { get; set; }
        public decimal Quantity { get; set; }
        public decimal UnitCost { get; set; }
        public decimal TotalCost { get; set; }
        public decimal BalanceAfter { get; set; }
        public string? ReferenceType { get; set; }
        public int? ReferenceId { get; set; }
        public string? ReferenceNumber { get; set; }
        public string? Notes { get; set; }
        public DateTime CreatedAt { get; set; }
    }

    /// <summary>
    /// طلب إنشاء تسوية مخزون
    /// Create Inventory Adjustment Request
    /// </summary>
    public class CreateInventoryAdjustmentRequest
    {
        [Required(ErrorMessage = "معرف الصنف مطلوب")]
        public int ItemId { get; set; }

        [Required(ErrorMessage = "معرف المخزن مطلوب")]
        public int WarehouseId { get; set; }

        [Required(ErrorMessage = "الكمية الجديدة مطلوبة")]
        [Range(0, double.MaxValue, ErrorMessage = "الكمية الجديدة يجب أن تكون أكبر من أو تساوي صفر")]
        public decimal NewQuantity { get; set; }

        [Range(0, double.MaxValue, ErrorMessage = "تكلفة الوحدة يجب أن تكون أكبر من أو تساوي صفر")]
        public decimal? UnitCost { get; set; }

        [StringLength(500, ErrorMessage = "السبب يجب أن يكون أقل من 500 حرف")]
        public string? Reason { get; set; }
    }

    /// <summary>
    /// طلب تحويل مخزون
    /// Create Inventory Transfer Request
    /// </summary>
    public class CreateInventoryTransferRequest
    {
        [Required(ErrorMessage = "معرف الصنف مطلوب")]
        public int ItemId { get; set; }

        [Required(ErrorMessage = "المخزن المصدر مطلوب")]
        public int FromWarehouseId { get; set; }

        [Required(ErrorMessage = "المخزن المستهدف مطلوب")]
        public int ToWarehouseId { get; set; }

        [Required(ErrorMessage = "الكمية مطلوبة")]
        [Range(0.001, double.MaxValue, ErrorMessage = "الكمية يجب أن تكون أكبر من صفر")]
        public decimal Quantity { get; set; }

        [StringLength(500, ErrorMessage = "الملاحظات يجب أن تكون أقل من 500 حرف")]
        public string? Notes { get; set; }
    }

    /// <summary>
    /// نتيجة عملية المخزون
    /// Inventory Result
    /// </summary>
    public class InventoryResult
    {
        public bool Success { get; set; }
        public string Message { get; set; } = string.Empty;
        public List<InventoryDto>? Inventories { get; set; }
        public int TotalCount { get; set; }
        public int Page { get; set; }
        public int PageSize { get; set; }
        public int TotalPages { get; set; }
        public List<string>? Errors { get; set; }
    }

    /// <summary>
    /// نتيجة عملية مخزون صنف معين
    /// Inventory Item Result
    /// </summary>
    public class InventoryItemResult
    {
        public bool Success { get; set; }
        public string Message { get; set; } = string.Empty;
        public List<InventoryDto>? Inventories { get; set; }
        public decimal TotalStock { get; set; }
        public decimal TotalValue { get; set; }
        public List<string>? Errors { get; set; }
    }

    /// <summary>
    /// نتيجة عملية حركة المخزون
    /// Inventory Movements Result
    /// </summary>
    public class InventoryMovementsResult
    {
        public bool Success { get; set; }
        public string Message { get; set; } = string.Empty;
        public List<InventoryMovementDto>? Movements { get; set; }
        public int TotalCount { get; set; }
        public int Page { get; set; }
        public int PageSize { get; set; }
        public int TotalPages { get; set; }
        public List<string>? Errors { get; set; }
    }

    /// <summary>
    /// نتيجة عملية تسوية المخزون
    /// Inventory Adjustment Result
    /// </summary>
    public class InventoryAdjustmentResult
    {
        public bool Success { get; set; }
        public string Message { get; set; } = string.Empty;
        public int? MovementId { get; set; }
        public decimal? OldQuantity { get; set; }
        public decimal? NewQuantity { get; set; }
        public decimal? AdjustmentQuantity { get; set; }
        public List<string>? Errors { get; set; }
    }

    /// <summary>
    /// نتيجة عملية تحويل المخزون
    /// Inventory Transfer Result
    /// </summary>
    public class InventoryTransferResult
    {
        public bool Success { get; set; }
        public string Message { get; set; } = string.Empty;
        public int? TransferOutMovementId { get; set; }
        public int? TransferInMovementId { get; set; }
        public List<string>? Errors { get; set; }
    }

    /// <summary>
    /// DTO لتقرير المخزون
    /// Inventory Report DTO
    /// </summary>
    public class InventoryReportDto
    {
        public string ItemName { get; set; } = string.Empty;
        public string? ItemCode { get; set; }
        public string UnitName { get; set; } = string.Empty;
        public decimal CurrentStock { get; set; }
        public decimal MinStock { get; set; }
        public decimal MaxStock { get; set; }
        public decimal AverageCost { get; set; }
        public decimal TotalValue { get; set; }
        public string StockStatus { get; set; } = string.Empty;
        public int DaysWithoutMovement { get; set; }
        public DateTime? LastMovementDate { get; set; }
    }

    /// <summary>
    /// DTO لتقرير المخزون المنخفض
    /// Low Stock Report DTO
    /// </summary>
    public class LowStockReportDto
    {
        public int ItemId { get; set; }
        public string ItemName { get; set; } = string.Empty;
        public string? ItemCode { get; set; }
        public string UnitName { get; set; } = string.Empty;
        public decimal CurrentStock { get; set; }
        public decimal MinStock { get; set; }
        public decimal ReorderPoint { get; set; }
        public decimal ShortageQuantity { get; set; }
        public string WarehouseName { get; set; } = string.Empty;
        public DateTime? LastMovementDate { get; set; }
        public string UrgencyLevel { get; set; } = string.Empty;
    }

    /// <summary>
    /// DTO لتقرير قيمة المخزون
    /// Inventory Valuation Report DTO
    /// </summary>
    public class InventoryValuationDto
    {
        public string CategoryName { get; set; } = string.Empty;
        public string ItemName { get; set; } = string.Empty;
        public string? ItemCode { get; set; }
        public decimal CurrentStock { get; set; }
        public decimal AverageCost { get; set; }
        public decimal LastCost { get; set; }
        public decimal TotalValueAverage { get; set; }
        public decimal TotalValueLast { get; set; }
        public string WarehouseName { get; set; } = string.Empty;
    }
}
