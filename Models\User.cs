using System.ComponentModel.DataAnnotations;

namespace ANWBakerySystem.Models
{
    /// <summary>
    /// نموذج المستخدمين مع نظام الصلاحيات
    /// Users model with permissions system
    /// </summary>
    public class User
    {
        [Key]
        public int UserId { get; set; }

        [Required]
        [StringLength(50)]
        [Display(Name = "اسم المستخدم")]
        public string Username { get; set; } = string.Empty;

        [Required]
        [StringLength(200)]
        [Display(Name = "الاسم الكامل")]
        public string FullName { get; set; } = string.Empty;

        [StringLength(200)]
        [Display(Name = "الاسم بالإنجليزية")]
        public string? FullNameEn { get; set; }

        [Required]
        [StringLength(200)]
        [Display(Name = "البريد الإلكتروني")]
        public string Email { get; set; } = string.Empty;

        [StringLength(100)]
        [Display(Name = "رقم الهاتف")]
        public string? Phone { get; set; }

        [Required]
        [StringLength(500)]
        [Display(Name = "كلمة المرور")]
        public string PasswordHash { get; set; } = string.Empty;

        [Required]
        [Display(Name = "الدور")]
        public UserRole Role { get; set; }

        [Display(Name = "نشط")]
        public bool IsActive { get; set; } = true;

        [Display(Name = "آخر تسجيل دخول")]
        public DateTime? LastLoginAt { get; set; }

        [Display(Name = "آخر دخول")]
        public DateTime? LastLogin { get; set; }

        [Display(Name = "تاريخ انتهاء كلمة المرور")]
        public DateTime? PasswordExpiryDate { get; set; }

        [Display(Name = "يجب تغيير كلمة المرور")]
        public bool MustChangePassword { get; set; } = false;

        [Display(Name = "محاولات تسجيل الدخول الفاشلة")]
        public int FailedLoginAttempts { get; set; } = 0;

        [Display(Name = "مقفل")]
        public bool IsLocked { get; set; } = false;

        [Display(Name = "تاريخ القفل")]
        public DateTime? LockedAt { get; set; }

        [StringLength(500)]
        [Display(Name = "ملاحظات")]
        public string? Notes { get; set; }

        [Display(Name = "تاريخ الإنشاء")]
        public DateTime CreatedAt { get; set; } = DateTime.Now;

        [Display(Name = "تاريخ التحديث")]
        public DateTime? UpdatedAt { get; set; }

        [Display(Name = "منشئ بواسطة")]
        public int? CreatedBy { get; set; }

        [Display(Name = "محدث بواسطة")]
        public int? UpdatedBy { get; set; }

        // Navigation properties
        public virtual User? Creator { get; set; }
        public virtual User? Updater { get; set; }
        public virtual ICollection<User> CreatedUsers { get; set; } = new List<User>();
        public virtual ICollection<User> UpdatedUsers { get; set; } = new List<User>();
        public virtual ICollection<UserPermission> UserPermissions { get; set; } = new List<UserPermission>();

        /// <summary>
        /// التحقق من وجود صلاحية معينة
        /// Check if user has specific permission
        /// </summary>
        public bool HasPermission(string permissionName)
        {
            return UserPermissions.Any(up => up.Permission.PermissionName == permissionName && up.IsGranted);
        }

        /// <summary>
        /// التحقق من صلاحية الوصول لوحدة معينة
        /// Check if user has access to specific module
        /// </summary>
        public bool HasModuleAccess(string moduleName)
        {
            return UserPermissions.Any(up => up.Permission.ModuleName == moduleName && up.IsGranted);
        }
    }

    public enum UserRole
    {
        [Display(Name = "مدير النظام")]
        Admin = 1,
        [Display(Name = "مدير")]
        Manager = 2,
        [Display(Name = "محاسب")]
        Accountant = 3,
        [Display(Name = "أمين صندوق")]
        Cashier = 4,
        [Display(Name = "موظف مبيعات")]
        SalesEmployee = 5,
        [Display(Name = "موظف مخزن")]
        WarehouseEmployee = 6,
        [Display(Name = "موظف")]
        Employee = 7,
        [Display(Name = "مستخدم")]
        User = 8
    }
}
