using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace ANWBakerySystem.Models
{
    /// <summary>
    /// نظام وحدات القياس المتقدم - يدعم الوحدات الهرمية (كبرى/متوسطة/صغرى)
    /// Advanced Unit Measurement System - Supports hierarchical units (Large/Medium/Small)
    /// </summary>
    public class Unit
    {
        [Key]
        public int UnitId { get; set; }

        [Required]
        [StringLength(100)]
        [Display(Name = "اسم الوحدة")]
        public string UnitName { get; set; } = string.Empty;

        [Required]
        [StringLength(20)]
        [Display(Name = "رمز الوحدة")]
        public string UnitSymbol { get; set; } = string.Empty;

        [Display(Name = "اسم الوحدة بالإنجليزية")]
        [StringLength(100)]
        public string? UnitNameEn { get; set; }

        /// <summary>
        /// العدد في الوحدة الكبرى - مثل: 1 كيس
        /// Count in large unit - e.g., 1 bag
        /// </summary>
        [Required]
        [Display(Name = "العدد في الوحدة الكبرى")]
        [Column(TypeName = "decimal(18,6)")]
        public decimal LargeUnitCount { get; set; } = 1;

        [Required]
        [StringLength(50)]
        [Display(Name = "اسم الوحدة الكبرى")]
        public string LargeUnitName { get; set; } = string.Empty;

        /// <summary>
        /// العدد في الوحدة المتوسطة - مثل: 50 كيلوجرام
        /// Count in medium unit - e.g., 50 kilograms
        /// </summary>
        [Required]
        [Display(Name = "العدد في الوحدة المتوسطة")]
        [Column(TypeName = "decimal(18,6)")]
        public decimal MediumUnitCount { get; set; } = 1;

        [Required]
        [StringLength(50)]
        [Display(Name = "اسم الوحدة المتوسطة")]
        public string MediumUnitName { get; set; } = string.Empty;

        /// <summary>
        /// العدد في الوحدة الصغرى - مثل: 50000 جرام
        /// Count in small unit - e.g., 50000 grams
        /// </summary>
        [Required]
        [Display(Name = "العدد في الوحدة الصغرى")]
        [Column(TypeName = "decimal(18,6)")]
        public decimal SmallUnitCount { get; set; } = 1;

        [Required]
        [StringLength(50)]
        [Display(Name = "اسم الوحدة الصغرى")]
        public string SmallUnitName { get; set; } = string.Empty;

        /// <summary>
        /// اسم الوحدة الأساسية للقياس - مثل: جرام، مليلتر
        /// Base unit name for measurement - e.g., gram, milliliter
        /// </summary>
        [Required]
        [StringLength(50)]
        [Display(Name = "الوحدة الأساسية")]
        public string BaseUnitName { get; set; } = string.Empty;

        [Display(Name = "ملاحظات التحويل")]
        [StringLength(500)]
        public string? ConversionNotes { get; set; }

        [Display(Name = "نشط")]
        public bool IsActive { get; set; } = true;

        [Display(Name = "تاريخ الإنشاء")]
        public DateTime CreatedAt { get; set; } = DateTime.Now;

        [Display(Name = "تاريخ التحديث")]
        public DateTime? UpdatedAt { get; set; }

        [Display(Name = "منشئ بواسطة")]
        public int CreatedBy { get; set; }

        [Display(Name = "محدث بواسطة")]
        public int? UpdatedBy { get; set; }

        // Navigation properties
        public virtual User Creator { get; set; } = null!;
        public virtual User? Updater { get; set; }
        public virtual ICollection<Item> Items { get; set; } = new List<Item>();
        public virtual ICollection<UnitConversion> ConversionsFrom { get; set; } = new List<UnitConversion>();
        public virtual ICollection<UnitConversion> ConversionsTo { get; set; } = new List<UnitConversion>();

        /// <summary>
        /// حساب معامل التحويل من الوحدة الكبرى إلى الصغرى
        /// Calculate conversion factor from large unit to small unit
        /// </summary>
        public decimal GetLargeToSmallConversionFactor()
        {
            return SmallUnitCount / LargeUnitCount;
        }

        /// <summary>
        /// حساب معامل التحويل من الوحدة المتوسطة إلى الصغرى
        /// Calculate conversion factor from medium unit to small unit
        /// </summary>
        public decimal GetMediumToSmallConversionFactor()
        {
            return SmallUnitCount / MediumUnitCount;
        }

        /// <summary>
        /// حساب معامل التحويل من الوحدة الكبرى إلى المتوسطة
        /// Calculate conversion factor from large unit to medium unit
        /// </summary>
        public decimal GetLargeToMediumConversionFactor()
        {
            return MediumUnitCount / LargeUnitCount;
        }
    }
}
