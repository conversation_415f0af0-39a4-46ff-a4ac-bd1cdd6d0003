using ANWBakerySystem.Models;

namespace ANWBakerySystem.Repositories
{
    /// <summary>
    /// واجهة مستودع وحدات القياس
    /// Unit Repository Interface
    /// </summary>
    public interface IUnitRepository
    {
        Task<IEnumerable<Unit>> GetAllUnitsAsync();
        Task<IEnumerable<Unit>> GetActiveUnitsAsync();
        Task<Unit?> GetUnitByIdAsync(int unitId);
        Task<Unit?> GetUnitByNameAsync(string unitName);
        Task<Unit> CreateUnitAsync(Unit unit);
        Task<Unit> UpdateUnitAsync(Unit unit);
        Task<bool> DeleteUnitAsync(int unitId);
        Task<bool> UnitExistsAsync(int unitId);
        Task<bool> UnitNameExistsAsync(string unitName);
        
        // تحويلات الوحدات
        // Unit Conversions
        Task<UnitConversion> CreateConversionAsync(UnitConversion conversion);
        Task<IEnumerable<UnitConversion>> GetConversionsAsync(int fromUnitId, int toUnitId);
        Task<decimal> GetConversionFactorAsync(int fromUnitId, int toUnitId, UnitType fromType, UnitType toType);
        Task<decimal> ConvertQuantityAsync(decimal quantity, int fromUnitId, int toUnitId, UnitType fromType, UnitType toType);
        
        // حسابات الوحدات المتقدمة
        // Advanced Unit Calculations
        Task<decimal> ConvertToBaseUnitAsync(decimal quantity, int unitId, UnitType unitType);
        Task<decimal> ConvertFromBaseUnitAsync(decimal baseQuantity, int unitId, UnitType unitType);
        Task<decimal> CalculatePriceByUnitTypeAsync(decimal basePrice, int unitId, UnitType unitType);
    }
}
