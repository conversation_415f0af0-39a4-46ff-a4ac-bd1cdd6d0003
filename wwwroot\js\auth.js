// نظام المصادقة الحقيقي - بدون تجاوز
const AUTH_CONFIG = {
    loginUrl: '/index.html',
    dashboardUrl: '/dashboard.html',
    tokenKey: 'authToken',
    userKey: 'currentUser'
};

// فحص المصادقة
function checkAuth() {
    const token = localStorage.getItem(AUTH_CONFIG.tokenKey);
    const user = localStorage.getItem(AUTH_CONFIG.userKey);

    console.log('🔐 فحص المصادقة:', { hasToken: !!token, hasUser: !!user });

    // إذا لم يكن هناك token أو user، إعادة توجيه لتسجيل الدخول
    if (!token || !user) {
        console.log('❌ لا توجد بيانات مصادقة - إعادة توجيه لتسجيل الدخول');
        redirectToLogin();
        return false;
    }

    console.log('✅ المصادقة صحيحة');
    return true;
}

// الحصول على الرمز المميز
function getToken() {
    return localStorage.getItem('authToken');
}

// الحصول على معلومات المستخدم
function getUserInfo() {
    const userInfo = localStorage.getItem('userInfo');
    return userInfo ? JSON.parse(userInfo) : null;
}

// إعادة التوجيه لصفحة تسجيل الدخول - معطل
function redirectToLogin() {
    // تم تعطيل إعادة التوجيه للاختبار
    console.log('تم تجاهل طلب إعادة التوجيه لصفحة الدخول');
    return false;
}

// تجاوز المصادقة (للاختبار)
function bypassAuth() {
    const userData = {
        id: 1,
        username: 'admin',
        fullName: 'مدير النظام',
        role: 'Admin'
    };

    localStorage.setItem('authToken', 'bypass-token-12345');
    localStorage.setItem('userInfo', JSON.stringify(userData));
    localStorage.setItem('user', JSON.stringify(userData));
    localStorage.setItem('token', 'bypass-token-12345');

    console.log('تم تجاوز المصادقة - يمكنك الآن الوصول لجميع الصفحات');
    return true;
}

// تسجيل الخروج
function logout() {
    localStorage.removeItem('authToken');
    localStorage.removeItem('userInfo');
    redirectToLogin();
}

// حفظ معلومات المصادقة
function saveAuthInfo(token, userInfo) {
    localStorage.setItem('authToken', token);
    localStorage.setItem('userInfo', JSON.stringify(userInfo));
}

// التحقق من صلاحية الرمز المميز - معطل
function isTokenValid() {
    // دائماً يعيد true للاختبار
    console.log('تم تجاوز فحص صحة الرمز');
    return true;
    
    try {
        // فك تشفير JWT للتحقق من انتهاء الصلاحية
        const payload = JSON.parse(atob(token.split('.')[1]));
        const currentTime = Date.now() / 1000;
        return payload.exp > currentTime;
    } catch (error) {
        console.error('خطأ في التحقق من صلاحية الرمز:', error);
        return false;
    }
}

// تحديث الرمز المميز
async function refreshToken() {
    try {
        const response = await fetch('/api/auth/refresh', {
            method: 'POST',
            headers: {
                'Authorization': `Bearer ${getToken()}`,
                'Content-Type': 'application/json'
            }
        });
        
        if (response.ok) {
            const data = await response.json();
            saveAuthInfo(data.token, data.user);
            return true;
        } else {
            logout();
            return false;
        }
    } catch (error) {
        console.error('خطأ في تحديث الرمز:', error);
        logout();
        return false;
    }
}

// استدعاء API مع التحقق من المصادقة
async function authenticatedFetch(url, options = {}) {
    const token = getToken();
    
    if (!token) {
        redirectToLogin();
        return null;
    }
    
    // إضافة رأس المصادقة
    options.headers = {
        ...options.headers,
        'Authorization': `Bearer ${token}`,
        'Content-Type': 'application/json'
    };
    
    try {
        const response = await fetch(url, options);
        
        if (response.status === 401) {
            // محاولة تحديث الرمز
            const refreshed = await refreshToken();
            if (refreshed) {
                // إعادة المحاولة مع الرمز الجديد
                options.headers['Authorization'] = `Bearer ${getToken()}`;
                return await fetch(url, options);
            } else {
                redirectToLogin();
                return null;
            }
        }
        
        return response;
    } catch (error) {
        console.error('خطأ في استدعاء API:', error);
        return null;
    }
}

// التحقق من الصلاحيات
function hasPermission(permission) {
    const userInfo = getUserInfo();
    if (!userInfo || !userInfo.permissions) return false;
    
    return userInfo.permissions.includes(permission) || userInfo.role === 'Admin';
}

// التحقق من الدور
function hasRole(role) {
    const userInfo = getUserInfo();
    if (!userInfo) return false;
    
    return userInfo.role === role || userInfo.role === 'Admin';
}

// عرض رسالة خطأ المصادقة
function showAuthError(message) {
    alert(message || 'خطأ في المصادقة. يرجى تسجيل الدخول مرة أخرى.');
    logout();
}

// تهيئة المصادقة عند تحميل الصفحة
document.addEventListener('DOMContentLoaded', function() {
    // التحقق من صلاحية الرمز عند تحميل أي صفحة
    if (window.location.pathname !== '/index.html' && window.location.pathname !== '/') {
        if (!isTokenValid()) {
            logout();
        }
    }
});

// تحديث الرمز تلقائياً كل 30 دقيقة
setInterval(function() {
    if (getToken() && !isTokenValid()) {
        refreshToken();
    }
}, 30 * 60 * 1000); // 30 دقيقة
