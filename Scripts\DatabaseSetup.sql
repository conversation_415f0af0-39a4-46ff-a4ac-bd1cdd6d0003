-- =============================================
-- نظام إدارة مخبوزات ANW - إعداد قاعدة البيانات
-- ANW Bakery Management System - Database Setup
-- =============================================

-- إنشاء قاعدة البيانات
IF NOT EXISTS (SELECT name FROM sys.databases WHERE name = 'ANWBakeryDB')
BEGIN
    CREATE DATABASE ANWBakeryDB
    COLLATE Arabic_CI_AS;
END
GO

USE ANWBakeryDB;
GO

-- =============================================
-- إنشاء المخططات (Schemas)
-- =============================================

IF NOT EXISTS (SELECT * FROM sys.schemas WHERE name = 'Security')
    EXEC('CREATE SCHEMA Security');
GO

IF NOT EXISTS (SELECT * FROM sys.schemas WHERE name = 'Inventory')
    EXEC('CREATE SCHEMA Inventory');
GO

IF NOT EXISTS (SELECT * FROM sys.schemas WHERE name = 'Sales')
    EXEC('CREATE SCHEMA Sales');
GO

IF NOT EXISTS (SELECT * FROM sys.schemas WHERE name = 'Production')
    EXEC('CREATE SCHEMA Production');
GO

IF NOT EXISTS (SELECT * FROM sys.schemas WHERE name = 'Accounting')
    EXEC('CREATE SCHEMA Accounting');
GO

-- =============================================
-- إنشاء الجداول الأساسية
-- =============================================

-- جدول المستخدمين
IF NOT EXISTS (SELECT * FROM sys.objects WHERE object_id = OBJECT_ID(N'[Security].[Users]') AND type in (N'U'))
BEGIN
    CREATE TABLE [Security].[Users] (
        [UserId] int IDENTITY(1,1) NOT NULL PRIMARY KEY,
        [Username] nvarchar(50) NOT NULL UNIQUE,
        [FullName] nvarchar(200) NOT NULL,
        [FullNameEn] nvarchar(200) NULL,
        [Email] nvarchar(200) NOT NULL UNIQUE,
        [Phone] nvarchar(100) NULL,
        [PasswordHash] nvarchar(500) NOT NULL,
        [Role] int NOT NULL DEFAULT 8, -- User role
        [IsActive] bit NOT NULL DEFAULT 1,
        [LastLoginAt] datetime2 NULL,
        [PasswordExpiryDate] datetime2 NULL,
        [MustChangePassword] bit NOT NULL DEFAULT 0,
        [FailedLoginAttempts] int NOT NULL DEFAULT 0,
        [IsLocked] bit NOT NULL DEFAULT 0,
        [LockedAt] datetime2 NULL,
        [Notes] nvarchar(500) NULL,
        [CreatedAt] datetime2 NOT NULL DEFAULT GETDATE(),
        [UpdatedAt] datetime2 NULL,
        [CreatedBy] int NULL,
        [UpdatedBy] int NULL
    );
END
GO

-- جدول الصلاحيات
IF NOT EXISTS (SELECT * FROM sys.objects WHERE object_id = OBJECT_ID(N'[Security].[Permissions]') AND type in (N'U'))
BEGIN
    CREATE TABLE [Security].[Permissions] (
        [PermissionId] int IDENTITY(1,1) NOT NULL PRIMARY KEY,
        [PermissionName] nvarchar(100) NOT NULL UNIQUE,
        [DisplayName] nvarchar(200) NOT NULL,
        [Description] nvarchar(500) NULL,
        [ModuleName] nvarchar(100) NOT NULL,
        [ModuleDisplayName] nvarchar(200) NOT NULL,
        [DisplayOrder] int NOT NULL DEFAULT 0,
        [IsActive] bit NOT NULL DEFAULT 1,
        [CreatedAt] datetime2 NOT NULL DEFAULT GETDATE()
    );
END
GO

-- جدول صلاحيات المستخدمين
IF NOT EXISTS (SELECT * FROM sys.objects WHERE object_id = OBJECT_ID(N'[Security].[UserPermissions]') AND type in (N'U'))
BEGIN
    CREATE TABLE [Security].[UserPermissions] (
        [UserPermissionId] int IDENTITY(1,1) NOT NULL PRIMARY KEY,
        [UserId] int NOT NULL,
        [PermissionId] int NOT NULL,
        [IsGranted] bit NOT NULL DEFAULT 0,
        [GrantedAt] datetime2 NULL,
        [GrantedBy] int NULL,
        [Notes] nvarchar(500) NULL,
        FOREIGN KEY ([UserId]) REFERENCES [Security].[Users]([UserId]),
        FOREIGN KEY ([PermissionId]) REFERENCES [Security].[Permissions]([PermissionId]),
        FOREIGN KEY ([GrantedBy]) REFERENCES [Security].[Users]([UserId])
    );
END
GO

-- جدول وحدات القياس
IF NOT EXISTS (SELECT * FROM sys.objects WHERE object_id = OBJECT_ID(N'[dbo].[Units]') AND type in (N'U'))
BEGIN
    CREATE TABLE [dbo].[Units] (
        [UnitId] int IDENTITY(1,1) NOT NULL PRIMARY KEY,
        [UnitName] nvarchar(100) NOT NULL UNIQUE,
        [UnitSymbol] nvarchar(20) NOT NULL,
        [UnitNameEn] nvarchar(100) NULL,
        [LargeUnitCount] decimal(18,6) NOT NULL DEFAULT 1,
        [LargeUnitName] nvarchar(50) NOT NULL,
        [MediumUnitCount] decimal(18,6) NOT NULL DEFAULT 1,
        [MediumUnitName] nvarchar(50) NOT NULL,
        [SmallUnitCount] decimal(18,6) NOT NULL DEFAULT 1,
        [SmallUnitName] nvarchar(50) NOT NULL,
        [BaseUnitName] nvarchar(50) NOT NULL,
        [ConversionNotes] nvarchar(500) NULL,
        [IsActive] bit NOT NULL DEFAULT 1,
        [CreatedAt] datetime2 NOT NULL DEFAULT GETDATE(),
        [UpdatedAt] datetime2 NULL,
        [CreatedBy] int NOT NULL,
        [UpdatedBy] int NULL,
        FOREIGN KEY ([CreatedBy]) REFERENCES [Security].[Users]([UserId]),
        FOREIGN KEY ([UpdatedBy]) REFERENCES [Security].[Users]([UserId])
    );
END
GO

-- جدول تحويلات الوحدات
IF NOT EXISTS (SELECT * FROM sys.objects WHERE object_id = OBJECT_ID(N'[dbo].[UnitConversions]') AND type in (N'U'))
BEGIN
    CREATE TABLE [dbo].[UnitConversions] (
        [ConversionId] int IDENTITY(1,1) NOT NULL PRIMARY KEY,
        [FromUnitId] int NOT NULL,
        [ToUnitId] int NOT NULL,
        [FromQuantity] decimal(18,6) NOT NULL,
        [ToQuantity] decimal(18,6) NOT NULL,
        [ConversionFactor] decimal(18,6) NOT NULL,
        [ConversionType] nvarchar(50) NULL,
        [Notes] nvarchar(500) NULL,
        [ConversionDate] datetime2 NOT NULL DEFAULT GETDATE(),
        [ConvertedBy] int NOT NULL,
        [ReferenceNumber] nvarchar(100) NULL,
        [OperationType] nvarchar(50) NULL,
        FOREIGN KEY ([FromUnitId]) REFERENCES [dbo].[Units]([UnitId]),
        FOREIGN KEY ([ToUnitId]) REFERENCES [dbo].[Units]([UnitId]),
        FOREIGN KEY ([ConvertedBy]) REFERENCES [Security].[Users]([UserId])
    );
END
GO

-- =============================================
-- إدراج البيانات الأولية
-- =============================================

-- إدراج المستخدم الافتراضي (admin)
IF NOT EXISTS (SELECT 1 FROM [Security].[Users] WHERE [Username] = 'admin')
BEGIN
    INSERT INTO [Security].[Users] ([Username], [FullName], [FullNameEn], [Email], [PasswordHash], [Role], [IsActive], [CreatedAt])
    VALUES ('admin', N'مدير النظام', 'System Administrator', '<EMAIL>', 
            '$2a$11$rOzJqQZ8kVJ8kVJ8kVJ8kOzJqQZ8kVJ8kVJ8kVJ8kOzJqQZ8kVJ8k', -- admin123
            1, 1, GETDATE());
END
GO

-- إدراج وحدات القياس الأساسية
IF NOT EXISTS (SELECT 1 FROM [dbo].[Units] WHERE [UnitName] = N'كيلوجرام')
BEGIN
    INSERT INTO [dbo].[Units] ([UnitName], [UnitSymbol], [UnitNameEn], [LargeUnitCount], [LargeUnitName], 
                               [MediumUnitCount], [MediumUnitName], [SmallUnitCount], [SmallUnitName], 
                               [BaseUnitName], [CreatedBy])
    VALUES (N'كيلوجرام', N'كجم', 'Kilogram', 1, N'كيلوجرام', 1, N'كيلوجرام', 1000, N'جرام', N'جرام', 1);
END
GO

IF NOT EXISTS (SELECT 1 FROM [dbo].[Units] WHERE [UnitName] = N'لتر')
BEGIN
    INSERT INTO [dbo].[Units] ([UnitName], [UnitSymbol], [UnitNameEn], [LargeUnitCount], [LargeUnitName], 
                               [MediumUnitCount], [MediumUnitName], [SmallUnitCount], [SmallUnitName], 
                               [BaseUnitName], [CreatedBy])
    VALUES (N'لتر', N'لتر', 'Liter', 1, N'لتر', 1, N'لتر', 1000, N'مليلتر', N'مليلتر', 1);
END
GO

IF NOT EXISTS (SELECT 1 FROM [dbo].[Units] WHERE [UnitName] = N'قطعة')
BEGIN
    INSERT INTO [dbo].[Units] ([UnitName], [UnitSymbol], [UnitNameEn], [LargeUnitCount], [LargeUnitName], 
                               [MediumUnitCount], [MediumUnitName], [SmallUnitCount], [SmallUnitName], 
                               [BaseUnitName], [CreatedBy])
    VALUES (N'قطعة', N'قطعة', 'Piece', 1, N'قطعة', 1, N'قطعة', 1, N'قطعة', N'قطعة', 1);
END
GO

-- إدراج الصلاحيات الأساسية
IF NOT EXISTS (SELECT 1 FROM [Security].[Permissions])
BEGIN
    INSERT INTO [Security].[Permissions] ([PermissionName], [DisplayName], [ModuleName], [ModuleDisplayName], [DisplayOrder])
    VALUES 
    -- صلاحيات المستخدمين
    ('Users.View', N'عرض المستخدمين', 'Users', N'إدارة المستخدمين', 1),
    ('Users.Create', N'إنشاء مستخدم', 'Users', N'إدارة المستخدمين', 2),
    ('Users.Edit', N'تعديل مستخدم', 'Users', N'إدارة المستخدمين', 3),
    ('Users.Delete', N'حذف مستخدم', 'Users', N'إدارة المستخدمين', 4),
    
    -- صلاحيات وحدات القياس
    ('Units.View', N'عرض وحدات القياس', 'Units', N'وحدات القياس', 10),
    ('Units.Create', N'إنشاء وحدة قياس', 'Units', N'وحدات القياس', 11),
    ('Units.Edit', N'تعديل وحدة قياس', 'Units', N'وحدات القياس', 12),
    ('Units.Delete', N'حذف وحدة قياس', 'Units', N'وحدات القياس', 13),
    
    -- صلاحيات الأطراف
    ('Parties.View', N'عرض العملاء والموردين', 'Parties', N'العملاء والموردين', 20),
    ('Parties.Create', N'إنشاء عميل/مورد', 'Parties', N'العملاء والموردين', 21),
    ('Parties.Edit', N'تعديل عميل/مورد', 'Parties', N'العملاء والموردين', 22),
    ('Parties.Delete', N'حذف عميل/مورد', 'Parties', N'العملاء والموردين', 23),
    
    -- صلاحيات المنتجات
    ('Items.View', N'عرض المنتجات', 'Items', N'إدارة المنتجات', 30),
    ('Items.Create', N'إنشاء منتج', 'Items', N'إدارة المنتجات', 31),
    ('Items.Edit', N'تعديل منتج', 'Items', N'إدارة المنتجات', 32),
    ('Items.Delete', N'حذف منتج', 'Items', N'إدارة المنتجات', 33),
    
    -- صلاحيات الفوترة
    ('Invoices.View', N'عرض الفواتير', 'Invoices', N'إدارة الفوترة', 40),
    ('Invoices.Create', N'إنشاء فاتورة', 'Invoices', N'إدارة الفوترة', 41),
    ('Invoices.Edit', N'تعديل فاتورة', 'Invoices', N'إدارة الفوترة', 42),
    ('Invoices.Delete', N'حذف فاتورة', 'Invoices', N'إدارة الفوترة', 43),
    ('Invoices.Print', N'طباعة فاتورة', 'Invoices', N'إدارة الفوترة', 44),
    
    -- صلاحيات المخزون
    ('Inventory.View', N'عرض المخزون', 'Inventory', N'إدارة المخزون', 50),
    ('Inventory.Adjust', N'تسوية المخزون', 'Inventory', N'إدارة المخزون', 51),
    ('Inventory.Transfer', N'تحويل المخزون', 'Inventory', N'إدارة المخزون', 52),
    ('Inventory.Count', N'جرد المخزون', 'Inventory', N'إدارة المخزون', 53),
    
    -- صلاحيات التقارير
    ('Reports.Sales', N'تقارير المبيعات', 'Reports', N'التقارير', 60),
    ('Reports.Purchases', N'تقارير المشتريات', 'Reports', N'التقارير', 61),
    ('Reports.Inventory', N'تقارير المخزون', 'Reports', N'التقارير', 62),
    ('Reports.Financial', N'التقارير المالية', 'Reports', N'التقارير', 63);
END
GO

-- =============================================
-- إنشاء الفهارس لتحسين الأداء
-- =============================================

-- فهارس جدول المستخدمين
IF NOT EXISTS (SELECT * FROM sys.indexes WHERE name = 'IX_Users_Username')
    CREATE NONCLUSTERED INDEX IX_Users_Username ON [Security].[Users] ([Username]);
GO

IF NOT EXISTS (SELECT * FROM sys.indexes WHERE name = 'IX_Users_Email')
    CREATE NONCLUSTERED INDEX IX_Users_Email ON [Security].[Users] ([Email]);
GO

-- فهارس جدول وحدات القياس
IF NOT EXISTS (SELECT * FROM sys.indexes WHERE name = 'IX_Units_UnitName')
    CREATE NONCLUSTERED INDEX IX_Units_UnitName ON [dbo].[Units] ([UnitName]);
GO

-- فهارس جدول تحويلات الوحدات
IF NOT EXISTS (SELECT * FROM sys.indexes WHERE name = 'IX_UnitConversions_FromToUnit')
    CREATE NONCLUSTERED INDEX IX_UnitConversions_FromToUnit ON [dbo].[UnitConversions] ([FromUnitId], [ToUnitId]);
GO

-- =============================================
-- إنشاء Views للتقارير
-- =============================================

-- عرض إحصائيات المستخدمين
IF NOT EXISTS (SELECT * FROM sys.views WHERE name = 'vw_UserStatistics')
BEGIN
    EXEC('CREATE VIEW [Security].[vw_UserStatistics] AS
    SELECT 
        COUNT(*) as TotalUsers,
        SUM(CASE WHEN IsActive = 1 THEN 1 ELSE 0 END) as ActiveUsers,
        SUM(CASE WHEN IsLocked = 1 THEN 1 ELSE 0 END) as LockedUsers,
        SUM(CASE WHEN Role = 1 THEN 1 ELSE 0 END) as AdminUsers,
        SUM(CASE WHEN Role = 2 THEN 1 ELSE 0 END) as ManagerUsers,
        SUM(CASE WHEN Role = 3 THEN 1 ELSE 0 END) as AccountantUsers
    FROM [Security].[Users]');
END
GO

-- عرض إحصائيات وحدات القياس
IF NOT EXISTS (SELECT * FROM sys.views WHERE name = 'vw_UnitStatistics')
BEGIN
    EXEC('CREATE VIEW [dbo].[vw_UnitStatistics] AS
    SELECT 
        COUNT(*) as TotalUnits,
        SUM(CASE WHEN IsActive = 1 THEN 1 ELSE 0 END) as ActiveUnits,
        COUNT(DISTINCT BaseUnitName) as UniqueBaseUnits
    FROM [dbo].[Units]');
END
GO

-- =============================================
-- إنشاء Stored Procedures
-- =============================================

-- إجراء للحصول على إحصائيات لوحة التحكم
IF NOT EXISTS (SELECT * FROM sys.procedures WHERE name = 'sp_GetDashboardStatistics')
BEGIN
    EXEC('CREATE PROCEDURE [dbo].[sp_GetDashboardStatistics]
    AS
    BEGIN
        SET NOCOUNT ON;
        
        SELECT 
            ''TotalUsers'' as StatType,
            COUNT(*) as StatValue
        FROM [Security].[Users]
        WHERE IsActive = 1
        
        UNION ALL
        
        SELECT 
            ''TotalUnits'' as StatType,
            COUNT(*) as StatValue
        FROM [dbo].[Units]
        WHERE IsActive = 1;
    END');
END
GO

-- =============================================
-- إنشاء Triggers للتدقيق
-- =============================================

-- Trigger لتسجيل تحديثات المستخدمين
IF NOT EXISTS (SELECT * FROM sys.triggers WHERE name = 'tr_Users_UpdateAudit')
BEGIN
    EXEC('CREATE TRIGGER [Security].[tr_Users_UpdateAudit]
    ON [Security].[Users]
    AFTER UPDATE
    AS
    BEGIN
        SET NOCOUNT ON;
        
        UPDATE [Security].[Users]
        SET UpdatedAt = GETDATE()
        WHERE UserId IN (SELECT UserId FROM inserted);
    END');
END
GO

-- Trigger لتسجيل تحديثات وحدات القياس
IF NOT EXISTS (SELECT * FROM sys.triggers WHERE name = 'tr_Units_UpdateAudit')
BEGIN
    EXEC('CREATE TRIGGER [dbo].[tr_Units_UpdateAudit]
    ON [dbo].[Units]
    AFTER UPDATE
    AS
    BEGIN
        SET NOCOUNT ON;
        
        UPDATE [dbo].[Units]
        SET UpdatedAt = GETDATE()
        WHERE UnitId IN (SELECT UnitId FROM inserted);
    END');
END
GO

-- =============================================
-- منح الصلاحيات للمستخدم الافتراضي
-- =============================================

-- منح جميع الصلاحيات للمدير
IF EXISTS (SELECT 1 FROM [Security].[Users] WHERE Username = 'admin')
BEGIN
    DECLARE @AdminUserId int = (SELECT UserId FROM [Security].[Users] WHERE Username = 'admin');
    
    INSERT INTO [Security].[UserPermissions] (UserId, PermissionId, IsGranted, GrantedAt, GrantedBy)
    SELECT @AdminUserId, PermissionId, 1, GETDATE(), @AdminUserId
    FROM [Security].[Permissions]
    WHERE NOT EXISTS (
        SELECT 1 FROM [Security].[UserPermissions] 
        WHERE UserId = @AdminUserId AND PermissionId = [Security].[Permissions].PermissionId
    );
END
GO

PRINT 'تم إعداد قاعدة البيانات بنجاح!';
PRINT 'Database setup completed successfully!';
GO
