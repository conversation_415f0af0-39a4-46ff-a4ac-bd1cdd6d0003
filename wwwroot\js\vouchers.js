// إدارة سندات القبض والصرف
const API_BASE_URL = '/api';

// متغيرات البيانات
let receiptVouchers = [];
let paymentVouchers = [];
let cashboxes = [];
let parties = [];

// تحميل الصفحة
document.addEventListener('DOMContentLoaded', function() {
    console.log('🔄 تحميل صفحة سندات القبض والصرف...');
    initializePage();
});

// تهيئة الصفحة
async function initializePage() {
    try {
        // تحميل البيانات الأساسية
        loadSampleCashboxes(); // تحميل مباشر للصناديق
        loadSampleParties(); // تحميل مباشر للأطراف

        // تحميل محتوى التبويب النشط
        loadReceiptVouchers();

        // إعداد أحداث التبويبات
        setupTabEvents();

        console.log('✅ تم تحميل صفحة سندات القبض والصرف بنجاح');
    } catch (error) {
        console.error('❌ خطأ في تحميل صفحة سندات القبض والصرف:', error);
        showError('خطأ في تحميل البيانات. يرجى إعادة تحميل الصفحة.');
    }
}

// إعداد أحداث التبويبات
function setupTabEvents() {
    const tabButtons = document.querySelectorAll('#vouchersTabs button[data-bs-toggle="tab"]');
    
    tabButtons.forEach(button => {
        button.addEventListener('shown.bs.tab', function(event) {
            const targetTab = event.target.getAttribute('data-bs-target');
            
            switch(targetTab) {
                case '#receipt':
                    loadReceiptVouchers();
                    break;
                case '#payment':
                    loadPaymentVouchers();
                    break;

                case '#parties':
                    displayParties();
                    break;
            }
        });
    });
}

// ========== إدارة الصناديق والبنوك ==========

// تحميل صناديق تجريبية
function loadSampleCashboxes() {
    cashboxes = [
        {
            cashboxId: 1,
            cashboxName: 'الصندوق الرئيسي',
            cashboxType: 'نقدي',
            currentBalance: 150000,
            description: 'الصندوق النقدي الرئيسي للمخبز',
            isActive: true
        },
        {
            cashboxId: 2,
            cashboxName: 'بنك الكريمي',
            cashboxType: 'بنكي',
            currentBalance: 500000,
            accountNumber: '*********',
            description: 'حساب جاري في بنك الكريمي',
            isActive: true
        },
        {
            cashboxId: 3,
            cashboxName: 'صندوق المبيعات',
            cashboxType: 'نقدي',
            currentBalance: 75000,
            description: 'صندوق مبيعات المحل',
            isActive: true
        },
        {
            cashboxId: 4,
            cashboxName: 'بنك سبأ',
            cashboxType: 'بنكي',
            currentBalance: 250000,
            accountNumber: '*********',
            description: 'حساب توفير في بنك سبأ',
            isActive: true
        }
    ];
    console.log('📦 تم تحميل الصناديق التجريبية:', cashboxes.length);
}



// ========== وظائف المساعدة ==========

// تنسيق العملة
function formatCurrency(amount) {
    return new Intl.NumberFormat('ar-YE', {
        style: 'currency',
        currency: 'YER',
        minimumFractionDigits: 0,
        maximumFractionDigits: 0
    }).format(amount).replace('YER', 'ر.ي');
}

// عرض رسالة خطأ
function showError(message) {
    const alertDiv = document.createElement('div');
    alertDiv.className = 'alert alert-danger alert-dismissible fade show position-fixed';
    alertDiv.style.cssText = 'top: 20px; right: 20px; z-index: 9999; min-width: 300px;';
    alertDiv.innerHTML = `
        <i class="fas fa-exclamation-triangle me-2"></i>
        ${message}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    `;
    document.body.appendChild(alertDiv);

    setTimeout(() => {
        if (alertDiv.parentNode) {
            alertDiv.remove();
        }
    }, 5000);
}

// عرض رسالة نجاح
function showSuccess(message) {
    const alertDiv = document.createElement('div');
    alertDiv.className = 'alert alert-success alert-dismissible fade show position-fixed';
    alertDiv.style.cssText = 'top: 20px; right: 20px; z-index: 9999; min-width: 300px;';
    alertDiv.innerHTML = `
        <i class="fas fa-check-circle me-2"></i>
        ${message}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    `;
    document.body.appendChild(alertDiv);

    setTimeout(() => {
        if (alertDiv.parentNode) {
            alertDiv.remove();
        }
    }, 3000);
}

// ========== إدارة الأطراف ==========

// تحميل الأطراف
function loadParties() {
    console.log('🔄 تحميل الأطراف...');

    // تحميل بيانات تجريبية مباشرة
    loadSampleParties();
    displayParties();
}

// تحميل أطراف تجريبية
function loadSampleParties() {
    parties = [
        {
            partyId: 1,
            partyName: 'أحمد محمد علي',
            partyType: 'عميل',
            phone: '777123456',
            address: 'شارع الزبيري، صنعاء',
            currentBalance: -15000, // مدين للشركة
            isActive: true
        },
        {
            partyId: 2,
            partyName: 'شركة الدقيق الذهبي',
            partyType: 'مورد',
            phone: '771234567',
            address: 'المنطقة الصناعية، صنعاء',
            currentBalance: 25000, // دائن للشركة
            isActive: true
        },
        {
            partyId: 3,
            partyName: 'محمد سالم الخباز',
            partyType: 'موظف',
            phone: '773456789',
            address: 'حي السبعين، صنعاء',
            currentBalance: 0,
            isActive: true
        },
        {
            partyId: 4,
            partyName: 'فاطمة أحمد',
            partyType: 'عميل',
            phone: '775678901',
            address: 'شارع الستين، صنعاء',
            currentBalance: -8000,
            isActive: true
        },
        {
            partyId: 5,
            partyName: 'علي محمد المالك',
            partyType: 'مالك',
            phone: '777890123',
            address: 'حي الحصبة، صنعاء',
            currentBalance: 0,
            isActive: true
        }
    ];
    console.log('📦 تم تحميل الأطراف التجريبية:', parties.length);
}

// عرض الأطراف
function displayParties() {
    const content = document.getElementById('partiesContent');

    if (parties.length === 0) {
        content.innerHTML = `
            <div class="text-center p-4">
                <i class="fas fa-users fa-3x text-muted mb-3"></i>
                <h5>لا توجد أطراف</h5>
                <p class="text-muted">ابدأ بإضافة طرف جديد</p>
            </div>
        `;
        return;
    }

    let html = `
        <div class="row mb-3">
            <div class="col-md-4">
                <input type="text" class="form-control" id="partiesSearch" placeholder="البحث في الأطراف..." onkeyup="searchParties()">
            </div>
            <div class="col-md-3">
                <select class="form-select" id="partiesFilter" onchange="filterParties()">
                    <option value="">جميع الأنواع</option>
                    <option value="عميل">عملاء</option>
                    <option value="مورد">موردين</option>
                    <option value="موظف">موظفين</option>
                    <option value="مالك">ملاك</option>
                </select>
            </div>
            <div class="col-md-3">
                <select class="form-select" id="balanceFilter" onchange="filterParties()">
                    <option value="">جميع الأرصدة</option>
                    <option value="positive">دائن</option>
                    <option value="negative">مدين</option>
                    <option value="zero">صفر</option>
                </select>
            </div>
            <div class="col-md-2">
                <button class="btn btn-primary w-100" onclick="loadParties()">
                    <i class="fas fa-sync"></i> تحديث
                </button>
            </div>
        </div>

        <div class="table-responsive">
            <table class="table table-striped table-hover">
                <thead class="table-dark">
                    <tr>
                        <th>اسم الطرف</th>
                        <th>النوع</th>
                        <th>الهاتف</th>
                        <th>العنوان</th>
                        <th>الرصيد الحالي</th>
                        <th>الحالة</th>
                        <th>الإجراءات</th>
                    </tr>
                </thead>
                <tbody>
    `;

    parties.forEach(party => {
        const statusBadge = party.isActive ?
            '<span class="badge bg-success">نشط</span>' :
            '<span class="badge bg-secondary">غير نشط</span>';

        let typeBadge = '';
        switch(party.partyType) {
            case 'عميل':
                typeBadge = '<span class="badge bg-primary"><i class="fas fa-user me-1"></i>عميل</span>';
                break;
            case 'مورد':
                typeBadge = '<span class="badge bg-warning"><i class="fas fa-truck me-1"></i>مورد</span>';
                break;
            case 'موظف':
                typeBadge = '<span class="badge bg-info"><i class="fas fa-user-tie me-1"></i>موظف</span>';
                break;
            case 'مالك':
                typeBadge = '<span class="badge bg-success"><i class="fas fa-crown me-1"></i>مالك</span>';
                break;
            default:
                typeBadge = '<span class="badge bg-secondary">غير محدد</span>';
        }

        let balanceColor = 'text-dark';
        let balanceIcon = '';
        if (party.currentBalance > 0) {
            balanceColor = 'text-success';
            balanceIcon = '<i class="fas fa-arrow-up me-1"></i>';
        } else if (party.currentBalance < 0) {
            balanceColor = 'text-danger';
            balanceIcon = '<i class="fas fa-arrow-down me-1"></i>';
        }

        html += `
            <tr>
                <td><strong>${party.partyName}</strong></td>
                <td>${typeBadge}</td>
                <td>${party.phone || '-'}</td>
                <td>${party.address || '-'}</td>
                <td class="${balanceColor}">
                    ${balanceIcon}<strong>${formatCurrency(Math.abs(party.currentBalance))}</strong>
                    ${party.currentBalance > 0 ? '<small class="text-muted">(دائن)</small>' :
                      party.currentBalance < 0 ? '<small class="text-muted">(مدين)</small>' : ''}
                </td>
                <td>${statusBadge}</td>
                <td>
                    <div class="btn-group btn-group-sm">
                        <button class="btn btn-outline-primary" onclick="editParty(${party.partyId})" title="تعديل">
                            <i class="fas fa-edit"></i>
                        </button>
                        <button class="btn btn-outline-info" onclick="viewPartyStatement(${party.partyId})" title="كشف حساب">
                            <i class="fas fa-file-alt"></i>
                        </button>
                        <button class="btn btn-outline-danger" onclick="deleteParty(${party.partyId})" title="حذف">
                            <i class="fas fa-trash"></i>
                        </button>
                    </div>
                </td>
            </tr>
        `;
    });

    html += `
                </tbody>
            </table>
        </div>
    `;

    content.innerHTML = html;
}

// ========== سندات القبض ==========

// تحميل سندات القبض
function loadReceiptVouchers() {
    console.log('🔄 تحميل سندات القبض...');

    // تحميل بيانات تجريبية مباشرة
    loadSampleReceiptVouchers();
    displayReceiptVouchers();
}

// تحميل سندات قبض تجريبية
function loadSampleReceiptVouchers() {
    receiptVouchers = [
        {
            voucherId: 1,
            voucherNumber: 'REC001',
            voucherDate: '2024-01-15',
            cashboxId: 1,
            partyId: 1,
            amount: 15000,
            description: 'سداد فاتورة رقم INV001',
            voucherType: 'سداد فاتورة',
            referenceNumber: 'INV001',
            createdBy: 'المدير',
            createdAt: '2024-01-15T10:30:00'
        },
        {
            voucherId: 2,
            voucherNumber: 'REC002',
            voucherDate: '2024-01-16',
            cashboxId: 2,
            partyId: 2,
            amount: 25000,
            description: 'مرتجع مشتريات',
            voucherType: 'مرتجع مشتريات',
            referenceNumber: 'PUR005',
            createdBy: 'المحاسب',
            createdAt: '2024-01-16T14:20:00'
        },
        {
            voucherId: 3,
            voucherNumber: 'REC003',
            voucherDate: '2024-01-17',
            cashboxId: 1,
            partyId: 3,
            amount: 5000,
            description: 'استرداد سلفة موظف',
            voucherType: 'استرداد سلفة',
            referenceNumber: '',
            createdBy: 'المدير',
            createdAt: '2024-01-17T09:15:00'
        }
    ];
    console.log('📦 تم تحميل سندات القبض التجريبية:', receiptVouchers.length);
}

// عرض سندات القبض
function displayReceiptVouchers() {
    const content = document.getElementById('receiptContent');

    if (receiptVouchers.length === 0) {
        content.innerHTML = `
            <div class="text-center p-4">
                <i class="fas fa-arrow-down fa-3x text-success mb-3"></i>
                <h5>لا توجد سندات قبض</h5>
                <p class="text-muted">ابدأ بإضافة سند قبض جديد</p>
            </div>
        `;
        return;
    }

    let html = `
        <div class="row mb-3">
            <div class="col-md-4">
                <input type="text" class="form-control" id="receiptSearch" placeholder="البحث في سندات القبض..." onkeyup="searchReceipts()">
            </div>
            <div class="col-md-3">
                <select class="form-select" id="receiptCashboxFilter" onchange="filterReceipts()">
                    <option value="">جميع الصناديق</option>
                    ${cashboxes.map(cb => `<option value="${cb.cashboxId}">${cb.cashboxName}</option>`).join('')}
                </select>
            </div>
            <div class="col-md-3">
                <input type="date" class="form-control" id="receiptDateFilter" onchange="filterReceipts()">
            </div>
            <div class="col-md-2">
                <button class="btn btn-primary w-100" onclick="loadReceiptVouchers()">
                    <i class="fas fa-sync"></i> تحديث
                </button>
            </div>
        </div>

        <div class="table-responsive">
            <table class="table table-striped table-hover">
                <thead class="table-dark">
                    <tr>
                        <th>رقم السند</th>
                        <th>التاريخ</th>
                        <th>الصندوق المتأثر</th>
                        <th>الطرف</th>
                        <th>المبلغ</th>
                        <th>نوع العملية</th>
                        <th>البيان</th>
                        <th>الإجراءات</th>
                    </tr>
                </thead>
                <tbody>
    `;

    receiptVouchers.forEach(voucher => {
        const cashbox = cashboxes.find(cb => cb.cashboxId === voucher.cashboxId);
        const party = parties.find(p => p.partyId === voucher.partyId);

        let voucherTypeBadge = '';
        switch(voucher.voucherType) {
            case 'سداد فاتورة':
                voucherTypeBadge = '<span class="badge bg-primary">سداد فاتورة</span>';
                break;
            case 'مرتجع مشتريات':
                voucherTypeBadge = '<span class="badge bg-warning">مرتجع مشتريات</span>';
                break;
            case 'استرداد سلفة':
                voucherTypeBadge = '<span class="badge bg-info">استرداد سلفة</span>';
                break;
            default:
                voucherTypeBadge = '<span class="badge bg-secondary">أخرى</span>';
        }

        html += `
            <tr>
                <td><strong>${voucher.voucherNumber}</strong></td>
                <td>${new Date(voucher.voucherDate).toLocaleDateString('ar-YE')}</td>
                <td>
                    <span class="badge ${cashbox?.cashboxType === 'نقدي' ? 'bg-primary' : 'bg-info'}">
                        <i class="fas ${cashbox?.cashboxType === 'نقدي' ? 'fa-cash-register' : 'fa-university'} me-1"></i>
                        ${cashbox?.cashboxName || 'غير محدد'}
                    </span>
                </td>
                <td>${party?.partyName || 'غير محدد'}</td>
                <td class="text-success"><strong>${formatCurrency(voucher.amount)}</strong></td>
                <td>${voucherTypeBadge}</td>
                <td>${voucher.description}</td>
                <td>
                    <div class="btn-group btn-group-sm">
                        <button class="btn btn-outline-primary" onclick="editReceiptVoucher(${voucher.voucherId})" title="تعديل">
                            <i class="fas fa-edit"></i>
                        </button>
                        <button class="btn btn-outline-success" onclick="printReceiptVoucher(${voucher.voucherId})" title="طباعة">
                            <i class="fas fa-print"></i>
                        </button>
                        <button class="btn btn-outline-info" onclick="printThermalReceipt(${voucher.voucherId})" title="طباعة حرارية">
                            <i class="fas fa-receipt"></i>
                        </button>
                        <button class="btn btn-outline-danger" onclick="deleteReceiptVoucher(${voucher.voucherId})" title="حذف">
                            <i class="fas fa-trash"></i>
                        </button>
                    </div>
                </td>
            </tr>
        `;
    });

    html += `
                </tbody>
            </table>
        </div>
    `;

    content.innerHTML = html;
}

// ========== سندات الصرف ==========

// تحميل سندات الصرف
function loadPaymentVouchers() {
    console.log('🔄 تحميل سندات الصرف...');

    // تحميل بيانات تجريبية مباشرة
    loadSamplePaymentVouchers();
    displayPaymentVouchers();
}

// تحميل سندات صرف تجريبية
function loadSamplePaymentVouchers() {
    paymentVouchers = [
        {
            voucherId: 1,
            voucherNumber: 'PAY001',
            voucherDate: '2024-01-15',
            cashboxId: 1,
            partyId: 2,
            amount: 50000,
            description: 'سداد فاتورة شراء دقيق',
            voucherType: 'سداد فاتورة شراء',
            referenceNumber: 'PUR001',
            createdBy: 'المدير',
            createdAt: '2024-01-15T11:30:00'
        },
        {
            voucherId: 2,
            voucherNumber: 'PAY002',
            voucherDate: '2024-01-16',
            cashboxId: 1,
            partyId: 3,
            amount: 15000,
            description: 'راتب شهر يناير',
            voucherType: 'راتب موظف',
            referenceNumber: '',
            createdBy: 'المحاسب',
            createdAt: '2024-01-16T16:00:00'
        },
        {
            voucherId: 3,
            voucherNumber: 'PAY003',
            voucherDate: '2024-01-17',
            cashboxId: 2,
            partyId: 1,
            amount: 8000,
            description: 'مرتجع مبيعات',
            voucherType: 'مرتجع مبيعات',
            referenceNumber: 'INV002',
            createdBy: 'المدير',
            createdAt: '2024-01-17T13:45:00'
        }
    ];
    console.log('📦 تم تحميل سندات الصرف التجريبية:', paymentVouchers.length);
}

// عرض سندات الصرف
function displayPaymentVouchers() {
    const content = document.getElementById('paymentContent');

    if (paymentVouchers.length === 0) {
        content.innerHTML = `
            <div class="text-center p-4">
                <i class="fas fa-arrow-up fa-3x text-danger mb-3"></i>
                <h5>لا توجد سندات صرف</h5>
                <p class="text-muted">ابدأ بإضافة سند صرف جديد</p>
            </div>
        `;
        return;
    }

    let html = `
        <div class="row mb-3">
            <div class="col-md-4">
                <input type="text" class="form-control" id="paymentSearch" placeholder="البحث في سندات الصرف..." onkeyup="searchPayments()">
            </div>
            <div class="col-md-3">
                <select class="form-select" id="paymentCashboxFilter" onchange="filterPayments()">
                    <option value="">جميع الصناديق</option>
                    ${cashboxes.map(cb => `<option value="${cb.cashboxId}">${cb.cashboxName}</option>`).join('')}
                </select>
            </div>
            <div class="col-md-3">
                <input type="date" class="form-control" id="paymentDateFilter" onchange="filterPayments()">
            </div>
            <div class="col-md-2">
                <button class="btn btn-primary w-100" onclick="loadPaymentVouchers()">
                    <i class="fas fa-sync"></i> تحديث
                </button>
            </div>
        </div>

        <div class="table-responsive">
            <table class="table table-striped table-hover">
                <thead class="table-dark">
                    <tr>
                        <th>رقم السند</th>
                        <th>التاريخ</th>
                        <th>الصندوق المتأثر</th>
                        <th>الطرف</th>
                        <th>المبلغ</th>
                        <th>نوع العملية</th>
                        <th>البيان</th>
                        <th>الإجراءات</th>
                    </tr>
                </thead>
                <tbody>
    `;

    paymentVouchers.forEach(voucher => {
        const cashbox = cashboxes.find(cb => cb.cashboxId === voucher.cashboxId);
        const party = parties.find(p => p.partyId === voucher.partyId);

        let voucherTypeBadge = '';
        switch(voucher.voucherType) {
            case 'سداد فاتورة شراء':
                voucherTypeBadge = '<span class="badge bg-primary">سداد فاتورة شراء</span>';
                break;
            case 'راتب موظف':
                voucherTypeBadge = '<span class="badge bg-info">راتب موظف</span>';
                break;
            case 'مرتجع مبيعات':
                voucherTypeBadge = '<span class="badge bg-warning">مرتجع مبيعات</span>';
                break;
            case 'مصروفات عامة':
                voucherTypeBadge = '<span class="badge bg-secondary">مصروفات عامة</span>';
                break;
            default:
                voucherTypeBadge = '<span class="badge bg-secondary">أخرى</span>';
        }

        html += `
            <tr>
                <td><strong>${voucher.voucherNumber}</strong></td>
                <td>${new Date(voucher.voucherDate).toLocaleDateString('ar-YE')}</td>
                <td>
                    <span class="badge ${cashbox?.cashboxType === 'نقدي' ? 'bg-primary' : 'bg-info'}">
                        <i class="fas ${cashbox?.cashboxType === 'نقدي' ? 'fa-cash-register' : 'fa-university'} me-1"></i>
                        ${cashbox?.cashboxName || 'غير محدد'}
                    </span>
                </td>
                <td>${party?.partyName || 'غير محدد'}</td>
                <td class="text-danger"><strong>${formatCurrency(voucher.amount)}</strong></td>
                <td>${voucherTypeBadge}</td>
                <td>${voucher.description}</td>
                <td>
                    <div class="btn-group btn-group-sm">
                        <button class="btn btn-outline-primary" onclick="editPaymentVoucher(${voucher.voucherId})" title="تعديل">
                            <i class="fas fa-edit"></i>
                        </button>
                        <button class="btn btn-outline-success" onclick="printPaymentVoucher(${voucher.voucherId})" title="طباعة">
                            <i class="fas fa-print"></i>
                        </button>
                        <button class="btn btn-outline-info" onclick="printThermalPayment(${voucher.voucherId})" title="طباعة حرارية">
                            <i class="fas fa-receipt"></i>
                        </button>
                        <button class="btn btn-outline-danger" onclick="deletePaymentVoucher(${voucher.voucherId})" title="حذف">
                            <i class="fas fa-trash"></i>
                        </button>
                    </div>
                </td>
            </tr>
        `;
    });

    html += `
                </tbody>
            </table>
        </div>
    `;

    content.innerHTML = html;
}

// ========== وظائف الإضافة والتعديل ==========

// إضافة سند قبض جديد
function showAddReceiptModal() {
    const modalHtml = `
        <div class="modal fade" id="receiptModal" tabindex="-1">
            <div class="modal-dialog modal-lg">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title">إضافة سند قبض جديد</h5>
                        <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                    </div>
                    <div class="modal-body">
                        <form id="receiptForm">
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="receiptNumber" class="form-label">رقم السند *</label>
                                        <input type="text" class="form-control" id="receiptNumber" value="${generateReceiptNumber()}" required>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="receiptDate" class="form-label">التاريخ *</label>
                                        <input type="date" class="form-control" id="receiptDate" value="${new Date().toISOString().split('T')[0]}" required>
                                    </div>
                                </div>
                            </div>

                            <div class="row">
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="receiptCashbox" class="form-label">الصندوق المتأثر *</label>
                                        <select class="form-select" id="receiptCashbox" required>
                                            <option value="">-- اختر الصندوق --</option>
                                            ${cashboxes.filter(cb => cb.isActive).map(cb => `<option value="${cb.cashboxId}">${cb.cashboxName} (${formatCurrency(cb.currentBalance)})</option>`).join('')}
                                        </select>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="receiptParty" class="form-label">الطرف *</label>
                                        <div class="position-relative">
                                            <input type="text" class="form-control party-search-input" id="receiptPartySearch" placeholder="ابحث بالكود أو الاسم..."
                                                   onkeyup="searchPartyForReceipt()" onfocus="showPartyDropdown('receipt')" required>
                                            <input type="hidden" id="receiptParty" required>
                                            <div id="receiptPartyDropdown" class="dropdown-menu w-100" style="display: none; max-height: 200px; overflow-y: auto;">
                                                <!-- نتائج البحث ستظهر هنا -->
                                            </div>
                                        </div>
                                        <small class="text-muted">اكتب جزء من الكود أو الاسم للبحث</small>
                                    </div>
                                </div>
                            </div>

                            <div class="row">
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="receiptAmount" class="form-label">المبلغ (ر.ي) *</label>
                                        <input type="number" class="form-control" id="receiptAmount" step="0.01" required>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="receiptType" class="form-label">نوع العملية *</label>
                                        <select class="form-select" id="receiptType" required>
                                            <option value="">-- اختر نوع العملية --</option>
                                            <option value="سداد فاتورة">سداد فاتورة</option>
                                            <option value="مرتجع مشتريات">مرتجع مشتريات</option>
                                            <option value="استرداد سلفة">استرداد سلفة</option>
                                            <option value="تحويل من صندوق">تحويل من صندوق</option>
                                            <option value="إيداع مالك">إيداع مالك</option>
                                            <option value="أخرى">أخرى</option>
                                        </select>
                                    </div>
                                </div>
                            </div>

                            <div class="mb-3">
                                <label for="receiptReference" class="form-label">رقم المرجع</label>
                                <input type="text" class="form-control" id="receiptReference" placeholder="رقم الفاتورة أو المرجع">
                            </div>

                            <div class="mb-3">
                                <label for="receiptDescription" class="form-label">البيان *</label>
                                <textarea class="form-control" id="receiptDescription" rows="3" required></textarea>
                            </div>
                        </form>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                        <button type="button" class="btn btn-success" onclick="saveReceiptVoucher()">
                            <i class="fas fa-save me-2"></i>حفظ سند القبض
                        </button>
                    </div>
                </div>
            </div>
        </div>
    `;

    document.body.insertAdjacentHTML('beforeend', modalHtml);
    const modal = new bootstrap.Modal(document.getElementById('receiptModal'));
    modal.show();

    document.getElementById('receiptModal').addEventListener('hidden.bs.modal', function() {
        this.remove();
    });
}

// إنشاء رقم سند قبض تلقائي
function generateReceiptNumber() {
    const maxNumber = Math.max(...receiptVouchers.map(v => {
        const match = v.voucherNumber?.match(/REC(\d+)/);
        return match ? parseInt(match[1]) : 0;
    }), 0);

    return `REC${(maxNumber + 1).toString().padStart(3, '0')}`;
}

// حفظ سند القبض
function saveReceiptVoucher() {
    const form = document.getElementById('receiptForm');
    if (!form.checkValidity()) {
        form.reportValidity();
        return;
    }

    // التحقق من اختيار الطرف
    const partyId = document.getElementById('receiptParty').value;
    if (!partyId) {
        showError('يرجى اختيار الطرف من قائمة البحث');
        return;
    }

    const voucherData = {
        voucherId: Math.max(...receiptVouchers.map(v => v.voucherId), 0) + 1,
        voucherNumber: document.getElementById('receiptNumber').value.trim(),
        voucherDate: document.getElementById('receiptDate').value,
        cashboxId: parseInt(document.getElementById('receiptCashbox').value),
        partyId: parseInt(partyId),
        amount: parseFloat(document.getElementById('receiptAmount').value),
        voucherType: document.getElementById('receiptType').value,
        referenceNumber: document.getElementById('receiptReference').value.trim(),
        description: document.getElementById('receiptDescription').value.trim(),
        createdBy: 'المستخدم الحالي',
        createdAt: new Date().toISOString()
    };

    // تحديث رصيد الصندوق
    const cashbox = cashboxes.find(cb => cb.cashboxId === voucherData.cashboxId);
    if (cashbox) {
        cashbox.currentBalance += voucherData.amount;
    }

    // تحديث رصيد الطرف (إذا كان عميل فيصبح أقل مديونية أو أكثر دائنية)
    const party = parties.find(p => p.partyId === voucherData.partyId);
    if (party) {
        if (party.partyType === 'عميل') {
            party.currentBalance += voucherData.amount; // تقليل المديونية
        } else if (party.partyType === 'مورد') {
            party.currentBalance -= voucherData.amount; // تقليل الدائنية
        }
    }

    receiptVouchers.push(voucherData);
    showSuccess('تم حفظ سند القبض بنجاح وتحديث أرصدة الصناديق والأطراف');
    bootstrap.Modal.getInstance(document.getElementById('receiptModal')).hide();
    displayReceiptVouchers();
    displayParties();
}

// إضافة سند صرف جديد
function showAddPaymentModal() {
    const modalHtml = `
        <div class="modal fade" id="paymentModal" tabindex="-1">
            <div class="modal-dialog modal-lg">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title">إضافة سند صرف جديد</h5>
                        <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                    </div>
                    <div class="modal-body">
                        <form id="paymentForm">
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="paymentNumber" class="form-label">رقم السند *</label>
                                        <input type="text" class="form-control" id="paymentNumber" value="${generatePaymentNumber()}" required>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="paymentDate" class="form-label">التاريخ *</label>
                                        <input type="date" class="form-control" id="paymentDate" value="${new Date().toISOString().split('T')[0]}" required>
                                    </div>
                                </div>
                            </div>

                            <div class="row">
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="paymentCashbox" class="form-label">الصندوق المتأثر *</label>
                                        <select class="form-select" id="paymentCashbox" required>
                                            <option value="">-- اختر الصندوق --</option>
                                            ${cashboxes.filter(cb => cb.isActive).map(cb => `<option value="${cb.cashboxId}">${cb.cashboxName} (${formatCurrency(cb.currentBalance)})</option>`).join('')}
                                        </select>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="paymentParty" class="form-label">الطرف *</label>
                                        <div class="position-relative">
                                            <input type="text" class="form-control party-search-input" id="paymentPartySearch" placeholder="ابحث بالكود أو الاسم..."
                                                   onkeyup="searchPartyForPayment()" onfocus="showPartyDropdown('payment')" required>
                                            <input type="hidden" id="paymentParty" required>
                                            <div id="paymentPartyDropdown" class="dropdown-menu w-100" style="display: none; max-height: 200px; overflow-y: auto;">
                                                <!-- نتائج البحث ستظهر هنا -->
                                            </div>
                                        </div>
                                        <small class="text-muted">اكتب جزء من الكود أو الاسم للبحث</small>
                                    </div>
                                </div>
                            </div>

                            <div class="row">
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="paymentAmount" class="form-label">المبلغ (ر.ي) *</label>
                                        <input type="number" class="form-control" id="paymentAmount" step="0.01" required>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="paymentType" class="form-label">نوع العملية *</label>
                                        <select class="form-select" id="paymentType" required>
                                            <option value="">-- اختر نوع العملية --</option>
                                            <option value="سداد فاتورة شراء">سداد فاتورة شراء</option>
                                            <option value="راتب موظف">راتب موظف</option>
                                            <option value="مرتجع مبيعات">مرتجع مبيعات</option>
                                            <option value="سلفة موظف">سلفة موظف</option>
                                            <option value="مصروفات عامة">مصروفات عامة</option>
                                            <option value="تحويل لصندوق">تحويل لصندوق</option>
                                            <option value="سحب مالك">سحب مالك</option>
                                            <option value="أخرى">أخرى</option>
                                        </select>
                                    </div>
                                </div>
                            </div>

                            <div class="mb-3">
                                <label for="paymentReference" class="form-label">رقم المرجع</label>
                                <input type="text" class="form-control" id="paymentReference" placeholder="رقم الفاتورة أو المرجع">
                            </div>

                            <div class="mb-3">
                                <label for="paymentDescription" class="form-label">البيان *</label>
                                <textarea class="form-control" id="paymentDescription" rows="3" required></textarea>
                            </div>
                        </form>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                        <button type="button" class="btn btn-danger" onclick="savePaymentVoucher()">
                            <i class="fas fa-save me-2"></i>حفظ سند الصرف
                        </button>
                    </div>
                </div>
            </div>
        </div>
    `;

    document.body.insertAdjacentHTML('beforeend', modalHtml);
    const modal = new bootstrap.Modal(document.getElementById('paymentModal'));
    modal.show();

    document.getElementById('paymentModal').addEventListener('hidden.bs.modal', function() {
        this.remove();
    });
}

// إنشاء رقم سند صرف تلقائي
function generatePaymentNumber() {
    const maxNumber = Math.max(...paymentVouchers.map(v => {
        const match = v.voucherNumber?.match(/PAY(\d+)/);
        return match ? parseInt(match[1]) : 0;
    }), 0);

    return `PAY${(maxNumber + 1).toString().padStart(3, '0')}`;
}

// حفظ سند الصرف
function savePaymentVoucher() {
    const form = document.getElementById('paymentForm');
    if (!form.checkValidity()) {
        form.reportValidity();
        return;
    }

    // التحقق من اختيار الطرف
    const partyId = document.getElementById('paymentParty').value;
    if (!partyId) {
        showError('يرجى اختيار الطرف من قائمة البحث');
        return;
    }

    const amount = parseFloat(document.getElementById('paymentAmount').value);
    const cashboxId = parseInt(document.getElementById('paymentCashbox').value);

    // التحقق من رصيد الصندوق
    const cashbox = cashboxes.find(cb => cb.cashboxId === cashboxId);
    if (cashbox && cashbox.currentBalance < amount) {
        showError(`رصيد الصندوق غير كافي. الرصيد الحالي: ${formatCurrency(cashbox.currentBalance)}`);
        return;
    }

    const voucherData = {
        voucherId: Math.max(...paymentVouchers.map(v => v.voucherId), 0) + 1,
        voucherNumber: document.getElementById('paymentNumber').value.trim(),
        voucherDate: document.getElementById('paymentDate').value,
        cashboxId: cashboxId,
        partyId: parseInt(partyId),
        amount: amount,
        voucherType: document.getElementById('paymentType').value,
        referenceNumber: document.getElementById('paymentReference').value.trim(),
        description: document.getElementById('paymentDescription').value.trim(),
        createdBy: 'المستخدم الحالي',
        createdAt: new Date().toISOString()
    };

    // تحديث رصيد الصندوق
    if (cashbox) {
        cashbox.currentBalance -= voucherData.amount;
    }

    // تحديث رصيد الطرف
    const party = parties.find(p => p.partyId === voucherData.partyId);
    if (party) {
        if (party.partyType === 'مورد') {
            party.currentBalance += voucherData.amount; // تقليل المديونية للمورد
        } else if (party.partyType === 'عميل') {
            party.currentBalance -= voucherData.amount; // زيادة المديونية للعميل
        }
    }

    paymentVouchers.push(voucherData);
    showSuccess('تم حفظ سند الصرف بنجاح وتحديث أرصدة الصناديق والأطراف');
    bootstrap.Modal.getInstance(document.getElementById('paymentModal')).hide();
    displayPaymentVouchers();
    displayParties();
}

// ========== وظائف الطباعة ==========

// طباعة سند قبض عادي
function printReceiptVoucher(voucherId) {
    const voucher = receiptVouchers.find(v => v.voucherId === voucherId);
    if (!voucher) {
        showError('السند غير موجود');
        return;
    }

    const cashbox = cashboxes.find(cb => cb.cashboxId === voucher.cashboxId);
    const party = parties.find(p => p.partyId === voucher.partyId);

    const printContent = `
        <div style="text-align: center; font-family: Arial, sans-serif; direction: rtl;">
            <h2>سند قبض</h2>
            <hr>
            <table style="width: 100%; margin: 20px 0;">
                <tr>
                    <td><strong>رقم السند:</strong> ${voucher.voucherNumber}</td>
                    <td><strong>التاريخ:</strong> ${new Date(voucher.voucherDate).toLocaleDateString('ar-YE')}</td>
                </tr>
                <tr>
                    <td><strong>الصندوق:</strong> ${cashbox?.cashboxName || 'غير محدد'}</td>
                    <td><strong>المبلغ:</strong> ${formatCurrency(voucher.amount)}</td>
                </tr>
                <tr>
                    <td colspan="2"><strong>استلمنا من:</strong> ${party?.partyName || 'غير محدد'}</td>
                </tr>
                <tr>
                    <td colspan="2"><strong>مبلغ وقدره:</strong> ${numberToWords(voucher.amount)} ريال يمني</td>
                </tr>
                <tr>
                    <td colspan="2"><strong>وذلك عن:</strong> ${voucher.description}</td>
                </tr>
                <tr>
                    <td colspan="2"><strong>نوع العملية:</strong> ${voucher.voucherType}</td>
                </tr>
                ${voucher.referenceNumber ? `<tr><td colspan="2"><strong>رقم المرجع:</strong> ${voucher.referenceNumber}</td></tr>` : ''}
            </table>
            <hr>
            <div style="margin-top: 50px;">
                <div style="float: right;">توقيع المستلم: ________________</div>
                <div style="float: left;">توقيع المحاسب: ________________</div>
                <div style="clear: both;"></div>
            </div>
        </div>
    `;

    const printWindow = window.open('', '_blank');
    printWindow.document.write(`
        <html>
            <head>
                <title>سند قبض - ${voucher.voucherNumber}</title>
                <style>
                    body { font-family: Arial, sans-serif; direction: rtl; }
                    table { border-collapse: collapse; }
                    td { padding: 8px; border: 1px solid #ddd; }
                </style>
            </head>
            <body>${printContent}</body>
        </html>
    `);
    printWindow.document.close();
    printWindow.print();
}

// طباعة سند صرف عادي
function printPaymentVoucher(voucherId) {
    const voucher = paymentVouchers.find(v => v.voucherId === voucherId);
    if (!voucher) {
        showError('السند غير موجود');
        return;
    }

    const cashbox = cashboxes.find(cb => cb.cashboxId === voucher.cashboxId);
    const party = parties.find(p => p.partyId === voucher.partyId);

    const printContent = `
        <div style="text-align: center; font-family: Arial, sans-serif; direction: rtl;">
            <h2>سند صرف</h2>
            <hr>
            <table style="width: 100%; margin: 20px 0;">
                <tr>
                    <td><strong>رقم السند:</strong> ${voucher.voucherNumber}</td>
                    <td><strong>التاريخ:</strong> ${new Date(voucher.voucherDate).toLocaleDateString('ar-YE')}</td>
                </tr>
                <tr>
                    <td><strong>الصندوق:</strong> ${cashbox?.cashboxName || 'غير محدد'}</td>
                    <td><strong>المبلغ:</strong> ${formatCurrency(voucher.amount)}</td>
                </tr>
                <tr>
                    <td colspan="2"><strong>صرفنا إلى:</strong> ${party?.partyName || 'غير محدد'}</td>
                </tr>
                <tr>
                    <td colspan="2"><strong>مبلغ وقدره:</strong> ${numberToWords(voucher.amount)} ريال يمني</td>
                </tr>
                <tr>
                    <td colspan="2"><strong>وذلك عن:</strong> ${voucher.description}</td>
                </tr>
                <tr>
                    <td colspan="2"><strong>نوع العملية:</strong> ${voucher.voucherType}</td>
                </tr>
                ${voucher.referenceNumber ? `<tr><td colspan="2"><strong>رقم المرجع:</strong> ${voucher.referenceNumber}</td></tr>` : ''}
            </table>
            <hr>
            <div style="margin-top: 50px;">
                <div style="float: right;">توقيع المستلم: ________________</div>
                <div style="float: left;">توقيع المحاسب: ________________</div>
                <div style="clear: both;"></div>
            </div>
        </div>
    `;

    const printWindow = window.open('', '_blank');
    printWindow.document.write(`
        <html>
            <head>
                <title>سند صرف - ${voucher.voucherNumber}</title>
                <style>
                    body { font-family: Arial, sans-serif; direction: rtl; }
                    table { border-collapse: collapse; }
                    td { padding: 8px; border: 1px solid #ddd; }
                </style>
            </head>
            <body>${printContent}</body>
        </html>
    `);
    printWindow.document.close();
    printWindow.print();
}

// طباعة حرارية لسند قبض
function printThermalReceipt(voucherId) {
    const voucher = receiptVouchers.find(v => v.voucherId === voucherId);
    if (!voucher) {
        showError('السند غير موجود');
        return;
    }

    const cashbox = cashboxes.find(cb => cb.cashboxId === voucher.cashboxId);
    const party = parties.find(p => p.partyId === voucher.partyId);

    const thermalContent = `
        <div style="width: 80mm; font-family: monospace; font-size: 12px; direction: rtl; text-align: center;">
            <h3>سند قبض</h3>
            <div style="text-align: right;">
                رقم: ${voucher.voucherNumber}<br>
                التاريخ: ${new Date(voucher.voucherDate).toLocaleDateString('ar-YE')}<br>
                الصندوق: ${cashbox?.cashboxName || 'غير محدد'}<br>
                من: ${party?.partyName || 'غير محدد'}<br>
                المبلغ: ${formatCurrency(voucher.amount)}<br>
                البيان: ${voucher.description}<br>
                النوع: ${voucher.voucherType}<br>
                ${voucher.referenceNumber ? `المرجع: ${voucher.referenceNumber}<br>` : ''}
            </div>
            <hr>
            <div style="text-align: center; font-size: 10px;">
                شكراً لكم
            </div>
        </div>
    `;

    const printWindow = window.open('', '_blank');
    printWindow.document.write(`
        <html>
            <head>
                <title>سند قبض حراري - ${voucher.voucherNumber}</title>
                <style>
                    body { font-family: monospace; font-size: 12px; margin: 0; padding: 10px; }
                    @media print { body { width: 80mm; } }
                </style>
            </head>
            <body>${thermalContent}</body>
        </html>
    `);
    printWindow.document.close();
    printWindow.print();
}

// طباعة حرارية لسند صرف
function printThermalPayment(voucherId) {
    const voucher = paymentVouchers.find(v => v.voucherId === voucherId);
    if (!voucher) {
        showError('السند غير موجود');
        return;
    }

    const cashbox = cashboxes.find(cb => cb.cashboxId === voucher.cashboxId);
    const party = parties.find(p => p.partyId === voucher.partyId);

    const thermalContent = `
        <div style="width: 80mm; font-family: monospace; font-size: 12px; direction: rtl; text-align: center;">
            <h3>سند صرف</h3>
            <div style="text-align: right;">
                رقم: ${voucher.voucherNumber}<br>
                التاريخ: ${new Date(voucher.voucherDate).toLocaleDateString('ar-YE')}<br>
                الصندوق: ${cashbox?.cashboxName || 'غير محدد'}<br>
                إلى: ${party?.partyName || 'غير محدد'}<br>
                المبلغ: ${formatCurrency(voucher.amount)}<br>
                البيان: ${voucher.description}<br>
                النوع: ${voucher.voucherType}<br>
                ${voucher.referenceNumber ? `المرجع: ${voucher.referenceNumber}<br>` : ''}
            </div>
            <hr>
            <div style="text-align: center; font-size: 10px;">
                شكراً لكم
            </div>
        </div>
    `;

    const printWindow = window.open('', '_blank');
    printWindow.document.write(`
        <html>
            <head>
                <title>سند صرف حراري - ${voucher.voucherNumber}</title>
                <style>
                    body { font-family: monospace; font-size: 12px; margin: 0; padding: 10px; }
                    @media print { body { width: 80mm; } }
                </style>
            </head>
            <body>${thermalContent}</body>
        </html>
    `);
    printWindow.document.close();
    printWindow.print();
}

// تحويل الأرقام إلى كلمات (مبسط)
function numberToWords(num) {
    // تحويل مبسط للأرقام إلى كلمات
    if (num === 0) return 'صفر';

    const ones = ['', 'واحد', 'اثنان', 'ثلاثة', 'أربعة', 'خمسة', 'ستة', 'سبعة', 'ثمانية', 'تسعة'];
    const tens = ['', '', 'عشرون', 'ثلاثون', 'أربعون', 'خمسون', 'ستون', 'سبعون', 'ثمانون', 'تسعون'];
    const teens = ['عشرة', 'أحد عشر', 'اثنا عشر', 'ثلاثة عشر', 'أربعة عشر', 'خمسة عشر', 'ستة عشر', 'سبعة عشر', 'ثمانية عشر', 'تسعة عشر'];

    if (num < 10) return ones[num];
    if (num < 20) return teens[num - 10];
    if (num < 100) return tens[Math.floor(num / 10)] + (num % 10 ? ' و' + ones[num % 10] : '');
    if (num < 1000) return ones[Math.floor(num / 100)] + ' مائة' + (num % 100 ? ' و' + numberToWords(num % 100) : '');
    if (num < 1000000) return numberToWords(Math.floor(num / 1000)) + ' ألف' + (num % 1000 ? ' و' + numberToWords(num % 1000) : '');

    return num.toString(); // للأرقام الكبيرة جداً
}

// ========== وظائف مساعدة أخرى ==========

// إضافة صندوق جديد
function showAddCashboxModal() {
    alert('إضافة صندوق جديد - سيتم تطويرها');
}

// إضافة طرف جديد
function showAddPartyModal() {
    alert('إضافة طرف جديد - سيتم تطويرها');
}

// وظائف البحث والفلترة
function searchCashboxes() {
    // سيتم تطويرها
}

function searchParties() {
    // سيتم تطويرها
}

function searchReceipts() {
    // سيتم تطويرها
}

function searchPayments() {
    // سيتم تطويرها
}

function filterParties() {
    // سيتم تطويرها
}

function filterReceipts() {
    // سيتم تطويرها
}

function filterPayments() {
    // سيتم تطويرها
}

// وظائف التعديل والحذف
function editCashbox(cashboxId) {
    alert(`تعديل الصندوق ${cashboxId} - سيتم تطويرها`);
}

function editParty(partyId) {
    alert(`تعديل الطرف ${partyId} - سيتم تطويرها`);
}

function editReceiptVoucher(voucherId) {
    alert(`تعديل سند القبض ${voucherId} - سيتم تطويرها`);
}

function editPaymentVoucher(voucherId) {
    alert(`تعديل سند الصرف ${voucherId} - سيتم تطويرها`);
}

function deleteCashbox(cashboxId) {
    if (confirm('هل أنت متأكد من حذف هذا الصندوق؟')) {
        alert(`حذف الصندوق ${cashboxId} - سيتم تطويرها`);
    }
}

function deleteParty(partyId) {
    if (confirm('هل أنت متأكد من حذف هذا الطرف؟')) {
        alert(`حذف الطرف ${partyId} - سيتم تطويرها`);
    }
}

function deleteReceiptVoucher(voucherId) {
    if (confirm('هل أنت متأكد من حذف سند القبض؟')) {
        alert(`حذف سند القبض ${voucherId} - سيتم تطويرها`);
    }
}

function deletePaymentVoucher(voucherId) {
    if (confirm('هل أنت متأكد من حذف سند الصرف؟')) {
        alert(`حذف سند الصرف ${voucherId} - سيتم تطويرها`);
    }
}

// كشوف الحسابات
function viewCashboxStatement(cashboxId) {
    alert(`كشف حساب الصندوق ${cashboxId} - سيتم تطويرها`);
}

function viewPartyStatement(partyId) {
    alert(`كشف حساب الطرف ${partyId} - سيتم تطويرها`);
}

// ========== وظائف البحث في الأطراف ==========

// البحث في الأطراف لسندات القبض
function searchPartyForReceipt() {
    const searchTerm = document.getElementById('receiptPartySearch').value.toLowerCase();
    const dropdown = document.getElementById('receiptPartyDropdown');

    if (searchTerm.length < 1) {
        dropdown.style.display = 'none';
        document.getElementById('receiptParty').value = '';
        return;
    }

    const filteredParties = parties.filter(party =>
        party.isActive && (
            party.partyName.toLowerCase().includes(searchTerm) ||
            party.partyId.toString().includes(searchTerm) ||
            party.partyType.toLowerCase().includes(searchTerm)
        )
    );

    displayPartyDropdown(filteredParties, 'receipt');
}

// البحث في الأطراف لسندات الصرف
function searchPartyForPayment() {
    const searchTerm = document.getElementById('paymentPartySearch').value.toLowerCase();
    const dropdown = document.getElementById('paymentPartyDropdown');

    if (searchTerm.length < 1) {
        dropdown.style.display = 'none';
        document.getElementById('paymentParty').value = '';
        return;
    }

    const filteredParties = parties.filter(party =>
        party.isActive && (
            party.partyName.toLowerCase().includes(searchTerm) ||
            party.partyId.toString().includes(searchTerm) ||
            party.partyType.toLowerCase().includes(searchTerm)
        )
    );

    displayPartyDropdown(filteredParties, 'payment');
}

// عرض قائمة الأطراف المنسدلة
function displayPartyDropdown(filteredParties, type) {
    const dropdown = document.getElementById(`${type}PartyDropdown`);

    if (filteredParties.length === 0) {
        dropdown.innerHTML = '<div class="dropdown-item text-muted">لا توجد نتائج</div>';
        dropdown.style.display = 'block';
        return;
    }

    let html = '';
    filteredParties.slice(0, 10).forEach(party => { // عرض أول 10 نتائج فقط
        let balanceText = '';
        let balanceClass = '';

        if (party.currentBalance > 0) {
            balanceText = `(دائن: ${formatCurrency(party.currentBalance)})`;
            balanceClass = 'text-success';
        } else if (party.currentBalance < 0) {
            balanceText = `(مدين: ${formatCurrency(Math.abs(party.currentBalance))})`;
            balanceClass = 'text-danger';
        }

        let typeIcon = '';
        switch(party.partyType) {
            case 'عميل':
                typeIcon = '<i class="fas fa-user text-primary me-2"></i>';
                break;
            case 'مورد':
                typeIcon = '<i class="fas fa-truck text-warning me-2"></i>';
                break;
            case 'موظف':
                typeIcon = '<i class="fas fa-user-tie text-info me-2"></i>';
                break;
            case 'مالك':
                typeIcon = '<i class="fas fa-crown text-success me-2"></i>';
                break;
        }

        html += `
            <div class="dropdown-item" style="cursor: pointer;" onclick="selectParty(${party.partyId}, '${party.partyName}', '${type}')">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        ${typeIcon}<strong>${party.partyName}</strong>
                        <br><small class="text-muted">كود: ${party.partyId} | ${party.partyType}</small>
                    </div>
                    <div class="text-end">
                        <small class="${balanceClass}">${balanceText}</small>
                    </div>
                </div>
            </div>
        `;
    });

    dropdown.innerHTML = html;
    dropdown.style.display = 'block';
}

// اختيار طرف من القائمة
function selectParty(partyId, partyName, type) {
    document.getElementById(`${type}PartySearch`).value = partyName;
    document.getElementById(`${type}Party`).value = partyId;
    document.getElementById(`${type}PartyDropdown`).style.display = 'none';
}

// إظهار قائمة الأطراف عند التركيز
function showPartyDropdown(type) {
    const searchTerm = document.getElementById(`${type}PartySearch`).value;
    if (searchTerm.length > 0) {
        if (type === 'receipt') {
            searchPartyForReceipt();
        } else {
            searchPartyForPayment();
        }
    } else {
        // عرض جميع الأطراف النشطة
        const activeParties = parties.filter(p => p.isActive);
        displayPartyDropdown(activeParties.slice(0, 10), type);
    }
}

// إخفاء القائمة عند النقر خارجها
document.addEventListener('click', function(event) {
    const receiptDropdown = document.getElementById('receiptPartyDropdown');
    const paymentDropdown = document.getElementById('paymentPartyDropdown');

    if (receiptDropdown && !event.target.closest('#receiptPartySearch') && !event.target.closest('#receiptPartyDropdown')) {
        receiptDropdown.style.display = 'none';
    }

    if (paymentDropdown && !event.target.closest('#paymentPartySearch') && !event.target.closest('#paymentPartyDropdown')) {
        paymentDropdown.style.display = 'none';
    }
});
