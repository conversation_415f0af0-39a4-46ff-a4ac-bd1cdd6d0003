@echo off
chcp 65001 >nul 2>&1
title نظام إدارة مخبوزات ANW

echo.
echo ========================================
echo    نظام إدارة مخبوزات ANW
echo    ANW Bakery Management System
echo ========================================
echo.

REM التأكد من وجود .NET
where dotnet >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ .NET غير مثبت على النظام
    echo ❌ .NET is not installed
    echo.
    echo 💡 يرجى تثبيت .NET 8.0 من:
    echo 💡 Please install .NET 8.0 from:
    echo    https://dotnet.microsoft.com/download
    echo.
    pause
    exit /b 1
)

REM الانتقال لمجلد المشروع
if not exist "C:\ANW_bakery" (
    echo ❌ مجلد المشروع غير موجود
    echo ❌ Project folder not found
    pause
    exit /b 1
)

cd /d "C:\ANW_bakery"

REM التحقق من وجود ملف المشروع
if not exist "ANWBakerySystem.csproj" (
    echo ❌ ملف المشروع غير موجود
    echo ❌ Project file not found
    pause
    exit /b 1
)

echo 🔄 إيقاف العمليات السابقة...
taskkill /f /im dotnet.exe >nul 2>&1

echo 🔄 استعادة الحزم...
dotnet restore
if %errorlevel% neq 0 (
    echo ❌ فشل في استعادة الحزم
    pause
    exit /b 1
)

echo 🔨 بناء المشروع...
dotnet build --configuration Release
if %errorlevel% neq 0 (
    echo ❌ فشل في بناء المشروع
    echo.
    echo 🔍 محاولة البناء مع التفاصيل...
    dotnet build --verbosity detailed
    pause
    exit /b 1
)

echo.
echo ✅ تم بناء المشروع بنجاح
echo.
echo 📋 معلومات النظام:
echo    🌐 الرابط: http://localhost:5000
echo    💾 قاعدة البيانات: SQL Server (دائمة)
echo    👤 المستخدم: admin
echo    🔑 كلمة المرور: admin123
echo.

echo 🚀 بدء تشغيل النظام...
echo.

REM فتح المتصفح بعد 5 ثوان
start /b timeout /t 5 >nul && start http://localhost:5000

echo ⚠️  لإيقاف النظام اضغط Ctrl+C
echo.

REM تشغيل التطبيق
dotnet run --configuration Release --urls "http://localhost:5000"

echo.
echo تم إيقاف النظام
pause
