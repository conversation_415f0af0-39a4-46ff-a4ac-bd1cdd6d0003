// إدارة الملاك والشركاء
const API_BASE_URL = '/api';

// متغيرات البيانات
let owners = [];

// تحميل الصفحة
document.addEventListener('DOMContentLoaded', function() {
    console.log('🔄 تحميل صفحة إدارة الملاك...');
    initializePage();
});

// تهيئة الصفحة
function initializePage() {
    try {
        // تحميل البيانات
        loadOwners();
        
        console.log('✅ تم تحميل صفحة إدارة الملاك بنجاح');
    } catch (error) {
        console.error('❌ خطأ في تحميل صفحة إدارة الملاك:', error);
        showError('خطأ في تحميل البيانات. يرجى إعادة تحميل الصفحة.');
    }
}

// تحميل الملاك
function loadOwners() {
    console.log('🔄 تحميل الملاك...');
    
    // تحميل بيانات تجريبية
    loadSampleOwners();
    displayOwners();
    updateStatistics();
}

// تحميل ملاك تجريبيين
function loadSampleOwners() {
    owners = [
        {
            ownerId: 1,
            ownerCode: 'OWN001',
            ownerName: 'أحمد محمد الشامي',
            phone: '777123456',
            email: '<EMAIL>',
            address: 'شارع الزبيري، صنعاء',
            capitalAmount: 500000,
            sharePercentage: 50.0,
            currentBalance: 25000,
            totalWithdrawals: 75000,
            joinDate: '2023-01-01',
            isActive: true,
            notes: 'المؤسس الرئيسي للمخبز'
        },
        {
            ownerId: 2,
            ownerCode: 'OWN002',
            ownerName: 'فاطمة علي الحداد',
            phone: '771234567',
            email: '<EMAIL>',
            address: 'حي السبعين، صنعاء',
            capitalAmount: 300000,
            sharePercentage: 30.0,
            currentBalance: 15000,
            totalWithdrawals: 45000,
            joinDate: '2023-02-15',
            isActive: true,
            notes: 'شريك مستثمر'
        },
        {
            ownerId: 3,
            ownerCode: 'OWN003',
            ownerName: 'محمد سالم القاضي',
            phone: '773456789',
            email: '<EMAIL>',
            address: 'شارع الستين، صنعاء',
            capitalAmount: 200000,
            sharePercentage: 20.0,
            currentBalance: -5000,
            totalWithdrawals: 35000,
            joinDate: '2023-03-01',
            isActive: true,
            notes: 'شريك صامت'
        }
    ];
    console.log('📦 تم تحميل الملاك التجريبيين:', owners.length);
}

// عرض الملاك
function displayOwners() {
    const content = document.getElementById('ownersContent');
    
    if (owners.length === 0) {
        content.innerHTML = `
            <div class="text-center p-4">
                <i class="fas fa-crown fa-3x text-muted mb-3"></i>
                <h5>لا يوجد ملاك</h5>
                <p class="text-muted">ابدأ بإضافة مالك جديد</p>
            </div>
        `;
        return;
    }
    
    let html = `
        <div class="table-responsive">
            <table class="table table-striped table-hover">
                <thead class="table-dark">
                    <tr>
                        <th>الكود</th>
                        <th>اسم المالك</th>
                        <th>الهاتف</th>
                        <th>رأس المال</th>
                        <th>نسبة الحصة</th>
                        <th>الرصيد الحالي</th>
                        <th>إجمالي السحوبات</th>
                        <th>تاريخ الانضمام</th>
                        <th>الحالة</th>
                        <th>الإجراءات</th>
                    </tr>
                </thead>
                <tbody>
    `;
    
    owners.forEach(owner => {
        const statusBadge = owner.isActive ? 
            '<span class="badge bg-success">نشط</span>' : 
            '<span class="badge bg-secondary">غير نشط</span>';
        
        let balanceColor = 'text-dark';
        let balanceIcon = '';
        if (owner.currentBalance > 0) {
            balanceColor = 'text-success';
            balanceIcon = '<i class="fas fa-arrow-up me-1"></i>';
        } else if (owner.currentBalance < 0) {
            balanceColor = 'text-danger';
            balanceIcon = '<i class="fas fa-arrow-down me-1"></i>';
        }
        
        let shareClass = '';
        if (owner.sharePercentage > 30) {
            shareClass = 'text-success fw-bold';
        } else if (owner.sharePercentage >= 10) {
            shareClass = 'text-warning fw-bold';
        } else {
            shareClass = 'text-info';
        }
        
        html += `
            <tr>
                <td><strong>${owner.ownerCode}</strong></td>
                <td>
                    <div>
                        <strong>${owner.ownerName}</strong>
                        ${owner.email ? `<br><small class="text-muted">${owner.email}</small>` : ''}
                    </div>
                </td>
                <td>${owner.phone || '-'}</td>
                <td><strong>${formatCurrency(owner.capitalAmount)}</strong></td>
                <td><span class="${shareClass}">${owner.sharePercentage}%</span></td>
                <td class="${balanceColor}">
                    ${balanceIcon}<strong>${formatCurrency(Math.abs(owner.currentBalance))}</strong>
                </td>
                <td>${formatCurrency(owner.totalWithdrawals || 0)}</td>
                <td>${owner.joinDate ? new Date(owner.joinDate).toLocaleDateString('ar-YE') : '-'}</td>
                <td>${statusBadge}</td>
                <td>
                    <div class="btn-group btn-group-sm">
                        <button class="btn btn-outline-primary" onclick="editOwner(${owner.ownerId})" title="تعديل">
                            <i class="fas fa-edit"></i>
                        </button>
                        <button class="btn btn-outline-success" onclick="showOwnerTransactions(${owner.ownerId})" title="المعاملات">
                            <i class="fas fa-exchange-alt"></i>
                        </button>
                        <button class="btn btn-outline-info" onclick="viewOwnerStatement(${owner.ownerId})" title="كشف حساب">
                            <i class="fas fa-file-alt"></i>
                        </button>
                        <button class="btn btn-outline-warning" onclick="showWithdrawalModal(${owner.ownerId})" title="سحب">
                            <i class="fas fa-hand-holding-usd"></i>
                        </button>
                        <button class="btn btn-outline-danger" onclick="deleteOwner(${owner.ownerId})" title="حذف">
                            <i class="fas fa-trash"></i>
                        </button>
                    </div>
                </td>
            </tr>
        `;
    });
    
    html += `
                </tbody>
            </table>
        </div>
    `;
    
    content.innerHTML = html;
}

// تحديث الإحصائيات
function updateStatistics() {
    const totalOwners = owners.length;
    const totalCapital = owners.reduce((sum, owner) => sum + (owner.capitalAmount || 0), 0);
    const totalShares = owners.reduce((sum, owner) => sum + (owner.sharePercentage || 0), 0);
    const totalWithdrawals = owners.reduce((sum, owner) => sum + (owner.totalWithdrawals || 0), 0);
    
    document.getElementById('totalOwners').textContent = totalOwners;
    document.getElementById('totalCapital').textContent = formatCurrency(totalCapital);
    document.getElementById('totalShares').textContent = totalShares.toFixed(1) + '%';
    document.getElementById('totalWithdrawals').textContent = formatCurrency(totalWithdrawals);
}

// إضافة مالك جديد
function showAddOwnerModal() {
    // إعادة تعيين النموذج
    document.getElementById('ownerForm').reset();
    document.getElementById('ownerId').value = '';
    document.getElementById('ownerModalTitle').textContent = 'إضافة مالك جديد';
    
    // إنشاء كود تلقائي
    document.getElementById('ownerCode').value = generateOwnerCode();
    
    // تعيين قيم افتراضية
    document.getElementById('currentBalance').value = 0;
    document.getElementById('joinDate').value = new Date().toISOString().split('T')[0];
    document.getElementById('ownerActive').checked = true;
    
    // إظهار النافذة
    const modal = new bootstrap.Modal(document.getElementById('ownerModal'));
    modal.show();
}

// إنشاء كود تلقائي للمالك
function generateOwnerCode() {
    const maxNumber = Math.max(...owners.map(owner => {
        const match = owner.ownerCode?.match(/OWN(\d+)/);
        return match ? parseInt(match[1]) : 0;
    }), 0);
    
    return `OWN${(maxNumber + 1).toString().padStart(3, '0')}`;
}

// حساب نسبة الحصة من رأس المال
function calculateSharePercentage() {
    const capitalAmount = parseFloat(document.getElementById('capitalAmount').value) || 0;
    const totalCapital = owners.reduce((sum, owner) => sum + (owner.capitalAmount || 0), 0);
    
    if (totalCapital > 0 && capitalAmount > 0) {
        const sharePercentage = (capitalAmount / (totalCapital + capitalAmount)) * 100;
        document.getElementById('sharePercentage').value = sharePercentage.toFixed(2);
    }
}

// حساب رأس المال من نسبة الحصة
function calculateCapitalAmount() {
    const sharePercentage = parseFloat(document.getElementById('sharePercentage').value) || 0;
    const totalCapital = owners.reduce((sum, owner) => sum + (owner.capitalAmount || 0), 0);
    
    if (sharePercentage > 0 && sharePercentage < 100) {
        const capitalAmount = (totalCapital * sharePercentage) / (100 - sharePercentage);
        document.getElementById('capitalAmount').value = capitalAmount.toFixed(2);
    }
}

// حفظ المالك
function saveOwner() {
    const form = document.getElementById('ownerForm');
    if (!form.checkValidity()) {
        form.reportValidity();
        return;
    }
    
    const ownerId = document.getElementById('ownerId').value;
    const ownerData = {
        ownerId: ownerId ? parseInt(ownerId) : Math.max(...owners.map(o => o.ownerId), 0) + 1,
        ownerCode: document.getElementById('ownerCode').value.trim(),
        ownerName: document.getElementById('ownerName').value.trim(),
        phone: document.getElementById('ownerPhone').value.trim(),
        email: document.getElementById('ownerEmail').value.trim(),
        address: document.getElementById('ownerAddress').value.trim(),
        capitalAmount: parseFloat(document.getElementById('capitalAmount').value) || 0,
        sharePercentage: parseFloat(document.getElementById('sharePercentage').value) || 0,
        currentBalance: parseFloat(document.getElementById('currentBalance').value) || 0,
        totalWithdrawals: 0,
        joinDate: document.getElementById('joinDate').value,
        isActive: document.getElementById('ownerActive').checked,
        notes: document.getElementById('ownerNotes').value.trim()
    };
    
    if (ownerId) {
        // تحديث مالك موجود
        const index = owners.findIndex(o => o.ownerId === parseInt(ownerId));
        if (index !== -1) {
            // الاحتفاظ بإجمالي السحوبات
            ownerData.totalWithdrawals = owners[index].totalWithdrawals || 0;
            owners[index] = ownerData;
            showSuccess('تم تحديث بيانات المالك بنجاح');
        }
    } else {
        // إضافة مالك جديد
        owners.push(ownerData);
        showSuccess('تم إضافة المالك بنجاح');
    }
    
    bootstrap.Modal.getInstance(document.getElementById('ownerModal')).hide();
    displayOwners();
    updateStatistics();
}

// ========== وظائف المساعدة ==========

// تنسيق العملة
function formatCurrency(amount) {
    return new Intl.NumberFormat('ar-YE', {
        style: 'currency',
        currency: 'YER',
        minimumFractionDigits: 0,
        maximumFractionDigits: 0
    }).format(amount).replace('YER', 'ر.ي');
}

// عرض رسالة خطأ
function showError(message) {
    const alertDiv = document.createElement('div');
    alertDiv.className = 'alert alert-danger alert-dismissible fade show position-fixed';
    alertDiv.style.cssText = 'top: 20px; right: 20px; z-index: 9999; min-width: 300px;';
    alertDiv.innerHTML = `
        <i class="fas fa-exclamation-triangle me-2"></i>
        ${message}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    `;
    document.body.appendChild(alertDiv);

    setTimeout(() => {
        if (alertDiv.parentNode) {
            alertDiv.remove();
        }
    }, 5000);
}

// عرض رسالة نجاح
function showSuccess(message) {
    const alertDiv = document.createElement('div');
    alertDiv.className = 'alert alert-success alert-dismissible fade show position-fixed';
    alertDiv.style.cssText = 'top: 20px; right: 20px; z-index: 9999; min-width: 300px;';
    alertDiv.innerHTML = `
        <i class="fas fa-check-circle me-2"></i>
        ${message}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    `;
    document.body.appendChild(alertDiv);

    setTimeout(() => {
        if (alertDiv.parentNode) {
            alertDiv.remove();
        }
    }, 3000);
}

// ========== وظائف إضافية ==========

// تعديل مالك
function editOwner(ownerId) {
    const owner = owners.find(o => o.ownerId === ownerId);
    if (!owner) {
        showError('المالك غير موجود');
        return;
    }

    // ملء النموذج ببيانات المالك
    document.getElementById('ownerId').value = owner.ownerId;
    document.getElementById('ownerCode').value = owner.ownerCode || '';
    document.getElementById('ownerName').value = owner.ownerName || '';
    document.getElementById('ownerPhone').value = owner.phone || '';
    document.getElementById('ownerEmail').value = owner.email || '';
    document.getElementById('ownerAddress').value = owner.address || '';
    document.getElementById('capitalAmount').value = owner.capitalAmount || 0;
    document.getElementById('sharePercentage').value = owner.sharePercentage || 0;
    document.getElementById('currentBalance').value = owner.currentBalance || 0;
    document.getElementById('joinDate').value = owner.joinDate || '';
    document.getElementById('ownerActive').checked = owner.isActive !== false;
    document.getElementById('ownerNotes').value = owner.notes || '';

    // تحديث العنوان
    document.getElementById('ownerModalTitle').textContent = 'تعديل بيانات المالك';

    // إظهار النافذة
    const modal = new bootstrap.Modal(document.getElementById('ownerModal'));
    modal.show();
}

// حذف مالك
function deleteOwner(ownerId) {
    const owner = owners.find(o => o.ownerId === ownerId);
    if (!owner) {
        showError('المالك غير موجود');
        return;
    }

    if (confirm(`هل أنت متأكد من حذف المالك "${owner.ownerName}"؟\nسيتم حذف جميع البيانات المرتبطة به.`)) {
        const index = owners.findIndex(o => o.ownerId === ownerId);
        if (index !== -1) {
            owners.splice(index, 1);
            showSuccess('تم حذف المالك بنجاح');
            displayOwners();
            updateStatistics();
        }
    }
}

// عرض معاملات المالك
function showOwnerTransactions(ownerId) {
    const owner = owners.find(o => o.ownerId === ownerId);
    if (!owner) {
        showError('المالك غير موجود');
        return;
    }

    alert(`معاملات المالك: ${owner.ownerName}\n\nهذه الوظيفة ستكون متاحة قريباً مع ربطها بسندات القبض والصرف.`);
}

// عرض كشف حساب المالك
function viewOwnerStatement(ownerId) {
    const owner = owners.find(o => o.ownerId === ownerId);
    if (!owner) {
        showError('المالك غير موجود');
        return;
    }

    const statementContent = `
        <div style="text-align: center; font-family: Arial, sans-serif; direction: rtl;">
            <h2>كشف حساب المالك</h2>
            <hr>
            <table style="width: 100%; margin: 20px 0;">
                <tr>
                    <td><strong>اسم المالك:</strong> ${owner.ownerName}</td>
                    <td><strong>الكود:</strong> ${owner.ownerCode}</td>
                </tr>
                <tr>
                    <td><strong>رأس المال:</strong> ${formatCurrency(owner.capitalAmount)}</td>
                    <td><strong>نسبة الحصة:</strong> ${owner.sharePercentage}%</td>
                </tr>
                <tr>
                    <td><strong>الرصيد الحالي:</strong> ${formatCurrency(owner.currentBalance)}</td>
                    <td><strong>إجمالي السحوبات:</strong> ${formatCurrency(owner.totalWithdrawals || 0)}</td>
                </tr>
                <tr>
                    <td><strong>تاريخ الانضمام:</strong> ${owner.joinDate ? new Date(owner.joinDate).toLocaleDateString('ar-YE') : '-'}</td>
                    <td><strong>الحالة:</strong> ${owner.isActive ? 'نشط' : 'غير نشط'}</td>
                </tr>
            </table>
            <hr>
            <p><strong>ملاحظات:</strong> ${owner.notes || 'لا توجد ملاحظات'}</p>
        </div>
    `;

    const printWindow = window.open('', '_blank');
    printWindow.document.write(`
        <html>
            <head>
                <title>كشف حساب المالك - ${owner.ownerName}</title>
                <style>
                    body { font-family: Arial, sans-serif; direction: rtl; }
                    table { border-collapse: collapse; }
                    td { padding: 8px; border: 1px solid #ddd; }
                </style>
            </head>
            <body>${statementContent}</body>
        </html>
    `);
    printWindow.document.close();
    printWindow.print();
}

// عرض نافذة سحب المالك
function showWithdrawalModal(ownerId) {
    const owner = owners.find(o => o.ownerId === ownerId);
    if (!owner) {
        showError('المالك غير موجود');
        return;
    }

    const amount = prompt(
        `سحب للمالك: ${owner.ownerName}\n` +
        `الرصيد الحالي: ${formatCurrency(owner.currentBalance)}\n` +
        `أدخل مبلغ السحب (ر.ي):`,
        ''
    );

    if (amount !== null && !isNaN(amount) && parseFloat(amount) > 0) {
        const withdrawalAmount = parseFloat(amount);

        // تحديث رصيد المالك
        owner.currentBalance -= withdrawalAmount;
        owner.totalWithdrawals = (owner.totalWithdrawals || 0) + withdrawalAmount;

        showSuccess(`تم تسجيل سحب بمبلغ ${formatCurrency(withdrawalAmount)} للمالك ${owner.ownerName}`);
        displayOwners();
        updateStatistics();

        // يمكن هنا إضافة ربط مع سندات الصرف
        console.log(`سحب مالك: ${withdrawalAmount} للمالك ${owner.ownerName}`);
    }
}

// البحث في الملاك
function searchOwners() {
    const searchTerm = document.getElementById('ownersSearch').value.toLowerCase();
    const filteredOwners = owners.filter(owner =>
        owner.ownerName.toLowerCase().includes(searchTerm) ||
        owner.ownerCode.toLowerCase().includes(searchTerm) ||
        (owner.phone && owner.phone.includes(searchTerm)) ||
        (owner.email && owner.email.toLowerCase().includes(searchTerm))
    );

    displayFilteredOwners(filteredOwners);
}

// فلترة الملاك
function filterOwners() {
    const statusFilter = document.getElementById('statusFilter').value;
    const shareFilter = document.getElementById('shareFilter').value;

    let filteredOwners = [...owners];

    // فلترة حسب الحالة
    if (statusFilter === 'active') {
        filteredOwners = filteredOwners.filter(owner => owner.isActive);
    } else if (statusFilter === 'inactive') {
        filteredOwners = filteredOwners.filter(owner => !owner.isActive);
    }

    // فلترة حسب الحصة
    if (shareFilter === 'major') {
        filteredOwners = filteredOwners.filter(owner => owner.sharePercentage > 30);
    } else if (shareFilter === 'medium') {
        filteredOwners = filteredOwners.filter(owner => owner.sharePercentage >= 10 && owner.sharePercentage <= 30);
    } else if (shareFilter === 'minor') {
        filteredOwners = filteredOwners.filter(owner => owner.sharePercentage < 10);
    }

    displayFilteredOwners(filteredOwners);
}

// عرض الملاك المفلترين
function displayFilteredOwners(filteredOwners) {
    // نفس منطق displayOwners لكن مع البيانات المفلترة
    const originalOwners = [...owners];
    owners = filteredOwners;
    displayOwners();
    owners = originalOwners;
}
