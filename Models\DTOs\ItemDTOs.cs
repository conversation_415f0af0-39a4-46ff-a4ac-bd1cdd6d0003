using System.ComponentModel.DataAnnotations;

namespace ANWBakerySystem.Models.DTOs
{
    /// <summary>
    /// DTO لعرض بيانات المنتج
    /// Item Data Transfer Object for display
    /// </summary>
    public class ItemDto
    {
        public int ItemId { get; set; }
        public string ItemCode { get; set; } = string.Empty;
        public string ItemName { get; set; } = string.Empty;
        public string? ItemNameEn { get; set; }
        public ItemType ItemType { get; set; }
        public int? CategoryId { get; set; }
        public string? CategoryName { get; set; }
        public int UnitId { get; set; }
        public string UnitName { get; set; } = string.Empty;
        public string UnitSymbol { get; set; } = string.Empty;
        public string? Barcode { get; set; }
        public string? Description { get; set; }
        public decimal PurchasePrice { get; set; }
        public decimal SalePrice { get; set; }
        public decimal WholesalePrice { get; set; }
        public decimal SalePriceLarge { get; set; }
        public decimal SalePriceMedium { get; set; }
        public decimal SalePriceSmall { get; set; }
        public decimal MinStock { get; set; }
        public decimal MaxStock { get; set; }
        public decimal ReorderPoint { get; set; }
        public decimal CurrentStock { get; set; }
        public bool TrackStock { get; set; }
        public bool AllowNegativeStock { get; set; }
        public bool HasExpiry { get; set; }
        public int ExpiryDays { get; set; }
        public string? Location { get; set; }
        public bool TrackBatches { get; set; }
        public bool TrackSerialNumbers { get; set; }
        public bool IsTaxable { get; set; }
        public decimal TaxPercentage { get; set; }
        public bool IsActive { get; set; }
        public DateTime CreatedAt { get; set; }
        public DateTime? UpdatedAt { get; set; }
    }

    /// <summary>
    /// طلب إنشاء منتج جديد
    /// Create Item Request
    /// </summary>
    public class CreateItemDto
    {
        [Required(ErrorMessage = "كود المنتج مطلوب")]
        [StringLength(50, ErrorMessage = "كود المنتج يجب أن يكون أقل من 50 حرف")]
        public string ItemCode { get; set; } = string.Empty;

        [Required(ErrorMessage = "اسم المنتج مطلوب")]
        [StringLength(200, ErrorMessage = "اسم المنتج يجب أن يكون أقل من 200 حرف")]
        public string ItemName { get; set; } = string.Empty;

        [StringLength(200, ErrorMessage = "الاسم بالإنجليزية يجب أن يكون أقل من 200 حرف")]
        public string? ItemNameEn { get; set; }

        [Required(ErrorMessage = "نوع المنتج مطلوب")]
        public ItemType ItemType { get; set; }

        public int? CategoryId { get; set; }

        // وحدات القياس المتقدمة
        [Required(ErrorMessage = "الوحدة الكبرى مطلوبة")]
        public int MajorUnitId { get; set; }

        public int? MediumUnitId { get; set; }

        public int? MinorUnitId { get; set; }

        [Range(1, double.MaxValue, ErrorMessage = "معامل التحويل من الكبرى للمتوسطة يجب أن يكون أكبر من صفر")]
        public decimal MajorToMediumFactor { get; set; } = 1;

        [Range(1, double.MaxValue, ErrorMessage = "معامل التحويل من المتوسطة للصغرى يجب أن يكون أكبر من صفر")]
        public decimal MediumToMinorFactor { get; set; } = 1;

        [StringLength(100, ErrorMessage = "الباركود يجب أن يكون أقل من 100 حرف")]
        public string? Barcode { get; set; }

        [StringLength(500, ErrorMessage = "الوصف يجب أن يكون أقل من 500 حرف")]
        public string? Description { get; set; }

        [Required(ErrorMessage = "سعر الشراء مطلوب")]
        [Range(0, double.MaxValue, ErrorMessage = "سعر الشراء يجب أن يكون أكبر من أو يساوي صفر")]
        public decimal PurchasePrice { get; set; }

        [Required(ErrorMessage = "سعر البيع مطلوب")]
        [Range(0, double.MaxValue, ErrorMessage = "سعر البيع يجب أن يكون أكبر من أو يساوي صفر")]
        public decimal SalePrice { get; set; }

        [Range(0, double.MaxValue, ErrorMessage = "الحد الأدنى للمخزون يجب أن يكون أكبر من أو يساوي صفر")]
        public decimal? MinimumStock { get; set; }

        [Range(0, double.MaxValue, ErrorMessage = "الحد الأقصى للمخزون يجب أن يكون أكبر من أو يساوي صفر")]
        public decimal? MaximumStock { get; set; }

        [Range(0, double.MaxValue, ErrorMessage = "نقطة إعادة الطلب يجب أن تكون أكبر من أو تساوي صفر")]
        public decimal? ReorderLevel { get; set; }

        [Range(0, double.MaxValue, ErrorMessage = "المخزون الحالي يجب أن يكون أكبر من أو يساوي صفر")]
        public decimal? CurrentStock { get; set; }

        public bool? TrackStock { get; set; }
        public bool? AllowNegativeStock { get; set; }
        public bool? HasExpiry { get; set; }

        [Range(0, int.MaxValue, ErrorMessage = "أيام انتهاء الصلاحية يجب أن تكون أكبر من أو تساوي صفر")]
        public int? ExpiryDays { get; set; }

        [StringLength(100, ErrorMessage = "الموقع يجب أن يكون أقل من 100 حرف")]
        public string? Location { get; set; }

        public bool? TrackBatches { get; set; }
        public bool? TrackSerialNumbers { get; set; }
        public bool? IsTaxable { get; set; }

        [Range(0, 100, ErrorMessage = "نسبة الضريبة يجب أن تكون بين 0 و 100")]
        public decimal? TaxPercentage { get; set; }

        public bool? IsActive { get; set; }
    }

    /// <summary>
    /// طلب تحديث منتج موجود
    /// Update Item Request
    /// </summary>
    public class UpdateItemDto
    {
        [Required(ErrorMessage = "كود المنتج مطلوب")]
        [StringLength(50, ErrorMessage = "كود المنتج يجب أن يكون أقل من 50 حرف")]
        public string ItemCode { get; set; } = string.Empty;

        [Required(ErrorMessage = "اسم المنتج مطلوب")]
        [StringLength(200, ErrorMessage = "اسم المنتج يجب أن يكون أقل من 200 حرف")]
        public string ItemName { get; set; } = string.Empty;

        [StringLength(200, ErrorMessage = "الاسم بالإنجليزية يجب أن يكون أقل من 200 حرف")]
        public string? ItemNameEn { get; set; }

        [Required(ErrorMessage = "نوع المنتج مطلوب")]
        public ItemType ItemType { get; set; }

        public int? CategoryId { get; set; }

        [Required(ErrorMessage = "وحدة القياس مطلوبة")]
        public int UnitId { get; set; }

        [StringLength(100, ErrorMessage = "الباركود يجب أن يكون أقل من 100 حرف")]
        public string? Barcode { get; set; }

        [StringLength(500, ErrorMessage = "الوصف يجب أن يكون أقل من 500 حرف")]
        public string? Description { get; set; }

        [Required(ErrorMessage = "سعر الشراء مطلوب")]
        [Range(0, double.MaxValue, ErrorMessage = "سعر الشراء يجب أن يكون أكبر من أو يساوي صفر")]
        public decimal PurchasePrice { get; set; }

        [Required(ErrorMessage = "سعر البيع مطلوب")]
        [Range(0, double.MaxValue, ErrorMessage = "سعر البيع يجب أن يكون أكبر من أو يساوي صفر")]
        public decimal SalePrice { get; set; }

        [Range(0, double.MaxValue, ErrorMessage = "سعر الجملة يجب أن يكون أكبر من أو يساوي صفر")]
        public decimal? WholesalePrice { get; set; }

        [Range(0, double.MaxValue, ErrorMessage = "سعر البيع الكبير يجب أن يكون أكبر من أو يساوي صفر")]
        public decimal? SalePriceLarge { get; set; }

        [Range(0, double.MaxValue, ErrorMessage = "سعر البيع المتوسط يجب أن يكون أكبر من أو يساوي صفر")]
        public decimal? SalePriceMedium { get; set; }

        [Range(0, double.MaxValue, ErrorMessage = "سعر البيع الصغير يجب أن يكون أكبر من أو يساوي صفر")]
        public decimal? SalePriceSmall { get; set; }

        [Range(0, double.MaxValue, ErrorMessage = "الحد الأدنى للمخزون يجب أن يكون أكبر من أو يساوي صفر")]
        public decimal? MinStock { get; set; }

        [Range(0, double.MaxValue, ErrorMessage = "الحد الأقصى للمخزون يجب أن يكون أكبر من أو يساوي صفر")]
        public decimal? MaxStock { get; set; }

        [Range(0, double.MaxValue, ErrorMessage = "نقطة إعادة الطلب يجب أن تكون أكبر من أو تساوي صفر")]
        public decimal? ReorderPoint { get; set; }

        public bool? TrackStock { get; set; }
        public bool? AllowNegativeStock { get; set; }
        public bool? HasExpiry { get; set; }

        [Range(0, int.MaxValue, ErrorMessage = "أيام انتهاء الصلاحية يجب أن تكون أكبر من أو تساوي صفر")]
        public int? ExpiryDays { get; set; }

        [StringLength(100, ErrorMessage = "الموقع يجب أن يكون أقل من 100 حرف")]
        public string? Location { get; set; }

        public bool? TrackBatches { get; set; }
        public bool? TrackSerialNumbers { get; set; }
        public bool? IsTaxable { get; set; }

        [Range(0, 100, ErrorMessage = "نسبة الضريبة يجب أن تكون بين 0 و 100")]
        public decimal? TaxPercentage { get; set; }

        public bool? IsActive { get; set; }
    }

    /// <summary>
    /// نتيجة عملية المنتج الواحد
    /// Single Item Result
    /// </summary>
    public class ItemResult
    {
        public bool Success { get; set; }
        public string Message { get; set; } = string.Empty;
        public ItemDto? Item { get; set; }
        public List<string>? Errors { get; set; }
    }

    /// <summary>
    /// نتيجة عملية المنتجات المتعددة
    /// Multiple Items Result
    /// </summary>
    public class ItemsResult
    {
        public bool Success { get; set; }
        public string Message { get; set; } = string.Empty;
        public List<ItemDto>? Items { get; set; }
        public int TotalCount { get; set; }
        public int Page { get; set; }
        public int PageSize { get; set; }
        public int TotalPages { get; set; }
        public List<string>? Errors { get; set; }
    }
}
