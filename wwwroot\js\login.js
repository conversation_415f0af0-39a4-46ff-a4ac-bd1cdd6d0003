// ملف تسجيل الدخول

// تسجيل الدخول
async function login() {
    const username = document.getElementById('username').value;
    const password = document.getElementById('password').value;
    
    if (!username || !password) {
        showError('يرجى إدخال اسم المستخدم وكلمة المرور');
        return;
    }
    
    try {
        showLoading(true);
        
        // اختبار الاتصال أولاً
        try {
            const testResponse = await fetch('/api/auth/test');
            console.log('اختبار الاتصال:', testResponse.ok);
        } catch (testError) {
            console.log('خطأ في اختبار الاتصال:', testError);
        }
        
        const response = await fetch('/api/auth/login', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'Accept': 'application/json'
            },
            body: JSON.stringify({
                username: username,
                password: password
            })
        });
        
        if (!response.ok) {
            throw new Error(`خطأ في الخادم: ${response.status}`);
        }
        
        const data = await response.json();
        
        if (data.success) {
            // حفظ بيانات المستخدم
            localStorage.setItem('user', JSON.stringify(data.user));
            localStorage.setItem('token', data.token);
            localStorage.setItem('authToken', data.token);
            localStorage.setItem('userInfo', JSON.stringify(data.user));
            
            showSuccess('تم تسجيل الدخول بنجاح');
            
            // الانتقال إلى لوحة التحكم
            setTimeout(() => {
                window.location.href = 'dashboard.html';
            }, 1000);
        } else {
            showError(data.message || 'خطأ في تسجيل الدخول');
        }
    } catch (error) {
        console.error('خطأ في الاتصال:', error);
        
        // تجربة تسجيل دخول محلي كحل بديل
        if (username === 'admin' && password === 'admin123') {
            const userData = {
                id: 1,
                username: 'admin',
                fullName: 'مدير النظام',
                role: 'Admin'
            };
            
            localStorage.setItem('user', JSON.stringify(userData));
            localStorage.setItem('token', 'local-token-12345');
            localStorage.setItem('authToken', 'local-token-12345');
            localStorage.setItem('userInfo', JSON.stringify(userData));
            
            showSuccess('تم تسجيل الدخول بنجاح');
            setTimeout(() => {
                window.location.href = 'dashboard.html';
            }, 1000);
        } else {
            showError('خطأ في الاتصال بالخادم. جرب: admin / admin123');
        }
    } finally {
        showLoading(false);
    }
}

// عرض رسالة خطأ
function showError(message) {
    const errorDiv = document.getElementById('error-message');
    if (errorDiv) {
        errorDiv.textContent = message;
        errorDiv.style.display = 'block';
        errorDiv.className = 'alert alert-danger';
    } else {
        alert('خطأ: ' + message);
    }
    
    // إخفاء الرسالة بعد 5 ثوان
    setTimeout(() => {
        if (errorDiv) {
            errorDiv.style.display = 'none';
        }
    }, 5000);
}

// عرض رسالة نجاح
function showSuccess(message) {
    const errorDiv = document.getElementById('error-message');
    if (errorDiv) {
        errorDiv.textContent = message;
        errorDiv.style.display = 'block';
        errorDiv.className = 'alert alert-success';
    } else {
        alert('نجح: ' + message);
    }
}

// عرض حالة التحميل
function showLoading(show) {
    const loginBtn = document.getElementById('loginBtn');
    const loadingSpinner = document.getElementById('loadingSpinner');
    
    if (loginBtn) {
        loginBtn.disabled = show;
        if (show) {
            loginBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> جاري تسجيل الدخول...';
        } else {
            loginBtn.innerHTML = '<i class="fas fa-sign-in-alt"></i> تسجيل الدخول';
        }
    }
    
    if (loadingSpinner) {
        loadingSpinner.style.display = show ? 'block' : 'none';
    }
}

// التحقق من Enter key
function handleKeyPress(event) {
    if (event.key === 'Enter') {
        login();
    }
}

// تحميل الصفحة
document.addEventListener('DOMContentLoaded', function() {
    // التحقق من وجود مستخدم مسجل دخول
    const token = localStorage.getItem('authToken');
    if (token) {
        window.location.href = 'dashboard.html';
        return;
    }
    
    // إضافة مستمعي الأحداث
    const usernameInput = document.getElementById('username');
    const passwordInput = document.getElementById('password');
    const loginBtn = document.getElementById('loginBtn');
    
    if (usernameInput) {
        usernameInput.addEventListener('keypress', handleKeyPress);
    }
    
    if (passwordInput) {
        passwordInput.addEventListener('keypress', handleKeyPress);
    }
    
    if (loginBtn) {
        loginBtn.addEventListener('click', login);
    }
    
    console.log('صفحة تسجيل الدخول جاهزة');
});
