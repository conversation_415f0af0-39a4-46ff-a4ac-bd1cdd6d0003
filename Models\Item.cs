using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace ANWBakerySystem.Models
{
    /// <summary>
    /// نموذج المنتجات والخامات والخدمات مع دعم وحدات القياس المتقدمة
    /// Items model for products, materials and services with advanced unit measurement support
    /// </summary>
    public class Item
    {
        [Key]
        public int ItemId { get; set; }

        [Required]
        [StringLength(50)]
        [Display(Name = "كود الصنف")]
        public string ItemCode { get; set; } = string.Empty;

        [Required]
        [StringLength(200)]
        [Display(Name = "اسم الصنف")]
        public string ItemName { get; set; } = string.Empty;

        [StringLength(200)]
        [Display(Name = "الاسم بالإنجليزية")]
        public string? ItemNameEn { get; set; }

        [Required]
        [Display(Name = "نوع الصنف")]
        public ItemType ItemType { get; set; }

        [Display(Name = "فئة الصنف")]
        public int? CategoryId { get; set; }

        [Required]
        [Display(Name = "وحدة القياس")]
        public int UnitId { get; set; }

        [StringLength(100)]
        [Display(Name = "الباركود")]
        public string? Barcode { get; set; }

        [StringLength(500)]
        [Display(Name = "الوصف")]
        public string? Description { get; set; }

        /// <summary>
        /// سعر الشراء بالوحدة الأساسية بالريال اليمني
        /// Purchase price per base unit in Yemeni Rial
        /// </summary>
        [Display(Name = "سعر الشراء (ر.ي)")]
        [Column(TypeName = "decimal(18,3)")]
        public decimal PurchasePrice { get; set; } = 0;

        /// <summary>
        /// سعر البيع بالوحدة الأساسية بالريال اليمني
        /// Sale price per base unit in Yemeni Rial
        /// </summary>
        [Display(Name = "سعر البيع (ر.ي)")]
        [Column(TypeName = "decimal(18,3)")]
        public decimal SalePrice { get; set; } = 0;

        /// <summary>
        /// سعر البيع بالوحدة الكبرى بالريال اليمني
        /// Sale price per large unit in Yemeni Rial
        /// </summary>
        [Display(Name = "سعر البيع - وحدة كبرى (ر.ي)")]
        [Column(TypeName = "decimal(18,3)")]
        public decimal SalePriceLarge { get; set; } = 0;

        /// <summary>
        /// سعر البيع بالوحدة المتوسطة بالريال اليمني
        /// Sale price per medium unit in Yemeni Rial
        /// </summary>
        [Display(Name = "سعر البيع - وحدة متوسطة (ر.ي)")]
        [Column(TypeName = "decimal(18,3)")]
        public decimal SalePriceMedium { get; set; } = 0;

        /// <summary>
        /// سعر البيع بالوحدة الصغرى بالريال اليمني
        /// Sale price per small unit in Yemeni Rial
        /// </summary>
        [Display(Name = "سعر البيع - وحدة صغرى (ر.ي)")]
        [Column(TypeName = "decimal(18,3)")]
        public decimal SalePriceSmall { get; set; } = 0;

        [Display(Name = "الحد الأدنى للمخزون")]
        [Column(TypeName = "decimal(18,6)")]
        public decimal MinimumStock { get; set; } = 0;

        [Display(Name = "الحد الأقصى للمخزون")]
        [Column(TypeName = "decimal(18,6)")]
        public decimal? MaximumStock { get; set; }

        [Display(Name = "نقطة إعادة الطلب")]
        [Column(TypeName = "decimal(18,6)")]
        public decimal ReorderPoint { get; set; } = 0;

        [Display(Name = "كمية إعادة الطلب")]
        [Column(TypeName = "decimal(18,6)")]
        public decimal ReorderQuantity { get; set; } = 0;

        [Display(Name = "مدة الصلاحية (أيام)")]
        public int? ShelfLifeDays { get; set; }

        [Display(Name = "درجة الحرارة المطلوبة")]
        [StringLength(100)]
        public string? RequiredTemperature { get; set; }

        [Display(Name = "تتبع الدفعات")]
        public bool TrackBatches { get; set; } = false;

        [Display(Name = "تتبع الأرقام التسلسلية")]
        public bool TrackSerialNumbers { get; set; } = false;

        [Display(Name = "خاضع للضريبة")]
        public bool IsTaxable { get; set; } = true;

        [Display(Name = "نسبة الضريبة")]
        [Column(TypeName = "decimal(5,2)")]
        public decimal TaxPercentage { get; set; } = 0;

        [Display(Name = "نشط")]
        public bool IsActive { get; set; } = true;

        [Display(Name = "تاريخ الإنشاء")]
        public DateTime CreatedAt { get; set; } = DateTime.Now;

        [Display(Name = "تاريخ التحديث")]
        public DateTime? UpdatedAt { get; set; }

        [Display(Name = "منشئ بواسطة")]
        public int CreatedBy { get; set; }

        [Display(Name = "محدث بواسطة")]
        public int? UpdatedBy { get; set; }

        // Navigation properties
        public virtual Unit Unit { get; set; } = null!;
        public virtual ItemCategory? Category { get; set; }
        public virtual User Creator { get; set; } = null!;
        public virtual User? Updater { get; set; }
        public virtual ICollection<InvoiceItem> InvoiceItems { get; set; } = new List<InvoiceItem>();
        public virtual ICollection<InventoryMovement> InventoryMovements { get; set; } = new List<InventoryMovement>();
        public virtual ICollection<RecipeItem> RecipeItems { get; set; } = new List<RecipeItem>();
        public virtual ICollection<Recipe> Recipes { get; set; } = new List<Recipe>();

        /// <summary>
        /// حساب السعر حسب نوع الوحدة
        /// Calculate price based on unit type
        /// </summary>
        public decimal GetPriceByUnitType(UnitType unitType)
        {
            return unitType switch
            {
                UnitType.Large => SalePriceLarge,
                UnitType.Medium => SalePriceMedium,
                UnitType.Small => SalePriceSmall,
                _ => SalePrice
            };
        }
    }

    public enum ItemType
    {
        [Display(Name = "منتج نهائي")]
        FinishedProduct = 1,
        [Display(Name = "مادة خام")]
        RawMaterial = 2,
        [Display(Name = "منتج نصف مصنع")]
        SemiFinished = 3,
        [Display(Name = "خدمة")]
        Service = 4,
        [Display(Name = "مستهلكات")]
        Consumables = 5,
        [Display(Name = "أصول")]
        Assets = 6
    }

    public enum UnitType
    {
        [Display(Name = "وحدة كبرى")]
        Large = 1,
        [Display(Name = "وحدة متوسطة")]
        Medium = 2,
        [Display(Name = "وحدة صغرى")]
        Small = 3,
        [Display(Name = "وحدة أساسية")]
        Base = 4
    }
}
