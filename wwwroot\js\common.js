// ملف JavaScript مشترك للوظائف العامة
// Common JavaScript functions

// إعدادات عامة
const API_BASE_URL = '/api';
const APP_NAME = 'نظام مخبوزات ANW';

// فحص حالة الخادم
async function checkServerStatus() {
    try {
        const response = await fetch('/health');
        if (response.ok) {
            const data = await response.json();
            console.log('✅ الخادم متصل:', data.message);
            return true;
        }
    } catch (error) {
        console.error('❌ خطأ في الاتصال بالخادم:', error);
        showConnectionError();
        return false;
    }
}

// عرض رسالة خطأ الاتصال
function showConnectionError() {
    const errorDiv = document.createElement('div');
    errorDiv.className = 'alert alert-warning alert-dismissible fade show position-fixed';
    errorDiv.style.cssText = 'top: 20px; right: 20px; z-index: 9999; max-width: 400px;';
    errorDiv.innerHTML = `
        <div class="d-flex align-items-center">
            <i class="fas fa-exclamation-triangle me-2"></i>
            <div>
                <strong>تحذير!</strong><br>
                يعمل النظام في الوضع التجريبي<br>
                <small>البيانات محفوظة مؤقت<|im_start|> في الذاكرة</small>
            </div>
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    `;
    document.body.appendChild(errorDiv);
    
    // إزالة الرسالة تلقائياً بعد 5 ثوان
    setTimeout(() => {
        if (errorDiv.parentNode) {
            errorDiv.remove();
        }
    }, 5000);
}

// عرض رسائل النجاح
function showSuccess(message) {
    showNotification(message, 'success');
}

// عرض رسائل الخطأ
function showError(message) {
    showNotification(message, 'danger');
}

// عرض رسائل المعلومات
function showInfo(message) {
    showNotification(message, 'info');
}

// عرض الإشعارات
function showNotification(message, type = 'info') {
    const alertDiv = document.createElement('div');
    alertDiv.className = `alert alert-${type} alert-dismissible fade show position-fixed`;
    alertDiv.style.cssText = 'top: 20px; left: 50%; transform: translateX(-50%); z-index: 9999; max-width: 500px;';
    
    const icon = {
        'success': 'fas fa-check-circle',
        'danger': 'fas fa-exclamation-circle',
        'warning': 'fas fa-exclamation-triangle',
        'info': 'fas fa-info-circle'
    }[type] || 'fas fa-info-circle';
    
    alertDiv.innerHTML = `
        <div class="d-flex align-items-center">
            <i class="${icon} me-2"></i>
            <span>${message}</span>
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    `;
    
    document.body.appendChild(alertDiv);
    
    // إزالة الإشعار تلقائياً
    setTimeout(() => {
        if (alertDiv.parentNode) {
            alertDiv.remove();
        }
    }, type === 'success' ? 3000 : 5000);
}

// تنسيق العملة (الريال اليمني)
function formatCurrency(amount) {
    if (typeof amount !== 'number') {
        amount = parseFloat(amount) || 0;
    }
    
    return new Intl.NumberFormat('ar-YE', {
        minimumFractionDigits: 0,
        maximumFractionDigits: 0
    }).format(amount) + ' ر.ي';
}

// تنسيق التاريخ
function formatDate(dateString) {
    if (!dateString) return '-';
    
    const date = new Date(dateString);
    return date.toLocaleDateString('ar-YE', {
        year: 'numeric',
        month: 'short',
        day: 'numeric'
    });
}

// تنسيق التاريخ والوقت
function formatDateTime(dateString) {
    if (!dateString) return '-';
    
    const date = new Date(dateString);
    return date.toLocaleString('ar-YE', {
        year: 'numeric',
        month: 'short',
        day: 'numeric',
        hour: '2-digit',
        minute: '2-digit'
    });
}

// التحقق من صحة البريد الإلكتروني
function isValidEmail(email) {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
}

// التحقق من صحة رقم الهاتف اليمني
function isValidYemeniPhone(phone) {
    const phoneRegex = /^(77|73|70|71)\d{7}$/;
    return phoneRegex.test(phone.replace(/\D/g, ''));
}

// تسجيل الخروج
function logout() {
    if (confirm('هل أنت متأكد من تسجيل الخروج؟')) {
        localStorage.removeItem('authToken');
        localStorage.removeItem('currentUser');
        localStorage.removeItem('userInfo');
        localStorage.removeItem('user');
        showSuccess('تم تسجيل الخروج بنجاح');
        setTimeout(() => {
            window.location.href = 'index.html';
        }, 1000);
    }
}

// التحقق من المصادقة
function checkAuthentication() {
    const token = localStorage.getItem('authToken');
    const user = localStorage.getItem('currentUser');
    
    if (!token || !user) {
        window.location.href = 'index.html';
        return false;
    }
    
    try {
        const userData = JSON.parse(user);
        const userNameElement = document.getElementById('currentUser');
        if (userNameElement) {
            userNameElement.textContent = userData.fullName || userData.username || 'مستخدم';
        }
        return true;
    } catch (error) {
        console.error('خطأ في بيانات المستخدم:', error);
        window.location.href = 'index.html';
        return false;
    }
}

// تحميل البيانات مع معالجة الأخطاء
async function fetchWithErrorHandling(url, options = {}) {
    try {
        const response = await fetch(url, {
            headers: {
                'Content-Type': 'application/json',
                ...options.headers
            },
            ...options
        });
        
        if (!response.ok) {
            throw new Error(`HTTP ${response.status}: ${response.statusText}`);
        }
        
        return await response.json();
    } catch (error) {
        console.error(`خطأ في ${url}:`, error);
        showError('خطأ في الاتصال بالخادم - يعمل النظام في الوضع التجريبي');
        throw error;
    }
}

// تشغيل فحص الخادم عند تحميل الصفحة
document.addEventListener('DOMContentLoaded', function() {
    checkServerStatus();
    
    // إضافة معلومات النظام في أسفل الصفحة
    const footer = document.createElement('div');
    footer.className = 'text-center text-muted mt-4 py-3';
    footer.innerHTML = `
        <small>
            ${APP_NAME} - النسخة التجريبية 1.0.0<br>
            <i class="fas fa-circle text-success"></i> متصل بالخادم
        </small>
    `;
    
    // إضافة الفوتر في نهاية الصفحة
    const mainContent = document.querySelector('main') || document.body;
    if (mainContent) {
        mainContent.appendChild(footer);
    }
});
