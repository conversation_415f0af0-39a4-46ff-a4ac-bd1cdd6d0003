<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إدارة العملاء والموردين - نظام إدارة مخبوزات ANW</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="css/style.css" rel="stylesheet">
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary">
        <div class="container-fluid">
            <a class="navbar-brand" href="dashboard.html">
                <i class="fas fa-bread-slice me-2"></i>
                نظام إدارة مخبوزات ANW
            </a>
            <div class="navbar-nav ms-auto">
                <a class="nav-link" href="dashboard.html">
                    <i class="fas fa-home"></i> الرئيسية
                </a>
                <a class="nav-link" href="#" onclick="logout()">
                    <i class="fas fa-sign-out-alt"></i> تسجيل الخروج
                </a>
            </div>
        </div>
    </nav>

    <div class="container-fluid mt-4">
        <div class="row">
            <!-- Sidebar -->
            <div class="col-md-3">
                <div class="card">
                    <div class="card-header">
                        <h5><i class="fas fa-list"></i> القوائم</h5>
                    </div>
                    <div class="list-group list-group-flush">
                        <a href="dashboard.html" class="list-group-item list-group-item-action">
                            <i class="fas fa-tachometer-alt"></i> لوحة التحكم
                        </a>
                        <a href="units.html" class="list-group-item list-group-item-action">
                            <i class="fas fa-balance-scale"></i> وحدات القياس
                        </a>
                        <a href="parties.html" class="list-group-item list-group-item-action active">
                            <i class="fas fa-users"></i> العملاء والموردين
                        </a>
                        <a href="items-management.html" class="list-group-item list-group-item-action">
                            <i class="fas fa-boxes"></i> الأصناف والمنتجات
                        </a>
                        <a href="invoices.html" class="list-group-item list-group-item-action">
                            <i class="fas fa-file-invoice"></i> الفواتير
                        </a>
                    </div>
                </div>
            </div>

            <!-- Main Content -->
            <div class="col-md-9">
                <div class="card">
                    <div class="card-header d-flex justify-content-between align-items-center">
                        <h4><i class="fas fa-users"></i> إدارة العملاء والموردين</h4>
                        <div>
                            <button class="btn btn-success" onclick="showAddModal()">
                                <i class="fas fa-plus"></i> إضافة جديد
                            </button>
                            <button class="btn btn-info" onclick="printParties()">
                                <i class="fas fa-print"></i> طباعة
                            </button>
                        </div>
                    </div>
                    <div class="card-body">
                        <!-- Filters -->
                        <div class="row mb-3">
                            <div class="col-md-4">
                                <input type="text" class="form-control" id="searchInput" placeholder="البحث..." onkeyup="searchParties()">
                            </div>
                            <div class="col-md-3">
                                <select class="form-select" id="typeFilter" onchange="filterParties()">
                                    <option value="">جميع الأنواع</option>
                                    <option value="customer">عملاء فقط</option>
                                    <option value="supplier">موردين فقط</option>
                                    <option value="both">عملاء وموردين</option>
                                </select>
                            </div>
                            <div class="col-md-3">
                                <select class="form-select" id="statusFilter" onchange="filterParties()">
                                    <option value="">جميع الحالات</option>
                                    <option value="true">نشط</option>
                                    <option value="false">غير نشط</option>
                                </select>
                            </div>
                            <div class="col-md-2">
                                <button class="btn btn-primary w-100" onclick="loadParties()">
                                    <i class="fas fa-sync"></i> تحديث
                                </button>
                            </div>
                        </div>

                        <!-- Parties Table -->
                        <div class="table-responsive">
                            <table class="table table-striped table-hover">
                                <thead class="table-dark">
                                    <tr>
                                        <th>الرقم</th>
                                        <th>الاسم</th>
                                        <th>النوع</th>
                                        <th>الهاتف</th>
                                        <th>الرصيد (ر.ي)</th>
                                        <th>حد الائتمان (ر.ي)</th>
                                        <th>الحالة</th>
                                        <th>الإجراءات</th>
                                    </tr>
                                </thead>
                                <tbody id="partiesTableBody">
                                    <tr>
                                        <td colspan="8" class="text-center">
                                            <div class="spinner-border text-primary" role="status">
                                                <span class="visually-hidden">جاري التحميل...</span>
                                            </div>
                                        </td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Add/Edit Party Modal -->
    <div class="modal fade" id="partyModal" tabindex="-1">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="modalTitle">إضافة عميل/مورد جديد</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <form id="partyForm">
                        <input type="hidden" id="partyId">
                        
                        <!-- Basic Info -->
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="partyName" class="form-label">الاسم *</label>
                                    <input type="text" class="form-control" id="partyName" required>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="partyNameEn" class="form-label">الاسم بالإنجليزية</label>
                                    <input type="text" class="form-control" id="partyNameEn">
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="partyCode" class="form-label">الكود</label>
                                    <input type="text" class="form-control" id="partyCode">
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="taxNumber" class="form-label">الرقم الضريبي</label>
                                    <input type="text" class="form-control" id="taxNumber">
                                </div>
                            </div>
                        </div>

                        <!-- Party Type -->
                        <div class="row">
                            <div class="col-md-12">
                                <div class="mb-3">
                                    <label class="form-label">نوع الطرف *</label>
                                    <div class="form-check-inline">
                                        <input class="form-check-input" type="checkbox" id="isCustomer" value="customer">
                                        <label class="form-check-label" for="isCustomer">عميل</label>
                                    </div>
                                    <div class="form-check-inline">
                                        <input class="form-check-input" type="checkbox" id="isSupplier" value="supplier">
                                        <label class="form-check-label" for="isSupplier">مورد</label>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Contact Info -->
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="phone" class="form-label">الهاتف</label>
                                    <input type="tel" class="form-control" id="phone">
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="email" class="form-label">البريد الإلكتروني</label>
                                    <input type="email" class="form-control" id="email">
                                </div>
                            </div>
                        </div>

                        <!-- Address -->
                        <div class="mb-3">
                            <label for="address" class="form-label">العنوان</label>
                            <textarea class="form-control" id="address" rows="2"></textarea>
                        </div>

                        <!-- Additional Info -->
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="commercialRegister" class="form-label">السجل التجاري</label>
                                    <input type="text" class="form-control" id="commercialRegister">
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="paymentTerms" class="form-label">شروط الدفع (أيام)</label>
                                    <input type="number" class="form-control" id="paymentTerms" value="0">
                                </div>
                            </div>
                        </div>

                        <!-- Financial Info -->
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="creditLimit" class="form-label">حد الائتمان (ر.ي)</label>
                                    <input type="number" class="form-control" id="creditLimit" step="0.001" value="0">
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="openingBalance" class="form-label">الرصيد الافتتاحي (ر.ي)</label>
                                    <input type="number" class="form-control" id="openingBalance" step="0.001" value="0">
                                </div>
                            </div>
                        </div>

                        <!-- Notes -->
                        <div class="mb-3">
                            <label for="notes" class="form-label">ملاحظات</label>
                            <textarea class="form-control" id="notes" rows="2"></textarea>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                    <button type="button" class="btn btn-primary" onclick="saveParty()">حفظ</button>
                </div>
            </div>
        </div>
    </div>

    <!-- Account Statement Modal -->
    <div class="modal fade" id="statementModal" tabindex="-1">
        <div class="modal-dialog modal-xl">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">كشف حساب</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body" id="statementContent">
                    <!-- Statement content will be loaded here -->
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-primary" onclick="printStatement()">
                        <i class="fas fa-print"></i> طباعة
                    </button>
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إغلاق</button>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="js/auth-simple.js"></script>
    <script src="js/parties.js"></script>
</body>
</html>
