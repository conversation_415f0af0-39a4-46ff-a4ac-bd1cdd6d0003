using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace ANWBakerySystem.Models
{
    /// <summary>
    /// وصفات الإنتاج مع حساب التكلفة بدقة
    /// Production recipes with accurate cost calculation
    /// </summary>
    public class Recipe
    {
        [Key]
        public int RecipeId { get; set; }

        [Required]
        [StringLength(50)]
        [Display(Name = "كود الوصفة")]
        public string RecipeCode { get; set; } = string.Empty;

        [Required]
        [StringLength(200)]
        [Display(Name = "اسم الوصفة")]
        public string RecipeName { get; set; } = string.Empty;

        [StringLength(200)]
        [Display(Name = "الاسم بالإنجليزية")]
        public string? RecipeNameEn { get; set; }

        [Required]
        [Display(Name = "المنتج النهائي")]
        public int ProductItemId { get; set; }

        [Required]
        [Display(Name = "وحدة الإنتاج")]
        public int ProductionUnitId { get; set; }

        [Required]
        [Display(Name = "نوع وحدة الإنتاج")]
        public UnitType ProductionUnitType { get; set; }

        /// <summary>
        /// كمية الإنتاج بالوحدة المختارة
        /// Production quantity in selected unit
        /// </summary>
        [Required]
        [Display(Name = "كمية الإنتاج")]
        [Column(TypeName = "decimal(18,6)")]
        public decimal ProductionQuantity { get; set; }

        /// <summary>
        /// كمية الإنتاج بالوحدة الأساسية
        /// Production quantity in base unit
        /// </summary>
        [Required]
        [Display(Name = "كمية الإنتاج الأساسية")]
        [Column(TypeName = "decimal(18,6)")]
        public decimal BaseProductionQuantity { get; set; }

        /// <summary>
        /// إجمالي تكلفة المواد الخام بالريال اليمني
        /// Total raw materials cost in Yemeni Rial
        /// </summary>
        [Display(Name = "تكلفة المواد الخام (ر.ي)")]
        [Column(TypeName = "decimal(18,3)")]
        public decimal RawMaterialsCost { get; set; } = 0;

        /// <summary>
        /// تكلفة العمالة بالريال اليمني
        /// Labor cost in Yemeni Rial
        /// </summary>
        [Display(Name = "تكلفة العمالة (ر.ي)")]
        [Column(TypeName = "decimal(18,3)")]
        public decimal LaborCost { get; set; } = 0;

        /// <summary>
        /// التكاليف الإضافية بالريال اليمني
        /// Additional costs in Yemeni Rial
        /// </summary>
        [Display(Name = "التكاليف الإضافية (ر.ي)")]
        [Column(TypeName = "decimal(18,3)")]
        public decimal AdditionalCosts { get; set; } = 0;

        /// <summary>
        /// إجمالي تكلفة الإنتاج بالريال اليمني
        /// Total production cost in Yemeni Rial
        /// </summary>
        [Display(Name = "إجمالي التكلفة (ر.ي)")]
        [Column(TypeName = "decimal(18,3)")]
        public decimal TotalCost { get; set; } = 0;

        /// <summary>
        /// تكلفة الوحدة الواحدة بالريال اليمني
        /// Cost per unit in Yemeni Rial
        /// </summary>
        [Display(Name = "تكلفة الوحدة (ر.ي)")]
        [Column(TypeName = "decimal(18,3)")]
        public decimal CostPerUnit { get; set; } = 0;

        [Display(Name = "وقت التحضير (دقيقة)")]
        public int PreparationTimeMinutes { get; set; } = 0;

        [Display(Name = "وقت الطبخ (دقيقة)")]
        public int CookingTimeMinutes { get; set; } = 0;

        [Display(Name = "إجمالي الوقت (دقيقة)")]
        public int TotalTimeMinutes { get; set; } = 0;

        [Display(Name = "درجة الحرارة")]
        [StringLength(100)]
        public string? Temperature { get; set; }

        [StringLength(1000)]
        [Display(Name = "تعليمات التحضير")]
        public string? PreparationInstructions { get; set; }

        [StringLength(1000)]
        [Display(Name = "تعليمات الطبخ")]
        public string? CookingInstructions { get; set; }

        [StringLength(500)]
        [Display(Name = "ملاحظات")]
        public string? Notes { get; set; }

        [Display(Name = "نشط")]
        public bool IsActive { get; set; } = true;

        [Display(Name = "تاريخ الإنشاء")]
        public DateTime CreatedAt { get; set; } = DateTime.Now;

        [Display(Name = "تاريخ التحديث")]
        public DateTime? UpdatedAt { get; set; }

        [Display(Name = "منشئ بواسطة")]
        public int CreatedBy { get; set; }

        [Display(Name = "محدث بواسطة")]
        public int? UpdatedBy { get; set; }

        // Navigation properties
        public virtual Item ProductItem { get; set; } = null!;
        public virtual Unit ProductionUnit { get; set; } = null!;
        public virtual User Creator { get; set; } = null!;
        public virtual User? Updater { get; set; }
        public virtual ICollection<RecipeItem> RecipeItems { get; set; } = new List<RecipeItem>();
        public virtual ICollection<ProductionOrder> ProductionOrders { get; set; } = new List<ProductionOrder>();

        /// <summary>
        /// حساب التكاليف تلقائياً
        /// Calculate costs automatically
        /// </summary>
        public void CalculateCosts()
        {
            RawMaterialsCost = RecipeItems.Sum(ri => ri.TotalCost);
            TotalCost = RawMaterialsCost + LaborCost + AdditionalCosts;
            CostPerUnit = BaseProductionQuantity > 0 ? TotalCost / BaseProductionQuantity : 0;
        }

        /// <summary>
        /// حساب إجمالي الوقت
        /// Calculate total time
        /// </summary>
        public void CalculateTotalTime()
        {
            TotalTimeMinutes = PreparationTimeMinutes + CookingTimeMinutes;
        }
    }

    // تم نقل تعريف RecipeItem إلى ملف منفصل لتجنب التضارب
    // RecipeItem definition moved to separate file to avoid conflicts
}
