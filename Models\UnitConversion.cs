using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace ANWBakerySystem.Models
{
    /// <summary>
    /// جدول تحويلات الوحدات - لحفظ تاريخ التحويلات والعمليات
    /// Unit Conversions Table - To save conversion history and operations
    /// </summary>
    public class UnitConversion
    {
        [Key]
        public int ConversionId { get; set; }

        [Required]
        [Display(Name = "الوحدة المصدر")]
        public int FromUnitId { get; set; }

        [Required]
        [Display(Name = "الوحدة الهدف")]
        public int ToUnitId { get; set; }

        [Required]
        [Display(Name = "الكمية المصدر")]
        [Column(TypeName = "decimal(18,6)")]
        public decimal FromQuantity { get; set; }

        [Required]
        [Display(Name = "الكمية الهدف")]
        [Column(TypeName = "decimal(18,6)")]
        public decimal ToQuantity { get; set; }

        [Required]
        [Display(Name = "معامل التحويل")]
        [Column(TypeName = "decimal(18,6)")]
        public decimal ConversionFactor { get; set; }

        [Display(Name = "نوع التحويل")]
        [StringLength(50)]
        public string ConversionType { get; set; } = string.Empty; // Large, Medium, Small

        [Display(Name = "ملاحظات")]
        [StringLength(500)]
        public string? Notes { get; set; }

        [Display(Name = "تاريخ التحويل")]
        public DateTime ConversionDate { get; set; } = DateTime.Now;

        [Display(Name = "محول بواسطة")]
        public int ConvertedBy { get; set; }

        [Display(Name = "رقم المرجع")]
        [StringLength(100)]
        public string? ReferenceNumber { get; set; }

        [Display(Name = "نوع العملية")]
        [StringLength(50)]
        public string? OperationType { get; set; } // Sale, Purchase, Production, Inventory

        // Navigation properties
        [ForeignKey("FromUnitId")]
        public virtual Unit FromUnit { get; set; } = null!;

        [ForeignKey("ToUnitId")]
        public virtual Unit ToUnit { get; set; } = null!;

        public virtual User Converter { get; set; } = null!;
    }
}
