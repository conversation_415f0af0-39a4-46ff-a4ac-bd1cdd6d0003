<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إدارة الملاك - نظام إدارة المخبز</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <link href="css/style.css" rel="stylesheet">
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary">
        <div class="container-fluid">
            <a class="navbar-brand" href="dashboard.html">
                <i class="fas fa-bread-slice me-2"></i>
                نظام إدارة المخبز
            </a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto">
                    <li class="nav-item">
                        <a class="nav-link" href="dashboard.html">
                            <i class="fas fa-home me-1"></i>الرئيسية
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="items-management.html">
                            <i class="fas fa-cubes me-1"></i>الأصناف والمنتجات
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="vouchers.html">
                            <i class="fas fa-receipt me-1"></i>سندات القبض والصرف
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link active" href="owners.html">
                            <i class="fas fa-crown me-1"></i>إدارة الملاك
                        </a>
                    </li>
                </ul>
                <ul class="navbar-nav">
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown">
                            <i class="fas fa-user me-1"></i>المستخدم
                        </a>
                        <ul class="dropdown-menu">
                            <li><a class="dropdown-item" href="#"><i class="fas fa-cog me-2"></i>الإعدادات</a></li>
                            <li><hr class="dropdown-divider"></li>
                            <li><a class="dropdown-item" href="index.html"><i class="fas fa-sign-out-alt me-2"></i>تسجيل الخروج</a></li>
                        </ul>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

    <!-- Main Content -->
    <div class="container-fluid mt-4">
        <div class="row">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <div class="d-flex justify-content-between align-items-center">
                            <h4><i class="fas fa-crown me-2"></i>إدارة الملاك والشركاء</h4>
                            <button class="btn btn-success" onclick="showAddOwnerModal()">
                                <i class="fas fa-plus"></i> إضافة مالك جديد
                            </button>
                        </div>
                    </div>
                    <div class="card-body">
                        <!-- Statistics Cards -->
                        <div class="row mb-4">
                            <div class="col-md-3">
                                <div class="card bg-primary text-white">
                                    <div class="card-body text-center">
                                        <i class="fas fa-crown fa-2x mb-2"></i>
                                        <h5 id="totalOwners">0</h5>
                                        <small>إجمالي الملاك</small>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="card bg-success text-white">
                                    <div class="card-body text-center">
                                        <i class="fas fa-arrow-up fa-2x mb-2"></i>
                                        <h5 id="totalCapital">0 ر.ي</h5>
                                        <small>إجمالي رؤوس الأموال</small>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="card bg-info text-white">
                                    <div class="card-body text-center">
                                        <i class="fas fa-percentage fa-2x mb-2"></i>
                                        <h5 id="totalShares">0%</h5>
                                        <small>إجمالي الحصص</small>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="card bg-warning text-white">
                                    <div class="card-body text-center">
                                        <i class="fas fa-hand-holding-usd fa-2x mb-2"></i>
                                        <h5 id="totalWithdrawals">0 ر.ي</h5>
                                        <small>إجمالي السحوبات</small>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Search and Filter -->
                        <div class="row mb-3">
                            <div class="col-md-4">
                                <input type="text" class="form-control" id="ownersSearch" placeholder="البحث في الملاك..." onkeyup="searchOwners()">
                            </div>
                            <div class="col-md-3">
                                <select class="form-select" id="statusFilter" onchange="filterOwners()">
                                    <option value="">جميع الحالات</option>
                                    <option value="active">نشط</option>
                                    <option value="inactive">غير نشط</option>
                                </select>
                            </div>
                            <div class="col-md-3">
                                <select class="form-select" id="shareFilter" onchange="filterOwners()">
                                    <option value="">جميع الحصص</option>
                                    <option value="major">حصة كبيرة (>30%)</option>
                                    <option value="medium">حصة متوسطة (10-30%)</option>
                                    <option value="minor">حصة صغيرة (<10%)</option>
                                </select>
                            </div>
                            <div class="col-md-2">
                                <button class="btn btn-primary w-100" onclick="loadOwners()">
                                    <i class="fas fa-sync"></i> تحديث
                                </button>
                            </div>
                        </div>

                        <!-- Owners Table -->
                        <div id="ownersContent">
                            <!-- Owners content will be loaded here -->
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Add/Edit Owner Modal -->
    <div class="modal fade" id="ownerModal" tabindex="-1">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="ownerModalTitle">إضافة مالك جديد</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <form id="ownerForm">
                        <input type="hidden" id="ownerId">
                        
                        <!-- Basic Information -->
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="ownerName" class="form-label">اسم المالك *</label>
                                    <input type="text" class="form-control" id="ownerName" required>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="ownerCode" class="form-label">كود المالك</label>
                                    <input type="text" class="form-control" id="ownerCode" readonly>
                                </div>
                            </div>
                        </div>
                        
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="ownerPhone" class="form-label">رقم الهاتف</label>
                                    <input type="tel" class="form-control" id="ownerPhone">
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="ownerEmail" class="form-label">البريد الإلكتروني</label>
                                    <input type="email" class="form-control" id="ownerEmail">
                                </div>
                            </div>
                        </div>
                        
                        <div class="mb-3">
                            <label for="ownerAddress" class="form-label">العنوان</label>
                            <textarea class="form-control" id="ownerAddress" rows="2"></textarea>
                        </div>
                        
                        <!-- Financial Information -->
                        <div class="card mb-3">
                            <div class="card-header">
                                <h6><i class="fas fa-money-bill-wave me-2"></i>المعلومات المالية</h6>
                            </div>
                            <div class="card-body">
                                <div class="row">
                                    <div class="col-md-4">
                                        <div class="mb-3">
                                            <label for="capitalAmount" class="form-label">رأس المال (ر.ي) *</label>
                                            <input type="number" class="form-control" id="capitalAmount" step="0.01" required onchange="calculateSharePercentage()">
                                        </div>
                                    </div>
                                    <div class="col-md-4">
                                        <div class="mb-3">
                                            <label for="sharePercentage" class="form-label">نسبة الحصة (%) *</label>
                                            <input type="number" class="form-control" id="sharePercentage" step="0.01" required onchange="calculateCapitalAmount()">
                                        </div>
                                    </div>
                                    <div class="col-md-4">
                                        <div class="mb-3">
                                            <label for="currentBalance" class="form-label">الرصيد الحالي (ر.ي)</label>
                                            <input type="number" class="form-control" id="currentBalance" step="0.01" value="0">
                                        </div>
                                    </div>
                                </div>
                                
                                <div class="alert alert-info">
                                    <small><i class="fas fa-info-circle me-2"></i>
                                    يمكنك إدخال رأس المال أو نسبة الحصة، وسيتم حساب الآخر تلقائياً بناءً على إجمالي رؤوس الأموال.
                                    </small>
                                </div>
                            </div>
                        </div>
                        
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="joinDate" class="form-label">تاريخ الانضمام</label>
                                    <input type="date" class="form-control" id="joinDate">
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-check mt-4">
                                    <input class="form-check-input" type="checkbox" id="ownerActive" checked>
                                    <label class="form-check-label" for="ownerActive">مالك نشط</label>
                                </div>
                            </div>
                        </div>
                        
                        <div class="mb-3">
                            <label for="ownerNotes" class="form-label">ملاحظات</label>
                            <textarea class="form-control" id="ownerNotes" rows="3"></textarea>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                    <button type="button" class="btn btn-success" onclick="saveOwner()">
                        <i class="fas fa-save me-2"></i>حفظ المالك
                    </button>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="js/auth-simple.js"></script>
    <script src="js/owners.js"></script>
</body>
</html>
