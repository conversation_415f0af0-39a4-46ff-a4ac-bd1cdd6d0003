<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إدارة الصناديق - نظام ANW</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
</head>
<body>
    <div class="container-fluid">
        <div class="row">
            <!-- Sidebar -->
            <div class="col-md-3 bg-dark text-white p-0">
                <div class="sidebar">
                    <div class="p-3 border-bottom">
                        <h5><i class="fas fa-calculator me-2"></i>نظام ANW</h5>
                        <small>إدارة مخبوزات مبسط</small>
                    </div>
                    <nav class="nav flex-column p-3">
                        <a href="items-simple.html" class="nav-link text-white">
                            <i class="fas fa-boxes me-2"></i>المنتجات والخامات
                        </a>
                        <a href="parties-simple.html" class="nav-link text-white">
                            <i class="fas fa-users me-2"></i>العملاء والموردين
                        </a>
                        <a href="employees-simple.html" class="nav-link text-white">
                            <i class="fas fa-user-tie me-2"></i>الموظفين
                        </a>
                        <a href="invoices-simple.html" class="nav-link text-white">
                            <i class="fas fa-file-invoice me-2"></i>الفواتير
                        </a>
                        <a href="inventory-simple.html" class="nav-link text-white">
                            <i class="fas fa-warehouse me-2"></i>المخزون
                        </a>
                        <a href="cash-simple.html" class="nav-link text-white active">
                            <i class="fas fa-cash-register me-2"></i>الصناديق
                        </a>
                        <a href="banks-simple.html" class="nav-link text-white">
                            <i class="fas fa-university me-2"></i>البنوك
                        </a>
                        <a href="owners-simple.html" class="nav-link text-white">
                            <i class="fas fa-crown me-2"></i>الملاك
                        </a>
                        <a href="users-simple.html" class="nav-link text-white">
                            <i class="fas fa-user-cog me-2"></i>المستخدمين
                        </a>
                        <a href="reports-simple.html" class="nav-link text-white">
                            <i class="fas fa-chart-bar me-2"></i>التقارير
                        </a>
                    </nav>
                </div>
            </div>

            <!-- Main Content -->
            <div class="col-md-9">
                <div class="p-4">
                    <!-- Header -->
                    <div class="d-flex justify-content-between align-items-center mb-4">
                        <h2><i class="fas fa-cash-register text-primary me-2"></i>إدارة الصناديق</h2>
                        <div>
                            <button class="btn btn-success" onclick="addCashBox()">
                                <i class="fas fa-plus me-1"></i>إضافة صندوق
                            </button>
                            <button class="btn btn-primary" onclick="addReceiptVoucher()">
                                <i class="fas fa-arrow-down me-1"></i>سند قبض
                            </button>
                            <button class="btn btn-warning" onclick="addPaymentVoucher()">
                                <i class="fas fa-arrow-up me-1"></i>سند صرف
                            </button>
                        </div>
                    </div>

                    <!-- Cash Boxes Summary -->
                    <div class="row mb-4">
                        <div class="col-md-12">
                            <div class="card">
                                <div class="card-header">
                                    <h5><i class="fas fa-cash-register me-2"></i>الصناديق النقدية</h5>
                                </div>
                                <div class="card-body">
                                    <div id="cashBoxesSummary" class="row">
                                        <!-- سيتم ملؤها بـ JavaScript -->
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Transactions Tabs -->
                    <ul class="nav nav-tabs mb-3">
                        <li class="nav-item">
                            <a class="nav-link active" onclick="showTab('vouchers')">سندات القبض والصرف</a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" onclick="showTab('cashboxes')">إدارة الصناديق</a>
                        </li>
                    </ul>

                    <!-- Vouchers Tab -->
                    <div id="vouchersTab" class="tab-content">
                        <!-- Filter -->
                        <div class="row mb-3">
                            <div class="col-md-3">
                                <select id="voucherTypeFilter" class="form-control" onchange="filterVouchers()">
                                    <option value="">جميع السندات</option>
                                    <option value="receipt">سندات القبض</option>
                                    <option value="payment">سندات الصرف</option>
                                </select>
                            </div>
                            <div class="col-md-3">
                                <select id="cashBoxFilter" class="form-control" onchange="filterVouchers()">
                                    <option value="">جميع الصناديق</option>
                                </select>
                            </div>
                            <div class="col-md-6">
                                <input type="text" id="voucherSearchInput" class="form-control" placeholder="البحث في السندات..." onkeyup="searchVouchers()">
                            </div>
                        </div>

                        <!-- Vouchers Table -->
                        <div class="card">
                            <div class="card-body">
                                <div class="table-responsive">
                                    <table class="table table-striped table-hover">
                                        <thead class="table-dark">
                                            <tr>
                                                <th>رقم السند</th>
                                                <th>النوع</th>
                                                <th>التاريخ</th>
                                                <th>الصندوق</th>
                                                <th>نوع الطرف</th>
                                                <th>الطرف</th>
                                                <th>المبلغ</th>
                                                <th>البيان</th>
                                                <th>الإجراءات</th>
                                            </tr>
                                        </thead>
                                        <tbody id="vouchersTableBody">
                                            <!-- سيتم ملؤها بـ JavaScript -->
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Cash Boxes Tab -->
                    <div id="cashboxesTab" class="tab-content" style="display: none;">
                        <div class="card">
                            <div class="card-body">
                                <div class="table-responsive">
                                    <table class="table table-striped table-hover">
                                        <thead class="table-dark">
                                            <tr>
                                                <th>اسم الصندوق</th>
                                                <th>الموقع</th>
                                                <th>المسؤول</th>
                                                <th>الرصيد الحالي</th>
                                                <th>الحالة</th>
                                                <th>الإجراءات</th>
                                            </tr>
                                        </thead>
                                        <tbody id="cashBoxesTableBody">
                                            <!-- سيتم ملؤها بـ JavaScript -->
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Add Cash Box Modal -->
    <div class="modal fade" id="cashBoxModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">إضافة صندوق نقدي</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <form id="cashBoxForm">
                        <div class="mb-3">
                            <label class="form-label">اسم الصندوق *</label>
                            <input type="text" id="cashBoxName" class="form-control" required>
                        </div>
                        <div class="mb-3">
                            <label class="form-label">الموقع</label>
                            <input type="text" id="cashBoxLocation" class="form-control">
                        </div>
                        <div class="mb-3">
                            <label class="form-label">المسؤول</label>
                            <input type="text" id="cashBoxResponsible" class="form-control">
                        </div>
                        <div class="mb-3">
                            <label class="form-label">الرصيد الافتتاحي</label>
                            <input type="number" id="openingBalance" class="form-control" value="0" step="0.01">
                        </div>
                        <div class="form-check mb-3">
                            <input type="checkbox" id="isActive" class="form-check-input" checked>
                            <label class="form-check-label">صندوق نشط</label>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                    <button type="button" class="btn btn-primary" onclick="saveCashBox()">حفظ</button>
                </div>
            </div>
        </div>
    </div>

    <!-- Voucher Modal -->
    <div class="modal fade" id="voucherModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">سند قبض/صرف</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <form id="voucherForm">
                        <div class="row">
                            <div class="col-md-4">
                                <div class="mb-3">
                                    <label class="form-label">نوع السند *</label>
                                    <select id="voucherType" class="form-control" required>
                                        <option value="">اختر النوع</option>
                                        <option value="receipt">سند قبض</option>
                                        <option value="payment">سند صرف</option>
                                    </select>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="mb-3">
                                    <label class="form-label">الصندوق *</label>
                                    <select id="voucherCashBox" class="form-control" required>
                                        <!-- سيتم ملؤها بـ JavaScript -->
                                    </select>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="mb-3">
                                    <label class="form-label">التاريخ *</label>
                                    <input type="date" id="voucherDate" class="form-control" required>
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">نوع الطرف *</label>
                                    <select id="partyType" class="form-control" required onchange="loadPartyOptions()">
                                        <option value="">اختر نوع الطرف</option>
                                        <option value="customer">عميل</option>
                                        <option value="supplier">مورد</option>
                                        <option value="employee">موظف</option>
                                        <option value="owner">مالك</option>
                                        <option value="bank">بنك</option>
                                        <option value="expense">مصروفات</option>
                                        <option value="revenue">إيرادات أخرى</option>
                                        <option value="other">أخرى</option>
                                    </select>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">الطرف المعني *</label>
                                    <select id="voucherParty" class="form-control" required>
                                        <option value="">اختر الطرف</option>
                                    </select>
                                </div>
                            </div>
                        </div>

                        <div id="expenseTypeSection" class="mb-3" style="display: none;">
                            <label class="form-label">نوع المصروف</label>
                            <select id="expenseType" class="form-control">
                                <option value="">اختر نوع المصروف</option>
                                <option value="electricity">كهرباء</option>
                                <option value="water">مياه</option>
                                <option value="rent">إيجار</option>
                                <option value="fuel">وقود</option>
                                <option value="maintenance">صيانة</option>
                                <option value="office_supplies">مستلزمات مكتبية</option>
                                <option value="transportation">مواصلات</option>
                                <option value="communication">اتصالات</option>
                                <option value="insurance">تأمين</option>
                                <option value="taxes">ضرائب ورسوم</option>
                                <option value="advertising">دعاية وإعلان</option>
                                <option value="other_expenses">مصروفات أخرى</option>
                            </select>
                        </div>

                        <div id="revenueTypeSection" class="mb-3" style="display: none;">
                            <label class="form-label">نوع الإيراد</label>
                            <select id="revenueType" class="form-control">
                                <option value="">اختر نوع الإيراد</option>
                                <option value="rent_income">إيراد إيجار</option>
                                <option value="interest_income">إيراد فوائد</option>
                                <option value="commission_income">إيراد عمولات</option>
                                <option value="service_income">إيراد خدمات</option>
                                <option value="investment_income">إيراد استثمارات</option>
                                <option value="other_income">إيرادات أخرى</option>
                            </select>
                        </div>

                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">المبلغ *</label>
                                    <input type="number" id="voucherAmount" class="form-control" required step="0.01">
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">رقم المرجع</label>
                                    <input type="text" id="voucherReference" class="form-control" placeholder="رقم الفاتورة أو المرجع">
                                </div>
                            </div>
                        </div>

                        <div class="mb-3">
                            <label class="form-label">البيان *</label>
                            <textarea id="voucherDescription" class="form-control" rows="3" required></textarea>
                        </div>

                        <div class="mb-3">
                            <label class="form-label">ملاحظات إضافية</label>
                            <textarea id="voucherNotes" class="form-control" rows="2"></textarea>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                    <button type="button" class="btn btn-primary" onclick="saveVoucher()">حفظ</button>
                </div>
            </div>
        </div>
    </div>

    <!-- Success/Error Messages -->
    <div id="messages"></div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // البيانات المحلية
        let cashBoxes = [];
        let vouchers = [];
        let parties = [];
        let employees = [];
        let owners = [];
        let banks = [];
        let nextCashBoxId = 1;
        let nextVoucherId = 1;
        let currentVoucherFilter = '';

        // تحميل البيانات عند بدء الصفحة
        document.addEventListener('DOMContentLoaded', function() {
            loadAllData();
            document.getElementById('voucherDate').value = new Date().toISOString().split('T')[0];
        });

        // تحميل جميع البيانات
        function loadAllData() {
            loadCashBoxes();
            loadVouchers();
            loadParties();
            loadEmployees();
            loadOwners();
            loadBanks();
        }

        // تحميل الأطراف
        function loadParties() {
            const savedParties = localStorage.getItem('anw_parties');
            if (savedParties) {
                parties = JSON.parse(savedParties);
            }
        }

        // تحميل الموظفين
        function loadEmployees() {
            const savedEmployees = localStorage.getItem('anw_employees');
            if (savedEmployees) {
                employees = JSON.parse(savedEmployees);
            }
        }

        // تحميل الملاك
        function loadOwners() {
            const savedOwners = localStorage.getItem('anw_owners');
            if (savedOwners) {
                owners = JSON.parse(savedOwners);
            } else {
                // إنشاء ملاك افتراضيين
                owners = [
                    {id: 1, name: 'أحمد محمد الشريف', share: 60, balance: 0, phone: '777-111111', notes: 'الشريك الأول'},
                    {id: 2, name: 'فاطمة علي السالم', share: 40, balance: 0, phone: '777-222222', notes: 'الشريك الثاني'}
                ];
                localStorage.setItem('anw_owners', JSON.stringify(owners));
            }
        }

        // تحميل البنوك
        function loadBanks() {
            const savedBanks = localStorage.getItem('anw_banks');
            if (savedBanks) {
                banks = JSON.parse(savedBanks);
            }
        }

        // تحميل الصناديق من localStorage
        function loadCashBoxes() {
            try {
                const savedCashBoxes = localStorage.getItem('anw_cashboxes');
                if (savedCashBoxes && savedCashBoxes !== 'null') {
                    cashBoxes = JSON.parse(savedCashBoxes);
                    if (cashBoxes.length > 0) {
                        nextCashBoxId = Math.max(...cashBoxes.map(c => c.id), 0) + 1;
                    }
                } else {
                    // إنشاء صناديق افتراضية
                    cashBoxes = [
                        {id: 1, name: 'الصندوق الرئيسي', location: 'المكتب الرئيسي', responsible: 'أمين الصندوق', balance: 500000, active: true},
                        {id: 2, name: 'صندوق المبيعات', location: 'قسم المبيعات', responsible: 'موظف المبيعات', balance: 150000, active: true}
                    ];
                    nextCashBoxId = 3;
                    saveCashBoxes();
                }
                displayCashBoxesSummary();
                displayCashBoxes();
                loadCashBoxOptions();
            } catch (error) {
                console.error('خطأ في تحميل الصناديق:', error);
                showMessage('خطأ في تحميل البيانات', 'danger');
            }
        }

        // تحميل السندات من localStorage
        function loadVouchers() {
            try {
                const savedVouchers = localStorage.getItem('anw_vouchers');
                if (savedVouchers && savedVouchers !== 'null') {
                    vouchers = JSON.parse(savedVouchers);
                    if (vouchers.length > 0) {
                        nextVoucherId = Math.max(...vouchers.map(v => v.id), 0) + 1;
                    }
                } else {
                    // إنشاء سندات افتراضية
                    vouchers = [
                        {id: 1, number: 'R001', type: 'receipt', date: '2024-01-15', cashBoxId: 1, amount: 50000, description: 'تحصيل من عميل مخبز الأمل', partyType: 'customer', partyId: 1, partyName: 'مخبز الأمل', reference: 'INV001'},
                        {id: 2, number: 'P001', type: 'payment', date: '2024-01-16', cashBoxId: 1, amount: 25000, description: 'دفع راتب شهر يناير', partyType: 'employee', partyId: 1, partyName: 'أحمد محمد علي', reference: 'SAL001'},
                        {id: 3, number: 'P002', type: 'payment', date: '2024-01-17', cashBoxId: 1, amount: 15000, description: 'دفع فاتورة كهرباء', partyType: 'expense', partyId: null, partyName: 'كهرباء', expenseType: 'electricity', reference: 'ELE001'},
                        {id: 4, number: 'R002', type: 'receipt', date: '2024-01-18', cashBoxId: 2, amount: 10000, description: 'إيراد إيجار محل', partyType: 'revenue', partyId: null, partyName: 'إيراد إيجار', revenueType: 'rent_income', reference: 'RENT001'}
                    ];
                    nextVoucherId = 5;
                    saveVouchers();
                }
                displayVouchers();
            } catch (error) {
                console.error('خطأ في تحميل السندات:', error);
                showMessage('خطأ في تحميل البيانات', 'danger');
            }
        }

        // حفظ الصناديق في localStorage
        function saveCashBoxes() {
            localStorage.setItem('anw_cashboxes', JSON.stringify(cashBoxes));
        }

        // حفظ السندات في localStorage
        function saveVouchers() {
            localStorage.setItem('anw_vouchers', JSON.stringify(vouchers));
        }

        // عرض ملخص الصناديق
        function displayCashBoxesSummary() {
            const summaryDiv = document.getElementById('cashBoxesSummary');
            let html = '';

            cashBoxes.forEach(cashBox => {
                const statusClass = cashBox.active ? 'success' : 'secondary';
                html += `
                    <div class="col-md-4 mb-3">
                        <div class="card border-${statusClass}">
                            <div class="card-body text-center">
                                <h5 class="card-title">${cashBox.name}</h5>
                                <h3 class="text-${statusClass}">${cashBox.balance.toLocaleString()} ر.ي</h3>
                                <p class="card-text">
                                    <small class="text-muted">${cashBox.location}</small><br>
                                    <small class="text-muted">المسؤول: ${cashBox.responsible}</small>
                                </p>
                            </div>
                        </div>
                    </div>
                `;
            });

            summaryDiv.innerHTML = html;
        }

        // عرض الصناديق
        function displayCashBoxes() {
            const tbody = document.getElementById('cashBoxesTableBody');

            if (cashBoxes.length === 0) {
                tbody.innerHTML = `
                    <tr>
                        <td colspan="6" class="text-center text-muted">
                            لا توجد صناديق
                            <br>
                            <button class="btn btn-primary mt-2" onclick="addCashBox()">إضافة صندوق جديد</button>
                        </td>
                    </tr>
                `;
                return;
            }

            let html = '';
            cashBoxes.forEach(cashBox => {
                const statusBadge = cashBox.active ?
                    '<span class="badge bg-success">نشط</span>' :
                    '<span class="badge bg-secondary">غير نشط</span>';

                html += `
                    <tr>
                        <td>${cashBox.name}</td>
                        <td>${cashBox.location}</td>
                        <td>${cashBox.responsible}</td>
                        <td class="fw-bold">${cashBox.balance.toLocaleString()} ر.ي</td>
                        <td>${statusBadge}</td>
                        <td>
                            <button class="btn btn-sm btn-warning" onclick="editCashBox(${cashBox.id})">
                                <i class="fas fa-edit"></i>
                            </button>
                            <button class="btn btn-sm btn-danger" onclick="deleteCashBox(${cashBox.id})">
                                <i class="fas fa-trash"></i>
                            </button>
                        </td>
                    </tr>
                `;
            });

            tbody.innerHTML = html;
        }

        // عرض السندات
        function displayVouchers() {
            const tbody = document.getElementById('vouchersTableBody');
            let filteredVouchers = vouchers;

            // تطبيق الفلتر
            if (currentVoucherFilter) {
                filteredVouchers = vouchers.filter(v => v.type === currentVoucherFilter);
            }

            if (filteredVouchers.length === 0) {
                tbody.innerHTML = `
                    <tr>
                        <td colspan="7" class="text-center text-muted">
                            لا توجد سندات
                            <br>
                            <button class="btn btn-primary mt-2" onclick="addReceiptVoucher()">إضافة سند قبض</button>
                            <button class="btn btn-warning mt-2" onclick="addPaymentVoucher()">إضافة سند صرف</button>
                        </td>
                    </tr>
                `;
                return;
            }

            let html = '';
            filteredVouchers.forEach(voucher => {
                const cashBox = cashBoxes.find(c => c.id === voucher.cashBoxId);
                const typeLabel = voucher.type === 'receipt' ? 'قبض' : 'صرف';
                const typeBadge = voucher.type === 'receipt' ? 'bg-success' : 'bg-danger';
                const amountClass = voucher.type === 'receipt' ? 'text-success' : 'text-danger';
                const partyTypeLabel = getPartyTypeLabel(voucher.partyType || 'other');
                const partyName = getPartyName(voucher.partyType, voucher.partyId, voucher.partyName || voucher.party);

                html += `
                    <tr>
                        <td>${voucher.number}</td>
                        <td><span class="badge ${typeBadge}">${typeLabel}</span></td>
                        <td>${new Date(voucher.date).toLocaleDateString('ar-YE')}</td>
                        <td>${cashBox ? cashBox.name : 'غير محدد'}</td>
                        <td><span class="badge bg-info">${partyTypeLabel}</span></td>
                        <td>${partyName}</td>
                        <td class="${amountClass} fw-bold">${voucher.amount.toLocaleString()} ر.ي</td>
                        <td>${voucher.description}</td>
                        <td>
                            <button class="btn btn-sm btn-info" onclick="printVoucher(${voucher.id})">
                                <i class="fas fa-print"></i>
                            </button>
                            <button class="btn btn-sm btn-warning" onclick="viewVoucherDetails(${voucher.id})">
                                <i class="fas fa-eye"></i>
                            </button>
                            <button class="btn btn-sm btn-danger" onclick="deleteVoucher(${voucher.id})">
                                <i class="fas fa-trash"></i>
                            </button>
                        </td>
                    </tr>
                `;
            });

            tbody.innerHTML = html;
        }

        // تحميل خيارات الصناديق
        function loadCashBoxOptions() {
            const select = document.getElementById('voucherCashBox');
            const filterSelect = document.getElementById('cashBoxFilter');

            let html = '<option value="">اختر الصندوق</option>';
            let filterHtml = '<option value="">جميع الصناديق</option>';

            cashBoxes.filter(c => c.active).forEach(cashBox => {
                html += `<option value="${cashBox.id}">${cashBox.name}</option>`;
                filterHtml += `<option value="${cashBox.id}">${cashBox.name}</option>`;
            });

            select.innerHTML = html;
            filterSelect.innerHTML = filterHtml;
        }

        // تحميل خيارات الأطراف حسب النوع
        function loadPartyOptions() {
            const partyType = document.getElementById('partyType').value;
            const partySelect = document.getElementById('voucherParty');
            const expenseSection = document.getElementById('expenseTypeSection');
            const revenueSection = document.getElementById('revenueTypeSection');

            // إخفاء جميع الأقسام أولاً
            expenseSection.style.display = 'none';
            revenueSection.style.display = 'none';

            let html = '<option value="">اختر الطرف</option>';

            switch(partyType) {
                case 'customer':
                    parties.filter(p => p.type === 'customer' || p.type === 'both').forEach(party => {
                        html += `<option value="${party.id}">${party.name}</option>`;
                    });
                    break;

                case 'supplier':
                    parties.filter(p => p.type === 'supplier' || p.type === 'both').forEach(party => {
                        html += `<option value="${party.id}">${party.name}</option>`;
                    });
                    break;

                case 'employee':
                    employees.filter(e => e.active).forEach(employee => {
                        html += `<option value="${employee.id}">${employee.name}</option>`;
                    });
                    break;

                case 'owner':
                    owners.forEach(owner => {
                        html += `<option value="${owner.id}">${owner.name}</option>`;
                    });
                    break;

                case 'bank':
                    banks.filter(b => b.active).forEach(bank => {
                        html += `<option value="${bank.id}">${bank.name} - ${bank.accountNumber}</option>`;
                    });
                    break;

                case 'expense':
                    html = '<option value="expense">مصروفات عامة</option>';
                    expenseSection.style.display = 'block';
                    break;

                case 'revenue':
                    html = '<option value="revenue">إيرادات أخرى</option>';
                    revenueSection.style.display = 'block';
                    break;

                case 'other':
                    html = '<option value="other">أخرى</option>';
                    break;
            }

            partySelect.innerHTML = html;
        }

        // الحصول على اسم الطرف
        function getPartyName(partyType, partyId, partyName) {
            if (partyName) return partyName;

            switch(partyType) {
                case 'customer':
                case 'supplier':
                    const party = parties.find(p => p.id == partyId);
                    return party ? party.name : 'غير محدد';

                case 'employee':
                    const employee = employees.find(e => e.id == partyId);
                    return employee ? employee.name : 'غير محدد';

                case 'owner':
                    const owner = owners.find(o => o.id == partyId);
                    return owner ? owner.name : 'غير محدد';

                case 'bank':
                    const bank = banks.find(b => b.id == partyId);
                    return bank ? `${bank.name} - ${bank.accountNumber}` : 'غير محدد';

                case 'expense':
                    return 'مصروفات';

                case 'revenue':
                    return 'إيرادات أخرى';

                default:
                    return 'غير محدد';
            }
        }

        // الحصول على تسمية نوع الطرف
        function getPartyTypeLabel(partyType) {
            const types = {
                'customer': 'عميل',
                'supplier': 'مورد',
                'employee': 'موظف',
                'owner': 'مالك',
                'bank': 'بنك',
                'expense': 'مصروفات',
                'revenue': 'إيرادات',
                'other': 'أخرى'
            };
            return types[partyType] || partyType;
        }

        // إضافة صندوق جديد
        function addCashBox() {
            clearCashBoxForm();
            document.querySelector('#cashBoxModal .modal-title').textContent = 'إضافة صندوق نقدي';
            new bootstrap.Modal(document.getElementById('cashBoxModal')).show();
        }

        // إضافة سند قبض
        function addReceiptVoucher() {
            clearVoucherForm();
            document.getElementById('voucherType').value = 'receipt';
            document.querySelector('#voucherModal .modal-title').textContent = 'سند قبض';
            new bootstrap.Modal(document.getElementById('voucherModal')).show();
        }

        // إضافة سند صرف
        function addPaymentVoucher() {
            clearVoucherForm();
            document.getElementById('voucherType').value = 'payment';
            document.querySelector('#voucherModal .modal-title').textContent = 'سند صرف';
            new bootstrap.Modal(document.getElementById('voucherModal')).show();
        }

        // حفظ الصندوق
        function saveCashBox() {
            const form = document.getElementById('cashBoxForm');
            const editId = form.dataset.editId;

            const cashBoxData = {
                name: document.getElementById('cashBoxName').value,
                location: document.getElementById('cashBoxLocation').value,
                responsible: document.getElementById('cashBoxResponsible').value,
                balance: parseFloat(document.getElementById('openingBalance').value) || 0,
                active: document.getElementById('isActive').checked
            };

            if (!cashBoxData.name) {
                showMessage('يرجى إدخال اسم الصندوق', 'danger');
                return;
            }

            if (editId) {
                const cashBoxIndex = cashBoxes.findIndex(c => c.id == editId);
                cashBoxes[cashBoxIndex] = { ...cashBoxes[cashBoxIndex], ...cashBoxData };
                showMessage('تم تحديث الصندوق بنجاح', 'success');
            } else {
                cashBoxData.id = nextCashBoxId++;
                cashBoxes.push(cashBoxData);
                showMessage('تم إضافة الصندوق بنجاح', 'success');
            }

            saveCashBoxes();
            displayCashBoxesSummary();
            displayCashBoxes();
            loadCashBoxOptions();
            bootstrap.Modal.getInstance(document.getElementById('cashBoxModal')).hide();
        }

        // حفظ السند
        function saveVoucher() {
            const voucherData = {
                type: document.getElementById('voucherType').value,
                cashBoxId: parseInt(document.getElementById('voucherCashBox').value),
                amount: parseFloat(document.getElementById('voucherAmount').value),
                date: document.getElementById('voucherDate').value,
                description: document.getElementById('voucherDescription').value,
                partyType: document.getElementById('partyType').value,
                partyId: document.getElementById('voucherParty').value ? parseInt(document.getElementById('voucherParty').value) : null,
                reference: document.getElementById('voucherReference').value,
                notes: document.getElementById('voucherNotes').value
            };

            if (!voucherData.type || !voucherData.cashBoxId || !voucherData.amount || !voucherData.description || !voucherData.partyType) {
                showMessage('يرجى ملء جميع الحقول المطلوبة', 'danger');
                return;
            }

            // إضافة معلومات إضافية حسب نوع الطرف
            if (voucherData.partyType === 'expense') {
                voucherData.expenseType = document.getElementById('expenseType').value;
                voucherData.partyName = getExpenseTypeLabel(voucherData.expenseType);
            } else if (voucherData.partyType === 'revenue') {
                voucherData.revenueType = document.getElementById('revenueType').value;
                voucherData.partyName = getRevenueTypeLabel(voucherData.revenueType);
            } else {
                voucherData.partyName = getPartyName(voucherData.partyType, voucherData.partyId);
            }

            // إنشاء رقم السند
            const prefix = voucherData.type === 'receipt' ? 'R' : 'P';
            const count = vouchers.filter(v => v.type === voucherData.type).length + 1;
            voucherData.number = `${prefix}${count.toString().padStart(3, '0')}`;
            voucherData.id = nextVoucherId++;

            // تحديث رصيد الصندوق
            const cashBox = cashBoxes.find(c => c.id === voucherData.cashBoxId);
            if (cashBox) {
                if (voucherData.type === 'receipt') {
                    cashBox.balance += voucherData.amount;
                } else {
                    cashBox.balance -= voucherData.amount;
                }
            }

            // تحديث رصيد الطرف المعني
            updatePartyBalance(voucherData);

            vouchers.push(voucherData);
            saveVouchers();
            saveCashBoxes();
            saveAllParties();
            displayVouchers();
            displayCashBoxesSummary();
            bootstrap.Modal.getInstance(document.getElementById('voucherModal')).hide();
            showMessage('تم حفظ السند بنجاح', 'success');
        }

        // تحديث رصيد الطرف
        function updatePartyBalance(voucher) {
            switch(voucher.partyType) {
                case 'customer':
                case 'supplier':
                    const party = parties.find(p => p.id === voucher.partyId);
                    if (party) {
                        if (voucher.type === 'receipt') {
                            party.balance -= voucher.amount; // تقليل دين العميل أو زيادة دين المورد
                        } else {
                            party.balance += voucher.amount; // زيادة دين العميل أو تقليل دين المورد
                        }
                    }
                    break;

                case 'employee':
                    const employee = employees.find(e => e.id === voucher.partyId);
                    if (employee) {
                        if (!employee.balance) employee.balance = 0;
                        if (voucher.type === 'receipt') {
                            employee.balance += voucher.amount; // زيادة مستحقات الموظف
                        } else {
                            employee.balance -= voucher.amount; // تقليل مستحقات الموظف (دفع راتب)
                        }
                    }
                    break;

                case 'owner':
                    const owner = owners.find(o => o.id === voucher.partyId);
                    if (owner) {
                        if (!owner.balance) owner.balance = 0;
                        if (voucher.type === 'receipt') {
                            owner.balance += voucher.amount; // زيادة رصيد المالك
                        } else {
                            owner.balance -= voucher.amount; // تقليل رصيد المالك (سحوبات)
                        }
                    }
                    break;

                case 'bank':
                    const bank = banks.find(b => b.id === voucher.partyId);
                    if (bank) {
                        if (voucher.type === 'receipt') {
                            bank.balance -= voucher.amount; // سحب من البنك للصندوق
                        } else {
                            bank.balance += voucher.amount; // إيداع من الصندوق للبنك
                        }
                    }
                    break;
            }
        }

        // حفظ جميع الأطراف
        function saveAllParties() {
            localStorage.setItem('anw_parties', JSON.stringify(parties));
            localStorage.setItem('anw_employees', JSON.stringify(employees));
            localStorage.setItem('anw_owners', JSON.stringify(owners));
            localStorage.setItem('anw_banks', JSON.stringify(banks));
        }

        // الحصول على تسمية نوع المصروف
        function getExpenseTypeLabel(expenseType) {
            const types = {
                'electricity': 'كهرباء',
                'water': 'مياه',
                'rent': 'إيجار',
                'fuel': 'وقود',
                'maintenance': 'صيانة',
                'office_supplies': 'مستلزمات مكتبية',
                'transportation': 'مواصلات',
                'communication': 'اتصالات',
                'insurance': 'تأمين',
                'taxes': 'ضرائب ورسوم',
                'advertising': 'دعاية وإعلان',
                'other_expenses': 'مصروفات أخرى'
            };
            return types[expenseType] || 'مصروفات';
        }

        // الحصول على تسمية نوع الإيراد
        function getRevenueTypeLabel(revenueType) {
            const types = {
                'rent_income': 'إيراد إيجار',
                'interest_income': 'إيراد فوائد',
                'commission_income': 'إيراد عمولات',
                'service_income': 'إيراد خدمات',
                'investment_income': 'إيراد استثمارات',
                'other_income': 'إيرادات أخرى'
            };
            return types[revenueType] || 'إيرادات أخرى';
        }

        // حذف سند
        function deleteVoucher(voucherId) {
            if (confirm('هل أنت متأكد من حذف هذا السند؟')) {
                const voucher = vouchers.find(v => v.id === voucherId);
                if (voucher) {
                    // إعادة تعديل رصيد الصندوق
                    const cashBox = cashBoxes.find(c => c.id === voucher.cashBoxId);
                    if (cashBox) {
                        if (voucher.type === 'receipt') {
                            cashBox.balance -= voucher.amount;
                        } else {
                            cashBox.balance += voucher.amount;
                        }
                    }
                }

                vouchers = vouchers.filter(v => v.id !== voucherId);
                saveVouchers();
                saveCashBoxes();
                displayVouchers();
                displayCashBoxesSummary();
                showMessage('تم حذف السند بنجاح', 'success');
            }
        }

        // طباعة سند
        function printVoucher(voucherId) {
            const voucher = vouchers.find(v => v.id === voucherId);
            const cashBox = cashBoxes.find(c => c.id === voucher.cashBoxId);

            if (voucher) {
                const typeLabel = voucher.type === 'receipt' ? 'سند قبض' : 'سند صرف';
                const printContent = `
                    <div style="text-align: center; font-family: Arial;">
                        <h2>${typeLabel}</h2>
                        <p>رقم السند: ${voucher.number}</p>
                        <p>التاريخ: ${new Date(voucher.date).toLocaleDateString('ar-YE')}</p>
                        <p>الصندوق: ${cashBox ? cashBox.name : 'غير محدد'}</p>
                        <p>المبلغ: ${voucher.amount.toLocaleString()} ر.ي</p>
                        <p>البيان: ${voucher.description}</p>
                        ${voucher.party ? `<p>الطرف: ${voucher.party}</p>` : ''}
                        <br><br>
                        <p>التوقيع: ________________</p>
                    </div>
                `;

                const printWindow = window.open('', '_blank');
                printWindow.document.write(printContent);
                printWindow.document.close();
                printWindow.print();
            }
        }

        // فلترة السندات
        function filterVouchers() {
            currentVoucherFilter = document.getElementById('voucherTypeFilter').value;
            displayVouchers();
        }

        // البحث في السندات
        function searchVouchers() {
            const searchTerm = document.getElementById('voucherSearchInput').value.toLowerCase();
            const tbody = document.getElementById('vouchersTableBody');

            let filteredVouchers = vouchers.filter(voucher => {
                return voucher.number.toLowerCase().includes(searchTerm) ||
                       voucher.description.toLowerCase().includes(searchTerm) ||
                       (voucher.party && voucher.party.toLowerCase().includes(searchTerm));
            });

            if (currentVoucherFilter) {
                filteredVouchers = filteredVouchers.filter(v => v.type === currentVoucherFilter);
            }

            // عرض النتائج (نفس كود displayVouchers لكن مع البيانات المفلترة)
            if (filteredVouchers.length === 0) {
                tbody.innerHTML = `<tr><td colspan="7" class="text-center text-muted">لا توجد نتائج للبحث</td></tr>`;
                return;
            }

            let html = '';
            filteredVouchers.forEach(voucher => {
                const cashBox = cashBoxes.find(c => c.id === voucher.cashBoxId);
                const typeLabel = voucher.type === 'receipt' ? 'قبض' : 'صرف';
                const typeBadge = voucher.type === 'receipt' ? 'bg-success' : 'bg-danger';
                const amountClass = voucher.type === 'receipt' ? 'text-success' : 'text-danger';

                html += `
                    <tr>
                        <td>${voucher.number}</td>
                        <td><span class="badge ${typeBadge}">${typeLabel}</span></td>
                        <td>${new Date(voucher.date).toLocaleDateString('ar-YE')}</td>
                        <td>${cashBox ? cashBox.name : 'غير محدد'}</td>
                        <td class="${amountClass} fw-bold">${voucher.amount.toLocaleString()} ر.ي</td>
                        <td>${voucher.description}</td>
                        <td>
                            <button class="btn btn-sm btn-info" onclick="printVoucher(${voucher.id})">
                                <i class="fas fa-print"></i>
                            </button>
                            <button class="btn btn-sm btn-danger" onclick="deleteVoucher(${voucher.id})">
                                <i class="fas fa-trash"></i>
                            </button>
                        </td>
                    </tr>
                `;
            });

            tbody.innerHTML = html;
        }

        // تبديل التبويبات
        function showTab(tabName) {
            // إخفاء جميع التبويبات
            document.getElementById('vouchersTab').style.display = 'none';
            document.getElementById('cashboxesTab').style.display = 'none';

            // إزالة active من جميع الروابط
            document.querySelectorAll('.nav-link').forEach(link => {
                link.classList.remove('active');
            });

            // إظهار التبويب المحدد
            if (tabName === 'vouchers') {
                document.getElementById('vouchersTab').style.display = 'block';
            } else if (tabName === 'cashboxes') {
                document.getElementById('cashboxesTab').style.display = 'block';
            }

            // إضافة active للرابط المحدد
            event.target.classList.add('active');
        }

        // تنظيف النماذج
        function clearCashBoxForm() {
            document.getElementById('cashBoxForm').reset();
            document.getElementById('cashBoxForm').removeAttribute('data-edit-id');
            document.getElementById('openingBalance').value = '0';
            document.getElementById('isActive').checked = true;
        }

        function clearVoucherForm() {
            document.getElementById('voucherForm').reset();
            document.getElementById('voucherDate').value = new Date().toISOString().split('T')[0];
            document.getElementById('expenseTypeSection').style.display = 'none';
            document.getElementById('revenueTypeSection').style.display = 'none';
            document.getElementById('voucherParty').innerHTML = '<option value="">اختر الطرف</option>';
        }

        // عرض رسالة
        function showMessage(message, type) {
            const messagesDiv = document.getElementById('messages');
            const alertHtml = `
                <div class="alert alert-${type} alert-dismissible fade show position-fixed"
                     style="top: 20px; left: 20px; z-index: 9999; min-width: 300px;">
                    ${message}
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
            `;
            messagesDiv.innerHTML = alertHtml;

            setTimeout(() => {
                const alert = messagesDiv.querySelector('.alert');
                if (alert) {
                    bootstrap.Alert.getOrCreateInstance(alert).close();
                }
            }, 3000);
        }

        console.log('✅ تم تحميل نظام إدارة الصناديق بنجاح');
    </script>

    <style>
        .sidebar {
            height: 100vh;
            position: fixed;
            width: 25%;
        }
        .nav-link.active {
            background-color: rgba(255,255,255,0.1);
            border-radius: 5px;
        }
        .table th {
            background-color: #343a40;
            color: white;
        }
        .tab-content {
            animation: fadeIn 0.3s;
        }
        @keyframes fadeIn {
            from { opacity: 0; }
            to { opacity: 1; }
        }
    </style>
</body>
</html>