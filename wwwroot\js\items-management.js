// إدارة الأصناف والمنتجات الموحدة - متصل بـ APIs
const API_BASE_URL = '/api';

// متغيرات البيانات
let materials = [];
let products = [];
let services = [];
let productionOperations = [];
let units = [];
let categories = [];

// تحميل الصفحة
document.addEventListener('DOMContentLoaded', function() {
    console.log('🔄 تحميل صفحة إدارة الأصناف والمنتجات...');
    initializePage();
});

// تهيئة الصفحة
async function initializePage() {
    try {
        // تحميل البيانات الأساسية
        await loadUnits();
        await loadCategories();
        
        // تحميل محتوى التبويب النشط
        await loadMaterials();
        
        // إعداد أحداث التبويبات
        setupTabEvents();
        
        console.log('✅ تم تحميل صفحة الأصناف والمنتجات بنجاح');
    } catch (error) {
        console.error('❌ خطأ في تحميل صفحة الأصناف والمنتجات:', error);
        showError('خطأ في تحميل البيانات. يرجى إعادة تحميل الصفحة.');
    }
}

// إعداد أحداث التبويبات
function setupTabEvents() {
    const tabButtons = document.querySelectorAll('#itemsTabs button[data-bs-toggle="tab"]');
    
    tabButtons.forEach(button => {
        button.addEventListener('shown.bs.tab', function(event) {
            const targetTab = event.target.getAttribute('data-bs-target');
            
            switch(targetTab) {
                case '#materials':
                    loadMaterials();
                    break;
                case '#products':
                    loadProducts();
                    break;
                case '#services':
                    loadServices();
                    break;
                case '#production':
                    loadProductionOperations();
                    break;
            }
        });
    });
}

// ========== تحميل البيانات الأساسية ==========

// تحميل وحدات القياس
async function loadUnits() {
    console.log('🔄 تحميل وحدات القياس...');
    
    try {
        const response = await fetch(`${API_BASE_URL}/units`);
        if (response.ok) {
            const result = await response.json();
            if (result.success) {
                units = result.data || [];
                console.log('✅ تم تحميل وحدات القياس:', units.length);
                return;
            }
        }
        
        // في حالة فشل API، عرض قائمة فارغة
        console.error('❌ فشل تحميل وحدات القياس من قاعدة البيانات');
        units = [];

    } catch (error) {
        console.error('❌ خطأ في تحميل وحدات القياس:', error);
        units = [];
    }
}

// تحميل الفئات
async function loadCategories() {
    console.log('🔄 تحميل الفئات...');
    
    try {
        const response = await fetch(`${API_BASE_URL}/items/categories`);
        if (response.ok) {
            const result = await response.json();
            if (result.success) {
                categories = result.data || [];
                console.log('✅ تم تحميل الفئات:', categories.length);
                return;
            }
        }
        
        // في حالة فشل API، عرض قائمة فارغة
        console.error('❌ فشل تحميل الفئات من قاعدة البيانات');
        categories = [];

    } catch (error) {
        console.error('❌ خطأ في تحميل الفئات:', error);
        categories = [];
    }
}

// ========== إدارة الخامات ==========

// تحميل الخامات
async function loadMaterials() {
    console.log('🔄 تحميل الخامات...');
    const content = document.getElementById('materialsContent');
    
    showTabLoading(content);
    
    try {
        const response = await fetch(`${API_BASE_URL}/items/materials`);
        if (response.ok) {
            const result = await response.json();
            if (result.success) {
                materials = result.data || [];
                displayMaterials();
                console.log('✅ تم تحميل الخامات:', materials.length);
                return;
            }
        }
        
        // في حالة فشل API، عرض قائمة فارغة
        console.error('❌ فشل تحميل الخامات من قاعدة البيانات');
        materials = [];
        displayMaterials();

    } catch (error) {
        console.error('❌ خطأ في تحميل الخامات:', error);
        materials = [];
        displayMaterials();
    }
}

// عرض الخامات
function displayMaterials() {
    const content = document.getElementById('materialsContent');
    
    if (materials.length === 0) {
        content.innerHTML = `
            <div class="text-center p-4">
                <i class="fas fa-cubes fa-3x text-muted mb-3"></i>
                <h5>لا توجد خامات</h5>
                <p class="text-muted">ابدأ بإضافة خامة جديدة</p>
            </div>
        `;
        return;
    }
    
    let html = `
        <div class="row mb-3">
            <div class="col-md-6">
                <input type="text" class="form-control" id="materialsSearch" placeholder="البحث في الخامات..." onkeyup="searchMaterials()">
            </div>
            <div class="col-md-3">
                <select class="form-select" id="materialsFilter" onchange="filterMaterials()">
                    <option value="">جميع الفئات</option>
                    ${categories.map(cat => `<option value="${cat.id}">${cat.name}</option>`).join('')}
                </select>
            </div>
            <div class="col-md-3">
                <button class="btn btn-primary w-100" onclick="loadMaterials()">
                    <i class="fas fa-sync"></i> تحديث
                </button>
            </div>
        </div>
        
        <div class="table-responsive">
            <table class="table table-striped table-hover">
                <thead class="table-dark">
                    <tr>
                        <th>الكود</th>
                        <th>اسم الخامة</th>
                        <th>نظام الوحدات</th>
                        <th>أسعار الشراء</th>
                        <th>المخزون الحالي</th>
                        <th>الحد الأدنى</th>
                        <th>الحالة</th>
                        <th>الإجراءات</th>
                    </tr>
                </thead>
                <tbody>
    `;
    
    materials.forEach(material => {
        const statusBadge = material.isActive ?
            '<span class="badge bg-success">نشط</span>' :
            '<span class="badge bg-secondary">غير نشط</span>';

        const stockBadge = material.currentStock <= material.minStock ?
            '<span class="badge bg-danger">منخفض</span>' :
            '<span class="badge bg-success">متوفر</span>';

        // نظام الوحدات الهرمي الصحيح
        const unit = getUnitById(material.unitId);

        const unitsSystem = `
            <div class="units-hierarchy">
                <div class="unit-level">
                    <span class="badge bg-primary">${unit?.majorUnit?.name || 'غير محدد'}</span>
                    <small class="text-muted">1</small>
                </div>
                <div class="unit-level mt-1">
                    <span class="badge bg-info">${unit?.mediumUnit?.name || 'غير محدد'}</span>
                    <small class="text-muted">${unit?.mediumUnit?.factor || 0}</small>
                </div>
                <div class="unit-level mt-1">
                    <span class="badge bg-secondary">${unit?.minorUnit?.name || 'غير محدد'}</span>
                    <small class="text-muted">${unit?.minorUnit?.factor || 0}</small>
                </div>
            </div>
        `;

        // أسعار الشراء للوحدات المختلفة
        const prices = `
            <div class="prices-hierarchy">
                <div class="price-level">
                    <strong>${formatCurrency(material.purchasePrice)}</strong>
                    <small class="text-muted">/${unit?.majorUnit?.name || 'وحدة'}</small>
                </div>
                <div class="price-level">
                    <strong>${formatCurrency(material.purchasePriceMedium)}</strong>
                    <small class="text-muted">/${unit?.mediumUnit?.name || 'وحدة'}</small>
                </div>
                <div class="price-level">
                    <strong>${formatCurrency(material.purchasePriceMinor)}</strong>
                    <small class="text-muted">/${unit?.minorUnit?.name || 'وحدة'}</small>
                </div>
            </div>
        `;

        // المخزون الحالي بالوحدات المختلفة
        const currentStock = `
            <div class="stock-hierarchy">
                <div class="stock-level">
                    <strong>${formatQuantity(material.currentStock)}</strong>
                    <small class="text-muted">${unit?.majorUnit?.name || 'وحدة'}</small>
                </div>
                <div class="stock-level">
                    <strong>${formatQuantity(material.currentStockMedium)}</strong>
                    <small class="text-muted">${unit?.mediumUnit?.name || 'وحدة'}</small>
                </div>
                <div class="stock-level">
                    <strong>${formatQuantity(material.currentStockMinor)}</strong>
                    <small class="text-muted">${unit?.minorUnit?.name || 'وحدة'}</small>
                </div>
            </div>
            ${stockBadge}
        `;

        // الحد الأدنى
        const minStock = `
            <div class="min-stock">
                <strong>${formatQuantity(material.minStock)}</strong>
                <small class="text-muted">${unit?.majorUnit?.name || 'وحدة'}</small>
            </div>
        `;

        html += `
            <tr>
                <td><strong>${material.itemCode || '-'}</strong></td>
                <td>
                    <strong>${material.itemName}</strong>
                    <br><small class="text-muted">ID: ${material.itemId}</small>
                </td>
                <td>${unitsSystem}</td>
                <td>${prices}</td>
                <td>${currentStock}</td>
                <td>${minStock}</td>
                <td>${statusBadge}</td>
                <td>
                    <div class="btn-group btn-group-sm">
                        <button class="btn btn-outline-primary" onclick="editMaterial(${material.itemId})" title="تعديل">
                            <i class="fas fa-edit"></i>
                        </button>
                        <button class="btn btn-outline-success" onclick="adjustMaterialStock(${material.itemId})" title="تعديل المخزون">
                            <i class="fas fa-warehouse"></i>
                        </button>
                        <button class="btn btn-outline-info" onclick="viewMaterialDetails(${material.itemId})" title="التفاصيل">
                            <i class="fas fa-eye"></i>
                        </button>
                        <button class="btn btn-outline-danger" onclick="deleteMaterial(${material.itemId})" title="حذف">
                            <i class="fas fa-trash"></i>
                        </button>
                    </div>
                </td>
            </tr>
        `;
    });
    
    html += `
                </tbody>
            </table>
        </div>
    `;
    
    content.innerHTML = html;
}

// ========== إدارة المنتجات ==========

// تحميل المنتجات
async function loadProducts() {
    console.log('🔄 تحميل المنتجات...');
    const content = document.getElementById('productsContent');
    
    showTabLoading(content);
    
    try {
        const response = await fetch(`${API_BASE_URL}/items/products`);
        if (response.ok) {
            const result = await response.json();
            if (result.success) {
                products = result.data || [];
                displayProducts();
                console.log('✅ تم تحميل المنتجات:', products.length);
                return;
            }
        }
        
        // في حالة فشل API، عرض قائمة فارغة
        console.error('❌ فشل تحميل المنتجات من قاعدة البيانات');
        products = [];
        displayProducts();

    } catch (error) {
        console.error('❌ خطأ في تحميل المنتجات:', error);
        products = [];
        displayProducts();
    }
}

// عرض المنتجات
function displayProducts() {
    const content = document.getElementById('productsContent');
    
    if (products.length === 0) {
        content.innerHTML = `
            <div class="text-center p-4">
                <i class="fas fa-box-open fa-3x text-muted mb-3"></i>
                <h5>لا توجد منتجات</h5>
                <p class="text-muted">ابدأ بإضافة منتج جديد</p>
            </div>
        `;
        return;
    }
    
    let html = `
        <div class="row mb-3">
            <div class="col-md-6">
                <input type="text" class="form-control" id="productsSearch" placeholder="البحث في المنتجات..." onkeyup="searchProducts()">
            </div>
            <div class="col-md-3">
                <select class="form-select" id="productsFilter" onchange="filterProducts()">
                    <option value="">جميع الفئات</option>
                    ${categories.map(cat => `<option value="${cat.id}">${cat.name}</option>`).join('')}
                </select>
            </div>
            <div class="col-md-3">
                <button class="btn btn-primary w-100" onclick="loadProducts()">
                    <i class="fas fa-sync"></i> تحديث
                </button>
            </div>
        </div>
        
        <div class="table-responsive">
            <table class="table table-striped table-hover">
                <thead class="table-dark">
                    <tr>
                        <th>الكود</th>
                        <th>اسم المنتج</th>
                        <th>الوحدة</th>
                        <th>التكلفة</th>
                        <th>سعر البيع</th>
                        <th>هامش الربح</th>
                        <th>المخزون</th>
                        <th>الوصفة</th>
                        <th>الإجراءات</th>
                    </tr>
                </thead>
                <tbody>
    `;
    
    products.forEach(product => {
        const hasRecipe = product.hasRecipe ?
            '<span class="badge bg-success">متوفرة</span>' :
            '<span class="badge bg-warning">غير متوفرة</span>';

        // عرض التكلفة والسعر (من الوصفة أو صفر)
        const costDisplay = product.costPrice > 0 ? formatCurrency(product.costPrice) : '<span class="text-muted">يحدد من الوصفة</span>';
        const priceDisplay = product.salePrice > 0 ? formatCurrency(product.salePrice) : '<span class="text-muted">يحدد من الوصفة</span>';

        html += `
            <tr>
                <td><strong>${product.itemCode || '-'}</strong></td>
                <td>${product.itemName}</td>
                <td><span class="badge bg-info">قطعة</span></td>
                <td>${costDisplay}</td>
                <td>${priceDisplay}</td>
                <td>-</td>
                <td>${formatQuantity(product.currentStock || 0)}</td>
                <td>${hasRecipe}</td>
                <td>
                    <div class="btn-group btn-group-sm">
                        <button class="btn btn-outline-primary" onclick="editProduct(${product.itemId})" title="تعديل">
                            <i class="fas fa-edit"></i>
                        </button>
                        <button class="btn btn-outline-success" onclick="manageRecipe(${product.itemId})" title="إدارة الوصفة">
                            <i class="fas fa-utensils"></i>
                        </button>
                        <button class="btn btn-outline-info" onclick="viewProductDetails(${product.itemId})" title="التفاصيل">
                            <i class="fas fa-eye"></i>
                        </button>
                        <button class="btn btn-outline-danger" onclick="deleteProduct(${product.itemId})" title="حذف">
                            <i class="fas fa-trash"></i>
                        </button>
                    </div>
                </td>
            </tr>
        `;
    });
    
    html += `
                </tbody>
            </table>
        </div>
    `;
    
    content.innerHTML = html;
}

// ========== وظائف المساعدة ==========

// الحصول على وحدة القياس بالمعرف
function getUnitById(unitId) {
    return units.find(unit => unit.unitId === unitId);
}

// الحصول على اسم الوحدة بالمعرف
function getUnitNameById(unitId) {
    const unit = getUnitById(unitId);
    return unit ? unit.unitName : 'غير محدد';
}

// عرض تحميل التبويب
function showTabLoading(container) {
    container.innerHTML = `
        <div class="text-center p-4">
            <div class="spinner-border text-primary" role="status">
                <span class="visually-hidden">جاري التحميل...</span>
            </div>
            <p class="mt-2">جاري التحميل...</p>
        </div>
    `;
}

// تنسيق العملة
function formatCurrency(amount) {
    return new Intl.NumberFormat('ar-YE', {
        style: 'currency',
        currency: 'YER',
        minimumFractionDigits: 0,
        maximumFractionDigits: 0
    }).format(amount).replace('YER', 'ر.ي');
}

// تنسيق الكمية
function formatQuantity(quantity) {
    return new Intl.NumberFormat('ar-YE', {
        minimumFractionDigits: 0,
        maximumFractionDigits: 3
    }).format(quantity);
}

// عرض رسالة خطأ
function showError(message) {
    const alertDiv = document.createElement('div');
    alertDiv.className = 'alert alert-danger alert-dismissible fade show position-fixed';
    alertDiv.style.cssText = 'top: 20px; right: 20px; z-index: 9999; min-width: 300px;';
    alertDiv.innerHTML = `
        <i class="fas fa-exclamation-triangle me-2"></i>
        ${message}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    `;
    document.body.appendChild(alertDiv);

    setTimeout(() => {
        if (alertDiv.parentNode) {
            alertDiv.remove();
        }
    }, 5000);
}

// عرض رسالة نجاح
function showSuccess(message) {
    const alertDiv = document.createElement('div');
    alertDiv.className = 'alert alert-success alert-dismissible fade show position-fixed';
    alertDiv.style.cssText = 'top: 20px; right: 20px; z-index: 9999; min-width: 300px;';
    alertDiv.innerHTML = `
        <i class="fas fa-check-circle me-2"></i>
        ${message}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    `;
    document.body.appendChild(alertDiv);

    setTimeout(() => {
        if (alertDiv.parentNode) {
            alertDiv.remove();
        }
    }, 3000);
}

// إظهار تحميل الحفظ
function showSaveLoading() {
    const saveBtn = document.querySelector('button[onclick="saveMaterial()"]');
    if (saveBtn) {
        saveBtn.disabled = true;
        saveBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>جاري الحفظ...';
    }
}

// إخفاء تحميل الحفظ
function hideSaveLoading() {
    const saveBtn = document.querySelector('button[onclick="saveMaterial()"]');
    if (saveBtn) {
        saveBtn.disabled = false;
        saveBtn.innerHTML = '<i class="fas fa-save me-2"></i>حفظ الخامة';
    }
}





// ========== وظائف الإجراءات ==========

// إضافة خامة جديدة
function showAddMaterialModal() {
    // إعادة تعيين النموذج
    document.getElementById('materialForm').reset();
    document.getElementById('materialId').value = '';
    document.getElementById('materialModalTitle').textContent = 'إضافة خامة جديدة';

    // إنشاء كود تلقائي
    document.getElementById('materialCode').value = generateMaterialCode();

    // تعيين قيم افتراضية
    document.getElementById('currentStock').value = 0;
    document.getElementById('minStock').value = 0;
    document.getElementById('materialActive').checked = true;

    // ملء القوائم
    populateCategoriesDropdown();
    populateUnitsDropdown();

    // إظهار النافذة
    const modal = new bootstrap.Modal(document.getElementById('materialModal'));
    modal.show();
}

// إنشاء كود تلقائي للخامة
function generateMaterialCode() {
    const prefix = 'MAT';
    const maxId = Math.max(...materials.map(m => {
        const match = m.itemCode?.match(/MAT(\d+)/);
        return match ? parseInt(match[1]) : 0;
    }), 0);

    const newNumber = (maxId + 1).toString().padStart(3, '0');
    return `${prefix}${newNumber}`;
}

// ملء قائمة الفئات
function populateCategoriesDropdown() {
    const select = document.getElementById('categorySelect');
    select.innerHTML = '<option value="">-- اختر الفئة --</option>';

    categories.forEach(category => {
        const option = document.createElement('option');
        option.value = category.id;
        option.textContent = category.name;
        select.appendChild(option);
    });
}

// ملء قائمة وحدات القياس
function populateUnitsDropdown() {
    const unitSelect = document.getElementById('unitSelect');

    // تنظيف القائمة
    unitSelect.innerHTML = '<option value="">-- اختر وحدة القياس --</option>';

    // ملء القائمة بوحدات القياس النشطة
    units.filter(unit => unit.isActive).forEach(unit => {
        const option = document.createElement('option');
        option.value = unit.unitId;
        option.textContent = unit.unitName;
        unitSelect.appendChild(option);
    });
}

// تحديث الوحدة المختارة
function updateSelectedUnit() {
    const unitId = document.getElementById('unitSelect').value;
    const unit = getUnitById(parseInt(unitId));

    if (unit) {
        // عرض تفاصيل الوحدة
        document.getElementById('unitDetails').style.display = 'block';
        document.getElementById('unitDetailsContent').innerHTML = `
            <strong>1 ${unit.majorUnit.name}</strong> =
            <strong>${unit.mediumUnit.factor} ${unit.mediumUnit.name}</strong> =
            <strong>${unit.minorUnit.factor} ${unit.minorUnit.name}</strong>
        `;

        // عرض حقول الأسعار المحسوبة
        document.getElementById('calculatedPrices').style.display = 'block';

        // إعادة حساب الأسعار إذا كان هناك سعر مدخل
        calculateUnitPrices();
    } else {
        document.getElementById('unitDetails').style.display = 'none';
        document.getElementById('calculatedPrices').style.display = 'none';
    }
}

// حساب أسعار الوحدات تلقائياً
function calculateUnitPrices() {
    const unitId = document.getElementById('unitSelect').value;
    const majorPrice = parseFloat(document.getElementById('majorUnitPrice').value) || 0;

    if (!unitId || majorPrice <= 0) return;

    const unit = getUnitById(parseInt(unitId));
    if (!unit) return;

    // حساب الأسعار بناءً على النظام الهرمي
    const mediumPrice = majorPrice / unit.mediumUnit.factor;
    const minorPrice = majorPrice / unit.minorUnit.factor;

    // عرض الأسعار المحسوبة
    document.getElementById('displayMajorPrice').value = majorPrice.toFixed(2);
    document.getElementById('displayMediumPrice').value = mediumPrice.toFixed(3);
    document.getElementById('displayMinorPrice').value = minorPrice.toFixed(6);
}

// حساب المخزون بالوحدات المختلفة
function calculateStockUnits() {
    const majorStock = parseFloat(document.getElementById('currentStock').value) || 0;
    const mediumFactor = parseFloat(document.getElementById('mediumUnitFactor').value) || 1;
    const minorFactor = parseFloat(document.getElementById('minorUnitFactor').value) || 1;

    // يمكن إضافة عرض المخزون بالوحدات المختلفة هنا
    updateUnitExample();
}

// تحديث مثال نظام الوحدات
function updateUnitExample() {
    const majorUnitId = document.getElementById('majorUnitSelect').value;
    const mediumUnitId = document.getElementById('mediumUnitSelect').value;
    const minorUnitId = document.getElementById('minorUnitSelect').value;

    const majorUnit = getUnitById(parseInt(majorUnitId));
    const mediumUnit = getUnitById(parseInt(mediumUnitId));
    const minorUnit = getUnitById(parseInt(minorUnitId));

    const majorName = majorUnit ? majorUnit.unitName : 'الوحدة الكبرى';
    const mediumName = mediumUnit ? mediumUnit.unitName : 'الوحدة المتوسطة';
    const minorName = minorUnit ? minorUnit.unitName : 'الوحدة الصغرى';

    const mediumFactor = document.getElementById('mediumUnitFactor').value || '0';
    const minorFactor = document.getElementById('minorUnitFactor').value || '0';

    const exampleText = `
        <strong>1 ${majorName}</strong> =
        <strong>${mediumFactor} ${mediumName}</strong> =
        <strong>${minorFactor} ${minorName}</strong>
    `;

    document.getElementById('exampleText').innerHTML = exampleText;

    // إعادة حساب الأسعار عند تغيير الوحدات
    calculateUnitPrices();
}

// حفظ الخامة
async function saveMaterial() {
    const form = document.getElementById('materialForm');
    if (!form.checkValidity()) {
        form.reportValidity();
        return;
    }

    try {
        showSaveLoading();

        const materialData = getMaterialFormData();
        const isEdit = document.getElementById('materialId').value;

        // محاكاة حفظ البيانات
        if (isEdit) {
            // تحديث خامة موجودة
            const index = materials.findIndex(m => m.itemId == isEdit);
            if (index !== -1) {
                materials[index] = { ...materialData, itemId: parseInt(isEdit) };
            }
            showSuccess('تم تحديث الخامة بنجاح');
        } else {
            // إضافة خامة جديدة
            materialData.itemId = Math.max(...materials.map(m => m.itemId), 0) + 1;
            materials.push(materialData);
            showSuccess('تم إضافة الخامة بنجاح');
        }

        // إخفاء النافذة وإعادة تحميل البيانات
        bootstrap.Modal.getInstance(document.getElementById('materialModal')).hide();
        displayMaterials();

    } catch (error) {
        console.error('❌ خطأ في حفظ الخامة:', error);
        showError('حدث خطأ أثناء حفظ الخامة');
    } finally {
        hideSaveLoading();
    }
}

// جمع بيانات النموذج
function getMaterialFormData() {
    const unitId = parseInt(document.getElementById('unitSelect').value);
    const unit = getUnitById(unitId);
    const majorPrice = parseFloat(document.getElementById('majorUnitPrice').value) || 0;
    const currentStock = parseFloat(document.getElementById('currentStock').value) || 0;

    if (!unit) {
        throw new Error('يجب اختيار وحدة قياس');
    }

    return {
        itemCode: document.getElementById('materialCode').value.trim(),
        itemName: document.getElementById('materialName').value.trim(),
        unitId: unitId,

        // الأسعار
        purchasePrice: majorPrice,
        purchasePriceMedium: majorPrice / unit.mediumUnit.factor,
        purchasePriceMinor: majorPrice / unit.minorUnit.factor,

        // المخزون
        currentStock: currentStock,
        currentStockMedium: currentStock * unit.mediumUnit.factor,
        currentStockMinor: currentStock * unit.minorUnit.factor,
        minStock: parseFloat(document.getElementById('minStock').value) || 0,

        // معلومات إضافية
        categoryId: parseInt(document.getElementById('categorySelect').value) || null,
        supplierName: document.getElementById('supplierName').value.trim(),
        storageLocation: document.getElementById('storageLocation').value.trim(),
        notes: document.getElementById('materialNotes').value.trim(),
        isActive: document.getElementById('materialActive').checked
    };
}

// تعديل خامة
function editMaterial(materialId) {
    const material = materials.find(m => m.itemId === materialId);
    if (!material) {
        showError('لم يتم العثور على الخامة');
        return;
    }

    // ملء النموذج ببيانات الخامة
    document.getElementById('materialId').value = material.itemId;
    document.getElementById('materialCode').value = material.itemCode || '';
    document.getElementById('materialName').value = material.itemName || '';

    // ملء القوائم أولاً
    populateCategoriesDropdown();
    populateUnitsDropdown();

    // اختيار الوحدة
    document.getElementById('unitSelect').value = material.unitId || '';
    updateSelectedUnit();

    // الأسعار
    document.getElementById('majorUnitPrice').value = material.purchasePrice || 0;
    calculateUnitPrices();

    // المخزون
    document.getElementById('currentStock').value = material.currentStock || 0;
    document.getElementById('minStock').value = material.minStock || 0;

    // معلومات إضافية
    document.getElementById('categorySelect').value = material.categoryId || '';
    document.getElementById('supplierName').value = material.supplierName || '';
    document.getElementById('storageLocation').value = material.storageLocation || '';
    document.getElementById('materialNotes').value = material.notes || '';
    document.getElementById('materialActive').checked = material.isActive !== false;

    // تحديث العنوان
    document.getElementById('materialModalTitle').textContent = 'تعديل الخامة';

    // إظهار النافذة
    const modal = new bootstrap.Modal(document.getElementById('materialModal'));
    modal.show();
}

// تعديل مخزون الخامة
function adjustMaterialStock(materialId) {
    const material = materials.find(m => m.itemId === materialId);
    if (!material) {
        showError('لم يتم العثور على الخامة');
        return;
    }

    const unit = getUnitById(material.unitId);
    if (!unit) {
        showError('وحدة القياس غير موجودة');
        return;
    }

    const newStock = prompt(
        `تعديل مخزون: ${material.itemName}\n` +
        `المخزون الحالي: ${material.currentStock} ${unit.majorUnit.name}\n` +
        `أدخل المخزون الجديد:`,
        material.currentStock
    );

    if (newStock !== null && !isNaN(newStock)) {
        const stockValue = parseFloat(newStock);

        // تحديث المخزون بجميع الوحدات
        material.currentStock = stockValue;
        material.currentStockMedium = stockValue * unit.mediumUnit.factor;
        material.currentStockMinor = stockValue * unit.minorUnit.factor;

        showSuccess('تم تحديث المخزون بنجاح');
        displayMaterials();
    }
}

// حذف خامة
function deleteMaterial(materialId) {
    if (confirm('هل أنت متأكد من حذف هذه الخامة؟')) {
        alert(`حذف الخامة ${materialId} - سيتم تطويرها`);
    }
}

// عرض تفاصيل خامة
function viewMaterialDetails(materialId) {
    alert(`عرض تفاصيل الخامة ${materialId} - سيتم تطويرها`);
}

// إضافة منتج جديد
function showAddProductModal() {
    // إنشاء نافذة إضافة منتج بسيطة
    const modalHtml = `
        <div class="modal fade" id="productModal" tabindex="-1">
            <div class="modal-dialog modal-lg">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title">إضافة منتج جديد</h5>
                        <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                    </div>
                    <div class="modal-body">
                        <form id="productForm">
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="productCode" class="form-label">كود المنتج *</label>
                                        <input type="text" class="form-control" id="productCode" value="${generateProductCode()}" required>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="productName" class="form-label">اسم المنتج *</label>
                                        <input type="text" class="form-control" id="productName" required>
                                    </div>
                                </div>
                            </div>

                            <div class="alert alert-info">
                                <h6><i class="fas fa-info-circle me-2"></i>ملاحظة مهمة:</h6>
                                <ul class="mb-0">
                                    <li><strong>وحدة المنتج:</strong> قطعة واحدة فقط</li>
                                    <li><strong>التكلفة والسعر:</strong> يتم تحديدهما من خلال الوصفة</li>
                                    <li><strong>بعد الحفظ:</strong> استخدم "إدارة الوصفة" لتحديد التكلفة والسعر</li>
                                </ul>
                            </div>

                            <div class="mb-3">
                                <label for="productCategory" class="form-label">الفئة (اختياري)</label>
                                <select class="form-select" id="productCategory">
                                    <option value="">-- اختر الفئة --</option>
                                    ${categories.map(c => `<option value="${c.id}">${c.name}</option>`).join('')}
                                </select>
                            </div>

                            <div class="form-check mb-3">
                                <input class="form-check-input" type="checkbox" id="productActive" checked>
                                <label class="form-check-label" for="productActive">منتج نشط</label>
                            </div>
                        </form>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                        <button type="button" class="btn btn-success" onclick="saveProduct()">
                            <i class="fas fa-save me-2"></i>حفظ المنتج
                        </button>
                    </div>
                </div>
            </div>
        </div>
    `;

    // إضافة النافذة للصفحة
    document.body.insertAdjacentHTML('beforeend', modalHtml);

    // إظهار النافذة
    const modal = new bootstrap.Modal(document.getElementById('productModal'));
    modal.show();

    // حذف النافذة عند الإغلاق
    document.getElementById('productModal').addEventListener('hidden.bs.modal', function() {
        this.remove();
    });
}

// إنشاء كود تلقائي للمنتج
function generateProductCode() {
    const prefix = 'PROD';
    const maxId = Math.max(...products.map(p => {
        const match = p.itemCode?.match(/PROD(\d+)/);
        return match ? parseInt(match[1]) : 0;
    }), 0);

    const newNumber = (maxId + 1).toString().padStart(3, '0');
    return `${prefix}${newNumber}`;
}

// حساب سعر المنتج
function calculateProductPrice() {
    const costPrice = parseFloat(document.getElementById('productCostPrice').value) || 0;
    const profitMargin = parseFloat(document.getElementById('productProfitMargin').value) || 0;

    if (costPrice > 0 && profitMargin > 0) {
        const salePrice = costPrice * (1 + profitMargin / 100);
        document.getElementById('productSalePrice').value = salePrice.toFixed(2);
    }
}

// حفظ المنتج
function saveProduct() {
    const form = document.getElementById('productForm');
    if (!form.checkValidity()) {
        form.reportValidity();
        return;
    }

    const productData = {
        itemId: Math.max(...products.map(p => p.itemId), 0) + 1,
        itemCode: document.getElementById('productCode').value.trim(),
        itemName: document.getElementById('productName').value.trim(),
        unitName: 'قطعة', // وحدة ثابتة
        categoryId: parseInt(document.getElementById('productCategory').value) || null,
        costPrice: 0, // يحدد من الوصفة
        salePrice: 0, // يحدد من الوصفة
        currentStock: 0,
        hasRecipe: false,
        isActive: document.getElementById('productActive').checked
    };

    products.push(productData);
    showSuccess('تم إضافة المنتج بنجاح! استخدم "إدارة الوصفة" لتحديد التكلفة والسعر');
    bootstrap.Modal.getInstance(document.getElementById('productModal')).hide();
    displayProducts();
}

// تعديل منتج
function editProduct(productId) {
    alert(`تعديل المنتج ${productId} - سيتم تطويرها`);
}

// إدارة وصفة المنتج
function manageRecipe(productId) {
    const product = products.find(p => p.itemId === productId);
    if (!product) {
        showError('المنتج غير موجود');
        return;
    }

    // إنشاء نافذة إدارة الوصفة
    const modalHtml = `
        <div class="modal fade" id="recipeModal" tabindex="-1">
            <div class="modal-dialog modal-xl">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title">إدارة وصفة المنتج: ${product.itemName}</h5>
                        <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                    </div>
                    <div class="modal-body">
                        <div class="row">
                            <div class="col-md-6">
                                <div class="card">
                                    <div class="card-header">
                                        <h6><i class="fas fa-cubes me-2"></i>الخامات المطلوبة</h6>
                                    </div>
                                    <div class="card-body">
                                        <div class="mb-3">
                                            <label class="form-label">إضافة خامة للوصفة</label>
                                            <div class="input-group">
                                                <select class="form-select" id="materialSelect">
                                                    <option value="">-- اختر الخامة --</option>
                                                    ${materials.filter(m => m.isActive).map(m => `<option value="${m.itemId}">${m.itemName}</option>`).join('')}
                                                </select>
                                                <button class="btn btn-primary" onclick="addMaterialToRecipe()">إضافة</button>
                                            </div>
                                        </div>
                                        <div id="recipeMaterials">
                                            <div class="alert alert-info">لا توجد خامات في الوصفة</div>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <div class="col-md-6">
                                <div class="card">
                                    <div class="card-header">
                                        <h6><i class="fas fa-concierge-bell me-2"></i>الخدمات المطلوبة</h6>
                                    </div>
                                    <div class="card-body">
                                        <div class="mb-3">
                                            <label class="form-label">إضافة خدمة للوصفة</label>
                                            <div class="input-group">
                                                <select class="form-select" id="serviceSelect">
                                                    <option value="">-- اختر الخدمة --</option>
                                                    ${services.filter(s => s.isActive).map(s => `<option value="${s.serviceId}">${s.serviceName}</option>`).join('')}
                                                </select>
                                                <button class="btn btn-primary" onclick="addServiceToRecipe()">إضافة</button>
                                            </div>
                                        </div>
                                        <div id="recipeServices">
                                            <div class="alert alert-info">لا توجد خدمات في الوصفة</div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="row mt-3">
                            <div class="col-12">
                                <div class="card">
                                    <div class="card-header">
                                        <h6><i class="fas fa-calculator me-2"></i>ملخص التكلفة</h6>
                                    </div>
                                    <div class="card-body">
                                        <div class="row">
                                            <div class="col-md-3">
                                                <div class="text-center">
                                                    <h6>تكلفة الخامات</h6>
                                                    <h4 class="text-primary" id="totalMaterialsCost">0 ر.ي</h4>
                                                </div>
                                            </div>
                                            <div class="col-md-3">
                                                <div class="text-center">
                                                    <h6>تكلفة الخدمات</h6>
                                                    <h4 class="text-info" id="totalServicesCost">0 ر.ي</h4>
                                                </div>
                                            </div>
                                            <div class="col-md-3">
                                                <div class="text-center">
                                                    <h6>إجمالي التكلفة</h6>
                                                    <h4 class="text-success" id="totalRecipeCost">0 ر.ي</h4>
                                                </div>
                                            </div>
                                            <div class="col-md-3">
                                                <div class="text-center">
                                                    <button class="btn btn-warning" onclick="updateProductCost()">
                                                        <i class="fas fa-sync me-2"></i>تحديث تكلفة المنتج
                                                    </button>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إغلاق</button>
                        <button type="button" class="btn btn-success" onclick="saveRecipe()">
                            <i class="fas fa-save me-2"></i>حفظ الوصفة
                        </button>
                    </div>
                </div>
            </div>
        </div>
    `;

    // إضافة النافذة للصفحة
    document.body.insertAdjacentHTML('beforeend', modalHtml);

    // تحميل الوصفة الحالية إن وجدت
    loadCurrentRecipe(productId);

    // إظهار النافذة
    const modal = new bootstrap.Modal(document.getElementById('recipeModal'));
    modal.show();

    // حذف النافذة عند الإغلاق
    document.getElementById('recipeModal').addEventListener('hidden.bs.modal', function() {
        this.remove();
    });
}

// متغيرات الوصفة الحالية
let currentRecipe = {
    productId: null,
    materials: [],
    services: []
};

// تحميل الوصفة الحالية
function loadCurrentRecipe(productId) {
    currentRecipe.productId = productId;
    currentRecipe.materials = [];
    currentRecipe.services = [];

    // يمكن تحميل الوصفة من قاعدة البيانات هنا
    updateRecipeDisplay();
}

// إضافة خامة للوصفة
function addMaterialToRecipe() {
    const materialId = document.getElementById('materialSelect').value;
    if (!materialId) {
        showError('يرجى اختيار خامة');
        return;
    }

    const material = materials.find(m => m.itemId == materialId);
    if (!material) {
        showError('الخامة غير موجودة');
        return;
    }

    // التحقق من عدم وجود الخامة مسبقاً
    if (currentRecipe.materials.find(m => m.materialId == materialId)) {
        showError('هذه الخامة موجودة بالفعل في الوصفة');
        return;
    }

    const quantity = prompt(`أدخل الكمية المطلوبة من ${material.itemName}:`, '1');
    if (quantity === null || isNaN(quantity) || parseFloat(quantity) <= 0) {
        return;
    }

    const unit = getUnitById(material.unitId);
    const unitOptions = [];
    if (unit) {
        unitOptions.push(unit.majorUnit.name);
        unitOptions.push(unit.mediumUnit.name);
        unitOptions.push(unit.minorUnit.name);
    }

    const selectedUnit = prompt(`اختر الوحدة:\n1- ${unitOptions[0] || 'وحدة'}\n2- ${unitOptions[1] || 'وحدة'}\n3- ${unitOptions[2] || 'وحدة'}\n\nأدخل رقم الوحدة:`, '1');
    if (!selectedUnit || !['1', '2', '3'].includes(selectedUnit)) {
        return;
    }

    const unitIndex = parseInt(selectedUnit) - 1;
    const unitName = unitOptions[unitIndex];

    // حساب التكلفة
    let unitPrice = 0;
    if (unitIndex === 0) unitPrice = material.purchasePrice;
    else if (unitIndex === 1) unitPrice = material.purchasePriceMedium;
    else unitPrice = material.purchasePriceMinor;

    const totalCost = parseFloat(quantity) * unitPrice;

    currentRecipe.materials.push({
        materialId: parseInt(materialId),
        materialName: material.itemName,
        quantity: parseFloat(quantity),
        unit: unitName,
        unitPrice: unitPrice,
        totalCost: totalCost
    });

    document.getElementById('materialSelect').value = '';
    updateRecipeDisplay();
}

// إضافة خدمة للوصفة
function addServiceToRecipe() {
    const serviceId = document.getElementById('serviceSelect').value;
    if (!serviceId) {
        showError('يرجى اختيار خدمة');
        return;
    }

    const service = services.find(s => s.serviceId == serviceId);
    if (!service) {
        showError('الخدمة غير موجودة');
        return;
    }

    // التحقق من عدم وجود الخدمة مسبقاً
    if (currentRecipe.services.find(s => s.serviceId == serviceId)) {
        showError('هذه الخدمة موجودة بالفعل في الوصفة');
        return;
    }

    const quantity = prompt(`أدخل الكمية المطلوبة من ${service.serviceName} (${service.serviceUnit}):`, '1');
    if (quantity === null || isNaN(quantity) || parseFloat(quantity) <= 0) {
        return;
    }

    const totalCost = parseFloat(quantity) * service.servicePrice;

    currentRecipe.services.push({
        serviceId: parseInt(serviceId),
        serviceName: service.serviceName,
        quantity: parseFloat(quantity),
        unit: service.serviceUnit,
        unitPrice: service.servicePrice,
        totalCost: totalCost
    });

    document.getElementById('serviceSelect').value = '';
    updateRecipeDisplay();
}

// تحديث عرض الوصفة
function updateRecipeDisplay() {
    // عرض الخامات
    const materialsDiv = document.getElementById('recipeMaterials');
    if (currentRecipe.materials.length === 0) {
        materialsDiv.innerHTML = '<div class="alert alert-info">لا توجد خامات في الوصفة</div>';
    } else {
        let html = '<div class="list-group">';
        currentRecipe.materials.forEach((item, index) => {
            html += `
                <div class="list-group-item d-flex justify-content-between align-items-center">
                    <div>
                        <strong>${item.materialName}</strong><br>
                        <small>${item.quantity} ${item.unit} × ${formatCurrency(item.unitPrice)} = ${formatCurrency(item.totalCost)}</small>
                    </div>
                    <button class="btn btn-sm btn-outline-danger" onclick="removeMaterialFromRecipe(${index})">
                        <i class="fas fa-trash"></i>
                    </button>
                </div>
            `;
        });
        html += '</div>';
        materialsDiv.innerHTML = html;
    }

    // عرض الخدمات
    const servicesDiv = document.getElementById('recipeServices');
    if (currentRecipe.services.length === 0) {
        servicesDiv.innerHTML = '<div class="alert alert-info">لا توجد خدمات في الوصفة</div>';
    } else {
        let html = '<div class="list-group">';
        currentRecipe.services.forEach((item, index) => {
            html += `
                <div class="list-group-item d-flex justify-content-between align-items-center">
                    <div>
                        <strong>${item.serviceName}</strong><br>
                        <small>${item.quantity} ${item.unit} × ${formatCurrency(item.unitPrice)} = ${formatCurrency(item.totalCost)}</small>
                    </div>
                    <button class="btn btn-sm btn-outline-danger" onclick="removeServiceFromRecipe(${index})">
                        <i class="fas fa-trash"></i>
                    </button>
                </div>
            `;
        });
        html += '</div>';
        servicesDiv.innerHTML = html;
    }

    // تحديث التكاليف
    const materialsCost = currentRecipe.materials.reduce((sum, item) => sum + item.totalCost, 0);
    const servicesCost = currentRecipe.services.reduce((sum, item) => sum + item.totalCost, 0);
    const totalCost = materialsCost + servicesCost;

    document.getElementById('totalMaterialsCost').textContent = formatCurrency(materialsCost);
    document.getElementById('totalServicesCost').textContent = formatCurrency(servicesCost);
    document.getElementById('totalRecipeCost').textContent = formatCurrency(totalCost);
}

// حذف خامة من الوصفة
function removeMaterialFromRecipe(index) {
    currentRecipe.materials.splice(index, 1);
    updateRecipeDisplay();
}

// حذف خدمة من الوصفة
function removeServiceFromRecipe(index) {
    currentRecipe.services.splice(index, 1);
    updateRecipeDisplay();
}

// تحديث تكلفة المنتج
function updateProductCost() {
    const materialsCost = currentRecipe.materials.reduce((sum, item) => sum + item.totalCost, 0);
    const servicesCost = currentRecipe.services.reduce((sum, item) => sum + item.totalCost, 0);
    const totalCost = materialsCost + servicesCost;

    const product = products.find(p => p.itemId === currentRecipe.productId);
    if (product) {
        product.costPrice = totalCost;

        // حساب سعر البيع بهامش ربح افتراضي 30%
        product.salePrice = totalCost * 1.3;

        showSuccess(`تم تحديث تكلفة المنتج: ${formatCurrency(totalCost)} | سعر البيع: ${formatCurrency(product.salePrice)}`);
        displayProducts();
    }
}

// حفظ الوصفة
function saveRecipe() {
    const product = products.find(p => p.itemId === currentRecipe.productId);
    if (product) {
        product.hasRecipe = currentRecipe.materials.length > 0 || currentRecipe.services.length > 0;
        product.recipe = {
            materials: [...currentRecipe.materials],
            services: [...currentRecipe.services]
        };

        showSuccess('تم حفظ الوصفة بنجاح');
        bootstrap.Modal.getInstance(document.getElementById('recipeModal')).hide();
        displayProducts();
    }
}

// حذف منتج
function deleteProduct(productId) {
    if (confirm('هل أنت متأكد من حذف هذا المنتج؟')) {
        alert(`حذف المنتج ${productId} - سيتم تطويرها`);
    }
}

// عرض تفاصيل منتج
function viewProductDetails(productId) {
    alert(`عرض تفاصيل المنتج ${productId} - سيتم تطويرها`);
}

// إضافة خدمة جديدة
function showAddServiceModal() {
    // إنشاء نافذة إضافة خدمة
    const modalHtml = `
        <div class="modal fade" id="serviceModal" tabindex="-1">
            <div class="modal-dialog modal-lg">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title">إضافة خدمة جديدة</h5>
                        <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                    </div>
                    <div class="modal-body">
                        <form id="serviceForm">
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="serviceName" class="form-label">اسم الخدمة *</label>
                                        <input type="text" class="form-control" id="serviceName" required>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="serviceCode" class="form-label">كود الخدمة</label>
                                        <input type="text" class="form-control" id="serviceCode" value="${generateServiceCode()}">
                                    </div>
                                </div>
                            </div>

                            <div class="row">
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="servicePrice" class="form-label">سعر الخدمة (ر.ي) *</label>
                                        <input type="number" class="form-control" id="servicePrice" step="0.001" required>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="serviceUnit" class="form-label">وحدة القياس</label>
                                        <select class="form-select" id="serviceUnit">
                                            <option value="مرة">مرة</option>
                                            <option value="ساعة">ساعة</option>
                                            <option value="يوم">يوم</option>
                                            <option value="قطعة">قطعة</option>
                                        </select>
                                    </div>
                                </div>
                            </div>

                            <div class="mb-3">
                                <label for="serviceDescription" class="form-label">وصف الخدمة</label>
                                <textarea class="form-control" id="serviceDescription" rows="3"></textarea>
                            </div>

                            <div class="form-check mb-3">
                                <input class="form-check-input" type="checkbox" id="serviceActive" checked>
                                <label class="form-check-label" for="serviceActive">خدمة نشطة</label>
                            </div>
                        </form>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                        <button type="button" class="btn btn-success" onclick="saveService()">
                            <i class="fas fa-save me-2"></i>حفظ الخدمة
                        </button>
                    </div>
                </div>
            </div>
        </div>
    `;

    // إضافة النافذة للصفحة
    document.body.insertAdjacentHTML('beforeend', modalHtml);

    // إظهار النافذة
    const modal = new bootstrap.Modal(document.getElementById('serviceModal'));
    modal.show();

    // حذف النافذة عند الإغلاق
    document.getElementById('serviceModal').addEventListener('hidden.bs.modal', function() {
        this.remove();
    });
}

// إنشاء كود تلقائي للخدمة
function generateServiceCode() {
    const prefix = 'SRV';
    const maxId = Math.max(...services.map(s => {
        const match = s.serviceCode?.match(/SRV(\d+)/);
        return match ? parseInt(match[1]) : 0;
    }), 0);

    const newNumber = (maxId + 1).toString().padStart(3, '0');
    return `${prefix}${newNumber}`;
}

// حفظ الخدمة
function saveService() {
    const form = document.getElementById('serviceForm');
    if (!form.checkValidity()) {
        form.reportValidity();
        return;
    }

    const serviceData = {
        serviceId: Math.max(...services.map(s => s.serviceId), 0) + 1,
        serviceCode: document.getElementById('serviceCode').value.trim(),
        serviceName: document.getElementById('serviceName').value.trim(),
        servicePrice: parseFloat(document.getElementById('servicePrice').value) || 0,
        serviceUnit: document.getElementById('serviceUnit').value,
        description: document.getElementById('serviceDescription').value.trim(),
        isActive: document.getElementById('serviceActive').checked
    };

    services.push(serviceData);
    showSuccess('تم إضافة الخدمة بنجاح');
    bootstrap.Modal.getInstance(document.getElementById('serviceModal')).hide();
    loadServices();
}

// تحميل الخدمات
async function loadServices() {
    const content = document.getElementById('servicesContent');
    showTabLoading(content);

    // تحميل بيانات تجريبية إذا لم تكن موجودة
    if (services.length === 0) {
        services = [
            {
                serviceId: 1,
                serviceCode: 'SRV001',
                serviceName: 'خدمة التوصيل',
                servicePrice: 500,
                serviceUnit: 'مرة',
                description: 'خدمة توصيل المنتجات للعملاء',
                isActive: true
            },
            {
                serviceId: 2,
                serviceCode: 'SRV002',
                serviceName: 'خدمة التغليف الخاص',
                servicePrice: 200,
                serviceUnit: 'قطعة',
                description: 'تغليف خاص للمناسبات',
                isActive: true
            }
        ];
    }

    setTimeout(() => {
        displayServices();
    }, 500);
}

// عرض الخدمات
function displayServices() {
    const content = document.getElementById('servicesContent');

    if (services.length === 0) {
        content.innerHTML = `
            <div class="text-center p-4">
                <i class="fas fa-concierge-bell fa-3x text-muted mb-3"></i>
                <h5>لا توجد خدمات</h5>
                <p class="text-muted">ابدأ بإضافة خدمة جديدة</p>
            </div>
        `;
        return;
    }

    let html = `
        <div class="table-responsive">
            <table class="table table-striped table-hover">
                <thead class="table-dark">
                    <tr>
                        <th>الكود</th>
                        <th>اسم الخدمة</th>
                        <th>السعر</th>
                        <th>الوحدة</th>
                        <th>الوصف</th>
                        <th>الحالة</th>
                        <th>الإجراءات</th>
                    </tr>
                </thead>
                <tbody>
    `;

    services.forEach(service => {
        const statusBadge = service.isActive ?
            '<span class="badge bg-success">نشط</span>' :
            '<span class="badge bg-secondary">غير نشط</span>';

        html += `
            <tr>
                <td><strong>${service.serviceCode}</strong></td>
                <td>${service.serviceName}</td>
                <td>${formatCurrency(service.servicePrice)}</td>
                <td><span class="badge bg-info">${service.serviceUnit}</span></td>
                <td>${service.description || '-'}</td>
                <td>${statusBadge}</td>
                <td>
                    <div class="btn-group btn-group-sm">
                        <button class="btn btn-outline-primary" onclick="editService(${service.serviceId})" title="تعديل">
                            <i class="fas fa-edit"></i>
                        </button>
                        <button class="btn btn-outline-danger" onclick="deleteService(${service.serviceId})" title="حذف">
                            <i class="fas fa-trash"></i>
                        </button>
                    </div>
                </td>
            </tr>
        `;
    });

    html += `
                </tbody>
            </table>
        </div>
    `;

    content.innerHTML = html;
}

// تعديل خدمة
function editService(serviceId) {
    alert(`تعديل الخدمة ${serviceId} - سيتم تطويرها`);
}

// حذف خدمة
function deleteService(serviceId) {
    if (confirm('هل أنت متأكد من حذف هذه الخدمة؟')) {
        const index = services.findIndex(s => s.serviceId === serviceId);
        if (index !== -1) {
            services.splice(index, 1);
            showSuccess('تم حذف الخدمة بنجاح');
            displayServices();
        }
    }
}

// إضافة عملية إنتاج
function showAddProductionModal() {
    if (products.length === 0) {
        showError('يجب إضافة منتجات أولاً قبل إنشاء عمليات الإنتاج');
        return;
    }

    // إنشاء نافذة إضافة عملية إنتاج
    const modalHtml = `
        <div class="modal fade" id="productionModal" tabindex="-1">
            <div class="modal-dialog modal-xl">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title">إضافة عملية إنتاج جديدة</h5>
                        <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                    </div>
                    <div class="modal-body">
                        <form id="productionForm">
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="productionProduct" class="form-label">المنتج *</label>
                                        <select class="form-select" id="productionProduct" required onchange="updateProductionDetails()">
                                            <option value="">-- اختر المنتج --</option>
                                            ${products.filter(p => p.isActive).map(p => `<option value="${p.itemId}">${p.itemName}</option>`).join('')}
                                        </select>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="productionQuantity" class="form-label">الكمية المطلوب إنتاجها *</label>
                                        <input type="number" class="form-control" id="productionQuantity" step="0.001" required onchange="calculateProductionCost()">
                                    </div>
                                </div>
                            </div>

                            <div class="row">
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="productionDate" class="form-label">تاريخ الإنتاج</label>
                                        <input type="date" class="form-control" id="productionDate" value="${new Date().toISOString().split('T')[0]}">
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="productionShift" class="form-label">الوردية</label>
                                        <select class="form-select" id="productionShift">
                                            <option value="صباحية">صباحية</option>
                                            <option value="مسائية">مسائية</option>
                                            <option value="ليلية">ليلية</option>
                                        </select>
                                    </div>
                                </div>
                            </div>

                            <div id="productionCostDetails" class="card mb-3" style="display: none;">
                                <div class="card-header">
                                    <h6><i class="fas fa-calculator me-2"></i>تفاصيل التكلفة</h6>
                                </div>
                                <div class="card-body">
                                    <div class="row">
                                        <div class="col-md-4">
                                            <div class="mb-3">
                                                <label class="form-label">تكلفة الخامات</label>
                                                <input type="number" class="form-control" id="materialsCost" readonly>
                                            </div>
                                        </div>
                                        <div class="col-md-4">
                                            <div class="mb-3">
                                                <label class="form-label">تكلفة الخدمات</label>
                                                <input type="number" class="form-control" id="servicesCost" readonly>
                                            </div>
                                        </div>
                                        <div class="col-md-4">
                                            <div class="mb-3">
                                                <label class="form-label">إجمالي التكلفة</label>
                                                <input type="number" class="form-control" id="totalCost" readonly>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <div class="mb-3">
                                <label for="productionNotes" class="form-label">ملاحظات</label>
                                <textarea class="form-control" id="productionNotes" rows="2"></textarea>
                            </div>
                        </form>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                        <button type="button" class="btn btn-success" onclick="saveProduction()">
                            <i class="fas fa-save me-2"></i>حفظ عملية الإنتاج
                        </button>
                    </div>
                </div>
            </div>
        </div>
    `;

    // إضافة النافذة للصفحة
    document.body.insertAdjacentHTML('beforeend', modalHtml);

    // إظهار النافذة
    const modal = new bootstrap.Modal(document.getElementById('productionModal'));
    modal.show();

    // حذف النافذة عند الإغلاق
    document.getElementById('productionModal').addEventListener('hidden.bs.modal', function() {
        this.remove();
    });
}

// تحديث تفاصيل الإنتاج
function updateProductionDetails() {
    const productId = document.getElementById('productionProduct').value;
    if (productId) {
        document.getElementById('productionCostDetails').style.display = 'block';
        calculateProductionCost();
    } else {
        document.getElementById('productionCostDetails').style.display = 'none';
    }
}

// حساب تكلفة الإنتاج
function calculateProductionCost() {
    const productId = document.getElementById('productionProduct').value;
    const quantity = parseFloat(document.getElementById('productionQuantity').value) || 0;

    if (!productId || quantity <= 0) return;

    const product = products.find(p => p.itemId == productId);
    if (!product) return;

    // حساب التكلفة بناءً على تكلفة المنتج الواحد (من الوصفة)
    const unitCost = product.costPrice || 0;
    const totalCost = unitCost * quantity;

    // توزيع التكلفة (تقديري)
    const materialsCost = totalCost * 0.7; // 70% خامات
    const servicesCost = totalCost * 0.3; // 30% خدمات

    document.getElementById('materialsCost').value = materialsCost.toFixed(2);
    document.getElementById('servicesCost').value = servicesCost.toFixed(2);
    document.getElementById('totalCost').value = totalCost.toFixed(2);
}

// حفظ عملية الإنتاج
function saveProduction() {
    const form = document.getElementById('productionForm');
    if (!form.checkValidity()) {
        form.reportValidity();
        return;
    }

    const productionData = {
        productionId: Math.max(...productionOperations.map(p => p.productionId), 0) + 1,
        productId: parseInt(document.getElementById('productionProduct').value),
        quantity: parseFloat(document.getElementById('productionQuantity').value),
        productionDate: document.getElementById('productionDate').value,
        shift: document.getElementById('productionShift').value,
        materialsCost: parseFloat(document.getElementById('materialsCost').value) || 0,
        servicesCost: parseFloat(document.getElementById('servicesCost').value) || 0,
        totalCost: parseFloat(document.getElementById('totalCost').value) || 0,
        notes: document.getElementById('productionNotes').value.trim(),
        status: 'مكتمل',
        createdAt: new Date().toISOString()
    };

    productionOperations.push(productionData);

    // تحديث مخزون المنتج
    const product = products.find(p => p.itemId == productionData.productId);
    if (product) {
        product.currentStock = (product.currentStock || 0) + productionData.quantity;
    }

    showSuccess('تم حفظ عملية الإنتاج بنجاح وتم تحديث المخزون');
    bootstrap.Modal.getInstance(document.getElementById('productionModal')).hide();
    loadProductionOperations();
}

// تحميل عمليات الإنتاج
async function loadProductionOperations() {
    const content = document.getElementById('productionContent');
    showTabLoading(content);

    setTimeout(() => {
        displayProductionOperations();
    }, 500);
}

// عرض عمليات الإنتاج
function displayProductionOperations() {
    const content = document.getElementById('productionContent');

    if (productionOperations.length === 0) {
        content.innerHTML = `
            <div class="text-center p-4">
                <i class="fas fa-industry fa-3x text-muted mb-3"></i>
                <h5>لا توجد عمليات إنتاج</h5>
                <p class="text-muted">ابدأ بإضافة عملية إنتاج جديدة</p>
            </div>
        `;
        return;
    }

    let html = `
        <div class="table-responsive">
            <table class="table table-striped table-hover">
                <thead class="table-dark">
                    <tr>
                        <th>رقم العملية</th>
                        <th>المنتج</th>
                        <th>الكمية</th>
                        <th>التاريخ</th>
                        <th>الوردية</th>
                        <th>التكلفة الإجمالية</th>
                        <th>الحالة</th>
                        <th>الإجراءات</th>
                    </tr>
                </thead>
                <tbody>
    `;

    productionOperations.forEach(operation => {
        const product = products.find(p => p.itemId === operation.productId);
        const productName = product ? product.itemName : 'منتج محذوف';

        const statusBadge = operation.status === 'مكتمل' ?
            '<span class="badge bg-success">مكتمل</span>' :
            '<span class="badge bg-warning">قيد التنفيذ</span>';

        html += `
            <tr>
                <td><strong>#${operation.productionId}</strong></td>
                <td>${productName}</td>
                <td>${formatQuantity(operation.quantity)} قطعة</td>
                <td>${new Date(operation.productionDate).toLocaleDateString('ar-YE')}</td>
                <td><span class="badge bg-info">${operation.shift}</span></td>
                <td>${formatCurrency(operation.totalCost)}</td>
                <td>${statusBadge}</td>
                <td>
                    <div class="btn-group btn-group-sm">
                        <button class="btn btn-outline-info" onclick="viewProductionDetails(${operation.productionId})" title="التفاصيل">
                            <i class="fas fa-eye"></i>
                        </button>
                        <button class="btn btn-outline-danger" onclick="deleteProduction(${operation.productionId})" title="حذف">
                            <i class="fas fa-trash"></i>
                        </button>
                    </div>
                </td>
            </tr>
        `;
    });

    html += `
                </tbody>
            </table>
        </div>
    `;

    content.innerHTML = html;
}

// عرض تفاصيل عملية الإنتاج
function viewProductionDetails(productionId) {
    const operation = productionOperations.find(p => p.productionId === productionId);
    if (!operation) {
        showError('عملية الإنتاج غير موجودة');
        return;
    }

    const product = products.find(p => p.itemId === operation.productId);
    const productName = product ? product.itemName : 'منتج محذوف';

    alert(`تفاصيل عملية الإنتاج #${operation.productionId}:\n\n` +
          `المنتج: ${productName}\n` +
          `الكمية: ${operation.quantity}\n` +
          `التاريخ: ${operation.productionDate}\n` +
          `الوردية: ${operation.shift}\n` +
          `تكلفة الخامات: ${formatCurrency(operation.materialsCost)}\n` +
          `تكلفة الخدمات: ${formatCurrency(operation.servicesCost)}\n` +
          `التكلفة الإجمالية: ${formatCurrency(operation.totalCost)}\n` +
          `الملاحظات: ${operation.notes || 'لا توجد'}`);
}

// حذف عملية إنتاج
function deleteProduction(productionId) {
    if (confirm('هل أنت متأكد من حذف عملية الإنتاج هذه؟\nسيتم خصم الكمية من مخزون المنتج.')) {
        const operation = productionOperations.find(p => p.productionId === productionId);
        if (operation) {
            // خصم الكمية من مخزون المنتج
            const product = products.find(p => p.itemId === operation.productId);
            if (product) {
                product.currentStock = Math.max(0, (product.currentStock || 0) - operation.quantity);
            }

            // حذف العملية
            const index = productionOperations.findIndex(p => p.productionId === productionId);
            if (index !== -1) {
                productionOperations.splice(index, 1);
                showSuccess('تم حذف عملية الإنتاج وتحديث المخزون');
                displayProductionOperations();
            }
        }
    }
}

// وظائف البحث والفلترة
function searchMaterials() {
    const searchTerm = document.getElementById('materialsSearch').value.toLowerCase();
    const categoryFilter = document.getElementById('materialsFilter').value;

    let filteredMaterials = materials;

    // تطبيق البحث
    if (searchTerm) {
        filteredMaterials = filteredMaterials.filter(material =>
            material.itemName.toLowerCase().includes(searchTerm) ||
            material.itemCode?.toLowerCase().includes(searchTerm) ||
            material.supplierName?.toLowerCase().includes(searchTerm)
        );
    }

    // تطبيق فلترة الفئة
    if (categoryFilter) {
        filteredMaterials = filteredMaterials.filter(material =>
            material.categoryId == categoryFilter
        );
    }

    // عرض النتائج المفلترة
    displayFilteredMaterials(filteredMaterials);
}

function filterMaterials() {
    searchMaterials(); // استخدام نفس منطق البحث
}

// عرض الخامات المفلترة
function displayFilteredMaterials(filteredMaterials) {
    const originalMaterials = materials;
    materials = filteredMaterials;
    displayMaterials();
    materials = originalMaterials;
}

function searchProducts() {
    // TODO: تطبيق البحث في المنتجات
}

function filterProducts() {
    // TODO: تطبيق فلترة المنتجات
}

// طباعة تقرير الخامات
function printMaterialsReport() {
    const printWindow = window.open('', '_blank');
    const currentDate = new Date().toLocaleDateString('ar-YE');

    let html = `
        <!DOCTYPE html>
        <html lang="ar" dir="rtl">
        <head>
            <meta charset="UTF-8">
            <title>تقرير الخامات - نظام إدارة مخبوزات ANW</title>
            <style>
                body { font-family: Arial, sans-serif; margin: 20px; }
                .header { text-align: center; margin-bottom: 30px; }
                .company-name { font-size: 24px; font-weight: bold; color: #0d6efd; }
                .report-title { font-size: 18px; margin: 10px 0; }
                .report-date { font-size: 14px; color: #666; }
                table { width: 100%; border-collapse: collapse; margin-top: 20px; }
                th, td { border: 1px solid #ddd; padding: 8px; text-align: center; }
                th { background-color: #f8f9fa; font-weight: bold; }
                .text-right { text-align: right; }
                .badge { padding: 2px 6px; border-radius: 3px; font-size: 11px; }
                .bg-success { background-color: #d4edda; color: #155724; }
                .bg-danger { background-color: #f8d7da; color: #721c24; }
                .bg-primary { background-color: #d1ecf1; color: #0c5460; }
                .bg-info { background-color: #d1ecf1; color: #0c5460; }
                .bg-secondary { background-color: #e2e3e5; color: #383d41; }
                .units-cell { font-size: 12px; line-height: 1.4; }
                .price-cell { font-size: 12px; line-height: 1.4; }
                @media print {
                    body { margin: 0; }
                    .no-print { display: none; }
                }
            </style>
        </head>
        <body>
            <div class="header">
                <div class="company-name">نظام إدارة مخبوزات ANW</div>
                <div class="report-title">تقرير الخامات والمواد الأولية</div>
                <div class="report-date">تاريخ التقرير: ${currentDate}</div>
            </div>

            <table>
                <thead>
                    <tr>
                        <th>الكود</th>
                        <th>اسم الخامة</th>
                        <th>نظام الوحدات</th>
                        <th>أسعار الشراء</th>
                        <th>المخزون الحالي</th>
                        <th>الحد الأدنى</th>
                        <th>الحالة</th>
                    </tr>
                </thead>
                <tbody>
    `;

    materials.forEach(material => {
        const statusBadge = material.isActive ?
            '<span class="badge bg-success">نشط</span>' :
            '<span class="badge bg-secondary">غير نشط</span>';

        const stockBadge = material.currentStock <= material.minStock ?
            '<span class="badge bg-danger">منخفض</span>' :
            '<span class="badge bg-success">متوفر</span>';

        html += `
            <tr>
                <td><strong>${material.itemCode || '-'}</strong></td>
                <td class="text-right">${material.itemName}</td>
                <td class="units-cell">
                    <div>1 ${material.majorUnit.name}</div>
                    <div>${material.mediumUnit.factor} ${material.mediumUnit.name}</div>
                    <div>${material.minorUnit.factor} ${material.minorUnit.name}</div>
                </td>
                <td class="price-cell">
                    <div>${formatCurrency(material.purchasePrice)}/${material.majorUnit.name}</div>
                    <div>${formatCurrency(material.purchasePriceMedium)}/${material.mediumUnit.name}</div>
                    <div>${formatCurrency(material.purchasePriceMinor)}/${material.minorUnit.name}</div>
                </td>
                <td>
                    <div><strong>${formatQuantity(material.currentStock)}</strong> ${material.majorUnit.name}</div>
                    <div>${formatQuantity(material.currentStockMedium)} ${material.mediumUnit.name}</div>
                    <div>${formatQuantity(material.currentStockMinor)} ${material.minorUnit.name}</div>
                    ${stockBadge}
                </td>
                <td>
                    <strong>${formatQuantity(material.minStock)}</strong><br>
                    ${material.majorUnit.name}
                </td>
                <td>${statusBadge}</td>
            </tr>
        `;
    });

    html += `
                </tbody>
            </table>

            <div style="margin-top: 30px; font-size: 12px; color: #666;">
                <p>إجمالي عدد الخامات: <strong>${materials.length}</strong></p>
                <p>الخامات النشطة: <strong>${materials.filter(m => m.isActive).length}</strong></p>
                <p>الخامات منخفضة المخزون: <strong>${materials.filter(m => m.currentStock <= m.minStock).length}</strong></p>
            </div>
        </body>
        </html>
    `;

    printWindow.document.write(html);
    printWindow.document.close();
    printWindow.print();
}

// تصدير الخامات إلى Excel
function exportMaterialsToExcel() {
    // محاكاة تصدير Excel
    showSuccess('سيتم تطوير ميزة تصدير Excel قريباً');
}

// تسجيل الخروج
function logout() {
    if (confirm('هل أنت متأكد من تسجيل الخروج؟')) {
        localStorage.removeItem('isLoggedIn');
        window.location.href = 'login.html';
    }
}
