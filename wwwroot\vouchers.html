<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>سندات القبض والصرف - نظام إدارة المخبز</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <link href="css/style.css" rel="stylesheet">
    <style>
        .dropdown-menu {
            border: 1px solid #ddd;
            border-radius: 0.375rem;
            box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
        }

        .dropdown-item:hover {
            background-color: #f8f9fa;
        }

        .position-relative .dropdown-menu {
            position: absolute;
            top: 100%;
            left: 0;
            z-index: 1000;
        }

        .party-search-input {
            border: 2px solid #e9ecef;
            transition: border-color 0.15s ease-in-out;
        }

        .party-search-input:focus {
            border-color: #0d6efd;
            box-shadow: 0 0 0 0.2rem rgba(13, 110, 253, 0.25);
        }
    </style>
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary">
        <div class="container-fluid">
            <a class="navbar-brand" href="dashboard.html">
                <i class="fas fa-bread-slice me-2"></i>
                نظام إدارة المخبز
            </a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto">
                    <li class="nav-item">
                        <a class="nav-link" href="dashboard.html">
                            <i class="fas fa-home me-1"></i>الرئيسية
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="items-management.html">
                            <i class="fas fa-cubes me-1"></i>الأصناف والمنتجات
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link active" href="vouchers.html">
                            <i class="fas fa-receipt me-1"></i>سندات القبض والصرف
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="units.html">
                            <i class="fas fa-balance-scale me-1"></i>وحدات القياس
                        </a>
                    </li>
                </ul>
                <ul class="navbar-nav">
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown">
                            <i class="fas fa-user me-1"></i>المستخدم
                        </a>
                        <ul class="dropdown-menu">
                            <li><a class="dropdown-item" href="#"><i class="fas fa-cog me-2"></i>الإعدادات</a></li>
                            <li><hr class="dropdown-divider"></li>
                            <li><a class="dropdown-item" href="index.html"><i class="fas fa-sign-out-alt me-2"></i>تسجيل الخروج</a></li>
                        </ul>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

    <!-- Main Content -->
    <div class="container-fluid mt-4">
        <div class="row">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <h4><i class="fas fa-receipt me-2"></i>إدارة سندات القبض والصرف</h4>
                    </div>
                    <div class="card-body">
                        <!-- Tabs Navigation -->
                        <ul class="nav nav-tabs" id="vouchersTabs" role="tablist">
                            <li class="nav-item" role="presentation">
                                <button class="nav-link active" id="receipt-tab" data-bs-toggle="tab" data-bs-target="#receipt" type="button" role="tab">
                                    <i class="fas fa-arrow-down text-success me-2"></i>سندات القبض
                                </button>
                            </li>
                            <li class="nav-item" role="presentation">
                                <button class="nav-link" id="payment-tab" data-bs-toggle="tab" data-bs-target="#payment" type="button" role="tab">
                                    <i class="fas fa-arrow-up text-danger me-2"></i>سندات الصرف
                                </button>
                            </li>

                            <li class="nav-item" role="presentation">
                                <button class="nav-link" id="parties-tab" data-bs-toggle="tab" data-bs-target="#parties" type="button" role="tab">
                                    <i class="fas fa-users text-info me-2"></i>إدارة الأطراف
                                </button>
                            </li>
                        </ul>

                        <!-- Tabs Content -->
                        <div class="tab-content" id="vouchersTabsContent">
                            <!-- Receipt Vouchers Tab -->
                            <div class="tab-pane fade show active" id="receipt" role="tabpanel">
                                <div class="d-flex justify-content-between align-items-center mb-3">
                                    <h5><i class="fas fa-arrow-down text-success me-2"></i>سندات القبض</h5>
                                    <button class="btn btn-success" onclick="showAddReceiptModal()">
                                        <i class="fas fa-plus"></i> إضافة سند قبض جديد
                                    </button>
                                </div>
                                <div id="receiptContent">
                                    <!-- Receipt vouchers content will be loaded here -->
                                </div>
                            </div>

                            <!-- Payment Vouchers Tab -->
                            <div class="tab-pane fade" id="payment" role="tabpanel">
                                <div class="d-flex justify-content-between align-items-center mb-3">
                                    <h5><i class="fas fa-arrow-up text-danger me-2"></i>سندات الصرف</h5>
                                    <button class="btn btn-danger" onclick="showAddPaymentModal()">
                                        <i class="fas fa-plus"></i> إضافة سند صرف جديد
                                    </button>
                                </div>
                                <div id="paymentContent">
                                    <!-- Payment vouchers content will be loaded here -->
                                </div>
                            </div>



                            <!-- Parties Tab -->
                            <div class="tab-pane fade" id="parties" role="tabpanel">
                                <div class="d-flex justify-content-between align-items-center mb-3">
                                    <h5><i class="fas fa-users text-info me-2"></i>إدارة الأطراف</h5>
                                    <button class="btn btn-info" onclick="showAddPartyModal()">
                                        <i class="fas fa-plus"></i> إضافة طرف جديد
                                    </button>
                                </div>
                                <div id="partiesContent">
                                    <!-- Parties content will be loaded here -->
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="js/auth-simple.js"></script>
    <script src="js/vouchers.js"></script>
</body>
</html>
