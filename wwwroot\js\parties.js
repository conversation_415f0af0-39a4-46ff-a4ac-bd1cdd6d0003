// إدارة العملاء والموردين - متصل بـ APIs
const API_BASE_URL = '/api';
let parties = [];
let filteredParties = [];
let currentParty = null;

// تحميل الصفحة
document.addEventListener('DOMContentLoaded', function() {
    console.log('🔄 تحميل صفحة العملاء والموردين...');
    loadParties();
});

// تحميل العملاء والموردين من API
async function loadParties() {
    console.log('🔄 تحميل العملاء والموردين...');
    showLoading();

    try {
        const response = await fetch(`${API_BASE_URL}/parties`);
        if (response.ok) {
            const result = await response.json();
            if (result.success) {
                parties = result.data || [];
                filteredParties = [...parties];
                displayParties(filteredParties);
                console.log('✅ تم تحميل العملاء والموردين:', parties.length);
                return;
            }
        }

        // في حالة فشل API، عرض قائمة فارغة
        console.error('❌ فشل تحميل العملاء والموردين من قاعدة البيانات');
        parties = [];
        filteredParties = [];
        displayParties([]);
        showError('لا توجد عملاء أو موردين في قاعدة البيانات. ابدأ بإضافة عميل أو مورد جديد.');

    } catch (error) {
        console.error('❌ خطأ في تحميل العملاء والموردين:', error);
        parties = [];
        filteredParties = [];
        displayParties([]);
        showError('خطأ في الاتصال بقاعدة البيانات.');
    }
}



// عرض العملاء والموردين
function displayParties(partiesData) {
    const tbody = document.getElementById('partiesTableBody');
    
    if (!partiesData || partiesData.length === 0) {
        tbody.innerHTML = `
            <tr>
                <td colspan="8" class="text-center text-muted">
                    <i class="fas fa-users fa-3x mb-3"></i>
                    <p>لا توجد عملاء أو موردين مضافين بعد</p>
                    <button class="btn btn-primary" onclick="showAddModal()">
                        <i class="fas fa-plus"></i> إضافة أول عميل/مورد
                    </button>
                </td>
            </tr>
        `;
        return;
    }

    tbody.innerHTML = partiesData.map((party, index) => {
        const partyType = getPartyTypeText(party);
        const balanceClass = party.currentBalance >= 0 ? 'text-success' : 'text-danger';
        const balanceIcon = party.currentBalance >= 0 ? 'fas fa-arrow-up' : 'fas fa-arrow-down';
        
        return `
            <tr>
                <td>${index + 1}</td>
                <td>
                    <strong>${party.partyName}</strong>
                    <br><small class="text-muted">${party.partyCode || '-'}</small>
                </td>
                <td>
                    <span class="badge ${getPartyTypeBadge(party)}">${partyType}</span>
                </td>
                <td>
                    <i class="fas fa-phone"></i> ${party.phone || '-'}
                    ${party.email ? `<br><i class="fas fa-envelope"></i> ${party.email}` : ''}
                </td>
                <td class="${balanceClass}">
                    <i class="${balanceIcon}"></i>
                    ${formatCurrency(party.currentBalance)}
                </td>
                <td>${formatCurrency(party.creditLimit)}</td>
                <td>
                    <span class="badge ${party.isActive ? 'bg-success' : 'bg-danger'}">
                        ${party.isActive ? 'نشط' : 'غير نشط'}
                    </span>
                </td>
                <td>
                    <div class="btn-group btn-group-sm">
                        <button class="btn btn-outline-primary" onclick="editParty(${party.partyId})" title="تعديل">
                            <i class="fas fa-edit"></i>
                        </button>
                        <button class="btn btn-outline-info" onclick="showStatement(${party.partyId})" title="كشف حساب">
                            <i class="fas fa-file-alt"></i>
                        </button>
                        <button class="btn btn-outline-danger" onclick="deleteParty(${party.partyId})" title="حذف">
                            <i class="fas fa-trash"></i>
                        </button>
                    </div>
                </td>
            </tr>
        `;
    }).join('');
}

// تحديد نوع الطرف
function getPartyTypeText(party) {
    if (party.isCustomer && party.isSupplier) return 'عميل ومورد';
    if (party.isCustomer) return 'عميل';
    if (party.isSupplier) return 'مورد';
    return 'غير محدد';
}

// تحديد لون شارة النوع
function getPartyTypeBadge(party) {
    if (party.isCustomer && party.isSupplier) return 'bg-info';
    if (party.isCustomer) return 'bg-success';
    if (party.isSupplier) return 'bg-warning';
    return 'bg-secondary';
}

// تنسيق العملة
function formatCurrency(amount) {
    return new Intl.NumberFormat('ar-YE', {
        style: 'currency',
        currency: 'YER',
        minimumFractionDigits: 3,
        maximumFractionDigits: 3
    }).format(amount).replace('YER', 'ر.ي');
}

// عرض حالة التحميل
function showLoading() {
    const tbody = document.getElementById('partiesTableBody');
    tbody.innerHTML = `
        <tr>
            <td colspan="8" class="text-center">
                <div class="spinner-border text-primary" role="status">
                    <span class="visually-hidden">جاري التحميل...</span>
                </div>
                <p class="mt-2">جاري تحميل العملاء والموردين...</p>
            </td>
        </tr>
    `;
}

// إظهار نافذة الإضافة
function showAddModal() {
    currentParty = null;
    document.getElementById('modalTitle').textContent = 'إضافة عميل/مورد جديد';
    document.getElementById('partyForm').reset();
    document.getElementById('partyId').value = '';
    document.getElementById('creditLimit').value = '0';
    document.getElementById('openingBalance').value = '0';
    
    const modal = new bootstrap.Modal(document.getElementById('partyModal'));
    modal.show();
}

// تعديل طرف
function editParty(partyId) {
    const party = parties.find(p => p.partyId === partyId);
    if (!party) return;
    
    currentParty = party;
    document.getElementById('modalTitle').textContent = 'تعديل بيانات الطرف';
    
    // ملء النموذج
    document.getElementById('partyId').value = party.partyId;
    document.getElementById('partyName').value = party.partyName;
    document.getElementById('partyCode').value = party.partyCode || '';
    document.getElementById('isCustomer').checked = party.isCustomer;
    document.getElementById('isSupplier').checked = party.isSupplier;
    document.getElementById('phone').value = party.phone || '';
    document.getElementById('email').value = party.email || '';
    document.getElementById('address').value = party.address || '';
    document.getElementById('creditLimit').value = party.creditLimit;
    document.getElementById('openingBalance').value = party.currentBalance;
    document.getElementById('notes').value = party.notes || '';
    
    const modal = new bootstrap.Modal(document.getElementById('partyModal'));
    modal.show();
}

// حفظ طرف (إضافة أو تحديث)
async function saveParty() {
    try {
        const formData = getPartyFormData();
        if (!validatePartyFormData(formData)) {
            return;
        }

        const isEdit = currentParty && currentParty.partyId;
        const url = isEdit ? `${API_BASE_URL}/parties/${currentParty.partyId}` : `${API_BASE_URL}/parties`;
        const method = isEdit ? 'PUT' : 'POST';

        showSaveLoading();

        const response = await fetch(url, {
            method: method,
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify(formData)
        });

        const result = await response.json();

        if (response.ok && result.success) {
            showSuccess(isEdit ? 'تم تحديث الطرف بنجاح' : 'تم إضافة الطرف بنجاح');
            const modal = bootstrap.Modal.getInstance(document.getElementById('partyModal'));
            modal.hide();
            await loadParties(); // إعادة تحميل القائمة
        } else {
            showError(result.message || 'حدث خطأ أثناء حفظ الطرف');
        }

    } catch (error) {
        console.error('❌ خطأ في حفظ الطرف:', error);
        showError('حدث خطأ في الاتصال بالخادم');
    } finally {
        hideSaveLoading();
    }
}

// جمع بيانات النموذج
function getPartyFormData() {
    const isCustomer = document.getElementById('isCustomer').checked;
    const isSupplier = document.getElementById('isSupplier').checked;

    // تحديد نوع الطرف
    let partyType = 1; // Customer
    if (isSupplier && !isCustomer) {
        partyType = 2; // Supplier
    } else if (isCustomer && isSupplier) {
        partyType = 3; // Both
    }

    return {
        partyCode: document.getElementById('partyCode').value.trim(),
        partyName: document.getElementById('partyName').value.trim(),
        partyNameEn: '', // TODO: إضافة حقل الاسم بالإنجليزية
        partyType: partyType,
        phone: document.getElementById('phone').value.trim(),
        email: document.getElementById('email').value.trim(),
        address: document.getElementById('address').value.trim(),
        taxNumber: '', // TODO: إضافة حقل الرقم الضريبي
        commercialRegister: '', // TODO: إضافة حقل السجل التجاري
        creditLimit: parseFloat(document.getElementById('creditLimit').value) || 0,
        paymentTerms: 0, // TODO: إضافة حقل شروط الدفع
        notes: document.getElementById('notes').value.trim(),
        isActive: true
    };
}

// التحقق من صحة البيانات
function validatePartyFormData(data) {
    if (!data.partyName) {
        showError('اسم الطرف مطلوب');
        return false;
    }

    const isCustomer = document.getElementById('isCustomer').checked;
    const isSupplier = document.getElementById('isSupplier').checked;

    if (!isCustomer && !isSupplier) {
        showError('يجب اختيار نوع الطرف (عميل أو مورد أو كلاهما)');
        return false;
    }

    if (data.creditLimit < 0) {
        showError('حد الائتمان يجب أن يكون أكبر من أو يساوي صفر');
        return false;
    }

    return true;
}

// حذف طرف
async function deleteParty(partyId) {
    if (!confirm('هل أنت متأكد من حذف هذا الطرف؟\nلا يمكن التراجع عن هذا الإجراء.')) {
        return;
    }

    try {
        const response = await fetch(`${API_BASE_URL}/parties/${partyId}`, {
            method: 'DELETE'
        });

        const result = await response.json();

        if (response.ok && result.success) {
            showSuccess('تم حذف الطرف بنجاح');
            await loadParties(); // إعادة تحميل القائمة
        } else {
            showError(result.message || 'حدث خطأ أثناء حذف الطرف');
        }

    } catch (error) {
        console.error('❌ خطأ في حذف الطرف:', error);
        showError('حدث خطأ في الاتصال بالخادم');
    }
}

// البحث والتصفية
function searchParties() {
    const searchTerm = document.getElementById('searchInput').value.toLowerCase();
    filterAndDisplay();
}

function filterParties() {
    filterAndDisplay();
}

function filterAndDisplay() {
    const searchTerm = document.getElementById('searchInput').value.toLowerCase();
    const typeFilter = document.getElementById('typeFilter').value;
    const statusFilter = document.getElementById('statusFilter').value;
    
    filteredParties = parties.filter(party => {
        // البحث النصي
        const matchesSearch = !searchTerm || 
            party.partyName.toLowerCase().includes(searchTerm) ||
            (party.partyCode && party.partyCode.toLowerCase().includes(searchTerm)) ||
            (party.phone && party.phone.includes(searchTerm));
        
        // تصفية النوع
        const matchesType = !typeFilter || 
            (typeFilter === 'customer' && party.isCustomer && !party.isSupplier) ||
            (typeFilter === 'supplier' && party.isSupplier && !party.isCustomer) ||
            (typeFilter === 'both' && party.isCustomer && party.isSupplier);
        
        // تصفية الحالة
        const matchesStatus = !statusFilter || 
            party.isActive.toString() === statusFilter;
        
        return matchesSearch && matchesType && matchesStatus;
    });
    
    displayParties(filteredParties);
}

// عرض كشف حساب
function showStatement(partyId) {
    const party = parties.find(p => p.partyId === partyId);
    if (!party) return;
    
    // محاكاة كشف حساب
    const statementContent = `
        <div class="text-center mb-4">
            <h4>كشف حساب - ${party.partyName}</h4>
            <p class="text-muted">من تاريخ: 01/01/2024 إلى تاريخ: ${new Date().toLocaleDateString('ar-YE')}</p>
        </div>
        
        <div class="row mb-3">
            <div class="col-md-6">
                <strong>اسم الطرف:</strong> ${party.partyName}<br>
                <strong>الكود:</strong> ${party.partyCode || '-'}<br>
                <strong>الهاتف:</strong> ${party.phone || '-'}
            </div>
            <div class="col-md-6">
                <strong>الرصيد الحالي:</strong> <span class="${party.currentBalance >= 0 ? 'text-success' : 'text-danger'}">${formatCurrency(party.currentBalance)}</span><br>
                <strong>حد الائتمان:</strong> ${formatCurrency(party.creditLimit)}<br>
                <strong>الرصيد المتاح:</strong> ${formatCurrency(party.creditLimit - Math.max(0, party.currentBalance))}
            </div>
        </div>
        
        <table class="table table-striped">
            <thead>
                <tr>
                    <th>التاريخ</th>
                    <th>البيان</th>
                    <th>مدين</th>
                    <th>دائن</th>
                    <th>الرصيد</th>
                </tr>
            </thead>
            <tbody>
                <tr>
                    <td>01/01/2024</td>
                    <td>رصيد افتتاحي</td>
                    <td>-</td>
                    <td>-</td>
                    <td>${formatCurrency(party.currentBalance)}</td>
                </tr>
                <tr class="text-muted">
                    <td colspan="5" class="text-center">لا توجد حركات أخرى</td>
                </tr>
            </tbody>
        </table>
    `;
    
    document.getElementById('statementContent').innerHTML = statementContent;
    const modal = new bootstrap.Modal(document.getElementById('statementModal'));
    modal.show();
}

// طباعة كشف حساب
function printStatement() {
    const content = document.getElementById('statementContent').innerHTML;
    const printWindow = window.open('', '_blank');
    printWindow.document.write(`
        <html>
        <head>
            <title>كشف حساب</title>
            <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
            <style>
                body { font-family: 'Arial', sans-serif; direction: rtl; }
                @media print { .no-print { display: none; } }
            </style>
        </head>
        <body>
            <div class="container">
                ${content}
            </div>
            <script>window.print();</script>
        </body>
        </html>
    `);
}

// طباعة قائمة العملاء والموردين
function printParties() {
    const printContent = `
        <div class="text-center mb-4">
            <h3>قائمة العملاء والموردين</h3>
            <p>تاريخ الطباعة: ${new Date().toLocaleDateString('ar-YE')}</p>
        </div>
        <table class="table table-bordered">
            <thead>
                <tr>
                    <th>الرقم</th>
                    <th>الاسم</th>
                    <th>النوع</th>
                    <th>الهاتف</th>
                    <th>الرصيد (ر.ي)</th>
                </tr>
            </thead>
            <tbody>
                ${filteredParties.map((party, index) => `
                    <tr>
                        <td>${index + 1}</td>
                        <td>${party.partyName}</td>
                        <td>${getPartyTypeText(party)}</td>
                        <td>${party.phone || '-'}</td>
                        <td>${formatCurrency(party.currentBalance)}</td>
                    </tr>
                `).join('')}
            </tbody>
        </table>
    `;
    
    const printWindow = window.open('', '_blank');
    printWindow.document.write(`
        <html>
        <head>
            <title>قائمة العملاء والموردين</title>
            <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
            <style>
                body { font-family: 'Arial', sans-serif; direction: rtl; }
                @media print { .no-print { display: none; } }
            </style>
        </head>
        <body>
            <div class="container">
                ${printContent}
            </div>
            <script>window.print();</script>
        </body>
        </html>
    `);
}

// عرض رسالة خطأ
function showError(message) {
    const alertDiv = document.createElement('div');
    alertDiv.className = 'alert alert-danger alert-dismissible fade show position-fixed';
    alertDiv.style.cssText = 'top: 20px; right: 20px; z-index: 9999; min-width: 300px;';
    alertDiv.innerHTML = `
        <i class="fas fa-exclamation-triangle me-2"></i>
        ${message}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    `;
    document.body.appendChild(alertDiv);

    setTimeout(() => {
        if (alertDiv.parentNode) {
            alertDiv.remove();
        }
    }, 5000);
}

// عرض رسالة نجاح
function showSuccess(message) {
    const alertDiv = document.createElement('div');
    alertDiv.className = 'alert alert-success alert-dismissible fade show position-fixed';
    alertDiv.style.cssText = 'top: 20px; right: 20px; z-index: 9999; min-width: 300px;';
    alertDiv.innerHTML = `
        <i class="fas fa-check-circle me-2"></i>
        ${message}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    `;
    document.body.appendChild(alertDiv);

    setTimeout(() => {
        if (alertDiv.parentNode) {
            alertDiv.remove();
        }
    }, 3000);
}

// عرض تحميل الحفظ
function showSaveLoading() {
    const saveBtn = document.querySelector('#partyModal .btn-primary');
    if (saveBtn) {
        saveBtn.disabled = true;
        saveBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>جاري الحفظ...';
    }
}

// إخفاء تحميل الحفظ
function hideSaveLoading() {
    const saveBtn = document.querySelector('#partyModal .btn-primary');
    if (saveBtn) {
        saveBtn.disabled = false;
        saveBtn.innerHTML = '<i class="fas fa-save me-2"></i>حفظ';
    }
}

// وظائف المساعدة والرسائل
function showError(message) {
    const alertDiv = document.createElement('div');
    alertDiv.className = 'alert alert-danger alert-dismissible fade show position-fixed';
    alertDiv.style.cssText = 'top: 20px; right: 20px; z-index: 9999; min-width: 300px;';
    alertDiv.innerHTML = `
        <i class="fas fa-exclamation-triangle me-2"></i>
        ${message}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    `;
    document.body.appendChild(alertDiv);

    // إزالة تلقائية بعد 5 ثوان
    setTimeout(() => {
        if (alertDiv.parentNode) {
            alertDiv.remove();
        }
    }, 5000);
}

function showSuccess(message) {
    const alertDiv = document.createElement('div');
    alertDiv.className = 'alert alert-success alert-dismissible fade show position-fixed';
    alertDiv.style.cssText = 'top: 20px; right: 20px; z-index: 9999; min-width: 300px;';
    alertDiv.innerHTML = `
        <i class="fas fa-check-circle me-2"></i>
        ${message}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    `;
    document.body.appendChild(alertDiv);

    // إزالة تلقائية بعد 3 ثوان
    setTimeout(() => {
        if (alertDiv.parentNode) {
            alertDiv.remove();
        }
    }, 3000);
}

function showSaveLoading() {
    const saveBtn = document.querySelector('#partyModal .btn-primary');
    if (saveBtn) {
        saveBtn.disabled = true;
        saveBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>جاري الحفظ...';
    }
}

function hideSaveLoading() {
    const saveBtn = document.querySelector('#partyModal .btn-primary');
    if (saveBtn) {
        saveBtn.disabled = false;
        saveBtn.innerHTML = 'حفظ';
    }
}

// وظيفة تسجيل الخروج
function logout() {
    if (confirm('هل أنت متأكد من تسجيل الخروج؟')) {
        localStorage.removeItem('isLoggedIn');
        window.location.href = 'login.html';
    }
}
