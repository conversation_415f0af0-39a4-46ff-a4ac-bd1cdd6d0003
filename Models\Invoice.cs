using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace ANWBakerySystem.Models
{
    /// <summary>
    /// نموذج الفوترة الموحدة - يدعم جميع أنواع الفواتير للعملاء والموردين
    /// Unified Invoice Model - Supports all invoice types for customers and suppliers
    /// </summary>
    public class Invoice
    {
        [Key]
        public int InvoiceId { get; set; }

        [Required]
        [StringLength(50)]
        [Display(Name = "رقم الفاتورة")]
        public string InvoiceNumber { get; set; } = string.Empty;

        [Required]
        [Display(Name = "نوع الفاتورة")]
        public InvoiceType InvoiceType { get; set; }

        [Required]
        [Display(Name = "تاريخ الفاتورة")]
        public DateTime InvoiceDate { get; set; } = DateTime.Now;

        [Display(Name = "تاريخ الاستحقاق")]
        public DateTime? DueDate { get; set; }

        /// <summary>
        /// الطرف - يمكن أن يكون عميل أو مورد حسب نوع الفاتورة
        /// Party - Can be customer or supplier based on invoice type
        /// </summary>
        [Display(Name = "الطرف")]
        public int? PartyId { get; set; }

        [StringLength(200)]
        [Display(Name = "اسم الطرف")]
        public string? PartyName { get; set; }

        [StringLength(500)]
        [Display(Name = "عنوان الطرف")]
        public string? PartyAddress { get; set; }

        [StringLength(100)]
        [Display(Name = "هاتف الطرف")]
        public string? PartyPhone { get; set; }

        /// <summary>
        /// إجمالي الفاتورة قبل الخصم والضريبة بالريال اليمني
        /// Invoice subtotal before discount and tax in Yemeni Rial
        /// </summary>
        [Display(Name = "الإجمالي الفرعي (ر.ي)")]
        [Column(TypeName = "decimal(18,3)")]
        public decimal SubTotal { get; set; } = 0;

        /// <summary>
        /// مبلغ الخصم بالريال اليمني
        /// Discount amount in Yemeni Rial
        /// </summary>
        [Display(Name = "الخصم (ر.ي)")]
        [Column(TypeName = "decimal(18,3)")]
        public decimal DiscountAmount { get; set; } = 0;

        [Display(Name = "نسبة الخصم")]
        [Column(TypeName = "decimal(5,2)")]
        public decimal DiscountPercentage { get; set; } = 0;

        /// <summary>
        /// مبلغ الضريبة بالريال اليمني
        /// Tax amount in Yemeni Rial
        /// </summary>
        [Display(Name = "الضريبة (ر.ي)")]
        [Column(TypeName = "decimal(18,3)")]
        public decimal TaxAmount { get; set; } = 0;

        [Display(Name = "نسبة الضريبة")]
        [Column(TypeName = "decimal(5,2)")]
        public decimal TaxPercentage { get; set; } = 0;

        /// <summary>
        /// الإجمالي النهائي بالريال اليمني
        /// Final total in Yemeni Rial
        /// </summary>
        [Display(Name = "الإجمالي النهائي (ر.ي)")]
        [Column(TypeName = "decimal(18,3)")]
        public decimal TotalAmount { get; set; } = 0;

        /// <summary>
        /// المبلغ المدفوع بالريال اليمني
        /// Paid amount in Yemeni Rial
        /// </summary>
        [Display(Name = "المدفوع (ر.ي)")]
        [Column(TypeName = "decimal(18,3)")]
        public decimal PaidAmount { get; set; } = 0;

        /// <summary>
        /// المبلغ المتبقي بالريال اليمني
        /// Remaining amount in Yemeni Rial
        /// </summary>
        [Display(Name = "المتبقي (ر.ي)")]
        [Column(TypeName = "decimal(18,3)")]
        public decimal RemainingAmount { get; set; } = 0;

        [Required]
        [Display(Name = "طريقة الدفع")]
        public PaymentMethod PaymentMethod { get; set; }

        [Display(Name = "حالة الفاتورة")]
        public InvoiceStatus Status { get; set; } = InvoiceStatus.Draft;

        [StringLength(500)]
        [Display(Name = "ملاحظات")]
        public string? Notes { get; set; }

        [StringLength(100)]
        [Display(Name = "رقم المرجع")]
        public string? ReferenceNumber { get; set; }

        [Display(Name = "فاتورة بيع سريع")]
        public bool IsQuickSale { get; set; } = false;

        [Display(Name = "مطبوعة")]
        public bool IsPrinted { get; set; } = false;

        [Display(Name = "تاريخ الطباعة")]
        public DateTime? PrintedAt { get; set; }

        [Display(Name = "عدد مرات الطباعة")]
        public int PrintCount { get; set; } = 0;

        [Display(Name = "تاريخ الإنشاء")]
        public DateTime CreatedAt { get; set; } = DateTime.Now;

        [Display(Name = "تاريخ التحديث")]
        public DateTime? UpdatedAt { get; set; }

        [Display(Name = "منشئ بواسطة")]
        public int CreatedBy { get; set; }

        [Display(Name = "محدث بواسطة")]
        public int? UpdatedBy { get; set; }

        // Navigation properties
        public virtual Party? Party { get; set; }
        public virtual User Creator { get; set; } = null!;
        public virtual User? Updater { get; set; }
        public virtual ICollection<InvoiceItem> InvoiceItems { get; set; } = new List<InvoiceItem>();
        public virtual ICollection<PartyTransaction> Transactions { get; set; } = new List<PartyTransaction>();

        /// <summary>
        /// حساب الإجماليات تلقائياً
        /// Calculate totals automatically
        /// </summary>
        public void CalculateTotals()
        {
            SubTotal = InvoiceItems.Sum(item => item.TotalAmount);
            
            if (DiscountPercentage > 0)
                DiscountAmount = SubTotal * (DiscountPercentage / 100);
            
            var afterDiscount = SubTotal - DiscountAmount;
            
            if (TaxPercentage > 0)
                TaxAmount = afterDiscount * (TaxPercentage / 100);
            
            TotalAmount = afterDiscount + TaxAmount;
            RemainingAmount = TotalAmount - PaidAmount;
        }
    }

    public enum InvoiceType
    {
        [Display(Name = "فاتورة مبيعات")]
        Sales = 1,
        [Display(Name = "فاتورة مشتريات")]
        Purchase = 2,
        [Display(Name = "مرتجع مبيعات")]
        SalesReturn = 3,
        [Display(Name = "مرتجع مشتريات")]
        PurchaseReturn = 4,
        [Display(Name = "بيع سريع")]
        QuickSale = 5
    }

    public enum PaymentMethod
    {
        [Display(Name = "نقدي")]
        Cash = 1,
        [Display(Name = "آجل")]
        Credit = 2,
        [Display(Name = "شيك")]
        Check = 3,
        [Display(Name = "تحويل بنكي")]
        BankTransfer = 4,
        [Display(Name = "بطاقة ائتمان")]
        CreditCard = 5,
        [Display(Name = "مختلط")]
        Mixed = 6
    }

    public enum InvoiceStatus
    {
        [Display(Name = "مسودة")]
        Draft = 1,
        [Display(Name = "معتمدة")]
        Approved = 2,
        [Display(Name = "مدفوعة")]
        Paid = 3,
        [Display(Name = "مدفوعة جزئياً")]
        PartiallyPaid = 4,
        [Display(Name = "ملغية")]
        Cancelled = 5,
        [Display(Name = "مرتجعة")]
        Returned = 6
    }
}
