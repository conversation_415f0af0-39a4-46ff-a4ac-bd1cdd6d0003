// إدارة الموظفين والاستحقاقات
let employees = [];
let employeeAccruals = [];
let employeeTransactions = [];
let selectedEmployee = null;

// تحميل الصفحة
document.addEventListener('DOMContentLoaded', function() {
    console.log('🔄 تحميل صفحة إدارة الموظفين والاستحقاقات...');
    initializePage();
});

// تهيئة الصفحة
async function initializePage() {
    try {
        loadEmployees();
        loadEmployeeAccruals();
        loadEmployeeTransactions();
        updateStatistics();
        populateEmployeeDropdowns();
        checkMissingAccruals();

        console.log('✅ تم تحميل صفحة الموظفين بنجاح');
    } catch (error) {
        console.error('❌ خطأ في تحميل صفحة الموظفين:', error);
        showError('خطأ في تحميل البيانات. يرجى إعادة تحميل الصفحة.');
    }
}

// تحميل الموظفين
function loadEmployees() {
    console.log('🔄 تحميل بيانات الموظفين...');

    // بيانات تجريبية محدثة
    employees = [
        {
            employeeId: 1,
            employeeName: 'أحمد محمد علي',
            position: 'manager',
            department: 'administration',
            baseSalary: 150000.000,
            transportAllowance: 15000.000,
            housingAllowance: 25000.000,
            foodAllowance: 10000.000,
            hireDate: '2023-01-15',
            phone: '777123456',
            email: '<EMAIL>',
            address: 'صنعاء - شارع الزبيري',
            isActive: true,
            createdDate: '2023-01-15',
            // الحقول الجديدة
            totalAccrued: 0,      // إجمالي المستحق
            totalPaid: 0,         // إجمالي المدفوع
            currentBalance: 0     // الرصيد الحالي
        },
        {
            employeeId: 2,
            employeeName: 'فاطمة أحمد سالم',
            position: 'cashier',
            department: 'sales',
            baseSalary: 80000.000,
            transportAllowance: 10000.000,
            housingAllowance: 0,
            foodAllowance: 8000.000,
            hireDate: '2023-03-01',
            phone: '777654321',
            email: '<EMAIL>',
            address: 'صنعاء - شارع الستين',
            isActive: true,
            createdDate: '2023-03-01',
            totalAccrued: 0,
            totalPaid: 0,
            currentBalance: 0
        },
        {
            employeeId: 3,
            employeeName: 'محمد سعد الدين',
            position: 'baker',
            department: 'production',
            baseSalary: 90000.000,
            transportAllowance: 12000.000,
            housingAllowance: 15000.000,
            foodAllowance: 8000.000,
            hireDate: '2023-02-10',
            phone: '777987654',
            email: '<EMAIL>',
            address: 'صنعاء - شارع الحصبة',
            isActive: true,
            createdDate: '2023-02-10',
            totalAccrued: 0,
            totalPaid: 0,
            currentBalance: 0
        },
        {
            employeeId: 4,
            employeeName: 'عائشة علي حسن',
            position: 'sales',
            department: 'sales',
            baseSalary: 70000.000,
            transportAllowance: 8000.000,
            housingAllowance: 0,
            foodAllowance: 6000.000,
            hireDate: '2023-04-20',
            phone: '777456789',
            email: '<EMAIL>',
            address: 'صنعاء - شارع الثورة',
            isActive: true,
            createdDate: '2023-04-20',
            totalAccrued: 0,
            totalPaid: 0,
            currentBalance: 0
        }
    ];

    // حساب الأرصدة من الاستحقاقات والمعاملات
    calculateEmployeeBalances();
    displayEmployees();
    console.log('📦 تم تحميل الموظفين:', employees.length);
}

// تحميل استحقاقات الموظفين
function loadEmployeeAccruals() {
    console.log('🔄 تحميل استحقاقات الموظفين...');

    // بيانات تجريبية للاستحقاقات
    employeeAccruals = [
        {
            accrualId: 1,
            employeeId: 1,
            month: '2024-01',
            baseSalary: 150000.000,
            allowances: 50000.000,
            penalties: 0,
            penaltyReason: '',
            netAmount: 200000.000,
            isProcessed: true,
            processedDate: '2024-01-31',
            createdBy: 'المدير'
        },
        {
            accrualId: 2,
            employeeId: 2,
            month: '2024-01',
            baseSalary: 80000.000,
            allowances: 18000.000,
            penalties: 5000.000,
            penaltyReason: 'تأخير في العمل',
            netAmount: 93000.000,
            isProcessed: true,
            processedDate: '2024-01-31',
            createdBy: 'المدير'
        },
        {
            accrualId: 3,
            employeeId: 1,
            month: '2024-02',
            baseSalary: 150000.000,
            allowances: 50000.000,
            penalties: 10000.000,
            penaltyReason: 'خصم إداري',
            netAmount: 190000.000,
            isProcessed: true,
            processedDate: '2024-02-29',
            createdBy: 'المدير'
        }
    ];

    console.log('📦 تم تحميل الاستحقاقات:', employeeAccruals.length);
}

// تحميل معاملات الموظفين
function loadEmployeeTransactions() {
    console.log('🔄 تحميل معاملات الموظفين...');

    // بيانات تجريبية للمعاملات
    employeeTransactions = [
        {
            transactionId: 1,
            employeeId: 1,
            type: 'accrual',
            amount: 200000.000,
            voucherNumber: '',
            description: 'استحقاق شهر يناير 2024',
            date: '2024-01-31'
        },
        {
            transactionId: 2,
            employeeId: 1,
            type: 'payment',
            amount: -150000.000,
            voucherNumber: 'PAY001',
            description: 'دفع جزء من راتب يناير',
            date: '2024-02-05'
        },
        {
            transactionId: 3,
            employeeId: 2,
            type: 'accrual',
            amount: 93000.000,
            voucherNumber: '',
            description: 'استحقاق شهر يناير 2024',
            date: '2024-01-31'
        },
        {
            transactionId: 4,
            employeeId: 1,
            type: 'accrual',
            amount: 190000.000,
            voucherNumber: '',
            description: 'استحقاق شهر فبراير 2024',
            date: '2024-02-29'
        },
        {
            transactionId: 5,
            employeeId: 1,
            type: 'receipt',
            amount: 5000.000,
            voucherNumber: 'REC005',
            description: 'استرداد سلفة',
            date: '2024-03-01'
        }
    ];

    console.log('📦 تم تحميل المعاملات:', employeeTransactions.length);
}

// حساب أرصدة الموظفين
function calculateEmployeeBalances() {
    employees.forEach(employee => {
        // حساب إجمالي الاستحقاقات
        const accruals = employeeAccruals.filter(a => a.employeeId === employee.employeeId && a.isProcessed);
        employee.totalAccrued = accruals.reduce((sum, a) => sum + a.netAmount, 0);

        // حساب إجمالي المدفوع
        const payments = employeeTransactions.filter(t => t.employeeId === employee.employeeId && t.type === 'payment');
        employee.totalPaid = Math.abs(payments.reduce((sum, t) => sum + t.amount, 0));

        // حساب الرصيد الحالي
        const allTransactions = employeeTransactions.filter(t => t.employeeId === employee.employeeId);
        employee.currentBalance = allTransactions.reduce((sum, t) => sum + t.amount, 0);
    });
}

// عرض الموظفين
function displayEmployees() {
    const tbody = document.getElementById('employeesTableBody');

    if (employees.length === 0) {
        tbody.innerHTML = `
            <tr>
                <td colspan="10" class="text-center text-muted">
                    <i class="fas fa-users fa-2x mb-2"></i>
                    <p>لا يوجد موظفين</p>
                </td>
            </tr>
        `;
        return;
    }

    tbody.innerHTML = employees.map(employee => {
        const totalSalary = employee.baseSalary + employee.transportAllowance + employee.housingAllowance + employee.foodAllowance;
        const lastAccrual = getLastAccrual(employee.employeeId);
        const balanceColor = employee.currentBalance >= 0 ? 'text-success' : 'text-danger';

        return `
            <tr onclick="selectEmployee(${employee.employeeId})" style="cursor: pointer;">
                <td>
                    <div>
                        <strong>${employee.employeeName}</strong>
                        <br>
                        <small class="text-muted">${employee.phone}</small>
                    </div>
                </td>
                <td>
                    <span class="badge ${getPositionBadge(employee.position)}">
                        ${getPositionText(employee.position)}
                    </span>
                </td>
                <td>
                    <span class="badge bg-secondary">
                        ${getDepartmentText(employee.department)}
                    </span>
                </td>
                <td>
                    <strong>${formatCurrency(totalSalary)}</strong>
                    <br>
                    <small class="text-muted">
                        أساسي: ${formatCurrency(employee.baseSalary)}
                        <br>بدلات: ${formatCurrency(totalSalary - employee.baseSalary)}
                    </small>
                </td>
                <td>
                    <strong class="text-primary">${formatCurrency(employee.totalAccrued)}</strong>
                </td>
                <td>
                    <strong class="${balanceColor}">${formatCurrency(employee.currentBalance)}</strong>
                </td>
                <td>
                    ${lastAccrual ? `
                        <span class="badge bg-info">${lastAccrual.month}</span>
                        <br>
                        <small class="text-muted">${formatCurrency(lastAccrual.netAmount)}</small>
                    ` : '<span class="text-muted">لا يوجد</span>'}
                </td>
                <td>${employee.hireDate}</td>
                <td>
                    <span class="badge ${employee.isActive ? 'bg-success' : 'bg-danger'}">
                        ${employee.isActive ? 'نشط' : 'غير نشط'}
                    </span>
                </td>
                <td>
                    <div class="btn-group btn-group-sm">
                        <button class="btn btn-outline-primary" onclick="editEmployee(${employee.employeeId})" title="تعديل">
                            <i class="fas fa-edit"></i>
                        </button>
                        <button class="btn btn-outline-info" onclick="viewEmployeeStatement(${employee.employeeId})" title="كشف حساب">
                            <i class="fas fa-file-alt"></i>
                        </button>
                        <button class="btn btn-outline-success" onclick="showEmployeeAccrualModal(${employee.employeeId})" title="قيد استحقاق">
                            <i class="fas fa-money-bill-wave"></i>
                        </button>
                        <button class="btn btn-outline-warning" onclick="createPaymentVoucher(${employee.employeeId})" title="سند صرف">
                            <i class="fas fa-hand-holding-usd"></i>
                        </button>
                    </div>
                </td>
            </tr>
        `;
    }).join('');
}

// الحصول على آخر استحقاق للموظف
function getLastAccrual(employeeId) {
    const accruals = employeeAccruals.filter(a => a.employeeId === employeeId && a.isProcessed);
    if (accruals.length === 0) return null;

    return accruals.sort((a, b) => new Date(b.month) - new Date(a.month))[0];
}

// تحديث الإحصائيات
function updateStatistics() {
    const totalEmployees = employees.length;
    const activeEmployees = employees.filter(e => e.isActive).length;
    const totalAccrued = employees.reduce((sum, e) => sum + e.totalAccrued, 0);
    const totalPaid = employees.reduce((sum, e) => sum + e.totalPaid, 0);

    document.getElementById('totalEmployees').textContent = totalEmployees;
    document.getElementById('activeEmployees').textContent = activeEmployees;
    document.getElementById('totalAccrued').textContent = formatCurrency(totalAccrued);
    document.getElementById('totalPaid').textContent = formatCurrency(totalPaid);
}

// ملء قوائم الموظفين
function populateEmployeeDropdowns() {
    const dropdowns = ['accrualEmployee', 'statusEmployee'];

    dropdowns.forEach(dropdownId => {
        const dropdown = document.getElementById(dropdownId);
        if (dropdown) {
            dropdown.innerHTML = dropdownId === 'accrualEmployee' ?
                '<option value="">-- جميع الموظفين النشطين --</option>' :
                '<option value="">-- جميع الموظفين --</option>';

            employees.forEach(employee => {
                if (dropdownId === 'accrualEmployee' && !employee.isActive) return;

                const option = document.createElement('option');
                option.value = employee.employeeId;
                option.textContent = employee.employeeName;
                dropdown.appendChild(option);
            });
        }
    });
}

// فحص الاستحقاقات المفقودة
function checkMissingAccruals() {
    const missingAccruals = [];
    const currentDate = new Date();

    employees.filter(e => e.isActive).forEach(employee => {
        const hireDate = new Date(employee.hireDate);
        let checkDate = new Date(hireDate.getFullYear(), hireDate.getMonth(), 1);

        while (checkDate < currentDate) {
            const monthKey = `${checkDate.getFullYear()}-${(checkDate.getMonth() + 1).toString().padStart(2, '0')}`;
            const hasAccrual = employeeAccruals.some(a =>
                a.employeeId === employee.employeeId &&
                a.month === monthKey &&
                a.isProcessed
            );

            if (!hasAccrual) {
                missingAccruals.push({
                    employeeId: employee.employeeId,
                    employeeName: employee.employeeName,
                    month: monthKey
                });
            }

            checkDate.setMonth(checkDate.getMonth() + 1);
        }
    });

    // عرض التنبيه
    const alertDiv = document.getElementById('missingAccrualsAlert');
    const countSpan = document.getElementById('missingCount');

    if (missingAccruals.length > 0) {
        countSpan.textContent = missingAccruals.length;
        alertDiv.style.display = 'block';
    } else {
        alertDiv.style.display = 'none';
    }

    return missingAccruals;
}

// ========== نوافذ الاستحقاقات ==========

// إظهار نافذة قيد الاستحقاقات
function showAccrualModal() {
    // تعيين الشهر الحالي
    const currentMonth = new Date().toISOString().slice(0, 7);
    document.getElementById('accrualMonth').value = currentMonth;

    // إعادة تعيين النموذج
    document.getElementById('accrualForm').reset();
    document.getElementById('accrualMonth').value = currentMonth;
    document.getElementById('penaltyAmount').value = 0;

    // تحديث البيانات
    loadAccrualData();

    const modal = new bootstrap.Modal(document.getElementById('accrualModal'));
    modal.show();
}

// تحميل بيانات الاستحقاق
function loadAccrualData() {
    const month = document.getElementById('accrualMonth').value;
    const selectedEmployeeId = document.getElementById('accrualEmployee').value;

    if (!month) return;

    const tbody = document.getElementById('accrualTableBody');
    const employeesToProcess = selectedEmployeeId ?
        employees.filter(e => e.employeeId == selectedEmployeeId && e.isActive) :
        employees.filter(e => e.isActive);

    if (employeesToProcess.length === 0) {
        tbody.innerHTML = '<tr><td colspan="6" class="text-center text-muted">لا يوجد موظفين للمعالجة</td></tr>';
        return;
    }

    tbody.innerHTML = employeesToProcess.map(employee => {
        const totalAllowances = employee.transportAllowance + employee.housingAllowance + employee.foodAllowance;
        const penaltyAmount = parseFloat(document.getElementById('penaltyAmount').value) || 0;
        const netAmount = employee.baseSalary + totalAllowances - penaltyAmount;

        // فحص إذا كان الاستحقاق موجود مسبقاً
        const existingAccrual = employeeAccruals.find(a =>
            a.employeeId === employee.employeeId &&
            a.month === month &&
            a.isProcessed
        );

        const statusBadge = existingAccrual ?
            '<span class="badge bg-success">تم القيد</span>' :
            '<span class="badge bg-warning">في الانتظار</span>';

        return `
            <tr>
                <td>${employee.employeeName}</td>
                <td>${formatCurrency(employee.baseSalary)}</td>
                <td>${formatCurrency(totalAllowances)}</td>
                <td>${formatCurrency(penaltyAmount)}</td>
                <td><strong>${formatCurrency(netAmount)}</strong></td>
                <td>${statusBadge}</td>
            </tr>
        `;
    }).join('');

    // تحديث الملخص
    updateAccrualSummary(employeesToProcess, penaltyAmount);
}

// تحديث ملخص الاستحقاق
function updateAccrualSummary(employeesToProcess, penaltyAmount) {
    const totalBaseSalary = employeesToProcess.reduce((sum, e) => sum + e.baseSalary, 0);
    const totalAllowances = employeesToProcess.reduce((sum, e) =>
        sum + e.transportAllowance + e.housingAllowance + e.foodAllowance, 0);
    const totalPenalties = penaltyAmount * employeesToProcess.length;
    const totalNet = totalBaseSalary + totalAllowances - totalPenalties;

    document.getElementById('accrualSummary').innerHTML = `
        <div class="row">
            <div class="col-md-3">
                <strong>عدد الموظفين:</strong> ${employeesToProcess.length}
            </div>
            <div class="col-md-3">
                <strong>إجمالي الرواتب:</strong> ${formatCurrency(totalBaseSalary)}
            </div>
            <div class="col-md-3">
                <strong>إجمالي البدلات:</strong> ${formatCurrency(totalAllowances)}
            </div>
            <div class="col-md-3">
                <strong>صافي الاستحقاق:</strong> <span class="text-success">${formatCurrency(totalNet)}</span>
            </div>
        </div>
    `;
}

// تحميل بيانات الموظف للاستحقاق
function loadEmployeeAccrualData() {
    loadAccrualData();
}

// حساب صافي الاستحقاق
function calculateNetAccrual() {
    loadAccrualData();
}

// معالجة الاستحقاقات
function processAccruals() {
    const form = document.getElementById('accrualForm');
    if (!form.checkValidity()) {
        form.reportValidity();
        return;
    }

    const month = document.getElementById('accrualMonth').value;
    const selectedEmployeeId = document.getElementById('accrualEmployee').value;
    const penaltyAmount = parseFloat(document.getElementById('penaltyAmount').value) || 0;
    const penaltyReason = document.getElementById('penaltyReason').value.trim();

    const employeesToProcess = selectedEmployeeId ?
        employees.filter(e => e.employeeId == selectedEmployeeId && e.isActive) :
        employees.filter(e => e.isActive);

    if (employeesToProcess.length === 0) {
        showError('لا يوجد موظفين للمعالجة');
        return;
    }

    // فحص الاستحقاقات الموجودة
    const existingAccruals = employeesToProcess.filter(employee =>
        employeeAccruals.some(a =>
            a.employeeId === employee.employeeId &&
            a.month === month &&
            a.isProcessed
        )
    );

    if (existingAccruals.length > 0) {
        const names = existingAccruals.map(e => e.employeeName).join(', ');
        if (!confirm(`تم قيد استحقاق شهر ${month} مسبقاً للموظفين: ${names}\n\nهل تريد المتابعة لباقي الموظفين؟`)) {
            return;
        }
    }

    let processedCount = 0;

    employeesToProcess.forEach(employee => {
        // تخطي الموظفين الذين تم قيد استحقاقهم مسبقاً
        const hasExistingAccrual = employeeAccruals.some(a =>
            a.employeeId === employee.employeeId &&
            a.month === month &&
            a.isProcessed
        );

        if (hasExistingAccrual) return;

        const totalAllowances = employee.transportAllowance + employee.housingAllowance + employee.foodAllowance;
        const netAmount = employee.baseSalary + totalAllowances - penaltyAmount;

        // إضافة الاستحقاق
        const accrualId = Math.max(...employeeAccruals.map(a => a.accrualId), 0) + 1;
        const newAccrual = {
            accrualId: accrualId,
            employeeId: employee.employeeId,
            month: month,
            baseSalary: employee.baseSalary,
            allowances: totalAllowances,
            penalties: penaltyAmount,
            penaltyReason: penaltyReason,
            netAmount: netAmount,
            isProcessed: true,
            processedDate: new Date().toISOString().split('T')[0],
            createdBy: 'المستخدم الحالي'
        };

        employeeAccruals.push(newAccrual);

        // إضافة معاملة الاستحقاق
        const transactionId = Math.max(...employeeTransactions.map(t => t.transactionId), 0) + 1;
        const newTransaction = {
            transactionId: transactionId,
            employeeId: employee.employeeId,
            type: 'accrual',
            amount: netAmount,
            voucherNumber: '',
            description: `استحقاق شهر ${month}${penaltyReason ? ` - ${penaltyReason}` : ''}`,
            date: new Date().toISOString().split('T')[0]
        };

        employeeTransactions.push(newTransaction);
        processedCount++;
    });

    if (processedCount > 0) {
        // إعادة حساب الأرصدة
        calculateEmployeeBalances();

        // تحديث العرض
        displayEmployees();
        updateStatistics();
        checkMissingAccruals();

        showSuccess(`تم قيد استحقاقات ${processedCount} موظف لشهر ${month} بنجاح`);
        bootstrap.Modal.getInstance(document.getElementById('accrualModal')).hide();
    } else {
        showError('لم يتم قيد أي استحقاقات جديدة');
    }
}

// إظهار نافذة حالة الاستحقاقات
function showAccrualStatusModal() {
    // تعيين التواريخ الافتراضية
    const currentDate = new Date();
    const fromDate = new Date(currentDate.getFullYear(), 0, 1); // بداية السنة

    document.getElementById('statusFromDate').value = fromDate.toISOString().slice(0, 7);
    document.getElementById('statusToDate').value = currentDate.toISOString().slice(0, 7);

    loadAccrualStatus();

    const modal = new bootstrap.Modal(document.getElementById('accrualStatusModal'));
    modal.show();
}

// تحميل حالة الاستحقاقات
function loadAccrualStatus() {
    const employeeId = document.getElementById('statusEmployee').value;
    const fromDate = document.getElementById('statusFromDate').value;
    const toDate = document.getElementById('statusToDate').value;

    if (!fromDate || !toDate) {
        document.getElementById('accrualStatusContent').innerHTML =
            '<div class="alert alert-warning">يرجى تحديد الفترة الزمنية</div>';
        return;
    }

    const employeesToShow = employeeId ?
        employees.filter(e => e.employeeId == employeeId) :
        employees.filter(e => e.isActive);

    let html = `
        <div class="table-responsive">
            <table class="table table-bordered table-striped">
                <thead class="table-dark">
                    <tr>
                        <th>الموظف</th>
    `;

    // إنشاء أعمدة الشهور
    const months = [];
    let currentMonth = new Date(fromDate + '-01');
    const endMonth = new Date(toDate + '-01');

    while (currentMonth <= endMonth) {
        const monthKey = `${currentMonth.getFullYear()}-${(currentMonth.getMonth() + 1).toString().padStart(2, '0')}`;
        months.push(monthKey);
        html += `<th class="text-center">${monthKey}</th>`;
        currentMonth.setMonth(currentMonth.getMonth() + 1);
    }

    html += `
                        <th>المجموع</th>
                    </tr>
                </thead>
                <tbody>
    `;

    employeesToShow.forEach(employee => {
        html += `<tr><td><strong>${employee.employeeName}</strong></td>`;

        let totalAccrued = 0;
        months.forEach(month => {
            const accrual = employeeAccruals.find(a =>
                a.employeeId === employee.employeeId &&
                a.month === month &&
                a.isProcessed
            );

            if (accrual) {
                html += `<td class="text-center">
                    <span class="badge bg-success" title="${formatCurrency(accrual.netAmount)}">✓</span>
                </td>`;
                totalAccrued += accrual.netAmount;
            } else {
                // فحص إذا كان الموظف موظف في هذا الشهر
                const hireDate = new Date(employee.hireDate);
                const monthDate = new Date(month + '-01');

                if (monthDate >= hireDate) {
                    html += `<td class="text-center">
                        <span class="badge bg-danger" title="لم يتم القيد">✗</span>
                    </td>`;
                } else {
                    html += `<td class="text-center">
                        <span class="text-muted">-</span>
                    </td>`;
                }
            }
        });

        html += `<td class="text-center"><strong>${formatCurrency(totalAccrued)}</strong></td></tr>`;
    });

    html += `
                </tbody>
            </table>
        </div>

        <div class="row mt-3">
            <div class="col-12">
                <div class="alert alert-info">
                    <strong>مفتاح الرموز:</strong>
                    <span class="badge bg-success me-2">✓</span> تم القيد
                    <span class="badge bg-danger me-2">✗</span> لم يتم القيد
                    <span class="text-muted">-</span> لم يكن موظفاً بعد
                </div>
            </div>
        </div>
    `;

    document.getElementById('accrualStatusContent').innerHTML = html;
}

// إظهار تقرير الاستحقاقات المفقودة
function showMissingAccrualsReport() {
    const missingAccruals = checkMissingAccruals();

    if (missingAccruals.length === 0) {
        document.getElementById('missingAccrualsContent').innerHTML =
            '<div class="alert alert-success"><i class="fas fa-check-circle me-2"></i>جميع الاستحقاقات محدثة!</div>';
    } else {
        // تجميع حسب الموظف
        const groupedByEmployee = {};
        missingAccruals.forEach(item => {
            if (!groupedByEmployee[item.employeeId]) {
                groupedByEmployee[item.employeeId] = {
                    employeeName: item.employeeName,
                    months: []
                };
            }
            groupedByEmployee[item.employeeId].months.push(item.month);
        });

        let html = `
            <div class="alert alert-warning">
                <i class="fas fa-exclamation-triangle me-2"></i>
                يوجد <strong>${missingAccruals.length}</strong> استحقاق شهري لم يتم قيده
            </div>

            <div class="table-responsive">
                <table class="table table-striped">
                    <thead class="table-dark">
                        <tr>
                            <th>الموظف</th>
                            <th>الشهور المفقودة</th>
                            <th>عدد الشهور</th>
                            <th>إجراءات</th>
                        </tr>
                    </thead>
                    <tbody>
        `;

        Object.values(groupedByEmployee).forEach(employee => {
            html += `
                <tr>
                    <td><strong>${employee.employeeName}</strong></td>
                    <td>
                        ${employee.months.map(month =>
                            `<span class="badge bg-warning me-1">${month}</span>`
                        ).join('')}
                    </td>
                    <td><span class="badge bg-danger">${employee.months.length}</span></td>
                    <td>
                        <button class="btn btn-sm btn-primary" onclick="processMissingAccrualsForEmployee(${Object.keys(groupedByEmployee).find(id => groupedByEmployee[id] === employee)}, '${employee.employeeName}')">
                            <i class="fas fa-magic"></i> قيد الكل
                        </button>
                    </td>
                </tr>
            `;
        });

        html += `
                    </tbody>
                </table>
            </div>
        `;

        document.getElementById('missingAccrualsContent').innerHTML = html;
    }

    const modal = new bootstrap.Modal(document.getElementById('missingAccrualsModal'));
    modal.show();
}

// قيد الاستحقاقات المفقودة لموظف محدد
function processMissingAccrualsForEmployee(employeeId, employeeName) {
    if (!confirm(`هل أنت متأكد من قيد جميع الاستحقاقات المفقودة للموظف: ${employeeName}؟\nسيتم قيدها بدون جزاءات.`)) {
        return;
    }

    const employee = employees.find(e => e.employeeId == employeeId);
    if (!employee) {
        showError('الموظف غير موجود');
        return;
    }

    // الحصول على الاستحقاقات المفقودة لهذا الموظف فقط
    const missingAccruals = [];
    const currentDate = new Date();
    const hireDate = new Date(employee.hireDate);
    let checkDate = new Date(hireDate.getFullYear(), hireDate.getMonth(), 1);

    while (checkDate < currentDate) {
        const monthKey = `${checkDate.getFullYear()}-${(checkDate.getMonth() + 1).toString().padStart(2, '0')}`;
        const hasAccrual = employeeAccruals.some(a =>
            a.employeeId === employee.employeeId &&
            a.month === monthKey &&
            a.isProcessed
        );

        if (!hasAccrual) {
            missingAccruals.push({
                employeeId: employee.employeeId,
                employeeName: employee.employeeName,
                month: monthKey
            });
        }

        checkDate.setMonth(checkDate.getMonth() + 1);
    }

    if (missingAccruals.length === 0) {
        showSuccess(`جميع استحقاقات الموظف ${employeeName} محدثة!`);
        return;
    }

    let processedCount = 0;

    missingAccruals.forEach(item => {
        const totalAllowances = employee.transportAllowance + employee.housingAllowance + employee.foodAllowance;
        const netAmount = employee.baseSalary + totalAllowances;

        // إضافة الاستحقاق
        const accrualId = Math.max(...employeeAccruals.map(a => a.accrualId), 0) + 1;
        const newAccrual = {
            accrualId: accrualId,
            employeeId: employee.employeeId,
            month: item.month,
            baseSalary: employee.baseSalary,
            allowances: totalAllowances,
            penalties: 0,
            penaltyReason: 'قيد تلقائي للاستحقاقات المفقودة',
            netAmount: netAmount,
            isProcessed: true,
            processedDate: new Date().toISOString().split('T')[0],
            createdBy: 'النظام التلقائي'
        };

        employeeAccruals.push(newAccrual);

        // إضافة معاملة الاستحقاق
        const transactionId = Math.max(...employeeTransactions.map(t => t.transactionId), 0) + 1;
        const newTransaction = {
            transactionId: transactionId,
            employeeId: employee.employeeId,
            type: 'accrual',
            amount: netAmount,
            voucherNumber: '',
            description: `استحقاق شهر ${item.month} - قيد تلقائي`,
            date: new Date().toISOString().split('T')[0]
        };

        employeeTransactions.push(newTransaction);
        processedCount++;
    });

    if (processedCount > 0) {
        // إعادة حساب الأرصدة
        calculateEmployeeBalances();

        // تحديث العرض
        displayEmployees();
        updateStatistics();
        checkMissingAccruals();

        showSuccess(`تم قيد ${processedCount} استحقاق مفقود للموظف ${employeeName} بنجاح`);

        // إعادة تحميل تقرير الاستحقاقات المفقودة
        showMissingAccrualsReport();
    }
}

// قيد جميع الاستحقاقات المفقودة
function processAllMissingAccruals() {
    if (!confirm('هل أنت متأكد من قيد جميع الاستحقاقات المفقودة؟\nسيتم قيدها بدون جزاءات.')) {
        return;
    }

    const missingAccruals = checkMissingAccruals();
    let processedCount = 0;

    missingAccruals.forEach(item => {
        const employee = employees.find(e => e.employeeId === item.employeeId);
        if (!employee) return;

        const totalAllowances = employee.transportAllowance + employee.housingAllowance + employee.foodAllowance;
        const netAmount = employee.baseSalary + totalAllowances;

        // إضافة الاستحقاق
        const accrualId = Math.max(...employeeAccruals.map(a => a.accrualId), 0) + 1;
        const newAccrual = {
            accrualId: accrualId,
            employeeId: employee.employeeId,
            month: item.month,
            baseSalary: employee.baseSalary,
            allowances: totalAllowances,
            penalties: 0,
            penaltyReason: 'قيد تلقائي للاستحقاقات المفقودة',
            netAmount: netAmount,
            isProcessed: true,
            processedDate: new Date().toISOString().split('T')[0],
            createdBy: 'النظام التلقائي'
        };

        employeeAccruals.push(newAccrual);

        // إضافة معاملة الاستحقاق
        const transactionId = Math.max(...employeeTransactions.map(t => t.transactionId), 0) + 1;
        const newTransaction = {
            transactionId: transactionId,
            employeeId: employee.employeeId,
            type: 'accrual',
            amount: netAmount,
            voucherNumber: '',
            description: `استحقاق شهر ${item.month} - قيد تلقائي`,
            date: new Date().toISOString().split('T')[0]
        };

        employeeTransactions.push(newTransaction);
        processedCount++;
    });

    if (processedCount > 0) {
        // إعادة حساب الأرصدة
        calculateEmployeeBalances();

        // تحديث العرض
        displayEmployees();
        updateStatistics();
        checkMissingAccruals();

        showSuccess(`تم قيد ${processedCount} استحقاق مفقود بنجاح`);
        bootstrap.Modal.getInstance(document.getElementById('missingAccrualsModal')).hide();
    }
}

// إظهار نافذة إضافة موظف
function showAddEmployeeModal() {
    document.getElementById('employeeModalTitle').textContent = 'إضافة موظف جديد';
    document.getElementById('employeeForm').reset();
    document.getElementById('employeeId').value = '';
    document.getElementById('isActive').checked = true;
    
    const modal = new bootstrap.Modal(document.getElementById('employeeModal'));
    modal.show();
}

// حفظ الموظف
function saveEmployee() {
    const form = document.getElementById('employeeForm');
    if (!form.checkValidity()) {
        form.reportValidity();
        return;
    }
    
    const employeeData = {
        employeeId: document.getElementById('employeeId').value || Date.now(),
        employeeName: document.getElementById('employeeName').value,
        position: document.getElementById('employeePosition').value,
        department: document.getElementById('employeeDepartment').value,
        baseSalary: parseFloat(document.getElementById('baseSalary').value) || 0,
        transportAllowance: parseFloat(document.getElementById('transportAllowance').value) || 0,
        housingAllowance: parseFloat(document.getElementById('housingAllowance').value) || 0,
        foodAllowance: parseFloat(document.getElementById('foodAllowance').value) || 0,
        hireDate: document.getElementById('hireDate').value,
        phone: document.getElementById('employeePhone').value,
        email: document.getElementById('employeeEmail').value,
        address: document.getElementById('employeeAddress').value,
        isActive: document.getElementById('isActive').checked,
        createdDate: new Date().toISOString().split('T')[0]
    };
    
    const existingIndex = employees.findIndex(e => e.employeeId == employeeData.employeeId);
    if (existingIndex !== -1) {
        employees[existingIndex] = employeeData;
    } else {
        employees.push(employeeData);
    }
    
    displayEmployees();
    updateStatistics();
    populateEmployeeDropdowns();
    
    bootstrap.Modal.getInstance(document.getElementById('employeeModal')).hide();
    alert('تم حفظ بيانات الموظف بنجاح!');
}

// ========== وظائف الموظفين الجديدة ==========

// عرض كشف حساب الموظف
function viewEmployeeStatement(employeeId) {
    const employee = employees.find(e => e.employeeId === employeeId);
    if (!employee) {
        showError('الموظف غير موجود');
        return;
    }

    const transactions = employeeTransactions.filter(t => t.employeeId === employeeId)
        .sort((a, b) => new Date(b.date) - new Date(a.date));

    let html = `
        <div style="text-align: center; font-family: Arial, sans-serif; direction: rtl;">
            <h2>كشف حساب الموظف</h2>
            <hr>
            <div style="text-align: right; margin: 20px 0;">
                <strong>اسم الموظف:</strong> ${employee.employeeName}<br>
                <strong>المنصب:</strong> ${getPositionText(employee.position)}<br>
                <strong>القسم:</strong> ${getDepartmentText(employee.department)}<br>
                <strong>تاريخ التوظيف:</strong> ${employee.hireDate}<br>
                <strong>الراتب الأساسي:</strong> ${formatCurrency(employee.baseSalary)}<br>
                <strong>إجمالي الاستحقاقات:</strong> ${formatCurrency(employee.totalAccrued)}<br>
                <strong>إجمالي المدفوع:</strong> ${formatCurrency(employee.totalPaid)}<br>
                <strong>الرصيد الحالي:</strong> ${formatCurrency(employee.currentBalance)}
            </div>
            <hr>
            <h4>تفاصيل المعاملات</h4>
            <table style="width: 100%; border-collapse: collapse; margin: 20px 0;">
                <thead>
                    <tr style="background-color: #f8f9fa;">
                        <th style="border: 1px solid #ddd; padding: 8px;">التاريخ</th>
                        <th style="border: 1px solid #ddd; padding: 8px;">النوع</th>
                        <th style="border: 1px solid #ddd; padding: 8px;">البيان</th>
                        <th style="border: 1px solid #ddd; padding: 8px;">المبلغ</th>
                        <th style="border: 1px solid #ddd; padding: 8px;">رقم السند</th>
                    </tr>
                </thead>
                <tbody>
    `;

    transactions.forEach(transaction => {
        const typeText = transaction.type === 'accrual' ? 'استحقاق' :
                        transaction.type === 'payment' ? 'دفع' : 'قبض';
        const amountColor = transaction.amount >= 0 ? 'green' : 'red';

        html += `
            <tr>
                <td style="border: 1px solid #ddd; padding: 8px;">${transaction.date}</td>
                <td style="border: 1px solid #ddd; padding: 8px;">${typeText}</td>
                <td style="border: 1px solid #ddd; padding: 8px;">${transaction.description}</td>
                <td style="border: 1px solid #ddd; padding: 8px; color: ${amountColor};">
                    ${formatCurrency(Math.abs(transaction.amount))}
                </td>
                <td style="border: 1px solid #ddd; padding: 8px;">${transaction.voucherNumber || '-'}</td>
            </tr>
        `;
    });

    html += `
                </tbody>
            </table>
        </div>
    `;

    const printWindow = window.open('', '_blank');
    printWindow.document.write(`
        <html>
            <head>
                <title>كشف حساب الموظف - ${employee.employeeName}</title>
                <style>
                    body { font-family: Arial, sans-serif; direction: rtl; }
                    table { border-collapse: collapse; width: 100%; }
                    th, td { border: 1px solid #ddd; padding: 8px; text-align: right; }
                    th { background-color: #f8f9fa; }
                </style>
            </head>
            <body>${html}</body>
        </html>
    `);
    printWindow.document.close();
    printWindow.print();
}

// إظهار نافذة استحقاق موظف محدد
function showEmployeeAccrualModal(employeeId) {
    document.getElementById('accrualEmployee').value = employeeId;
    showAccrualModal();
}

// إنشاء سند صرف للموظف
function createPaymentVoucher(employeeId) {
    const employee = employees.find(e => e.employeeId === employeeId);
    if (!employee) {
        showError('الموظف غير موجود');
        return;
    }

    if (employee.currentBalance <= 0) {
        showError(`لا يوجد رصيد مستحق للموظف ${employee.employeeName}`);
        return;
    }

    const amount = prompt(
        `إنشاء سند صرف للموظف: ${employee.employeeName}\n` +
        `الرصيد المستحق: ${formatCurrency(employee.currentBalance)}\n\n` +
        `أدخل مبلغ السند (ر.ي):`,
        employee.currentBalance.toString()
    );

    if (amount !== null && !isNaN(amount) && parseFloat(amount) > 0) {
        const paymentAmount = parseFloat(amount);

        if (paymentAmount > employee.currentBalance) {
            showError('المبلغ أكبر من الرصيد المستحق');
            return;
        }

        // إضافة المعاملة
        const transactionId = Math.max(...employeeTransactions.map(t => t.transactionId), 0) + 1;
        const voucherNumber = `PAY${transactionId.toString().padStart(3, '0')}`;

        const newTransaction = {
            transactionId: transactionId,
            employeeId: employee.employeeId,
            type: 'payment',
            amount: -paymentAmount,
            voucherNumber: voucherNumber,
            description: `دفع راتب للموظف ${employee.employeeName}`,
            date: new Date().toISOString().split('T')[0]
        };

        employeeTransactions.push(newTransaction);

        // إعادة حساب الأرصدة
        calculateEmployeeBalances();
        displayEmployees();
        updateStatistics();

        showSuccess(`تم إنشاء سند صرف رقم ${voucherNumber} بمبلغ ${formatCurrency(paymentAmount)} للموظف ${employee.employeeName}`);
    }
}

// تصدير تقرير حالة الاستحقاقات
function exportAccrualStatus() {
    alert('وظيفة التصدير ستكون متاحة قريباً');
}

// ========== وظائف مساعدة ==========

// عرض رسالة خطأ
function showError(message) {
    const alertDiv = document.createElement('div');
    alertDiv.className = 'alert alert-danger alert-dismissible fade show position-fixed';
    alertDiv.style.cssText = 'top: 20px; right: 20px; z-index: 9999; min-width: 300px;';
    alertDiv.innerHTML = `
        <i class="fas fa-exclamation-triangle me-2"></i>
        ${message}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    `;
    document.body.appendChild(alertDiv);

    setTimeout(() => {
        if (alertDiv.parentNode) {
            alertDiv.remove();
        }
    }, 5000);
}

// عرض رسالة نجاح
function showSuccess(message) {
    const alertDiv = document.createElement('div');
    alertDiv.className = 'alert alert-success alert-dismissible fade show position-fixed';
    alertDiv.style.cssText = 'top: 20px; right: 20px; z-index: 9999; min-width: 300px;';
    alertDiv.innerHTML = `
        <i class="fas fa-check-circle me-2"></i>
        ${message}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    `;
    document.body.appendChild(alertDiv);

    setTimeout(() => {
        if (alertDiv.parentNode) {
            alertDiv.remove();
        }
    }, 3000);
}

// ========== وظائف الموظفين المحدثة ==========

// تعديل الموظف
function editEmployee(employeeId) {
    const employee = employees.find(e => e.employeeId === employeeId);
    if (!employee) {
        showError('الموظف غير موجود');
        return;
    }

    // ملء النموذج ببيانات الموظف
    document.getElementById('employeeModalTitle').textContent = 'تعديل بيانات الموظف';
    document.getElementById('employeeId').value = employee.employeeId;
    document.getElementById('employeeName').value = employee.employeeName;
    document.getElementById('employeePosition').value = employee.position;
    document.getElementById('employeeDepartment').value = employee.department;
    document.getElementById('baseSalary').value = employee.baseSalary;
    document.getElementById('transportAllowance').value = employee.transportAllowance;
    document.getElementById('housingAllowance').value = employee.housingAllowance;
    document.getElementById('foodAllowance').value = employee.foodAllowance;
    document.getElementById('hireDate').value = employee.hireDate;
    document.getElementById('employeePhone').value = employee.phone;
    document.getElementById('employeeEmail').value = employee.email;
    document.getElementById('employeeAddress').value = employee.address;
    document.getElementById('isActive').checked = employee.isActive;

    const modal = new bootstrap.Modal(document.getElementById('employeeModal'));
    modal.show();
}

// دوال مساعدة
function getPositionText(position) {
    const positions = {
        'manager': 'مدير',
        'baker': 'خباز',
        'cashier': 'أمين صندوق',
        'sales': 'مندوب مبيعات',
        'cleaner': 'عامل نظافة',
        'security': 'حارس أمن'
    };
    return positions[position] || position;
}

function getPositionBadge(position) {
    const badges = {
        'manager': 'bg-primary',
        'baker': 'bg-success',
        'cashier': 'bg-info',
        'sales': 'bg-warning',
        'cleaner': 'bg-secondary',
        'security': 'bg-dark'
    };
    return badges[position] || 'bg-secondary';
}

function getDepartmentText(department) {
    const departments = {
        'production': 'الإنتاج',
        'sales': 'المبيعات',
        'administration': 'الإدارة',
        'maintenance': 'الصيانة'
    };
    return departments[department] || department;
}

function formatCurrency(amount) {
    return new Intl.NumberFormat('ar-YE', {
        style: 'currency',
        currency: 'YER',
        minimumFractionDigits: 3,
        maximumFractionDigits: 3
    }).format(amount).replace('YER', 'ر.ي');
}

// اختيار الموظف
function selectEmployee(employeeId) {
    selectedEmployee = employees.find(e => e.employeeId === employeeId);
    console.log('تم اختيار الموظف:', selectedEmployee);

    // يمكن إضافة منطق إضافي هنا مثل تمييز الصف المختار
    const rows = document.querySelectorAll('#employeesTableBody tr');
    rows.forEach(row => row.classList.remove('table-active'));

    const selectedRow = document.querySelector(`#employeesTableBody tr[onclick*="${employeeId}"]`);
    if (selectedRow) {
        selectedRow.classList.add('table-active');
    }
}
