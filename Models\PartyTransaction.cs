using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace ANWBakerySystem.Models
{
    /// <summary>
    /// معاملات الأطراف - لتتبع جميع المعاملات المالية مع العملاء والموردين
    /// Party Transactions - To track all financial transactions with customers and suppliers
    /// </summary>
    public class PartyTransaction
    {
        [Key]
        public int TransactionId { get; set; }

        [Required]
        [Display(Name = "الطرف")]
        public int PartyId { get; set; }

        [Required]
        [StringLength(50)]
        [Display(Name = "رقم المرجع")]
        public string ReferenceNumber { get; set; } = string.Empty;

        [Required]
        [Display(Name = "نوع المعاملة")]
        public TransactionType TransactionType { get; set; }

        [Required]
        [Display(Name = "تاريخ المعاملة")]
        public DateTime TransactionDate { get; set; } = DateTime.Now;

        /// <summary>
        /// المبلغ المدين بالريال اليمني
        /// Debit amount in Yemeni Rial
        /// </summary>
        [Display(Name = "مدين (ر.ي)")]
        [Column(TypeName = "decimal(18,3)")]
        public decimal DebitAmount { get; set; } = 0;

        /// <summary>
        /// المبلغ الدائن بالريال اليمني
        /// Credit amount in Yemeni Rial
        /// </summary>
        [Display(Name = "دائن (ر.ي)")]
        [Column(TypeName = "decimal(18,3)")]
        public decimal CreditAmount { get; set; } = 0;

        /// <summary>
        /// الرصيد بعد المعاملة بالريال اليمني
        /// Balance after transaction in Yemeni Rial
        /// </summary>
        [Display(Name = "الرصيد (ر.ي)")]
        [Column(TypeName = "decimal(18,3)")]
        public decimal BalanceAfter { get; set; } = 0;

        [StringLength(500)]
        [Display(Name = "الوصف")]
        public string? Description { get; set; }

        [StringLength(500)]
        [Display(Name = "ملاحظات")]
        public string? Notes { get; set; }

        [Display(Name = "رقم الفاتورة")]
        public int? InvoiceId { get; set; }

        [Display(Name = "رقم السند")]
        public int? VoucherId { get; set; }

        [Display(Name = "منشئ بواسطة")]
        public int CreatedBy { get; set; }

        [Display(Name = "تاريخ الإنشاء")]
        public DateTime CreatedAt { get; set; } = DateTime.Now;

        // Navigation properties
        public virtual Party Party { get; set; } = null!;
        public virtual Invoice? Invoice { get; set; }
        public virtual User Creator { get; set; } = null!;
    }

    public enum TransactionType
    {
        [Display(Name = "فاتورة مبيعات")]
        SalesInvoice = 1,
        [Display(Name = "فاتورة مشتريات")]
        PurchaseInvoice = 2,
        [Display(Name = "سند قبض")]
        Receipt = 3,
        [Display(Name = "سند صرف")]
        Payment = 4,
        [Display(Name = "مرتجع مبيعات")]
        SalesReturn = 5,
        [Display(Name = "مرتجع مشتريات")]
        PurchaseReturn = 6,
        [Display(Name = "رصيد افتتاحي")]
        OpeningBalance = 7,
        [Display(Name = "تسوية")]
        Adjustment = 8,
        [Display(Name = "خصم")]
        Discount = 9,
        [Display(Name = "فوائد")]
        Interest = 10
    }
}
