@echo off
echo ========================================
echo    إنشاء قاعدة البيانات - ANW Bakery
echo    Create Database - ANW Bakery
echo ========================================
echo.

echo 🗄️ حذف قاعدة البيانات القديمة (إن وجدت)...
if exist ANWBakery.db del ANWBakery.db
if exist "bin\Debug\net8.0\ANWBakery.db" del "bin\Debug\net8.0\ANWBakery.db"

echo.
echo 🔧 تجميع المشروع...
dotnet build

if %ERRORLEVEL% NEQ 0 (
    echo ❌ فشل في التجميع
    pause
    exit /b 1
)

echo.
echo 🗄️ إنشاء قاعدة البيانات...
dotnet run --no-build &

echo.
echo ⏳ انتظار 10 ثوان لإنشاء قاعدة البيانات...
timeout /t 10 /nobreak > nul

echo.
echo 🔍 البحث عن قاعدة البيانات...
if exist ANWBakery.db (
    echo ✅ تم العثور على قاعدة البيانات: ANWBakery.db
    dir ANWBakery.db
) else if exist "bin\Debug\net8.0\ANWBakery.db" (
    echo ✅ تم العثور على قاعدة البيانات: bin\Debug\net8.0\ANWBakery.db
    dir "bin\Debug\net8.0\ANWBakery.db"
    echo.
    echo 📁 نسخ قاعدة البيانات إلى المجلد الرئيسي...
    copy "bin\Debug\net8.0\ANWBakery.db" ANWBakery.db
) else (
    echo ❌ لم يتم العثور على قاعدة البيانات
    echo 🔍 البحث في جميع المجلدات...
    dir /s *.db
)

echo.
echo 🌐 افتح المتصفح على: http://localhost:5000
echo 📝 بيانات الدخول: admin / admin123
echo.
pause
