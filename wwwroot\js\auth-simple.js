// نظام المصادقة المبسط - ANW Bakery System
// Simple Authentication System - ANW Bakery System

// التحقق من تسجيل الدخول
function checkAuth() {
    const token = localStorage.getItem('anw_token');
    const user = localStorage.getItem('anw_user');

    // تجاهل التحقق في صفحة تسجيل الدخول
    if (window.location.pathname === '/index.html' || window.location.pathname === '/') {
        return true;
    }

    if (!token || !user) {
        console.log('لا يوجد token أو user، إعادة توجيه لصفحة الدخول');
        window.location.href = '/index.html';
        return false;
    }

    try {
        const userData = JSON.parse(user);
        // تحديث معلومات المستخدم في الواجهة
        updateUserInfo(userData);
        return true;
    } catch (error) {
        console.error('خطأ في بيانات المستخدم:', error);
        logout();
        return false;
    }
}

// تحديث معلومات المستخدم في الواجهة
function updateUserInfo(userData) {
    // تحديث اسم المستخدم في الشريط العلوي
    const userNameElements = document.querySelectorAll('.user-name');
    userNameElements.forEach(element => {
        element.textContent = userData.fullName || userData.username;
    });
    
    // تحديث صورة المستخدم
    const userAvatarElements = document.querySelectorAll('.user-avatar');
    userAvatarElements.forEach(element => {
        element.textContent = (userData.fullName || userData.username).charAt(0).toUpperCase();
    });
}

// تسجيل الخروج
function logout() {
    // حذف بيانات المصادقة
    localStorage.removeItem('anw_token');
    localStorage.removeItem('anw_user');
    
    // إعادة توجيه لصفحة تسجيل الدخول
    window.location.href = '/index.html';
}

// الحصول على رمز المصادقة
function getAuthToken() {
    return localStorage.getItem('anw_token');
}

// الحصول على بيانات المستخدم الحالي
function getCurrentUser() {
    const user = localStorage.getItem('anw_user');
    if (user) {
        try {
            return JSON.parse(user);
        } catch (error) {
            console.error('خطأ في تحليل بيانات المستخدم:', error);
            return null;
        }
    }
    return null;
}

// إضافة رمز المصادقة للطلبات
function addAuthHeader(headers = {}) {
    const token = getAuthToken();
    if (token) {
        headers['Authorization'] = `Bearer ${token}`;
    }
    return headers;
}

// طلب HTTP مع المصادقة
async function authenticatedFetch(url, options = {}) {
    const headers = addAuthHeader(options.headers || {});
    
    const response = await fetch(url, {
        ...options,
        headers: headers
    });
    
    // التحقق من انتهاء صلاحية الرمز
    if (response.status === 401) {
        console.warn('انتهت صلاحية جلسة المستخدم');
        logout();
        return null;
    }
    
    return response;
}

// تشغيل التحقق من المصادقة عند تحميل الصفحة
document.addEventListener('DOMContentLoaded', function() {
    console.log('تحميل الصفحة:', window.location.pathname);

    // تجاهل التحقق في صفحة تسجيل الدخول
    if (window.location.pathname === '/index.html' || window.location.pathname === '/') {
        console.log('صفحة تسجيل الدخول - تجاهل التحقق');
        return;
    }

    // إضافة تأخير قصير لتجنب التضارب
    setTimeout(() => {
        // التحقق من المصادقة
        if (!checkAuth()) {
            console.log('فشل في التحقق من المصادقة');
            return;
        }

        console.log('✅ تم التحقق من المصادقة بنجاح');
    }, 100);
});

// إضافة مستمع لأزرار تسجيل الخروج
document.addEventListener('click', function(event) {
    if (event.target.matches('[onclick*="logout"]') || 
        event.target.closest('[onclick*="logout"]')) {
        event.preventDefault();
        logout();
    }
});

console.log('✅ تم تحميل نظام المصادقة المبسط');
