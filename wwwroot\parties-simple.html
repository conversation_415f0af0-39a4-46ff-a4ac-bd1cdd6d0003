<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>العملاء والموردين - نظام ANW</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
</head>
<body>
    <div class="container-fluid">
        <div class="row">
            <!-- Sidebar -->
            <div class="col-md-3 bg-dark text-white p-0">
                <div class="sidebar">
                    <div class="p-3 border-bottom">
                        <h5><i class="fas fa-calculator me-2"></i>نظام ANW</h5>
                        <small>إدارة مخبوزات مبسط</small>
                    </div>
                    <nav class="nav flex-column p-3">
                        <a href="items-simple.html" class="nav-link text-white">
                            <i class="fas fa-boxes me-2"></i>المنتجات والخامات
                        </a>
                        <a href="parties-simple.html" class="nav-link text-white active">
                            <i class="fas fa-users me-2"></i>العملاء والموردين
                        </a>
                        <a href="employees-simple.html" class="nav-link text-white">
                            <i class="fas fa-user-tie me-2"></i>الموظفين
                        </a>
                        <a href="invoices-simple.html" class="nav-link text-white">
                            <i class="fas fa-file-invoice me-2"></i>الفواتير
                        </a>
                        <a href="inventory-simple.html" class="nav-link text-white">
                            <i class="fas fa-warehouse me-2"></i>المخزون
                        </a>
                        <a href="cash-simple.html" class="nav-link text-white">
                            <i class="fas fa-cash-register me-2"></i>الصناديق
                        </a>
                        <a href="banks-simple.html" class="nav-link text-white">
                            <i class="fas fa-university me-2"></i>البنوك
                        </a>
                        <a href="owners-simple.html" class="nav-link text-white">
                            <i class="fas fa-crown me-2"></i>الملاك
                        </a>
                        <a href="users-simple.html" class="nav-link text-white">
                            <i class="fas fa-user-cog me-2"></i>المستخدمين
                        </a>
                        <a href="reports-simple.html" class="nav-link text-white">
                            <i class="fas fa-chart-bar me-2"></i>التقارير
                        </a>
                    </nav>
                </div>
            </div>

            <!-- Main Content -->
            <div class="col-md-9">
                <div class="p-4">
                    <!-- Header -->
                    <div class="d-flex justify-content-between align-items-center mb-4">
                        <h2><i class="fas fa-users text-primary me-2"></i>العملاء والموردين</h2>
                        <div>
                            <button class="btn btn-success" onclick="addParty()">
                                <i class="fas fa-plus me-1"></i>إضافة طرف
                            </button>
                            <button class="btn btn-info" onclick="exportParties()">
                                <i class="fas fa-download me-1"></i>تصدير
                            </button>
                        </div>
                    </div>

                    <!-- Filter Tabs -->
                    <ul class="nav nav-tabs mb-3">
                        <li class="nav-item">
                            <a class="nav-link active" onclick="filterParties('all')">الكل</a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" onclick="filterParties('customer')">العملاء</a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" onclick="filterParties('supplier')">الموردين</a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" onclick="filterParties('both')">عميل ومورد</a>
                        </li>
                    </ul>

                    <!-- Search -->
                    <div class="row mb-3">
                        <div class="col-md-6">
                            <input type="text" id="searchInput" class="form-control" placeholder="البحث بالاسم أو الهاتف..." onkeyup="searchParties()">
                        </div>
                    </div>

                    <!-- Parties Table -->
                    <div class="card">
                        <div class="card-body">
                            <div class="table-responsive">
                                <table class="table table-striped table-hover">
                                    <thead class="table-dark">
                                        <tr>
                                            <th>الاسم</th>
                                            <th>النوع</th>
                                            <th>الهاتف</th>
                                            <th>العنوان</th>
                                            <th>الرصيد</th>
                                            <th>الحالة</th>
                                            <th>الإجراءات</th>
                                        </tr>
                                    </thead>
                                    <tbody id="partiesTableBody">
                                        <tr>
                                            <td colspan="7" class="text-center">
                                                <div class="spinner-border text-primary" role="status">
                                                    <span class="visually-hidden">جاري التحميل...</span>
                                                </div>
                                                <p class="mt-2">جاري تحميل العملاء والموردين...</p>
                                            </td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Add/Edit Party Modal -->
    <div class="modal fade" id="partyModal" tabindex="-1">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">إضافة طرف جديد</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <form id="partyForm">
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">اسم الطرف *</label>
                                    <input type="text" id="partyName" class="form-control" required>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">نوع الطرف *</label>
                                    <select id="partyType" class="form-control" required>
                                        <option value="">اختر النوع</option>
                                        <option value="customer">عميل</option>
                                        <option value="supplier">مورد</option>
                                        <option value="both">عميل ومورد</option>
                                    </select>
                                </div>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">رقم الهاتف</label>
                                    <input type="text" id="phone" class="form-control">
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">البريد الإلكتروني</label>
                                    <input type="email" id="email" class="form-control">
                                </div>
                            </div>
                        </div>
                        <div class="mb-3">
                            <label class="form-label">العنوان</label>
                            <textarea id="address" class="form-control" rows="2"></textarea>
                        </div>
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">الرصيد الافتتاحي</label>
                                    <input type="number" id="openingBalance" class="form-control" value="0" step="0.01">
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">حد الائتمان</label>
                                    <input type="number" id="creditLimit" class="form-control" value="0" step="0.01">
                                </div>
                            </div>
                        </div>
                        <div class="mb-3">
                            <label class="form-label">ملاحظات</label>
                            <textarea id="notes" class="form-control" rows="2"></textarea>
                        </div>
                        <div class="form-check mb-3">
                            <input type="checkbox" id="isActive" class="form-check-input" checked>
                            <label class="form-check-label">طرف نشط</label>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                    <button type="button" class="btn btn-primary" onclick="saveParty()">حفظ</button>
                </div>
            </div>
        </div>
    </div>

    <!-- Success/Error Messages -->
    <div id="messages"></div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // البيانات المحلية
        let parties = [];
        let nextId = 1;
        let currentFilter = 'all';

        // تحميل البيانات عند بدء الصفحة
        document.addEventListener('DOMContentLoaded', function() {
            loadParties();
        });

        // تحميل الأطراف من localStorage
        function loadParties() {
            try {
                const savedParties = localStorage.getItem('anw_parties');
                if (savedParties && savedParties !== 'null') {
                    parties = JSON.parse(savedParties);
                    if (parties.length > 0) {
                        nextId = Math.max(...parties.map(p => p.id), 0) + 1;
                    }
                } else {
                    // إنشاء أطراف افتراضية
                    parties = [
                        {id: 1, name: 'مخبز الأمل', type: 'customer', phone: '01-234567', email: '<EMAIL>', address: 'صنعاء - شارع الزبيري', balance: 15000, creditLimit: 50000, notes: 'عميل مميز', active: true},
                        {id: 2, name: 'شركة الدقيق الذهبي', type: 'supplier', phone: '01-345678', email: '<EMAIL>', address: 'الحديدة - الميناء', balance: -25000, creditLimit: 100000, notes: 'مورد رئيسي للدقيق', active: true},
                        {id: 3, name: 'مطعم الفردوس', type: 'both', phone: '01-456789', email: '<EMAIL>', address: 'تعز - شارع جمال', balance: 8000, creditLimit: 30000, notes: 'عميل ومورد للمعجنات', active: true}
                    ];
                    nextId = 4;
                    saveParties();
                }
                displayParties();
            } catch (error) {
                console.error('خطأ في تحميل الأطراف:', error);
                showMessage('خطأ في تحميل البيانات', 'danger');
            }
        }

        // حفظ الأطراف في localStorage
        function saveParties() {
            localStorage.setItem('anw_parties', JSON.stringify(parties));
        }

        // عرض الأطراف
        function displayParties() {
            const tbody = document.getElementById('partiesTableBody');
            let filteredParties = parties;

            // تطبيق الفلتر
            if (currentFilter !== 'all') {
                filteredParties = parties.filter(party => party.type === currentFilter);
            }

            if (filteredParties.length === 0) {
                tbody.innerHTML = `
                    <tr>
                        <td colspan="7" class="text-center text-muted">
                            لا توجد أطراف
                            <br>
                            <button class="btn btn-primary mt-2" onclick="addParty()">إضافة طرف جديد</button>
                        </td>
                    </tr>
                `;
                return;
            }

            let html = '';
            filteredParties.forEach(party => {
                const typeLabel = getTypeLabel(party.type);
                const statusBadge = party.active ? 
                    '<span class="badge bg-success">نشط</span>' : 
                    '<span class="badge bg-secondary">غير نشط</span>';
                
                const balanceClass = party.balance < 0 ? 'text-danger' : party.balance > 0 ? 'text-success' : '';

                html += `
                    <tr>
                        <td>${party.name}</td>
                        <td><span class="badge bg-info">${typeLabel}</span></td>
                        <td>${party.phone || '-'}</td>
                        <td>${party.address || '-'}</td>
                        <td class="${balanceClass}">${party.balance.toLocaleString()} ر.ي</td>
                        <td>${statusBadge}</td>
                        <td>
                            <button class="btn btn-sm btn-warning" onclick="editParty(${party.id})">
                                <i class="fas fa-edit"></i>
                            </button>
                            <button class="btn btn-sm btn-danger" onclick="deleteParty(${party.id})">
                                <i class="fas fa-trash"></i>
                            </button>
                        </td>
                    </tr>
                `;
            });

            tbody.innerHTML = html;
        }

        // الحصول على تسمية النوع
        function getTypeLabel(type) {
            const types = {
                'customer': 'عميل',
                'supplier': 'مورد',
                'both': 'عميل ومورد'
            };
            return types[type] || type;
        }

        // فلترة الأطراف
        function filterParties(filter) {
            currentFilter = filter;
            
            // تحديث التبويبات
            document.querySelectorAll('.nav-link').forEach(link => {
                link.classList.remove('active');
            });
            event.target.classList.add('active');
            
            displayParties();
        }

        // البحث في الأطراف
        function searchParties() {
            const searchTerm = document.getElementById('searchInput').value.toLowerCase();
            const tbody = document.getElementById('partiesTableBody');
            
            let filteredParties = parties.filter(party => {
                return party.name.toLowerCase().includes(searchTerm) ||
                       (party.phone && party.phone.toLowerCase().includes(searchTerm));
            });

            // تطبيق فلتر النوع أيضاً
            if (currentFilter !== 'all') {
                filteredParties = filteredParties.filter(party => party.type === currentFilter);
            }

            if (filteredParties.length === 0) {
                tbody.innerHTML = `
                    <tr>
                        <td colspan="7" class="text-center text-muted">لا توجد نتائج للبحث</td>
                    </tr>
                `;
                return;
            }

            let html = '';
            filteredParties.forEach(party => {
                const typeLabel = getTypeLabel(party.type);
                const statusBadge = party.active ? 
                    '<span class="badge bg-success">نشط</span>' : 
                    '<span class="badge bg-secondary">غير نشط</span>';
                
                const balanceClass = party.balance < 0 ? 'text-danger' : party.balance > 0 ? 'text-success' : '';

                html += `
                    <tr>
                        <td>${party.name}</td>
                        <td><span class="badge bg-info">${typeLabel}</span></td>
                        <td>${party.phone || '-'}</td>
                        <td>${party.address || '-'}</td>
                        <td class="${balanceClass}">${party.balance.toLocaleString()} ر.ي</td>
                        <td>${statusBadge}</td>
                        <td>
                            <button class="btn btn-sm btn-warning" onclick="editParty(${party.id})">
                                <i class="fas fa-edit"></i>
                            </button>
                            <button class="btn btn-sm btn-danger" onclick="deleteParty(${party.id})">
                                <i class="fas fa-trash"></i>
                            </button>
                        </td>
                    </tr>
                `;
            });

            tbody.innerHTML = html;
        }

        // إضافة طرف جديد
        function addParty() {
            clearForm();
            document.querySelector('.modal-title').textContent = 'إضافة طرف جديد';
            new bootstrap.Modal(document.getElementById('partyModal')).show();
        }

        // تحرير طرف
        function editParty(partyId) {
            const party = parties.find(p => p.id === partyId);
            if (party) {
                document.getElementById('partyName').value = party.name;
                document.getElementById('partyType').value = party.type;
                document.getElementById('phone').value = party.phone || '';
                document.getElementById('email').value = party.email || '';
                document.getElementById('address').value = party.address || '';
                document.getElementById('openingBalance').value = party.balance;
                document.getElementById('creditLimit').value = party.creditLimit;
                document.getElementById('notes').value = party.notes || '';
                document.getElementById('isActive').checked = party.active;
                
                document.getElementById('partyForm').dataset.editId = partyId;
                document.querySelector('.modal-title').textContent = 'تحرير الطرف';
                
                new bootstrap.Modal(document.getElementById('partyModal')).show();
            }
        }

        // حذف طرف
        function deleteParty(partyId) {
            if (confirm('هل أنت متأكد من حذف هذا الطرف؟')) {
                parties = parties.filter(p => p.id !== partyId);
                saveParties();
                displayParties();
                showMessage('تم حذف الطرف بنجاح', 'success');
            }
        }

        // حفظ الطرف
        function saveParty() {
            const form = document.getElementById('partyForm');
            const editId = form.dataset.editId;
            
            const partyData = {
                name: document.getElementById('partyName').value,
                type: document.getElementById('partyType').value,
                phone: document.getElementById('phone').value,
                email: document.getElementById('email').value,
                address: document.getElementById('address').value,
                balance: parseFloat(document.getElementById('openingBalance').value) || 0,
                creditLimit: parseFloat(document.getElementById('creditLimit').value) || 0,
                notes: document.getElementById('notes').value,
                active: document.getElementById('isActive').checked
            };

            // التحقق من البيانات
            if (!partyData.name || !partyData.type) {
                showMessage('يرجى ملء جميع الحقول المطلوبة', 'danger');
                return;
            }

            if (editId) {
                // تحديث طرف موجود
                const partyIndex = parties.findIndex(p => p.id == editId);
                parties[partyIndex] = { ...parties[partyIndex], ...partyData };
                showMessage('تم تحديث الطرف بنجاح', 'success');
            } else {
                // إضافة طرف جديد
                partyData.id = nextId++;
                parties.push(partyData);
                showMessage('تم إضافة الطرف بنجاح', 'success');
            }

            saveParties();
            displayParties();
            bootstrap.Modal.getInstance(document.getElementById('partyModal')).hide();
        }

        // تنظيف النموذج
        function clearForm() {
            document.getElementById('partyForm').reset();
            document.getElementById('partyForm').removeAttribute('data-edit-id');
            document.getElementById('openingBalance').value = '0';
            document.getElementById('creditLimit').value = '0';
            document.getElementById('isActive').checked = true;
        }

        // تصدير الأطراف
        function exportParties() {
            const csvContent = "data:text/csv;charset=utf-8," 
                + "الاسم,النوع,الهاتف,البريد,العنوان,الرصيد,حد الائتمان,الحالة\n"
                + parties.map(party => 
                    `${party.name},${getTypeLabel(party.type)},${party.phone || ''},${party.email || ''},${party.address || ''},${party.balance},${party.creditLimit},${party.active ? 'نشط' : 'غير نشط'}`
                ).join("\n");

            const encodedUri = encodeURI(csvContent);
            const link = document.createElement("a");
            link.setAttribute("href", encodedUri);
            link.setAttribute("download", "parties.csv");
            document.body.appendChild(link);
            link.click();
            document.body.removeChild(link);
            
            showMessage('تم تصدير الأطراف بنجاح', 'success');
        }

        // عرض رسالة
        function showMessage(message, type) {
            const messagesDiv = document.getElementById('messages');
            const alertHtml = `
                <div class="alert alert-${type} alert-dismissible fade show position-fixed" 
                     style="top: 20px; left: 20px; z-index: 9999; min-width: 300px;">
                    ${message}
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
            `;
            messagesDiv.innerHTML = alertHtml;
            
            setTimeout(() => {
                const alert = messagesDiv.querySelector('.alert');
                if (alert) {
                    bootstrap.Alert.getOrCreateInstance(alert).close();
                }
            }, 3000);
        }

        console.log('✅ تم تحميل نظام العملاء والموردين بنجاح');
    </script>

    <style>
        .sidebar {
            height: 100vh;
            position: fixed;
            width: 25%;
        }
        .nav-link.active {
            background-color: rgba(255,255,255,0.1);
            border-radius: 5px;
        }
        .table th {
            background-color: #343a40;
            color: white;
        }
    </style>
</body>
</html>
