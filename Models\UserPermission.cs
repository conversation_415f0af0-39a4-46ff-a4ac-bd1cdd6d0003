using System.ComponentModel.DataAnnotations;

namespace ANWBakerySystem.Models
{
    /// <summary>
    /// صلاحيات المستخدمين
    /// User Permissions
    /// </summary>
    public class UserPermission
    {
        [Key]
        public int UserPermissionId { get; set; }

        [Required]
        public int UserId { get; set; }

        [Required]
        public int PermissionId { get; set; }

        [Required]
        public bool IsGranted { get; set; } = true;

        public DateTime GrantedAt { get; set; } = DateTime.Now;

        public int GrantedBy { get; set; }

        public DateTime? RevokedAt { get; set; }

        public int? RevokedBy { get; set; }

        // Navigation properties
        public virtual User User { get; set; } = null!;
        public virtual Permission Permission { get; set; } = null!;
        public virtual User GrantedByUser { get; set; } = null!;
        public virtual User? RevokedByUser { get; set; }
    }
}
