using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace ANWBakerySystem.Models
{
    /// <summary>
    /// حساب بنكي
    /// Bank Account
    /// </summary>
    public class BankAccount
    {
        [Key]
        public int BankAccountId { get; set; }

        [Required]
        public int BankId { get; set; }

        [Required]
        [StringLength(100)]
        public string AccountNumber { get; set; } = string.Empty;

        [Required]
        [StringLength(200)]
        public string AccountName { get; set; } = string.Empty;

        [StringLength(200)]
        public string? AccountNameEn { get; set; }

        public BankAccountType AccountType { get; set; } = BankAccountType.Current;

        [Column(TypeName = "decimal(18,3)")]
        public decimal OpeningBalance { get; set; } = 0;

        [Column(TypeName = "decimal(18,3)")]
        public decimal CurrentBalance { get; set; } = 0;

        [StringLength(50)]
        public string? IBAN { get; set; }

        [StringLength(20)]
        public string? SwiftCode { get; set; }

        [StringLength(500)]
        public string? Notes { get; set; }

        public bool IsActive { get; set; } = true;

        public DateTime CreatedAt { get; set; } = DateTime.Now;
        public DateTime? UpdatedAt { get; set; }
        public int CreatedBy { get; set; }
        public int? UpdatedBy { get; set; }

        // Navigation properties
        public virtual Bank Bank { get; set; } = null!;
        public virtual User Creator { get; set; } = null!;
        public virtual User? Updater { get; set; }
        public virtual ICollection<BankTransaction> BankTransactions { get; set; } = new List<BankTransaction>();
    }

    /// <summary>
    /// حركة بنكية
    /// Bank Transaction
    /// </summary>
    public class BankTransaction
    {
        [Key]
        public int BankTransactionId { get; set; }

        [Required]
        public int BankAccountId { get; set; }

        [Required]
        [StringLength(50)]
        public string TransactionNumber { get; set; } = string.Empty;

        [Required]
        public DateTime TransactionDate { get; set; } = DateTime.Now;

        [Required]
        public BankTransactionType TransactionType { get; set; }

        [Required]
        [StringLength(500)]
        public string Description { get; set; } = string.Empty;

        [Column(TypeName = "decimal(18,3)")]
        public decimal Amount { get; set; } = 0;

        [Column(TypeName = "decimal(18,3)")]
        public decimal BalanceAfter { get; set; } = 0;

        [StringLength(100)]
        public string? ReferenceNumber { get; set; }

        [StringLength(200)]
        public string? PayeeName { get; set; }

        [StringLength(100)]
        public string? CheckNumber { get; set; }

        public DateTime? CheckDate { get; set; }

        public BankTransactionStatus Status { get; set; } = BankTransactionStatus.Pending;

        public DateTime CreatedAt { get; set; } = DateTime.Now;
        public int CreatedBy { get; set; }

        // Navigation properties
        public virtual BankAccount BankAccount { get; set; } = null!;
        public virtual User Creator { get; set; } = null!;
    }

    public enum BankAccountType
    {
        [Display(Name = "حساب جاري")]
        Current = 1,
        [Display(Name = "حساب توفير")]
        Savings = 2,
        [Display(Name = "وديعة ثابتة")]
        FixedDeposit = 3,
        [Display(Name = "حساب استثماري")]
        Investment = 4
    }

    public enum BankTransactionType
    {
        [Display(Name = "إيداع")]
        Deposit = 1,
        [Display(Name = "سحب")]
        Withdrawal = 2,
        [Display(Name = "تحويل وارد")]
        TransferIn = 3,
        [Display(Name = "تحويل صادر")]
        TransferOut = 4,
        [Display(Name = "رسوم بنكية")]
        BankFees = 5,
        [Display(Name = "فوائد")]
        Interest = 6
    }

    public enum BankTransactionStatus
    {
        [Display(Name = "معلق")]
        Pending = 1,
        [Display(Name = "مؤكد")]
        Confirmed = 2,
        [Display(Name = "ملغي")]
        Cancelled = 3
    }
}
