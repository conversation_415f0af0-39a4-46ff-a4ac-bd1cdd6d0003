// إدارة التقارير المالية - متصل بـ APIs
const API_BASE_URL = '/api';
let currentReportType = '';
let reportData = {};

// تحميل الصفحة
document.addEventListener('DOMContentLoaded', function() {
    console.log('🔄 تحميل صفحة التقارير...');
    initializePage();
});

// تهيئة الصفحة
async function initializePage() {
    try {
        console.log('✅ تم تحميل صفحة التقارير بنجاح');
    } catch (error) {
        console.error('❌ خطأ في تحميل صفحة التقارير:', error);
        showError('خطأ في تحميل البيانات. يرجى إعادة تحميل الصفحة.');
    }
}

// توليد التقرير
async function generateReport(reportType) {
    console.log('🔄 توليد التقرير:', reportType);
    currentReportType = reportType;
    
    const reportContent = document.getElementById('reportContent');
    showReportLoading();

    try {
        // محاولة جلب البيانات من API
        const data = await fetchReportData(reportType);
        
        if (data) {
            displayReport(reportType, data);
        } else {
            // عرض تقرير تجريبي
            displaySampleReport(reportType);
        }

    } catch (error) {
        console.error('❌ خطأ في توليد التقرير:', error);
        displaySampleReport(reportType);
    }
}

// جلب بيانات التقرير من API
async function fetchReportData(reportType) {
    try {
        let endpoint = '';
        
        switch(reportType) {
            case 'income_statement':
                endpoint = '/reports/income-statement';
                break;
            case 'balance_sheet':
                endpoint = '/reports/balance-sheet';
                break;
            case 'daily_sales':
                endpoint = '/reports/sales/daily';
                break;
            case 'monthly_sales':
                endpoint = '/reports/sales/monthly';
                break;
            case 'inventory_status':
                endpoint = '/reports/inventory/status';
                break;
            case 'production_summary':
                endpoint = '/reports/production/summary';
                break;
            default:
                return null;
        }

        const response = await fetch(`${API_BASE_URL}${endpoint}`);
        if (response.ok) {
            const result = await response.json();
            if (result.success) {
                return result.data;
            }
        }
        
        return null;
        
    } catch (error) {
        console.error('❌ خطأ في جلب بيانات التقرير:', error);
        return null;
    }
}

// عرض التقرير
function displayReport(reportType, data) {
    const reportContent = document.getElementById('reportContent');
    const reports = getReportTitles();
    
    let html = `
        <div class="alert alert-success">
            <i class="fas fa-check-circle me-2"></i>
            تم توليد تقرير: <strong>${reports[reportType]}</strong>
        </div>
    `;

    switch(reportType) {
        case 'income_statement':
            html += generateIncomeStatementHTML(data);
            break;
        case 'balance_sheet':
            html += generateBalanceSheetHTML(data);
            break;
        case 'daily_sales':
        case 'monthly_sales':
            html += generateSalesReportHTML(data);
            break;
        case 'inventory_status':
            html += generateInventoryReportHTML(data);
            break;
        case 'production_summary':
            html += generateProductionReportHTML(data);
            break;
        default:
            html += generateGenericReportHTML(data);
    }

    reportContent.innerHTML = html;
}

// عرض تقرير تجريبي
function displaySampleReport(reportType) {
    const reportContent = document.getElementById('reportContent');
    const reports = getReportTitles();
    
    reportContent.innerHTML = `
        <div class="alert alert-info">
            <i class="fas fa-info-circle me-2"></i>
            تم اختيار تقرير: <strong>${reports[reportType]}</strong>
        </div>
        <div class="text-center">
            <i class="fas fa-chart-line fa-3x text-primary mb-3"></i>
            <h5>تقرير ${reports[reportType]}</h5>
            <p class="text-muted">عرض بيانات تجريبية - سيتم ربطه بالبيانات الفعلية قريباً</p>
            <div class="mt-4">
                <div class="row">
                    <div class="col-md-3">
                        <div class="card bg-light">
                            <div class="card-body text-center">
                                <h6>إجمالي المبيعات</h6>
                                <h4 class="text-success">125,500 ر.ي</h4>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="card bg-light">
                            <div class="card-body text-center">
                                <h6>إجمالي المشتريات</h6>
                                <h4 class="text-danger">85,200 ر.ي</h4>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="card bg-light">
                            <div class="card-body text-center">
                                <h6>صافي الربح</h6>
                                <h4 class="text-primary">40,300 ر.ي</h4>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="card bg-light">
                            <div class="card-body text-center">
                                <h6>عدد الفواتير</h6>
                                <h4 class="text-info">25</h4>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    `;
}

// عناوين التقارير
function getReportTitles() {
    return {
        'income_statement': 'قائمة الدخل',
        'balance_sheet': 'الميزانية العمومية',
        'cash_flow': 'قائمة التدفقات النقدية',
        'trial_balance': 'ميزان المراجعة',
        'daily_sales': 'تقرير المبيعات اليومية',
        'monthly_sales': 'تقرير المبيعات الشهرية',
        'customer_sales': 'تقرير مبيعات العملاء',
        'product_sales': 'تقرير مبيعات المنتجات',
        'inventory_status': 'تقرير حالة المخزون',
        'low_stock': 'تقرير المخزون المنخفض',
        'inventory_movement': 'تقرير حركة المخزون',
        'waste_report': 'تقرير التالف والهالك',
        'production_summary': 'ملخص الإنتاج',
        'production_cost': 'تقرير تكلفة الإنتاج',
        'production_efficiency': 'تقرير كفاءة الإنتاج',
        'recipe_analysis': 'تحليل الوصفات'
    };
}

// توليد HTML لقائمة الدخل
function generateIncomeStatementHTML(data) {
    return `
        <div class="table-responsive">
            <table class="table table-bordered">
                <thead class="table-primary">
                    <tr>
                        <th colspan="2" class="text-center">قائمة الدخل</th>
                    </tr>
                </thead>
                <tbody>
                    <tr>
                        <td><strong>الإيرادات</strong></td>
                        <td class="text-end"><strong>${formatCurrency(data?.revenue || 125500)}</strong></td>
                    </tr>
                    <tr>
                        <td>تكلفة البضاعة المباعة</td>
                        <td class="text-end">${formatCurrency(data?.cogs || 65000)}</td>
                    </tr>
                    <tr class="table-success">
                        <td><strong>إجمالي الربح</strong></td>
                        <td class="text-end"><strong>${formatCurrency((data?.revenue || 125500) - (data?.cogs || 65000))}</strong></td>
                    </tr>
                    <tr>
                        <td>المصروفات التشغيلية</td>
                        <td class="text-end">${formatCurrency(data?.expenses || 20200)}</td>
                    </tr>
                    <tr class="table-primary">
                        <td><strong>صافي الربح</strong></td>
                        <td class="text-end"><strong>${formatCurrency((data?.revenue || 125500) - (data?.cogs || 65000) - (data?.expenses || 20200))}</strong></td>
                    </tr>
                </tbody>
            </table>
        </div>
    `;
}

// توليد HTML للميزانية العمومية
function generateBalanceSheetHTML(data) {
    return `
        <div class="row">
            <div class="col-md-6">
                <h6 class="text-primary">الأصول</h6>
                <table class="table table-sm table-bordered">
                    <tr><td>النقدية والبنوك</td><td class="text-end">${formatCurrency(data?.cash || 170000)}</td></tr>
                    <tr><td>العملاء</td><td class="text-end">${formatCurrency(data?.receivables || 40000)}</td></tr>
                    <tr><td>المخزون</td><td class="text-end">${formatCurrency(data?.inventory || 88000)}</td></tr>
                    <tr><td>الأصول الثابتة</td><td class="text-end">${formatCurrency(data?.fixedAssets || 95000)}</td></tr>
                    <tr class="table-primary"><td><strong>إجمالي الأصول</strong></td><td class="text-end"><strong>${formatCurrency(393000)}</strong></td></tr>
                </table>
            </div>
            <div class="col-md-6">
                <h6 class="text-danger">الخصوم وحقوق الملكية</h6>
                <table class="table table-sm table-bordered">
                    <tr><td>الموردين</td><td class="text-end">${formatCurrency(data?.payables || 26000)}</td></tr>
                    <tr><td>المستحقات</td><td class="text-end">${formatCurrency(data?.accruals || 20500)}</td></tr>
                    <tr><td>رأس المال</td><td class="text-end">${formatCurrency(data?.capital || 200000)}</td></tr>
                    <tr><td>الأرباح المحتجزة</td><td class="text-end">${formatCurrency(data?.retainedEarnings || 146500)}</td></tr>
                    <tr class="table-primary"><td><strong>إجمالي الخصوم وحقوق الملكية</strong></td><td class="text-end"><strong>${formatCurrency(393000)}</strong></td></tr>
                </table>
            </div>
        </div>
    `;
}

// وظائف المساعدة
function showReportLoading() {
    const reportContent = document.getElementById('reportContent');
    reportContent.innerHTML = `
        <div class="text-center p-4">
            <div class="spinner-border text-primary" role="status">
                <span class="visually-hidden">جاري توليد التقرير...</span>
            </div>
            <p class="mt-2">جاري توليد التقرير...</p>
        </div>
    `;
}

function formatCurrency(amount) {
    return new Intl.NumberFormat('ar-YE', {
        style: 'currency',
        currency: 'YER',
        minimumFractionDigits: 0,
        maximumFractionDigits: 0
    }).format(amount).replace('YER', 'ر.ي');
}

function printReport() {
    window.print();
}

function exportReport() {
    if (!currentReportType) {
        showError('يرجى اختيار تقرير أولاً');
        return;
    }
    
    // TODO: تطبيق تصدير التقرير
    showSuccess('سيتم تطوير ميزة التصدير قريباً');
}

function showError(message) {
    const alertDiv = document.createElement('div');
    alertDiv.className = 'alert alert-danger alert-dismissible fade show position-fixed';
    alertDiv.style.cssText = 'top: 20px; right: 20px; z-index: 9999; min-width: 300px;';
    alertDiv.innerHTML = `
        <i class="fas fa-exclamation-triangle me-2"></i>
        ${message}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    `;
    document.body.appendChild(alertDiv);
    
    setTimeout(() => {
        if (alertDiv.parentNode) {
            alertDiv.remove();
        }
    }, 5000);
}

function showSuccess(message) {
    const alertDiv = document.createElement('div');
    alertDiv.className = 'alert alert-success alert-dismissible fade show position-fixed';
    alertDiv.style.cssText = 'top: 20px; right: 20px; z-index: 9999; min-width: 300px;';
    alertDiv.innerHTML = `
        <i class="fas fa-check-circle me-2"></i>
        ${message}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    `;
    document.body.appendChild(alertDiv);
    
    setTimeout(() => {
        if (alertDiv.parentNode) {
            alertDiv.remove();
        }
    }, 3000);
}

// تسجيل الخروج
function logout() {
    if (confirm('هل أنت متأكد من تسجيل الخروج؟')) {
        localStorage.removeItem('isLoggedIn');
        window.location.href = 'login.html';
    }
}
