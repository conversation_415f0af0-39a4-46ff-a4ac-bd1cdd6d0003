using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace ANWBakerySystem.Models
{
    /// <summary>
    /// أوامر الإنتاج
    /// Production Orders
    /// </summary>
    public class ProductionOrder
    {
        [Key]
        public int ProductionOrderId { get; set; }

        [Required]
        [StringLength(50)]
        [Display(Name = "رقم أمر الإنتاج")]
        public string OrderNumber { get; set; } = string.Empty;

        [Required]
        [Display(Name = "تاريخ الأمر")]
        public DateTime OrderDate { get; set; } = DateTime.Now;

        [Display(Name = "تاريخ الإنتاج المخطط")]
        public DateTime? PlannedProductionDate { get; set; }

        [Display(Name = "تاريخ الإنتاج الفعلي")]
        public DateTime? ActualProductionDate { get; set; }

        [Required]
        [Display(Name = "الوصفة")]
        public int RecipeId { get; set; }

        [Required]
        [Display(Name = "المنتج")]
        public int ProductItemId { get; set; }

        [Required]
        [Display(Name = "المخزن")]
        public int WarehouseId { get; set; }

        /// <summary>
        /// الكمية المطلوب إنتاجها
        /// Required production quantity
        /// </summary>
        [Required]
        [Display(Name = "الكمية المطلوبة")]
        [Column(TypeName = "decimal(18,6)")]
        public decimal RequiredQuantity { get; set; }

        /// <summary>
        /// الكمية المنتجة فعلياً
        /// Actually produced quantity
        /// </summary>
        [Display(Name = "الكمية المنتجة")]
        [Column(TypeName = "decimal(18,6)")]
        public decimal ProducedQuantity { get; set; } = 0;

        /// <summary>
        /// التكلفة المخططة بالريال اليمني
        /// Planned cost in Yemeni Rial
        /// </summary>
        [Display(Name = "التكلفة المخططة (ر.ي)")]
        [Column(TypeName = "decimal(18,3)")]
        public decimal PlannedCost { get; set; } = 0;

        /// <summary>
        /// التكلفة الفعلية بالريال اليمني
        /// Actual cost in Yemeni Rial
        /// </summary>
        [Display(Name = "التكلفة الفعلية (ر.ي)")]
        [Column(TypeName = "decimal(18,3)")]
        public decimal ActualCost { get; set; } = 0;

        [Required]
        [Display(Name = "حالة الأمر")]
        public ProductionOrderStatus Status { get; set; } = ProductionOrderStatus.Planned;

        [Display(Name = "الأولوية")]
        public ProductionPriority Priority { get; set; } = ProductionPriority.Normal;

        [StringLength(500)]
        [Display(Name = "ملاحظات")]
        public string? Notes { get; set; }

        [Display(Name = "تاريخ البدء")]
        public DateTime? StartedAt { get; set; }

        [Display(Name = "تاريخ الانتهاء")]
        public DateTime? CompletedAt { get; set; }

        [Display(Name = "بدأ بواسطة")]
        public int? StartedBy { get; set; }

        [Display(Name = "انتهى بواسطة")]
        public int? CompletedBy { get; set; }

        [Display(Name = "تاريخ الإنشاء")]
        public DateTime CreatedAt { get; set; } = DateTime.Now;

        [Display(Name = "تاريخ التحديث")]
        public DateTime? UpdatedAt { get; set; }

        [Display(Name = "منشئ بواسطة")]
        public int CreatedBy { get; set; }

        [Display(Name = "محدث بواسطة")]
        public int? UpdatedBy { get; set; }

        // Navigation properties
        public virtual Recipe Recipe { get; set; } = null!;
        public virtual Item ProductItem { get; set; } = null!;
        public virtual Warehouse Warehouse { get; set; } = null!;
        public virtual User Creator { get; set; } = null!;
        public virtual User? Updater { get; set; }
        public virtual User? StartedByUser { get; set; }
        public virtual User? CompletedByUser { get; set; }
        public virtual ICollection<ProductionOrderItem> ProductionOrderItems { get; set; } = new List<ProductionOrderItem>();

        /// <summary>
        /// حساب التكلفة المخططة
        /// Calculate planned cost
        /// </summary>
        public void CalculatePlannedCost()
        {
            if (Recipe != null)
            {
                var multiplier = RequiredQuantity / Recipe.BaseProductionQuantity;
                PlannedCost = Recipe.TotalCost * multiplier;
            }
        }

        /// <summary>
        /// بدء الإنتاج
        /// Start production
        /// </summary>
        public void StartProduction(int userId)
        {
            Status = ProductionOrderStatus.InProgress;
            StartedAt = DateTime.Now;
            StartedBy = userId;
            UpdatedAt = DateTime.Now;
            UpdatedBy = userId;
        }

        /// <summary>
        /// إنهاء الإنتاج
        /// Complete production
        /// </summary>
        public void CompleteProduction(int userId, decimal producedQty, decimal actualCostValue)
        {
            Status = ProductionOrderStatus.Completed;
            CompletedAt = DateTime.Now;
            CompletedBy = userId;
            ProducedQuantity = producedQty;
            ActualCost = actualCostValue;
            UpdatedAt = DateTime.Now;
            UpdatedBy = userId;
        }
    }

    /// <summary>
    /// مواد أمر الإنتاج
    /// Production Order Items
    /// </summary>
    public class ProductionOrderItem
    {
        [Key]
        public int ProductionOrderItemId { get; set; }

        [Required]
        [Display(Name = "أمر الإنتاج")]
        public int ProductionOrderId { get; set; }

        [Required]
        [Display(Name = "المادة الخام")]
        public int ItemId { get; set; }

        [Required]
        [Display(Name = "الكمية المطلوبة")]
        [Column(TypeName = "decimal(18,6)")]
        public decimal RequiredQuantity { get; set; }

        [Display(Name = "الكمية المستخدمة")]
        [Column(TypeName = "decimal(18,6)")]
        public decimal UsedQuantity { get; set; } = 0;

        [Display(Name = "التكلفة المخططة (ر.ي)")]
        [Column(TypeName = "decimal(18,3)")]
        public decimal PlannedCost { get; set; } = 0;

        [Display(Name = "التكلفة الفعلية (ر.ي)")]
        [Column(TypeName = "decimal(18,3)")]
        public decimal ActualCost { get; set; } = 0;

        [Display(Name = "حالة المادة")]
        public ProductionItemStatus Status { get; set; } = ProductionItemStatus.Planned;

        // Navigation properties
        public virtual ProductionOrder ProductionOrder { get; set; } = null!;
        public virtual Item Item { get; set; } = null!;
    }

    public enum ProductionOrderStatus
    {
        [Display(Name = "مخطط")]
        Planned = 1,
        [Display(Name = "قيد التنفيذ")]
        InProgress = 2,
        [Display(Name = "مكتمل")]
        Completed = 3,
        [Display(Name = "ملغي")]
        Cancelled = 4,
        [Display(Name = "معلق")]
        OnHold = 5
    }

    public enum ProductionPriority
    {
        [Display(Name = "منخفضة")]
        Low = 1,
        [Display(Name = "عادية")]
        Normal = 2,
        [Display(Name = "عالية")]
        High = 3,
        [Display(Name = "عاجلة")]
        Urgent = 4
    }

    public enum ProductionItemStatus
    {
        [Display(Name = "مخطط")]
        Planned = 1,
        [Display(Name = "محجوز")]
        Reserved = 2,
        [Display(Name = "مستخدم")]
        Used = 3,
        [Display(Name = "ملغي")]
        Cancelled = 4
    }
}
