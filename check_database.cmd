@echo off
chcp 65001 > nul
echo ========================================
echo     فحص حالة قاعدة البيانات
echo     Database Status Check
echo ========================================
echo.

echo 🔍 فحص اتصال قاعدة البيانات...
echo.

REM فحص SQL Server LocalDB
echo 📊 فحص SQL Server LocalDB...
sqlcmd -S "(localdb)\mssqllocaldb" -Q "SELECT name FROM sys.databases WHERE name = 'ANWBakeryDB_Real'" -h -1 2>nul | findstr "ANWBakeryDB_Real" >nul
if %ERRORLEVEL% EQU 0 (
    echo ✅ قاعدة البيانات موجودة في LocalDB
    set "DB_FOUND=1"
    set "DB_SERVER=LocalDB"
    
    echo.
    echo 📋 معلومات قاعدة البيانات:
    sqlcmd -S "(localdb)\mssqllocaldb" -d "ANWBakeryDB_Real" -Q "SELECT 'Database Name: ' + DB_NAME() as Info UNION ALL SELECT 'Server: (localdb)\mssqllocaldb' UNION ALL SELECT 'Status: Online' UNION ALL SELECT 'Created: ' + CONVERT(varchar, create_date, 120) FROM sys.databases WHERE name = DB_NAME()" -h -1 2>nul
    
    echo.
    echo 📊 إحصائيات الجداول:
    sqlcmd -S "(localdb)\mssqllocaldb" -d "ANWBakeryDB_Real" -Q "SELECT t.name as 'Table Name', p.rows as 'Row Count' FROM sys.tables t INNER JOIN sys.partitions p ON t.object_id = p.object_id WHERE p.index_id IN (0,1) ORDER BY t.name" -h -1 2>nul
    
) else (
    echo ❌ قاعدة البيانات غير موجودة في LocalDB
    
    REM فحص SQL Server Express
    echo.
    echo 📊 فحص SQL Server Express...
    sqlcmd -S ".\SQLEXPRESS" -Q "SELECT name FROM sys.databases WHERE name = 'ANWBakeryDB_Real'" -h -1 2>nul | findstr "ANWBakeryDB_Real" >nul
    if %ERRORLEVEL% EQU 0 (
        echo ✅ قاعدة البيانات موجودة في SQL Server Express
        set "DB_FOUND=1"
        set "DB_SERVER=SQLEXPRESS"
        
        echo.
        echo 📋 معلومات قاعدة البيانات:
        sqlcmd -S ".\SQLEXPRESS" -d "ANWBakeryDB_Real" -Q "SELECT 'Database Name: ' + DB_NAME() as Info UNION ALL SELECT 'Server: .\SQLEXPRESS' UNION ALL SELECT 'Status: Online' UNION ALL SELECT 'Created: ' + CONVERT(varchar, create_date, 120) FROM sys.databases WHERE name = DB_NAME()" -h -1 2>nul
        
        echo.
        echo 📊 إحصائيات الجداول:
        sqlcmd -S ".\SQLEXPRESS" -d "ANWBakeryDB_Real" -Q "SELECT t.name as 'Table Name', p.rows as 'Row Count' FROM sys.tables t INNER JOIN sys.partitions p ON t.object_id = p.object_id WHERE p.index_id IN (0,1) ORDER BY t.name" -h -1 2>nul
        
    ) else (
        echo ❌ قاعدة البيانات غير موجودة في SQL Server Express
        set "DB_FOUND=0"
    )
)

echo.
echo ========================================

if "%DB_FOUND%"=="1" (
    echo ✅ حالة قاعدة البيانات: متصلة وجاهزة
    echo 🔗 الخادم: %DB_SERVER%
    echo 📁 اسم قاعدة البيانات: ANWBakeryDB_Real
    echo 💾 النوع: SQL Server Database
    echo.
    echo 🚀 يمكنك الآن تشغيل التطبيق:
    echo    dotnet run
    echo.
    echo 🌐 أو فتح المتصفح على:
    echo    http://localhost:5000
    echo.
    echo 🔐 بيانات الدخول:
    echo    👤 admin
    echo    🔑 admin123
) else (
    echo ❌ حالة قاعدة البيانات: غير موجودة
    echo.
    echo 🔧 لإنشاء قاعدة البيانات، قم بتشغيل:
    echo    setup_real_database.cmd
    echo.
    echo 📥 أو تأكد من تثبيت SQL Server:
    echo    - SQL Server LocalDB
    echo    - SQL Server Express
    echo    - SQL Server Developer Edition
)

echo.
echo ========================================
pause
