using Microsoft.EntityFrameworkCore;
using ANWBakerySystem.Data;
using ANWBakerySystem.Models;

namespace ANWBakerySystem.Repositories
{
    /// <summary>
    /// تطبيق مستودع وحدات القياس المتقدم
    /// Advanced Unit Repository Implementation
    /// </summary>
    public class UnitRepository : IUnitRepository
    {
        private readonly ANWBakeryDbContext _context;

        public UnitRepository(ANWBakeryDbContext context)
        {
            _context = context;
        }

        public async Task<IEnumerable<Unit>> GetAllUnitsAsync()
        {
            return await _context.Units
                .Include(u => u.Creator)
                .Include(u => u.Updater)
                .OrderBy(u => u.UnitName)
                .ToListAsync();
        }

        public async Task<IEnumerable<Unit>> GetActiveUnitsAsync()
        {
            return await _context.Units
                .Where(u => u.IsActive)
                .OrderBy(u => u.UnitName)
                .ToListAsync();
        }

        public async Task<Unit?> GetUnitByIdAsync(int unitId)
        {
            return await _context.Units
                .Include(u => u.Creator)
                .Include(u => u.Updater)
                .FirstOrDefaultAsync(u => u.UnitId == unitId);
        }

        public async Task<Unit?> GetUnitByNameAsync(string unitName)
        {
            return await _context.Units
                .FirstOrDefaultAsync(u => u.UnitName == unitName);
        }

        public async Task<Unit> CreateUnitAsync(Unit unit)
        {
            _context.Units.Add(unit);
            await _context.SaveChangesAsync();
            return unit;
        }

        public async Task<Unit> UpdateUnitAsync(Unit unit)
        {
            unit.UpdatedAt = DateTime.Now;
            _context.Units.Update(unit);
            await _context.SaveChangesAsync();
            return unit;
        }

        public async Task<bool> DeleteUnitAsync(int unitId)
        {
            var unit = await _context.Units.FindAsync(unitId);
            if (unit == null) return false;

            unit.IsActive = false;
            unit.UpdatedAt = DateTime.Now;
            await _context.SaveChangesAsync();
            return true;
        }

        public async Task<bool> UnitExistsAsync(int unitId)
        {
            return await _context.Units.AnyAsync(u => u.UnitId == unitId);
        }

        public async Task<bool> UnitNameExistsAsync(string unitName)
        {
            return await _context.Units.AnyAsync(u => u.UnitName == unitName);
        }

        #region Unit Conversions

        public async Task<UnitConversion> CreateConversionAsync(UnitConversion conversion)
        {
            _context.UnitConversions.Add(conversion);
            await _context.SaveChangesAsync();
            return conversion;
        }

        public async Task<IEnumerable<UnitConversion>> GetConversionsAsync(int fromUnitId, int toUnitId)
        {
            return await _context.UnitConversions
                .Include(uc => uc.FromUnit)
                .Include(uc => uc.ToUnit)
                .Where(uc => uc.FromUnitId == fromUnitId && uc.ToUnitId == toUnitId)
                .OrderByDescending(uc => uc.ConversionDate)
                .ToListAsync();
        }

        public async Task<decimal> GetConversionFactorAsync(int fromUnitId, int toUnitId, UnitType fromType, UnitType toType)
        {
            if (fromUnitId == toUnitId && fromType == toType)
                return 1;

            var fromUnit = await GetUnitByIdAsync(fromUnitId);
            var toUnit = await GetUnitByIdAsync(toUnitId);

            if (fromUnit == null || toUnit == null)
                return 1;

            // تحويل من الوحدة المصدر إلى الوحدة الأساسية
            decimal fromToBase = GetUnitTypeConversionFactor(fromUnit, fromType);
            
            // تحويل من الوحدة الأساسية إلى الوحدة الهدف
            decimal baseToTarget = GetUnitTypeConversionFactor(toUnit, toType);

            // إذا كانت الوحدات مختلفة، نحتاج لتحويل إضافي
            if (fromUnitId != toUnitId)
            {
                // هنا يمكن إضافة منطق تحويل بين وحدات مختلفة
                // مثل تحويل من كيلوجرام إلى لتر (يحتاج كثافة المادة)
                return 1; // افتراضي
            }

            return fromToBase / baseToTarget;
        }

        public async Task<decimal> ConvertQuantityAsync(decimal quantity, int fromUnitId, int toUnitId, UnitType fromType, UnitType toType)
        {
            var conversionFactor = await GetConversionFactorAsync(fromUnitId, toUnitId, fromType, toType);
            return quantity * conversionFactor;
        }

        #endregion

        #region Advanced Unit Calculations

        public async Task<decimal> ConvertToBaseUnitAsync(decimal quantity, int unitId, UnitType unitType)
        {
            var unit = await GetUnitByIdAsync(unitId);
            if (unit == null) return quantity;

            var conversionFactor = GetUnitTypeConversionFactor(unit, unitType);
            return quantity * conversionFactor;
        }

        public async Task<decimal> ConvertFromBaseUnitAsync(decimal baseQuantity, int unitId, UnitType unitType)
        {
            var unit = await GetUnitByIdAsync(unitId);
            if (unit == null) return baseQuantity;

            var conversionFactor = GetUnitTypeConversionFactor(unit, unitType);
            return baseQuantity / conversionFactor;
        }

        public async Task<decimal> CalculatePriceByUnitTypeAsync(decimal basePrice, int unitId, UnitType unitType)
        {
            var unit = await GetUnitByIdAsync(unitId);
            if (unit == null) return basePrice;

            var conversionFactor = GetUnitTypeConversionFactor(unit, unitType);
            return basePrice * conversionFactor;
        }

        #endregion

        #region Private Helper Methods

        /// <summary>
        /// حساب معامل التحويل حسب نوع الوحدة
        /// Calculate conversion factor based on unit type
        /// </summary>
        private decimal GetUnitTypeConversionFactor(Unit unit, UnitType unitType)
        {
            return unitType switch
            {
                UnitType.Large => unit.GetLargeToSmallConversionFactor(),
                UnitType.Medium => unit.GetMediumToSmallConversionFactor(),
                UnitType.Small => 1,
                UnitType.Base => 1,
                _ => 1
            };
        }

        #endregion
    }
}
