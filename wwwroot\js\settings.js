// إعدادات النظام
let systemSettings = {
    general: {
        companyName: 'مخبوزات ANW',
        companyAddress: 'صنعاء - اليمن',
        companyPhone: '+967-1-234567',
        companyEmail: '<EMAIL>'
    },
    currency: {
        baseCurrency: 'YER',
        decimalPlaces: 3,
        currencySymbol: 'ر.ي'
    },
    system: {
        language: 'ar',
        dateFormat: 'dd/mm/yyyy',
        enableNotifications: true,
        enableBackup: true
    },
    printing: {
        defaultPrinter: 'system',
        paperSize: 'A4',
        printLogo: true,
        printBarcode: true
    },
    security: {
        sessionTimeout: 30,
        enableAuditLog: true,
        requirePasswordChange: false
    }
};

// تحميل الصفحة
document.addEventListener('DOMContentLoaded', function() {
    console.log('🔓 صفحة إعدادات النظام - تم تجاوز فحص المصادقة');
    loadSettings();
    updateDatabaseInfo();
});

// تحميل الإعدادات
function loadSettings() {
    console.log('تحميل إعدادات النظام...');
    
    // تحميل الإعدادات العامة
    document.getElementById('companyName').value = systemSettings.general.companyName;
    document.getElementById('companyAddress').value = systemSettings.general.companyAddress;
    document.getElementById('companyPhone').value = systemSettings.general.companyPhone;
    document.getElementById('companyEmail').value = systemSettings.general.companyEmail;
    
    // تحميل إعدادات العملة
    document.getElementById('baseCurrency').value = systemSettings.currency.baseCurrency;
    document.getElementById('decimalPlaces').value = systemSettings.currency.decimalPlaces;
    document.getElementById('currencySymbol').value = systemSettings.currency.currencySymbol;
    
    // تحميل إعدادات النظام
    document.getElementById('language').value = systemSettings.system.language;
    document.getElementById('dateFormat').value = systemSettings.system.dateFormat;
    document.getElementById('enableNotifications').checked = systemSettings.system.enableNotifications;
    document.getElementById('enableBackup').checked = systemSettings.system.enableBackup;
    
    // تحميل إعدادات الطباعة
    document.getElementById('defaultPrinter').value = systemSettings.printing.defaultPrinter;
    document.getElementById('paperSize').value = systemSettings.printing.paperSize;
    document.getElementById('printLogo').checked = systemSettings.printing.printLogo;
    document.getElementById('printBarcode').checked = systemSettings.printing.printBarcode;
    
    // تحميل إعدادات الأمان
    document.getElementById('sessionTimeout').value = systemSettings.security.sessionTimeout;
    document.getElementById('enableAuditLog').checked = systemSettings.security.enableAuditLog;
    document.getElementById('requirePasswordChange').checked = systemSettings.security.requirePasswordChange;
}

// حفظ الإعدادات العامة
function saveGeneralSettings() {
    systemSettings.general.companyName = document.getElementById('companyName').value;
    systemSettings.general.companyAddress = document.getElementById('companyAddress').value;
    systemSettings.general.companyPhone = document.getElementById('companyPhone').value;
    systemSettings.general.companyEmail = document.getElementById('companyEmail').value;
    
    console.log('تم حفظ الإعدادات العامة:', systemSettings.general);
    showSuccessMessage('تم حفظ الإعدادات العامة بنجاح!');
}

// حفظ إعدادات العملة
function saveCurrencySettings() {
    systemSettings.currency.decimalPlaces = parseInt(document.getElementById('decimalPlaces').value);
    
    console.log('تم حفظ إعدادات العملة:', systemSettings.currency);
    showSuccessMessage('تم حفظ إعدادات العملة بنجاح!');
}

// حفظ إعدادات النظام
function saveSystemSettings() {
    systemSettings.system.language = document.getElementById('language').value;
    systemSettings.system.dateFormat = document.getElementById('dateFormat').value;
    systemSettings.system.enableNotifications = document.getElementById('enableNotifications').checked;
    systemSettings.system.enableBackup = document.getElementById('enableBackup').checked;
    
    console.log('تم حفظ إعدادات النظام:', systemSettings.system);
    showSuccessMessage('تم حفظ إعدادات النظام بنجاح!');
}

// حفظ إعدادات الطباعة
function savePrintingSettings() {
    systemSettings.printing.defaultPrinter = document.getElementById('defaultPrinter').value;
    systemSettings.printing.paperSize = document.getElementById('paperSize').value;
    systemSettings.printing.printLogo = document.getElementById('printLogo').checked;
    systemSettings.printing.printBarcode = document.getElementById('printBarcode').checked;
    
    console.log('تم حفظ إعدادات الطباعة:', systemSettings.printing);
    showSuccessMessage('تم حفظ إعدادات الطباعة بنجاح!');
}

// حفظ إعدادات الأمان
function saveSecuritySettings() {
    systemSettings.security.sessionTimeout = parseInt(document.getElementById('sessionTimeout').value);
    systemSettings.security.enableAuditLog = document.getElementById('enableAuditLog').checked;
    systemSettings.security.requirePasswordChange = document.getElementById('requirePasswordChange').checked;
    
    console.log('تم حفظ إعدادات الأمان:', systemSettings.security);
    showSuccessMessage('تم حفظ إعدادات الأمان بنجاح!');
}

// تحديث معلومات قاعدة البيانات
function updateDatabaseInfo() {
    // محاكاة حجم قاعدة البيانات
    const usedSpace = 2.5; // MB
    const totalSpace = 10; // MB
    const percentage = (usedSpace / totalSpace) * 100;
    
    const progressBar = document.getElementById('dbSizeProgress');
    progressBar.style.width = percentage + '%';
    progressBar.textContent = Math.round(percentage) + '%';
    
    // تحديد لون شريط التقدم حسب الاستخدام
    progressBar.className = 'progress-bar';
    if (percentage > 80) {
        progressBar.classList.add('bg-danger');
    } else if (percentage > 60) {
        progressBar.classList.add('bg-warning');
    } else {
        progressBar.classList.add('bg-success');
    }
}

// إنشاء نسخة احتياطية
function createBackup() {
    showLoadingMessage('جاري إنشاء النسخة الاحتياطية...');
    
    // محاكاة عملية النسخ الاحتياطي
    setTimeout(() => {
        const backupData = {
            timestamp: new Date().toISOString(),
            settings: systemSettings,
            version: '1.0.0',
            size: '2.5 MB'
        };
        
        console.log('النسخة الاحتياطية:', backupData);
        
        // محاكاة تحميل الملف
        const dataStr = JSON.stringify(backupData, null, 2);
        const dataBlob = new Blob([dataStr], {type: 'application/json'});
        const url = URL.createObjectURL(dataBlob);
        
        const link = document.createElement('a');
        link.href = url;
        link.download = `anw_backup_${new Date().toISOString().split('T')[0]}.json`;
        link.click();
        
        URL.revokeObjectURL(url);
        showSuccessMessage('تم إنشاء النسخة الاحتياطية وتحميلها بنجاح!');
    }, 2000);
}

// تحسين قاعدة البيانات
function optimizeDatabase() {
    if (confirm('هل أنت متأكد من تحسين قاعدة البيانات؟ قد تستغرق هذه العملية بعض الوقت.')) {
        showLoadingMessage('جاري تحسين قاعدة البيانات...');
        
        setTimeout(() => {
            console.log('تم تحسين قاعدة البيانات');
            updateDatabaseInfo();
            showSuccessMessage('تم تحسين قاعدة البيانات بنجاح! تم توفير 0.3 MB من المساحة.');
        }, 3000);
    }
}

// تصغير حجم قاعدة البيانات
function minimizeDatabase() {
    if (confirm('هل أنت متأكد من تصغير حجم قاعدة البيانات؟ سيتم حذف البيانات القديمة والمؤقتة.')) {
        showLoadingMessage('جاري تصغير حجم قاعدة البيانات...');
        
        setTimeout(() => {
            console.log('تم تصغير حجم قاعدة البيانات');
            updateDatabaseInfo();
            showSuccessMessage('تم تصغير حجم قاعدة البيانات بنجاح! تم توفير 0.8 MB من المساحة.');
        }, 4000);
    }
}

// إعادة تعيين قاعدة البيانات
function resetDatabase() {
    if (confirm('تحذير: هذه العملية ستحذف جميع البيانات وتعيد النظام لحالته الأولى. هل أنت متأكد؟')) {
        if (confirm('هذا تأكيد أخير. جميع البيانات ستفقد نهائياً. هل تريد المتابعة؟')) {
            showLoadingMessage('جاري إعادة تعيين قاعدة البيانات...');
            
            setTimeout(() => {
                console.log('تم إعادة تعيين قاعدة البيانات');
                updateDatabaseInfo();
                showSuccessMessage('تم إعادة تعيين قاعدة البيانات بنجاح! النظام جاهز للاستخدام.');
            }, 5000);
        }
    }
}

// عرض رسالة نجاح
function showSuccessMessage(message) {
    // إنشاء تنبيه Bootstrap
    const alertDiv = document.createElement('div');
    alertDiv.className = 'alert alert-success alert-dismissible fade show position-fixed';
    alertDiv.style.top = '20px';
    alertDiv.style.right = '20px';
    alertDiv.style.zIndex = '9999';
    alertDiv.innerHTML = `
        <i class="fas fa-check-circle me-2"></i>
        ${message}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    `;
    
    document.body.appendChild(alertDiv);
    
    // إزالة التنبيه تلقائياً بعد 3 ثوان
    setTimeout(() => {
        if (alertDiv.parentNode) {
            alertDiv.parentNode.removeChild(alertDiv);
        }
    }, 3000);
}

// عرض رسالة تحميل
function showLoadingMessage(message) {
    // إنشاء تنبيه تحميل
    const alertDiv = document.createElement('div');
    alertDiv.className = 'alert alert-info alert-dismissible fade show position-fixed';
    alertDiv.style.top = '20px';
    alertDiv.style.right = '20px';
    alertDiv.style.zIndex = '9999';
    alertDiv.id = 'loadingAlert';
    alertDiv.innerHTML = `
        <div class="d-flex align-items-center">
            <div class="spinner-border spinner-border-sm me-2" role="status"></div>
            ${message}
        </div>
    `;
    
    document.body.appendChild(alertDiv);
}

// إزالة رسالة التحميل
function hideLoadingMessage() {
    const loadingAlert = document.getElementById('loadingAlert');
    if (loadingAlert) {
        loadingAlert.parentNode.removeChild(loadingAlert);
    }
}

// تصدير الإعدادات
function exportSettings() {
    const settingsData = {
        exportDate: new Date().toISOString(),
        settings: systemSettings,
        version: '1.0.0'
    };
    
    const dataStr = JSON.stringify(settingsData, null, 2);
    const dataBlob = new Blob([dataStr], {type: 'application/json'});
    const url = URL.createObjectURL(dataBlob);
    
    const link = document.createElement('a');
    link.href = url;
    link.download = `anw_settings_${new Date().toISOString().split('T')[0]}.json`;
    link.click();
    
    URL.revokeObjectURL(url);
    showSuccessMessage('تم تصدير الإعدادات بنجاح!');
}

// استيراد الإعدادات
function importSettings() {
    const input = document.createElement('input');
    input.type = 'file';
    input.accept = '.json';
    
    input.onchange = function(event) {
        const file = event.target.files[0];
        if (file) {
            const reader = new FileReader();
            reader.onload = function(e) {
                try {
                    const importedData = JSON.parse(e.target.result);
                    if (importedData.settings) {
                        systemSettings = importedData.settings;
                        loadSettings();
                        showSuccessMessage('تم استيراد الإعدادات بنجاح!');
                    } else {
                        alert('ملف الإعدادات غير صحيح');
                    }
                } catch (error) {
                    alert('خطأ في قراءة ملف الإعدادات');
                }
            };
            reader.readAsText(file);
        }
    };
    
    input.click();
}
